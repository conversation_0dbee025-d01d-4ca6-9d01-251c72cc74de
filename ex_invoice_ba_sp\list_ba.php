<?php
ob_start();
session_start();
include('../include/ex_fungsi.php');
include('../include/validasi.php');
require_once('../security_helper.php');
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$halaman_id=4872;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

if (empty($user_id) || empty($user_org)) {
  echo '<script type="text/javascript">
          alert("Session expired. Please login again.");
          window.location.href = "../index.php";
        </script>';
  exit();
}

if (!isset($_SESSION['csrf_token'])) {
  $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (!isset($_POST['csrf_token']) || ($_SESSION['csrf_token'] !== $_POST['csrf_token'])) {
    echo '<script type="text/javascript">
            alert("Invalid request. Please try again.");
            window.location.href = "' . htmlspecialchars($_SERVER['PHP_SELF'], ENT_QUOTES, 'UTF-8') . '";
          </script>';
    exit();
  }
}

if (!isset($_SESSION['last_regeneration'])) {
  $_SESSION['last_regeneration'] = time();
} else if (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
  session_regenerate_id(true);
  $_SESSION['last_regeneration'] = time();
}
// var_dump($_SESSION['user_id']);
// $mp_coics=$fungsi->getComin($conn,$user_org);
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// }

//echo $orgIn;




$page="list_ba.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$tanggal_mulai = isset($_POST['tanggal_mulai']) ? trim($_POST['tanggal_mulai']) : '';
$tanggal_selesai = isset($_POST['tanggal_selesai']) ? trim($_POST['tanggal_selesai']) : '';
$no_ba = isset($_POST['no_ba']) ? trim($_POST['no_ba']) : '';
$statuse = isset($_POST['statuse']) ? trim($_POST['statuse']) : '';

if (!is_valid_date($tanggal_mulai)) {
  $tanggal_mulai = '';
  $komen = "Invalid start date format. Please use DD-MM-YYYY format.";
}
if (!is_valid_date($tanggal_selesai)) {
  $tanggal_selesai = '';
  $komen = "Invalid end date format. Please use DD-MM-YYYY format.";
}

$allowed_statuses = array('', '50-', '10', '50');
if (!in_array($statuse, $allowed_statuses)) {
  $statuse = '';
}

$shp_cost_filter = '';
switch ($user_id) {
  case '1489':
    $shp_cost_filter = ' AND A.TOTAL_INV <= 50000000 ';
    break;
  case '1490':
    $shp_cost_filter = ' AND A.TOTAL_INV > 50000000 AND A.TOTAL_INV <= 1000000000 ';
    break;
  case '1491':
    $shp_cost_filter = ' AND A.TOTAL_INV > 1000000000 ';
    break;
  default:
    $shp_cost_filter = '';
    break;
}

$currentPage="list_ba.php";
$komen="";
if(isset($_POST['cari'])){
	$_SESSION['tgl_mulai_inv'] = $_POST['tanggal_mulai'];
	$_SESSION['tgl_end_inv'] = $_POST['tanggal_selesai'];
	$_SESSION['no_inv'] = $_POST['no_invoice'];
	if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_ba == "" and $statuse == ""){
		$sql= "
			SELECT DISTINCT A.ORG, A.NO_BA, A.NO_VENDOR, A.NAMA_VENDOR, A.NO_PAJAK_EX, A.TGL_INVOICE, A.TGL_BA, A.KLAIM_SEMEN, A.KLAIM_KTG, A.PDPKS, A.PDPKK, A.PAJAK_INV, A.TOTAL_INV, A.NO_BAF, A.STATUS_BA, A.FILENAME, TO_CHAR(A.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1, B.STATUS_BA_INVOICE, C.WARNA_PLAT FROM EX_BA A LEFT JOIN EX_BA_INVOICE B ON B.NO_BA = A.NO_BA AND B.DIPAKAI = 1 LEFT JOIN EX_TRANS_HDR C ON A.NO_BA = C.NO_BA WHERE
				A.DELETE_MARK ='0'
				AND A.ORG = :org_param
				AND A.NO_BA IS NOT NULL
				AND (C.WARNA_PLAT IS NOT NULL OR C.WARNA_PLAT != '')
				AND A.STATUS_BA = '50'
				AND B.STATUS_BA_INVOICE IS NULL
				AND (
					B.STATUS_BA_INVOICE IS NULL
					OR B.STATUS_BA_INVOICE = '10'
					OR B.STATUS_BA_INVOICE = '30'
					OR B.STATUS_BA_INVOICE = '40'
					OR B.STATUS_BA_INVOICE = '50'
				)
				" . $shp_cost_filter . "
			ORDER BY
				A.ORG,
				A.NO_VENDOR,
				A.NO_BA DESC
		";
	} else {
		$pakeor=0;
		$sql= "
			SELECT DISTINCT A.ORG, A.NO_BA, A.NO_VENDOR, A.NAMA_VENDOR, A.NO_PAJAK_EX, A.TGL_INVOICE, A.TGL_BA, A.KLAIM_SEMEN, A.KLAIM_KTG, A.PDPKS, A.PDPKK, A.PAJAK_INV, A.TOTAL_INV, A.NO_BAF, A.STATUS_BA, A.FILENAME, TO_CHAR(A.TGL_BA, 'DD-MM-YYYY') AS TGL_INVOICE1, B.STATUS_BA_INVOICE, C.WARNA_PLAT FROM EX_BA A LEFT JOIN EX_BA_INVOICE B ON B.NO_BA = A.NO_BA AND B.DIPAKAI = 1 LEFT JOIN EX_TRANS_HDR C ON A.NO_BA = C.NO_BA WHERE (C.WARNA_PLAT IS NOT NULL OR C.WARNA_PLAT != '') AND B.STATUS_BA_INVOICE IS NULL AND
		";

		$bind_params = array();

		if($vendor!=""){
		$sql.=" A.NO_VENDOR LIKE :vendor_param";
		$bind_params['vendor_param'] = '%' . $vendor . '%';
		$pakeor=1;
		}
		if($tanggal_mulai!="" or $tanggal_selesai!=""){

			if ($tanggal_mulai=="")
			$tanggal_mulai_sql = "01-01-1990";
			else
			$tanggal_mulai_sql = $tanggal_mulai;

			if ($tanggal_selesai=="")
			$tanggal_selesai_sql = "12-12-9999";
			else
			$tanggal_selesai_sql = $tanggal_selesai;

			if($pakeor==1){
			$sql.=" AND A.TGL_BA BETWEEN TO_DATE(:tanggal_mulai_param, 'DD-MM-YYYY') AND TO_DATE(:tanggal_selesai_param, 'DD-MM-YYYY') ";
			}else{
			$sql.=" A.TGL_BA BETWEEN TO_DATE(:tanggal_mulai_param, 'DD-MM-YYYY') AND TO_DATE(:tanggal_selesai_param, 'DD-MM-YYYY') ";
			$pakeor=1;
			}
			$bind_params['tanggal_mulai_param'] = $tanggal_mulai_sql;
			$bind_params['tanggal_selesai_param'] = $tanggal_selesai_sql;
		}
		if($no_ba!=""){
			if($pakeor==1){
			$sql.=" AND A.NO_BA LIKE :no_ba_param ";
			}else{
			$sql.=" A.NO_BA LIKE :no_ba_param ";
			$pakeor=1;
			}
			$bind_params['no_ba_param'] = '%' . $no_ba . '%';
		}
		if($statuse == "50-"){
			$sql.=" AND B.STATUS_BA_INVOICE IS NULL ";
		}
		else if($statuse == "10"){
			$sql.=" AND B.STATUS_BA_INVOICE = '10' ";
		}
		else if($statuse == "50"){
			$sql.=" AND B.STATUS_BA_INVOICE='20' ";
		}else{
			$sql.=" AND (
				B.STATUS_BA_INVOICE IS NULL
				OR B.STATUS_BA_INVOICE = '10'
				OR B.STATUS_BA_INVOICE = '20'
				OR B.STATUS_BA_INVOICE = '30'
				OR B.STATUS_BA_INVOICE = '40'
				OR B.STATUS_BA_INVOICE = '50'
			) ";
		}
		$sql.="
			AND
				A.DELETE_MARK ='0'
				AND A.ORG = :org_param
				AND A.NO_BA IS NOT NULL
				AND A.STATUS_BA  = '50'
				" . $shp_cost_filter . "
			ORDER BY
				A.ORG,
				A.NO_VENDOR,
				A.NO_BA DESC
		";
		$bind_params['org_param'] = $orgIn;
	}
	$query= oci_parse($conn, $sql);

	if($vendor=="" and $tanggal_mulai == "" and $tanggal_selesai == "" and $no_ba == "" and $statuse == ""){
		oci_bind_by_name($query, ':org_param', $orgIn);
	} else {
  		foreach($bind_params as $param_name => $param_value) {
			oci_bind_by_name($query, ':' . $param_name, $bind_params[$param_name]);
		}
	}

	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$com[]=$row[ORG];
		$no_ba_v[]=$row[NO_BA];
		$vendor_v[]=$row[NO_VENDOR];
		$nama_vendor_v[]=$row[NAMA_VENDOR];
		$no_pajak_ex_v[]=$row[NO_PAJAK_EX];
		$tgl_invoice_v[]=$row[TGL_INVOICE];
		$tgl_ba_v[]=$row[TGL_BA];
		$klaim_semen_v[]=$row[KLAIM_SEMEN];
		$klaim_ktg_v[]=$row[KLAIM_KTG];
		$pdpks_v[]=$row[PDPKS]; 
		$pend_ktg_v[]=$row[PDPKK]; 
		$pajak_v[]=$row[PAJAK_INV];
		$total_klaim_v[]=$row[TOTAL_INV];
		$no_baf[]=$row[NO_BAF];
		$status[]=$row[STATUS_BA];
		$status_invoice[]=$row[STATUS_BA_INVOICE];
		$filename[]=$row[FILENAME];
		$warna_plat_v[]=$row[WARNA_PLAT]; 
		
	}
	$total=count($no_ba_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar BA Rekapitulasi</th>
</tr></table></div>
<?php
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search BA Rekapitulasi </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<?php echo htmlspecialchars($page, ENT_QUOTES, 'UTF-8'); ?>" >
  <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>" />
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Berita Acara</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?php echo htmlspecialchars($no_ba, ENT_QUOTES, 'UTF-8'); ?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Periode Berita Acara </td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?php echo htmlspecialchars($hanyabaca, ENT_QUOTES, 'UTF-8'); ?> value="<?php echo htmlspecialchars($tanggal_mulai, ENT_QUOTES, 'UTF-8'); ?>" />
          <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
        &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?php echo htmlspecialchars($hanyabaca, ENT_QUOTES, 'UTF-8'); ?> value="<?php echo htmlspecialchars($tanggal_selesai, ENT_QUOTES, 'UTF-8'); ?>" />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
    </tr>
	
    <tr>
      <td  class="puso">Status </td>
      <td  class="puso">:</td>
      <td ><select name="statuse" id="statuse">
			  <option value="">- Pilih Status -</option>
			  <option value="50-">BA Approved</option>
			</select>
		</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
	<? } ?>
<br />
<br />
<?php
	if($total>0){

?>
<form id="data_claim" name="data_claim" method="post" action="komentar.php" >

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA Rekapitulasi </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		<td align="center"><strong>No.</strong></td>
		<td align="center"><strong>Org</strong></td>
		<td align="center"><strong>BA Rekapitulasi</strong></td>
		<td align="center"><strong>Tgl BA</strong></td>
		<td align="center"><strong>Total SPJ</strong></td>
		<td align="center"><strong>Status</strong></td>
		<td align="center"><strong>Vendor</strong></td>
		<td align="center"><strong>Warna Plat</strong></td>
		<td align="center"><strong>Aksi</strong></td>
      </tr >
	  </thead>
	  <tbody>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
                $orgCom="orgke".$i;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>

		<td align="center"><?php echo htmlspecialchars($b, ENT_QUOTES, 'UTF-8'); ?></td>
        <td align="center"><?php echo htmlspecialchars($com[$i], ENT_QUOTES, 'UTF-8'); ?><input name="<?php echo htmlspecialchars($orgCom, ENT_QUOTES, 'UTF-8'); ?>" id="<?php echo htmlspecialchars($orgCom, ENT_QUOTES, 'UTF-8'); ?>" type="hidden" value="<?php echo htmlspecialchars($com[$i], ENT_QUOTES, 'UTF-8'); ?>" /></td>
		<? 
		$no_cek=$no_ba_v[$i];
		?>	
		<td align="center">
			<a href="javascript:popUp('../ex_ba_sp/upload/<?php echo htmlspecialchars($filename[$i], ENT_QUOTES, 'UTF-8'); ?>')"><?php echo htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8'); ?></a>
		</td>
		<td align="center"><?php echo htmlspecialchars($tgl_ba_v[$i], ENT_QUOTES, 'UTF-8'); ?></td>
		<td align="center"><?php echo htmlspecialchars(number_format($total_klaim_v[$i]+$pajak_v[$i],0,",","."), ENT_QUOTES, 'UTF-8'); ?></td>
		<td align="center"><?php
			if($status_invoice[$i] == '') {
				if($status[$i] == '50') {
					echo htmlspecialchars('BA Approved', ENT_QUOTES, 'UTF-8');
				}
			} else {
				if($status_invoice[$i] == '10') { // upload invoice
					echo htmlspecialchars('Upload Invoice', ENT_QUOTES, 'UTF-8');
				} else if($status_invoice[$i] == '20') { // waiting approval
					echo htmlspecialchars('Approved', ENT_QUOTES, 'UTF-8');
				} else if($status_invoice[$i] == '30') { // revisi
					echo htmlspecialchars('Reverse', ENT_QUOTES, 'UTF-8');
				} else if($status_invoice[$i] == '40') { // reject
					echo htmlspecialchars('Rejected', ENT_QUOTES, 'UTF-8');
				} else if($status_invoice[$i] == '50') { // approve
					echo htmlspecialchars('Approved', ENT_QUOTES, 'UTF-8');
				}else{
					echo htmlspecialchars($status_invoice[$i], ENT_QUOTES, 'UTF-8');
				}
			}
		?></td>
		<td align="center"><?php echo htmlspecialchars($nama_vendor_v[$i], ENT_QUOTES, 'UTF-8'); ?></td>
		<td align="center"><?php echo htmlspecialchars($warna_plat_v[$i], ENT_QUOTES, 'UTF-8'); ?></td>
		<td align="center"><?php
			if($status_invoice[$i] == '') {
				if($status[$i] == '50') {
					echo '<a href="create_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Create</a>';
				}
			} else {
				if($status_invoice[$i] == '10') { // upload invoice
					//echo '<a href="upload_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Detail</a>';
				} else if($status_invoice[$i] == '20') { // waiting approval
					//echo '<a href="approval_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Detail</a>';
				} else if($status_invoice[$i] == '30') { // revisi
					//echo '<a href="create_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Create</a>';
					//echo '<a href="approval_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Detail</a>';
				} else if($status_invoice[$i] == '40') { // reject
					//echo '<a href="detail_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Detail</a>';
					//echo '<a href="revisi_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Detail</a>';
				} else if($status_invoice[$i] == '50') { // approve
					echo '<a href="detail_invoice_ba.php?no_ba='.htmlspecialchars($no_ba_v[$i], ENT_QUOTES, 'UTF-8').'">Detail</a>';
				}
			}
		?></td>
		</tr>
	  <? } ?>
			<tr class="quote">
				<td colspan="9" align="center" style="padding: 8px;">
					<a href="<?php echo htmlspecialchars($page, ENT_QUOTES, 'UTF-8'); ?>" class="button">Back</a>
				</td>
			</tr>
		</tbody>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?php
echo htmlspecialchars($komen, ENT_QUOTES, 'UTF-8');

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>

</body>
</html>
