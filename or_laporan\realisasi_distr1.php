<? 

$distr = '';
$tgl_fr = '';
$tgl_to = '';
$noso = '';

session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);


include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=153;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr=$_SESSION['distr_id'];
$distr=$fungsi->sapcode($distr);
$mp_coics=$fungsi->getComin($conn,$user_org);

$page="realisasi_distr1.php";
$currentPage="realisasi_distr1.php";
$komen="";
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
    <script language="JavaScript">
        <!--
        alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
        //-->
    </script>

    <a href="../index.php">Login....</a>
<?

exit();
}

if($_SESSION['channel'] == '50'){
    $nama = "Customer";
} else{
    $nama = "Distributor";
}

//$tgl_fr = '01'.date("-m-Y");
$tgl_fr = date("d-m-Y",strtotime('-6 days'));
$tgl_to = date("d-m-Y");
 
if(isset($_POST['cari'])){
		$org = $_POST['org'];
                echo $org;
               
                $tglz = $_POST['tgl1'];
		list($day,$month,$year)=explode('-', $tglz);
		$tglz=$year;
		//echo $tglz;
                
                $tgly = $_POST['tgl2'];
		list($day,$month,$year)=explode('-', $tgly);
		$tgly=$year;
		//echo $tgly;
                
		$nama_sold_to = $_POST['nama_sold_to'];
		$exp = $_POST['vendor'];
		$distrik = $_POST['kode_distrik'];
		$status= $_POST['status'];
		$noso = $fungsi->sapcode($_POST['noso']);
		$auart = $_POST['so_type'];
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

		$sap = new SAPConnection();
	        $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
                $fce->X_VKORG = $org;
                $fce->LRI_VKORG->row['SIGN']='I';
                $fce->LRI_VKORG->row['OPTION']='EQ';
                $fce->LRI_VKORG->row['LOW']=$org;
                $fce->LRI_VKORG->row['HIGH']='';
                $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
                
		$fce->X_TGL1 = $tglm;
		$fce->X_TGL2 = $tgls; // tgl sampai
		$fce->X_KUNNR = $distr; // distributor
                $fce->X_STATUS = $status; // status
                
                if ($org == '3000'){
                $fce->X_WITH_LOOP = 'X';
                }
                if($distrik!=''){
                    $fce->X_BZIRK = $distrik; // distrik
                }		
                if($noso!=''){
                    $fce->X_VBELN = $noso; // ekspeditur
                }
                if($exp!=''){
                    $fce->X_LIFNR =  $exp; // ekspeditur
                }
		if ($auart == 'X') {
			$fce->X_KONFIRMASI = $auart;
		} else {
			$fce->X_AUART = $auart; // tipe so
		}
                
                  //incompoany
                if(count($mp_coics)>0){
                    foreach ($mp_coics as $keyOrg2 => $valorgm2){
                        if($keyOrg2=='2000'){
                            continue;
                        }
                        $fce->LRI_VKORG->row['SIGN']='I';
                        $fce->LRI_VKORG->row['OPTION']='EQ';
                        $fce->LRI_VKORG->row['LOW']=$keyOrg2;
                        $fce->LRI_VKORG->row['HIGH']='';
                        $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
                    }
                }
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
				$no_pp[$s] = $fce->ZDATA->row["NO_MINTA"];
				$no_kontrak[$s] = $fce->ZDATA->row["VGBEL"];
				$no_so[$s] = $fce->ZDATA->row["NO_SO"];
				$tipe_so[$s] = $fce->ZDATA->row["AUART"];
				$tgl_so[$s] = $fce->ZDATA->row["AUDAT"];
				$no_spj[$s] = $fce->ZDATA->row["NO_SPJ"];
				$no_do[$s] = $fce->ZDATA->row["NO_DO"];
				$pricegrp[$s] = $fce->ZDATA->row["PLTYP"];
                                $pricegrpdeks[$s] = $fce->ZDATA->row["PTEXT"];
				$inco[$s] = $fce->ZDATA->row["INCOTERM"];
				$nobooking[$s] = $fce->ZDATA->row["NO_BOOKING"];
				$tgl_spj[$s] = $fce->ZDATA->row["TGL_CMPLT"];
				$jam_spj[$s] = $fce->ZDATA->row["JAM_CMPLT"];
				$tgl_do[$s] = $fce->ZDATA->row["TGL_DO"];
				$tgl_pp[$s] = $fce->ZDATA->row["TGL_MINTA"];
				$qty_do[$s] = $fce->ZDATA->row["KWANTUM"];
				$nopol[$s] = $fce->ZDATA->row["NO_POLISI"];
				// $no_spps[$s] = $fce->ZDATA->row["NO_SPPS"];
				$sopir[$s] = $fce->ZDATA->row["NAMA_SOPIR"];
				$kdshipto[$s] = $fce->ZDATA->row["KODE_DA"];
				$nmshipto[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$alamat[$s] = $fce->ZDATA->row["ALAMAT_DA"];
				$kddistrik[$s] = $fce->ZDATA->row["AREA"];
				$nmdistrik[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$soldto[$s] = $fce->ZDATA->row["SOLD_TO"];
				$nama_sold[$s] = $fce->ZDATA->row["NAMA_SOLD_TO"];
				$kdplant[$s] = $fce->ZDATA->row["PLANT"];
				$nmplant[$s] = $fce->ZDATA->row["NAMA_PLANT"];
				$kdexp[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$nmexp[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$tstatus[$s] = $fce->ZDATA->row["STATUS"];
				$produk[$s] = $fce->ZDATA->row["PRODUK"];
				$uom[$s] = $fce->ZDATA->row["UOM"];
				$kapal[$s] = $fce->ZDATA->row["NAMA_KAPAL"];
				$harga[$s] = $fce->ZDATA->row["HARGA"];
				$qty_so[$s] = $fce->ZDATA->row["KWMENG"];
				$billing[$s] = $fce->ZDATA->row["NO_BILLING"];
				$top[$s] = $fce->ZDATA->row["ZTERM"];

				if ($fce->ZDATA->row["KWMENG"] > 0)
				$harga_satuan[$s] =  $fce->ZDATA->row["HARGA"]/$fce->ZDATA->row["KWMENG"];
				else
				$harga_satuan[$s] =  0;
				$harga_shp[$s]= $harga_satuan[$s] * $fce->ZDATA->row["KWANTUM"];				
                $posnr[$s] = $fce->ZDATA->row["LINE_SO"];
// GET NO SPPS
$plant       = $fce->ZDATA->row["PLANT"];
$distributor = $fce->ZDATA->row["SOLD_TO"];
$distrik     = $fce->ZDATA->row["AREA"];

$sql = "
    SELECT 
        ma.FORMAT_SPPS || '/' ||
        TO_CHAR(ma.CREATED_AT, 'MMYYYY') || '/' ||
        LPAD(ma.LAST_SEQUENCE, 4, '0') AS NO_SPPS
    FROM MAPPING_PLANT_DIST_SPPS ma
    WHERE ma.DEL != '1'
      AND ma.PLANT = '$plant'
      AND ma.DISTRIBUTOR = '$distributor'
      AND ma.DISTRIK = '$distrik'
      AND ROWNUM = 1
    ORDER BY ma.CREATED_AT DESC
";
$stid = oci_parse($conn, $sql);
oci_execute($stid);
// $no_spps = "";
if ($row = oci_fetch_assoc($stid)) {
    // $no_spps = $row['NO_SPPS'];
	$no_spps[$s] = $row['NO_SPPS'];
}
oci_free_statement($stid);
// echo $no_spps[$s];
// END GET SPPS
            $s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($nopol);	
}

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function finddistrik() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_distrik.php?sold_to="+com_sold.value;
		popUp(strURL);
}
function findvendor() {	
    var com = document.getElementById("org");
	var strURL="cari_vendor.php?org="+com.value;
	popUp(strURL);
}
function ketik_vendor(obj) {
    var com = document.getElementById("org");
	var com_nama=document.getElementById('nama_vendor');						
	com_nama.value = "";
	var strURL="ketik_vendor.php?org="+com.value+"&vendor="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('vendordiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}


function cekTgl(){
    var a = document.getElementById("tgl1").value;
    var b = document.getElementById("tgl2").value;
    var explod = a.split('-');
    var explod2 = b.split('-');
    var tgl = new Date();
    var tgl_a = tgl.setFullYear(explod[2],explod[1],explod[0]);
    var tgl_b = tgl.setFullYear(explod2[2],explod2[1],explod2[0]);
    var milisecond = 60*60*24*1000;
//    alert(a);    
   // if(explod[1]==explod2[1]){
        var c = ((tgl_b-tgl_a)/ milisecond);
    //}
//    alert(tgl_a+'<=>'+tgl_b+'=='+c);
        if(c>100){
            alert('Maaf, tanggal maksimal 7 hari :)');
            return false;
        }
} 
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Realisasi Shipment </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Realisasi Shipment </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('tgl1','','R','tgl2','','R','status','','R');return document.hasil">
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>    
    <tr>
      <td  class="puso">Distrik </td>
      <td  class="puso">:</td>
      <td ><input type="hidden" value="<?=$distr;?>" id="sold_to" name="sold_to" size="10">
	  <div id="shiptodiv">
	  <input type="text" value="" class="inputlabel" id="kode_distrik" name="kode_distrik" size="10" readonly="true">
	  <input type="text" value="" class="inputlabel" id="nama_distrik" name="nama_distrik"  size="20"  readonly="true" >
      <input name="btn_distrik" type="button" class="button" id="btn_distrik" value="..." onClick="finddistrik()"/>
      <input name="val_error_distrik" type="hidden" id="val_error_distrik" value="0" />
    </div>
</td></tr>
    <tr>
      <td  class="puso">Ekspeditur </td>
      <td  class="puso">:</td>
      <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="vendordiv">
		  <input name="vendor" type="text"  id="vendor" class="inputlabel" value="" onChange="ketik_vendor(this);" maxlength="10" size="10"/>
	 	  <input name="nama_vendor" type="text"  id="nama_vendor" class="inputlabel" value="" readonly="true"  size="30"/>
	  	  <input name="btn_vendor" type="button" class="button" id="btn_vendor" value="..." onClick="findvendor()"/>
		</div></td>
    </tr>
    <tr>
      <td  class="puso">Tanggal SPJ</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=$tgl_fr?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=$tgl_to?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>
	  <tr>
      <td  class="puso">Status Transaksi</td>
      <td  class="puso">:</td>
      <td ><select name="status" id="status" required>
	  			<option value="">---Pilih Status---</option> 
			  <? $fungsi->or_status_shipment();?>
			</select>
		</td>
    </tr>
	<tr>
   <td ><strong>Tipe Order</strong></td>
   <td><strong>:</strong></td>
   <td colspan="2"><select name="so_type" id="so_type" onChange="document.tambah.nama_so_type.value=this.options[this.selectedIndex].title;nolc();cekkontrak();">
		<option value="">---Pilih Tipe Order---</option>
		<? $fungsi->or_order_type('ZOR'); ?>     
		</select>	
		<input type="hidden" value="Sales Standart" id="nama_so_type" name="nama_so_type" />&nbsp;&nbsp;
		<label id="labelc" style="visibility:hidden">Nomor LC : </label>
		<input type="text" style="visibility:hidden" value="" id="lcnum" name="lcnum" size="10"/></td>
	</tr>
    <tr>
      <td  class="puso">No Sales Order</td>
      <td  class="puso">:</td>
      <td ><input name="noso" type="text" id="noso" size=12 value="<?=$noso?>"/>	</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button" onclick="return cekTgl();" /> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<div class="nonPrint" >
<form name="export" method="post" action="realisasi_distr_xls.php">
<input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
<input name="noso" type="hidden" id="noso" value="<?=$noso?>"/>
<input name="tipeso" type="hidden" id="tipeso" value="<?=$auart?>"/>
<input name="vendor" type="hidden"  id="vendor" class="inputlabel" value="<?=$vendor;?>"  maxlength="10" size="10"/>
<input type="hidden" value="<?=$kode_distrik;?>" class="inputlabel" id="hidden" name="kode_distrik" size="10">
<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tglm?>"/>
<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgls?>"/>
<input name="status" type="hidden" id="status" size=12 value="<?=$status?>"/>
<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="button" /> 	
&nbsp;&nbsp;
<input name="excel" type="Submit" id="excel" value="Export" class="button" /> 	
&nbsp;&nbsp;
<a href="realisasi_distr1.php" target="isi" class="button">Back</a>
</form>
</div>
	<table width="2100" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Realisasi Shipment</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	
	<table width="2000" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
		<td align="center"><strong >Tgl PP</strong></td>		
		<td align="center"><strong >No Kontrak</strong></td>
		<td align="center"><strong >No SO</strong></td>
		<td align="center"><strong >Tiper Order</strong></td>
		<td align="center"><strong >Tgl SO</strong></td>
		<td align="center"><strong >Incoterm</strong></td>		
		<td align="center"><strong >No Booking</strong></td>
		<td align="center"><strong >No DO</strong></td>
		<td align="center"><strong >Tgl DO</strong></td>
		<td align="center"><strong >Produk</strong></td>
		<td align="center"><strong >Qty DO</strong></td>
		<td align="center"><strong >UOM</strong></td>
		<td align="center"><strong >Klp Harga</strong></td>	
		<td align="center"><strong >No SPJ</strong></td>
		<td align="center"><strong >Tgl SPJ</strong></td>
		<td align="center"><strong >Jam SPJ</strong></td>
		<td align="center"><strong >No SPPS</strong></td>
		<td align="center"><strong >No Polisi</strong></td>
		<td align="center"><strong >Nama Sopir</strong></td>
		<td align="center"><strong >Kode <?=$nama?></strong></td>
		<td align="center"><strong ><?=$nama?></strong></td>
		<td align="center"><strong >Kode Shipto</strong></td>
		<td align="center"><strong >Nama Shipto</strong></td>
		<td align="center"><strong >Alamat Shipto</strong></td>
		<td align="center"><strong >Kode Distrik</strong></td>
		<td align="center"><strong >Distrik</strong></td>
		<td align="center"><strong >Kode Ekspeditur</strong></td>
		<td align="center"><strong >Ekspeditur</strong></td>
		<td align="center"><strong>Kd Plant</strong></td>
		<td align="center"><strong>Plant</strong></td>
		<td align="center"><strong>Nama Kapal </strong></td>
		<td align="center"><strong>Status</strong></td>
                <td align="center"><strong>Line SO</strong></td>
                <td align="center"><strong>PriceList</strong></td>
                <td align="center"><strong>Harga</strong></td>
                <td align="center"><strong>Nilai</strong></td>
                <td align="center"><strong>No Billing</strong></td>
                <td align="center"><strong>TOP</strong></td>
      </tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp[$i]; ?></td>
		<td align="left"><? $thn=substr($tgl_pp[$i],0,4);
							$bln=substr($tgl_pp[$i],4,2);
							$hr=substr($tgl_pp[$i],6,2);
							$tglpp=$hr.'-'.$bln.'-'.$thn;
							echo $tglpp; ?></td>
		<td align="center"><? echo $no_kontrak[$i]; ?></td>
		<td align="center"><? echo $no_so[$i]; ?></td>
		<td align="center"><? echo $tipe_so[$i]; ?></td>
		<td align="left"><? $thn=substr($tgl_so[$i],0,4);
							$bln=substr($tgl_so[$i],4,2);
							$hr=substr($tgl_so[$i],6,2);
							$tglso=$hr.'-'.$bln.'-'.$thn;
							echo $tglso; ?></td>
		<td align="center"><? echo $inco[$i]; ?></td>
		<td align="center"><? echo $nobooking[$i]; ?></td> 
		<td align="center"><? echo $no_do[$i]; ?></td>
		<td align="left"><? $thn=substr($tgl_do[$i],0,4);
							$bln=substr($tgl_do[$i],4,2);
							$hr=substr($tgl_do[$i],6,2);
							$tgldo=$hr.'-'.$bln.'-'.$thn;
							echo $tgldo;  ?></td>
		<td align="left"><? echo $produk[$i]; ?></td>
		<td align="left"><? echo $qty_do[$i]; ?></td>
		<td align="left"><? echo $uom[$i]; ?></td>
		<td align="center"><? echo $pricegrp[$i]; ?></td>
		<td align="left"><? echo $no_spj[$i]; ?></td>
		<td align="center"><? $thn=substr($tgl_spj[$i],0,4);
							$bln=substr($tgl_spj[$i],4,2);
							$hr=substr($tgl_spj[$i],6,2);
							$tglspj=$hr.'-'.$bln.'-'.$thn;
							echo $tglspj;  ?></td>
		<td align="left"><? $jam=substr($jam_spj[$i],0,2);
							$mnt=substr($jam_spj[$i],2,2);
							$dtk=substr($jam_spj[$i],4,2);
							$jamspj=$jam.':'.$mnt.':'.$dtk;
							echo $jamspj; ?></td>
		<td align="left"><? echo $no_spps[$i]; ?></td>
		<td align="left"><? echo $nopol[$i]; ?></td>
		<td align="left"><? echo $sopir[$i]; ?></td>
		<td align="left"><? echo $soldto[$i]; ?></td>
		<td align="left"><? echo $nama_sold[$i]; ?></td>
		<td align="left"><? echo $kdshipto[$i]; ?></td>
		<td align="left"><? echo $nmshipto[$i]; ?></td>
		<td align="left"><? echo $alamat[$i]; ?></td>
		<td align="left"><? echo $kddistrik[$i]; ?></td>
		<td align="left"><? echo $nmdistrik[$i]; ?></td>
		<td align="left"><? echo $kdexp[$i]; ?></td>
		<td align="left"><? echo $nmexp[$i]; ?></td>
		<td align="left"><? echo $kdplant[$i]; ?></td>
		<td align="left"><? echo $nmplant[$i]; ?></td>
		<td align="left"><? echo $kapal[$i]; ?></td>
		<td align="left"><? echo $tstatus[$i]; ?></td>
                <td align="left"><? echo $posnr[$i]; ?></td>
                <td align="left"><? echo $pricegrp[$i]." - ".$pricegrpdeks[$i]; ?></td>
                <td align="left"><? echo number_format($harga_satuan[$i], 2, ",", "."); ?></td>
                <td align="left"><? echo number_format($harga_shp[$i], 2, ",", "."); ?></td>
		<td align="left"><? echo $billing[$i]; ?></td>
		<td align="left"><? echo $top[$i]; ?></td>
		</tr>
	  <? } ?>
	 <tr>
	 <td colspan="11" align="center">TOTAL</td>
	 <td align="right"><?=number_format($totaldo,3,',','.')?></td>
	 <td>&nbsp;</td>
	 </tr> 
	</table>
	
	<p>&nbsp;</p>

	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
