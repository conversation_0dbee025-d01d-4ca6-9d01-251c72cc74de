<?php
if (file_exists('../include/ex_fungsi.php')) {
  include_once '../include/ex_fungsi.php';
} elseif (file_exists('../../include/ex_fungsi.php')) {
  include_once '../../include/ex_fungsi.php';
}

$base_url = "https://dev-app.sig.id/dev/sd/sdonline/"; // dev
// $base_url = "https://csms.sig.id/sdonline/"; // prod
define("BASE_URL", $base_url);

if (!function_exists('get_file_name')) {
  function get_file_name($file_url) {
    return basename($file_url); // dev
    // return $file_url; // prod
  }
}

if (!function_exists('get_base_url')) {
  function get_base_url() {
    return BASE_URL;
  }
}

if (!function_exists('get_base_home')) {
  function get_base_home() {
    return BASE_URL . 'login.php';
  }
}

function get_pejabat_eks_manual(){
    $fungsi=new ex_fungsi();
    $conn=$fungsi->ex_koneksi();

    $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Ekspeditur - Manual BASTP'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }

class PDFExporter {
  public function beritaAcara($no_ba) {
    $urlPrint = 'http://************/dev/sd/sdonline/ex_ba_sp/print_ba_new.php?no_ba=' . $no_ba; // dev
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_ba_sp/print_ba_new.php' . '?no_ba=' . $no_ba; // prod
    // $urlPrint = 'http://***********/dev/sd/sdonline/ex_ba/print_ba_new.php?no_ba=' . $no_ba;
    // $urlPrint = 'http://**********/sdonline/ex_ba/print_ba_new.php?no_ba=' . $no_ba;
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);
    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlPrint . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlRenderPdf . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    return $response;
  }

  public function beritaAcara2($no_ba, $server = 'dev') {
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba2.php?no_ba=" . $no_ba . '&server=' . $server;

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);
    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlConverter . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceKuitansi($no_ba) {
    $user_id = $_SESSION['user_id'];

    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id"; // dev SP
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id";
    // $urlPrint = "https://csms.sig.id/sdonline/ex_invoice_ba/print_invoice_ba_new.php?no_ba=0000000900&user_id=23386";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_ba_new.php?no_ba=$no_ba&user_id=$user_id";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";
    // $urlConverter = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceSPDenda($no_ba) {
    $user_id = $_SESSION['user_id'];

    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda.php?no_ba=$no_ba"; // dev SP
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda.php?no_ba=$no_ba"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_sp_denda.php?no_ba=$no_ba";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_sp_denda.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceSpDendaTanggalSama($no_ba) {
    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba"; // dev SP
    // $urlPrint = "https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function invoiceSpDendaTanggalBeda($no_ba) {
    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba"; // dev SP
    // $urlPrint = "https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba"; // prod
    // $urlPrint = "http://***********/dev/sd/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba";
    // $urlPrint = "http://**********/sdonline/ex_invoice_ba/print_invoice_sp_denda_tgl_beda.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      throw new Exception(curl_errno($ch) . ' - ' . curl_error($ch));
    }

    curl_close($ch);

    return $response;
  }

  public function draftPPL($no_invoice) {
    $urlPrint = 'http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download'; // dev SP
    // $urlPrint = 'https://csms.sig.id/sdonline/ex_invoice_ba_sp/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download'; // prod
    // $urlPrint = 'http://***********/dev/sd/sdonline/ex_invoice_ba/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download';
    // $urlPrint = 'http://**********/sdonline/ex_invoice_ba/print_draft_ppl_permintaan.php?no_invoice=' . $no_invoice . '&type=download';
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Mendapatkan struktur html yang akan dikirim saat render pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);
    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlPrint . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    $headers = array(
      'Content-Type: application/json',
    );

    $dataRenderPdf = array(
      'content' => $response,
    );

    // Merender html menjadi pdf
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);

    // Jika terdapat error saat request
    if (curl_errno($ch)) {
      echo "URL: " . $urlRenderPdf . "<br />";
      echo curl_errno($ch) . " " . curl_error($ch);
      curl_close($ch);
      die();
    }

    curl_close($ch);

    return $response;
  }
}

?>