<?php
include ('../../include/ex_fungsi.php');
require_once ('../../security_helper.php');
require_once 'helper.php';
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$valid_users = array(
    "smbr-jaya" => "i-love-smbr"
);

if (!isset($_SERVER['PHP_AUTH_USER']) || !isset($_SERVER['PHP_AUTH_PW'])) {
    header('WWW-Authenticate: Basic realm="My API"');
    header('HTTP/1.0 401 Unauthorized');
    echo json_encode(array("status" => "401", "message" => "Authentication required"));
    exit;
}

$username = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

if (!isset($valid_users[$username]) || $valid_users[$username] != $password) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("status" => "403", "message" => "Invalid credentials"));
    exit;
}

if (!isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("status" => "403", "message" => "Missing X-CSRF-Token header"));
    exit;
}

$rawInput = file_get_contents('php://input');
$request = json_decode($rawInput, true); // true = array, bukan object

$response = array(
    "status" => "400",
    "message" => "Parameter tidak sesuai",
    "data" => array()
);

if (
    isset($request['no_invoice']) && isset($request['no_ba'])
) {
    $no_invoice = $request['no_invoice'];
    $no_ba = $request['no_ba'];

    if (empty($no_invoice)) {
        header("HTTP/1.1 400 Bad Request");
        $response['status'] = "400";
        $response['message'] = "no_invoice wajib diisi!";
        echo json_encode($response);
        exit;
    }

    if (empty($no_ba)) {
        header("HTTP/1.1 400 Bad Request");
        $response['status'] = "400";
        $response['message'] = "no_ba wajib diisi!";
        echo json_encode($response);
        exit;
    }

    $sql = "SELECT * FROM EX_INVOICE_SMBR_EKS_DOKUMEN WHERE NO_INV = :no_invoice";
    $stmt = oci_parse($conn, $sql);
    oci_bind_by_name($stmt, ":no_invoice", $no_ba);
    oci_execute($stmt);

    while ($row = oci_fetch_assoc($stmt)) {
        $sql2 = "SELECT * FROM EX_INVOICE_SMBR_RUN_PPL WHERE NO_INV = :no_invoice";
        $stmt2 = oci_parse($conn, $sql2);
        oci_bind_by_name($stmt2, ":no_invoice", $no_invoice);
        oci_execute($stmt2);

        while ($row2 = oci_fetch_assoc($stmt2)){
            $gross_amount = nullable($row2['GROSS_AMOUNT']);
        }

        $data = array(
            "no_ekspedisi" => nullable($row['NO_EKSPEDISI']),
            "gross_amount" => $gross_amount
        );
    }

    if(count($data) == 0){
        header("HTTP/1.1 404 Not Found");
        $response['status'] = "404";
        $response['message'] = "data dengan no_ba $no_invoice tidak ditemukan";
        echo json_encode($response);
        exit;
    }

    $response['status'] = "200";
    $response['message'] = "Success";
    $response['data'] = $data;
}

header('Content-Type: application/json');
echo json_encode($response);
?>
