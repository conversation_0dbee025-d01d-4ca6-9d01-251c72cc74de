<?php

session_start();
//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

//$ids = htmlspecialchars($_REQUEST['id']);

$LAMPIRAN	= ($_REQUEST['LAMPIRAN']);
//$LAMPIRAN1 = str_replace('\\"', '"', $LAMPIRAN);

/*echo "order : ".$order.'<br>';
echo "sort : ".$sort.'<br>';*/

if(isset ($aksi)){
//    if($aksi == 'show'){
//        displayData($conn,$sort,$order);
//        
//    }
    switch($aksi) { 
        case 'show' :
            {
				 $where = '';
        if($WTRNCODE==''){
            $where  .= "";
        }else{
           $distr =  $WTRNCODE;
	
            $where  .= "  NO_BA = '{$distr}'";  
        }
  
        
        if($where == ''){
			
            echo json_encode('tidak ada data yang dicari');
			
        }else{
		$sql = "
            SELECT
            *
            FROM
            EX_BA
			WHERE
					 {$where} 
			";
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            $result=array();
            $i=0;
			//echo $sql;
			while($row=oci_fetch_array($query)){
			 array_push($result, $row);
            }
                echo json_encode($result);
		}
            
            }
						
        break;
		
        case 'update' :
        {
            $sql= "
            UPDATE EX_BA  set
            STATUS_BA		= '$STATUS_BA',
			SIGN_STATUS_2 = '$SIGN_STATUS_2'
            WHERE
            NO_BA = '$NO_BA' AND ID = '$ID'
			";
            
          // echo $sql; //print "$LAMPIRAN1";
            $query= oci_parse($conn, $sql);
            $result = oci_execute($query);
            
				//echo $result;
            
            if ($result){
                    echo json_encode(array('success'=>true));
            } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
    }
    

    
    
}


?>
