<? 
session_start();

function sendMail($data){
    $nmStatus = 'Submission';
    $mail = $data['emaildist'];
    $sbj = "Reschdule, Reopen, Reject Submission Approval";
    $msgs ="<thead>
        <tr>
            <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
                <h2><b>Reschedule SO</b></h2>
                <br/>
                <p>Mohon untuk ditindaklanjuti pengajuan pengajuan reschedule SO dengan detail di bawah ini :</p>
                <br/>
            </td>
        </tr>
    </thead>";
    // $part="http://10.15.5.150/dev/sd/sdonline/or_transaksi/vReqAppMHoliday.php";
 //dev
  //$part="https://csms.semenindonesia.com/sparebag/ListApproval.php";//prod
  
  $tgl       = date('d-m-Y');
      
  // $to        = $data[0]['EMAIL_KABIRO'];  
  // if (!$to) {
  //     return false;
  // } 
  // var_dump($mail);
  $to       = $mail;
  $subject   = $sbj;
  
  $message1  = "<html><head></head>";
  
  $message1 .= $msgs;



  $message1 .= "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
  $message1 .= "
      <div align=\"center\">
      <table width=\"95%\" align=\"center\" class=\"adminlist\" id=\"myScrollTable\">
      <thead>
        <tr class=\"quote\">
          <td ><strong>&nbsp;&nbsp;No.</strong></td>
          <td align=\"center\"><strong >Soldto</strong></td>
          <td align=\"center\"><strong >shipto</strong></td>
          <td align=\"center\"><strong >No PP</strong></td>
          <td align=\"center\"><strong >No SO</strong></td>
          <td align=\"center\"><strong >Request Date</strong></td>
          <td align=\"center\"><strong >Status</strong></td>
           <td align=\"center\"><strong>Request By</strong></td>
        </tr>
        </thead>
        <tbody>
               ";

          $b=1;
  
          $message1.= " 
          <td align=\"center\">".$b."</td>
          <td align=\"center\">".$data['soldto']."</td>       
          <td align=\"center\">".$data['shipto']."</td>
          <td align=\"center\">".$data['no_pp']."</td>
          <td align=\"center\">".$data['no_so']."</td>
          <td align=\"center\">".$data['req_date']."</td>
          <td align=\"center\"><button style=\"background-color:#4CAF50\">".$nmStatus."</button></td>
          <td align=\"center\">".$data['req_by']."</td>
          </tr>";
  
  //         }
  // }
  
  $message1 .= "";
  $message1 .= "</html>";
  
  $headers  = "MIME-Version: 1.0" . "\r\n";
  $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
  $headers .= 'From: <EMAIL>' . "\r\n";

  // if ($data['status'] != '2') {
  //     $fungsi=new or_fungsi();
  //     $conn=$fungsi->or_koneksi();
  //     // get cc email berdasarkan distrik
  //     $getData = "SELECT DISTINCT BZIRK FROM RFC_Z_ZCSD_SHIPTO WHERE KUNN2 = ".$data['shipto']." AND ROWNUM = 1";
  //                     $cek = oci_parse($conn, $getData);
  //                     oci_execute($cek);
  //                     $hasilCek = oci_fetch_array($cek);
  //     $getKT = $hasilCek[0];
  //     if ($getKT != '') {
           

  //     $getData2 = "SELECT CC_EMAIL FROM MAINTAIN_CC_EMAIL WHERE KD_KOTA = '$getKT'";
  //     $cek2 = oci_parse($conn, $getData2);
  //     oci_execute($cek2);
  //     $hasilCek2 = oci_fetch_array($cek2);
  //     $getCC = $hasilCek2[0];

  //     }

  // $headers .= 'Cc:' .$getCC. "\r\n";


  // }
 
      


  // $headers .= 'Cc: <EMAIL>' . "\r\n";
  // echo $message1;
  $send1=mail($to,$subject,$message1,$headers);
  
      if (($send1 )){
          // echo "Sending Mail Success";
      } else {
          // echo "Sending Mail Failed";
      }
   
   $response['jml_sukses'] = $no;
   $response['jml_fail']   = $no1;  
   return $response;
}

if(isset($_POST['edit_data_pp'])){
  include ('../include/or_fungsi.php');
  $fungsi=new or_fungsi();
  $conn=$fungsi->or_koneksi();
  $row = null; 
  $get = "SELECT USERNAME1, USERNAME2 FROM MAPPING_APPROVAL_SO_RESCHEDULE WHERE CODE_SHIPTO = '$ship_to' AND FLAG_DEL != 'Y'";

  $query_get = oci_parse($conn, $get);
  oci_execute($query_get);
  $row = oci_fetch_array($query_get, OCI_ASSOC+OCI_RETURN_NULLS);

  if($row['USERNAME1'] == $_SESSION['user_name']){
    $row['USERNAME1'] = null;
  }

  if (!$row) {
    $get = "SELECT USERNAME1, USERNAME2 FROM MAPPING_APPROVAL_SO_RESCHEDULE WHERE CODE_SHIPTO = '0' AND FLAG_DEL != 'Y'";
    $query_get = oci_parse($conn, $get);
    oci_execute($query_get);
    $row = oci_fetch_array($query_get, OCI_ASSOC+OCI_RETURN_NULLS);
    if($row['USERNAME1'] == $_SESSION['user_name']){
      $row['USERNAME1'] = null;
    }
  }

  if(strpos($_SESSION['user_name'], 'DIST') !== false){
    $submitter = "DISTRIBUTION";
  }elseif(strpos($_SESSION['user_name'], 'AREA') !== false){
    $submitter = "SALESAREA";
  }else{
    $submitter = "SCM";
  }

  // Query add data to table SUBM SO RESCHEDULE untuk pengajuan 
  $add = "INSERT INTO SUBM_SO_RESCHEDULE (ID_SHIPTO, ID_SOLD_TO, FIRST_DATE, NO_PP, NO_SO, INSERT_BY, INSERT_DATE, NM_SHIPTO, RDD, STATUS, LAST_UPDATED_BY, LAST_UPDATED_DATE, REQ_STATUS_SO, DELETE_MARK, TYPE_APPROVAL, APP1_ID, APP2_ID, JENIS_KIRIM, SUBMITTER, RDD_BEFORE, FIRST_DATE_BEFORE, KD_PROP)
          VALUES ('$ship_to', '$sold_to', TO_DATE('$tgl_kirim_leadtime', 'DD-MM-YYYY'), '$no_pp', '$no_so', '".$_SESSION['user_name']."', TO_DATE('".date("d-m-Y")."', 'DD-MM-YYYY'), '$name', TO_DATE('$tgl_kirim_approve', 'DD-MM-YYYY'), " . ($row['USERNAME1'] ? '1' : '2') . ", '".$_SESSION['user_name']."', TO_DATE('".date("d-m-Y")."', 'DD-MM-YYYY'), '$statuspp', 0, '$type_approval', '".$row['USERNAME1']."', '".$row['USERNAME2']."', '$jenis_kirim', '$submitter', TO_DATE('$rdd_before', 'DD-MM-YYYY'), TO_DATE('$first_date_before', 'DD-MM-YYYY'), '$kd_prop')";
  
  $dataLempar = array(
    'emaildist' =>  $row['USERNAME1'],
    'soldto' => $sold_to,
    'shipto' => $ship_to,
    'no_pp'=> $no_pp,//$shiptoname, 
    'req_date'=> $tgl_kirim_leadtime,
    'req_by' => $_SESSION['user_name'],
    'status' => "1",
    'no_so' => $no_so
  );
  
  $query_add = oci_parse($conn, $add);
  oci_execute($query_add);

  // send mail 
  sendMail($dataLempar);
    
  exit;
}

if(isset($_POST['hitung_tanggal'])){
  $update = "
    SELECT STANDART_AREA FROM ZMD_LEADTIME_SO WHERE PLANT = '" . $_POST['plant'] . "' AND KOTA = '" . $_POST['kota'] ."' AND KD_MATERIAL = '" . $_POST['material'] . "'
  ";
  // echo $update; exit;
  include ('../include/or_fungsi.php');
  $fungsi=new or_fungsi();
  $conn=$fungsi->or_koneksi();
  $query_update = oci_parse($conn, $update);
  oci_execute($query_update);
  // $update_status = oci_num_rows($query_update) ? true : false;
  $hasil = oci_fetch_array($query_update);
  echo $hasil['STANDART_AREA'];
  exit;
}

include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../MainPHPExcel/PHPExcel/IOFactory.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();


$halaman_id=201;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr=$_SESSION['distr_id'];
$distr=$fungsi->sapcode($distr);
// echo var_dump($distr);
$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	//$sap->Connect($link_koneksi_sap);
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	else if ($sap->GetStatus() != SAPRFC_OK ) {
		echo $sap->PrintStatus();
		exit;
	}

	// //Pemanggilan RFC Cari
	// $fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN2");
	// if ($fce == false ) { $sap->PrintStatus(); exit; }

	// $fce->XVKORG = "7900";
	// $fce->XFLAG = "O";
	// $fce->XPOSNR = "000000";
	// // if($NO_DOC_HDR1dov!='' && $ORGdocv!='' && $TAHUNdocv!=''){
	// 	$fce->LR_EDATU->row["SIGN"] = "I";
	// 	$fce->LR_EDATU->row["OPTION"] = "BT";
	// 	$fce->LR_EDATU->row["LOW"] = "20230701";
	// 	$fce->LR_EDATU->row["HIGH"] = "20230730";
	// 	$fce->LR_EDATU->Append($fce->LR_EDATU->row);
	// // }
	// $fce->Call();
	// if ($fce->GetStatus() == SAPRFC_OK ) {		
	// 	$fce->RETURN_DATA->Reset();
	// 	echo count($fce->RETURN_DATA);
	// 	$s=0;
	// 	while ( $fce->RETURN_DATA->Next() ){ 
	// 		$datadoc[$s]=$fce->RETURN_DATA->row;
	// 		// $datadoc=$fce->RETURN_DATA->rowLast;
	// 		$s++;
	// 	}
	// }else
	// 	$fce->PrintStatus();
	// $fce->Close();
	// $sap->Close();

// if ($_SESSION['user_tipe']=="admin" or $_SESSION['user_tipe']=="pemasaran" )$hanya_baca = "";
// else $hanya_baca = "readonly = 'readonly'";

// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
if (false) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

// exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="list_so.php";
$no_pp = $_POST['no_pp'];
$sold_to = $_POST['sold_to'];
$ship_to = $_POST['ship_to'];
$produk = $_POST['produk'];
$distrik = $_POST['distrik'];
$pp_from = $_POST['pp_from'];
$pp_to = $_POST['pp_to'];
$req_from = explode("-",$_POST['req_from']);
if($req_from!="") $req_from = $req_from[2].$req_from[1].$req_from[0];
$req_to = explode("-",$_POST['req_to']);
if($req_to!="") $req_to = $req_to[2].$req_to[1].$req_to[0];

$currentPage="list_so.php";
$komen="";
$total = 0;

if(isset($_POST['cari'])){
  $folder = "dataxls/";
  $target_file = $folder . basename($_FILES["no_so"]["name"]); // Dapatkan nama file yang diunggah
  $uploadOk = 1;
  $fileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));

  // Periksa apakah file adalah file Excel
  if($fileType != "xls" && $fileType != "xlsx") {
    $uploadOk = 0;
    // echo "File harus dalam format XLS atau XLSX.";
  }

  // Periksa jika uploadOk adalah 0 karena adanya kesalahan
  if ($uploadOk == 0) {
    // echo "File tidak dapat diunggah.";
  }else{
    if (move_uploaded_file($_FILES["no_so"]["tmp_name"], $target_file)) {
      // Baca file Excel
      $objPHPExcel = PHPExcel_IOFactory::load($target_file);

      // Dapatkan semua data dari file Excel
      $sheetData = $objPHPExcel->getActiveSheet()->toArray(null, true, true, true);

      // Proses data untuk mendapatkan nomor SO
      $no_so_list = array();
      $index = 1; // Baris ke-2
      foreach ($sheetData as $row) {
        if ($index > 1) {
          $no_so_list[] = $row['A']; // Nomor SO diurutkan kebawah pada kolom A
        }
        $index++;
      }

    } else {
        echo "Terjadi kesalahan saat mengunggah file.";
    }
  }
  //Pemanggilan RFC Cari
  $datadoc = array(); // Inisialisasi array untuk menyimpan data
  if (is_array($no_so_list) || is_object($no_so_list)) {
    foreach($no_so_list as $so){
      if($so != ""){
        $fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN2");
        if ($fce == false ) { $sap->PrintStatus(); exit; }
        if($no_so!="") $fce->XVBELN = $so;
        if($ship_to!="") $fce->XKUNNR2 = $ship_to;
        if($produk!="") $fce->XMATNR = $produk;
        if($distrik!="") $fce->XBZIRK = $distrik;
  
        $fce->XVKORG = $user_org;
        $fce->XKUNNR = $distr;
        $fce->XFLAG = "O";
        $fce->XPOSNR = "000000";
        $fce->LR_EDATU->row["SIGN"] = "I";
        $fce->LR_EDATU->row["OPTION"] = "BT";
        $fce->LR_EDATU->row["LOW"] = $req_from;
        $fce->LR_EDATU->row["HIGH"] = $req_to;
        $fce->LR_EDATU->Append($fce->LR_EDATU->row);
  
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {		
            $fce->RETURN_DATA->Reset();
            $s=0;
            while ( $fce->RETURN_DATA->Next() ){ 
                $datadoc[]=$fce->RETURN_DATA->row; // Tambahkan hasil ke dalam array
                $s++;
            }
        } else {
            $fce->PrintStatus();
        }
        $fce->Close();
      }
    }
  }else{
    $fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN2");
    if ($fce == false ) { $sap->PrintStatus(); exit; }
    if($ship_to!="") $fce->XKUNNR2 = $ship_to;
    if($produk!="") $fce->XMATNR = $produk;
    if($distrik!="") $fce->XBZIRK = $distrik;

    $fce->XVKORG = $user_org;
    $fce->XFLAG = "O";
    $fce->XPOSNR = "000000";
    $fce->LR_EDATU->row["SIGN"] = "I";
    $fce->LR_EDATU->row["OPTION"] = "BT";
    $fce->LR_EDATU->row["LOW"] = $req_from;
    $fce->LR_EDATU->row["HIGH"] = $req_to;
    $fce->LR_EDATU->Append($fce->LR_EDATU->row);

    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK ) {		
        $fce->RETURN_DATA->Reset();
        $s=0;
        while ( $fce->RETURN_DATA->Next() ){ 
            $datadoc[]=$fce->RETURN_DATA->row; // Tambahkan hasil ke dalam array
            $s++;
        }
    } else {
        $fce->PrintStatus();
    }
    $fce->Close();
  }
  $sap->Close();
  $total = count($datadoc);
}

$s = 0;

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function IsNumeric(obj)
  //  check for valid numeric strings	
  {
  var strValidChars = "0123456789";
  var strChar;
  var strString = obj.value;
  if (strString.length == 0){
    alert("Harus Diisi Angka..!!!");
  obj.value="";
  return false;
  } else {
    //  test strString consists of valid characters listed above
    for (i = 0; i < strString.length; i++)
    {
    strChar = strString.charAt(i);
    if (strValidChars.indexOf(strChar) == -1)
      {
      alert("Hanya Masukkan Angka 0-9...!");
      return false;
      }
    }
  } 
}

function clickIE()
 
	{if (document.all)
	{(message);return false;}
}
 
function clickNS(e) {
	if
	(document.layers||(document.getElementById&&!document.all))
	{
	if (e.which==2||e.which==3) {(message);return false;}}}
	if (document.layers)
	{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
	else
	{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;
}

var jumlah_edit_arr = [];
function checkForother(obj,kei) {
	if (!document.layers) {  

		var deliveryblock = document.getElementById('delivery-block'+kei);
		var ppsched = document.getElementById('ppsched'+kei);
		var reason = document.getElementById('reason'+kei);
		var ppreq = document.getElementById('ppreq'+kei);
		var uom = document.getElementById('ppqtyuom'+kei);
		var jumlah = document.getElementById('jumlah-edit');
		var jumlahInt = parseInt(jumlah.value);
		
		var rowke = 'centang'+kei;
		var com_rowke = document.getElementById(rowke);
		if (obj.value == "0") {
      if($("#abgru"+kei).val() == '')
			ppsched.disabled = "";
			reason.disabled = "";
			ppreq.disabled = "";
			uom.disabled = "";

			com_rowke.class="ontab";
			obj.value = "1";
      jumlahInt++;
      jumlah_edit_arr.push(kei);

      // set qto
      var url = "list_so.php";
      var params = 'set_qto=true&plant='+$("#plant"+kei).text()+'&user_org='+$("#org"+kei).text()+'&distrik='+$("#kota"+kei).text()+'&material='+$("#material_full"+kei).text()+'&unit='+$("#unit"+kei).text();
      try {
        fetch(window.location.href, {
          method: "POST",
          headers: {
            "Content-type": "application/x-www-form-urlencoded",
          },
          body: params,
        })
        .then(response => {
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          return response.text();
        })
        .then(data => {
          console.log(data);
          // data isine qto, tinggal assign di html
          document.getElementById('qto'+kei).value = data;
        })
        .catch(error => {
          alert("There was a problem while using fetch API:\n" + error.message);
        });
      } catch (error) {
        alert("There was a problem while using fetch API:\n" + error.message);
      }
		} else {
			ppsched.disabled = "disabled";
			reason.disabled = "disabled";
			ppreq.disabled = "disabled";
			uom.disabled = "disabled";

			obj.value = "0";  
			com_rowke.class="";
      jumlahInt--;
      jumlah_edit_arr = jumlah_edit_arr.filter(item => item !== kei);
		}
    jumlah.value = jumlahInt;
	}
}

var checkboxCount = 0;
async function saveiki() {
  $(".loader-page").css("visibility", "visible");
  $(".loader").css("visibility", "visible");
  var nama_user = '<?php session_start();echo $_SESSION["user_name"];?>';
  var url = "list_so.php";
  var is_an_checked = false;

  for (let index = 0; index < checkboxCount; index++) {
    if ($("#centang" + index).is(":checked")) {
      // if(!$("#reason"+index).val()){
      //   alert("Reason for Ejection PP harus diisi pada baris "+(index+1));
      //   console.log("Reason for Ejection PP kosong");
      //   $(".loader").css("visibility", "hidden");
      //   $(".loader-page").css("visibility", "hidden");
      //   return;
      // }
      if ($("#reason"+index).val() !== "") {
          var type_approval = "REJECT"; 
      } else if ($("#ppsched"+index).val() !== $("#soreq"+index).text() && $("#ppreq"+index).val() !== $("#sordd"+index).text() && $("#reason"+index).val() == "") {
          var type_approval = "RESCHEDULE";
      } else {
          var type_approval = "REOPEN";
      }
      if(parseInt($("#ppqtyuom"+index).val()) < parseInt($("#qtymin"+index).val())){
        alert("Tidak boleh kurang dari "+$("#qtymin"+index).val()+" pada baris "+(index+1));
        console.log("PP Order QTY kurang");
        $(".loader").css("visibility", "hidden");
        $(".loader-page").css("visibility", "hidden");
        return;
      }
      is_an_checked = true;
      var params = 'edit_data_pp=true&xorg='+$("#org"+index).text()+'&xplant='+$("#plant"+index).text()+'&tgl_kirim_approve='+$("#ppsched"+index).val()+'&tgl_kirim_leadtime='+$("#ppreq"+index).val()+'&statuspp='+$("#reason"+index).val()+'&no_pp='+$("#nopp"+index).text()+'&no_so='+$("#noso"+index).text()+'&soreq='+$("#soreq"+index).text()+'&ship_to='+$("#ship_to"+index).text()+'&sold_to='+$("#sold_to"+index).text()+'&name='+$("#name1"+index).text()+'&type_approval='+type_approval+'&jenis_kirim='+$("#jenis_kirim"+index).val()+'&rdd_before='+$("#soreq"+index).text()+'&first_date_before='+$("#sordd"+index).text()+'&kd_prop='+$("#kota"+index).text();
      // alert(params);exit;

      try {
        const response = await fetch(window.location.href, {
          method: "POST",
          headers: {
            "Content-type": "application/x-www-form-urlencoded",
          },
          body: params,
        });

        if (response.ok) {
          const data = await response.text();
          console.log(data);
        } else {
          throw new Error("Network response was not ok");
        }
      } catch (error) {
        alert("There was a problem while using fetch API:\n" + error.message);
      }
    }
    if (index == checkboxCount-1 && is_an_checked) {
      // alert('Data yang dipilih berhasil diganti!');
      location.reload();
    }
  }
  if (!is_an_checked) {
    alert('Pilih data terlebih dahulu!');
    $(".loader-page").css("visibility", "hidden");
    $(".loader").css("visibility", "hidden");
  }
}

function centangAll(){
  if($("#centang-all").prop("checked")){
    $(".centang-all").prop("checked", false);
    $(".centang-all").prop("checked", true);
  }
}

function getXMLHTTP() { 
  var xmlhttp=false;	
  try{
    xmlhttp=new XMLHttpRequest();
  }
  catch(e)	{		
    try{			
      xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
    }
    catch(e){
      try{
      xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
      }
      catch(e1){
        xmlhttp=false;
      }
    }
  }
    
  return xmlhttp;
}

function settgl(param){
  $(".loader-page").css("visibility", "visible");
  $(".loader").css("visibility", "visible");

  $(document).on("keypress", 'form', function(e) {
    	var code = e.keyCode || e.which;
    	console.log(code);
    	if (code == 13) {
    		console.log('Inside');
    		e.preventDefault();
    		return false;
    	}
    });
    document.getElementById("simpan").disabled = true;
    document.getElementById("reason"+param).disabled = true;
    var com_tgl = document.getElementById("ppsched"+param);
    var jkirim = document.getElementById('jenis_kirim'+param);
    var val_jkirim = jkirim.value;
    var com_kn = document.getElementById('tglnya'+param);
    var com_knplus1 = document.getElementById('tgl1nya'+param);
    var knplus1 = com_knplus1.value;
    var tgl = com_tgl.value;
    var kn = com_kn.value;
    var tgl1 = parseInt(tgl.substr(0, 2), 10);
    var bln1 = parseInt(tgl.substr(3, 2), 10);
    var th1 = parseInt(tgl.substr(6, 4), 10);
    var tglo = bln1 + "/" + tgl1 + "/" + th1;
    var tglx = new Date(tglo);
    var tgl2 = parseInt(kn.substr(0, 2), 10);
    var bln2 = parseInt(kn.substr(3, 2), 10);
    var th2 = parseInt(kn.substr(6, 4), 10);
    var tgln = bln2 + "/" + tgl2 + "/" + th2;
    var knx = new Date(tgln);
    ///plus 1 hari
    var tglkn1 = parseInt(knplus1.substr(0, 2), 10);
    var blnkn1 = parseInt(knplus1.substr(3, 2), 10);
    var thkn1 = parseInt(knplus1.substr(6, 4), 10);
    var tglkno = blnkn1 + "/" + tglkn1 + "/" + thkn1;
    var ttglknx = new Date(tglkno);
    ////////////////////////////////////// //SOCC v2.0
    // var jkemasan = document.getElementById('ppsalesunit'+param);
    // switch (jkemasan.value) {
    // 	case 'CURAH':
    // 		tipe = '121-302';
    // 		break;
    // 	case 'MORTAR ZAK':
    // 		tipe = '121-701';
    // 		break;
    // 	case 'MORTAR CRH':
    // 		tipe = '121-702';
    // 		break;
    // 	case 'M3':
    // 		tipe = '121-800';
    // 		break;
    // 	case 'M3 PALLET1':
    // 		tipe = '121-800';
    // 		break;
    // 	default:
    // 		tipe = '121-301';
    // }
    var tipe = document.getElementById('material'+param).value;
    var plant = document.getElementById('plant'+param);
    var val_plant=plant.textContent;
    var kotatj = document.getElementById('kota'+param);
    var val_kotatj=kotatj.textContent;
    var com = document.getElementById('org'+param).textContent;
    var strURL = "../or_transaksi/loadexplanning3.php?orgin=" + com.value + "&loadtype=" + val_jkirim + "&produkin=" + tipe + "&plant=" + val_plant + "&action=config_rdd_typeload";
    var reqrdd = getXMLHTTP()
    reqrdd.onreadystatechange = function() {
    	//lodingact(0);
    	if (reqrdd.readyState == 4) {
    		// only if "OK"
    		if (reqrdd.status == 200) {
    			//lodingact(1);
    			var loadtipe = reqrdd.responseText;
    			const rddconfigs_material = loadtipe.split("/");
    			var loadingtype1 = rddconfigs_material[0];
    			var materialtype1 = rddconfigs_material[1];
    			materialtype1 = materialtype1.substr(0, 7);
    			if (tipe == materialtype1) {
    				var strURL = "../or_transaksi/loadexplanning3.php?orgin=" + com.value + "&plant=" + val_plant + "&produkin=" + tipe + "&kodedistrik=" + val_kotatj + "&action=config_umur_so";
    				var reqs = getXMLHTTP();
    				if (reqs) {
    					reqs.onreadystatechange = function() {
    						//lodingact(0);
    						if (reqs.readyState == 4) {
    							if (reqs.status == 200) {
    								// lodingact(1);
    								config = reqs.responseText;
    								var strURL = "../or_transaksi/loadexplanning3.php?orgin=" + com.value + "&plant=" + val_plant + "&produkin=" + tipe + "&kodedistrik=" + val_kotatj + "&action=leadtime_so";
    								var req = getXMLHTTP();
    								if (req) {
    									req.onreadystatechange = function() {
    										if (req.readyState == 4) {
    											// only if "OK"
    											if (req.status == 200) {
    												var leadtime_so_config = req.responseText;
    												const rddconfigs = loadtipe.split("/");
    												var loadingtype = rddconfigs[0];
    												var materialtype = rddconfigs[1];
    												const leadtimeconfigs = leadtime_so_config.split("/");
    												var leadtime_so = leadtimeconfigs[0];
    												var rddminplus1 = leadtimeconfigs[1];
    												var horizon_rdd = leadtimeconfigs[2];
    												// alert(rddconfigs);
    												// alert(loadingtype);
    												// alert(materialtype);
    												loadingtype = loadtipe.substr(0, 3);
    												if ((leadtime_so.length == 0 && val_jkirim == loadingtype) || (leadtime_so.length == 0 && loadingtype == 'FRC')) {
    													alert("leadtime belum diinput, silahkan hubungi SPC untuk berikutnya diteruskan ke SCM");
    													com_tgl.value = '';
    												} else {
    													// alert(loadtipe);
    													// alert("masuk RDD");
    													// if(leadtime_so.length == 0){

    													if (horizon_rdd == '' || horizon_rdd == null) {
    														var tglkn1 = parseInt(leadtime_so.substr(0, 2), 10);
    														var blnkn1 = parseInt(leadtime_so.substr(3, 2), 10);
    														var thkn1 = parseInt(leadtime_so.substr(6, 4), 10);
    													} else {
    														var tglkn1 = parseInt(knplus1.substr(0, 2), 10);
    														var blnkn1 = parseInt(knplus1.substr(3, 2), 10);
    														var thkn1 = parseInt(knplus1.substr(6, 4), 10);
    													}

    													var tglkno = blnkn1 + "/" + tglkn1 + "/" + thkn1;
    													var tglknx = new Date(tglkno);

    													Date.prototype.addDays = function(days) {
    														const date = new Date(this.valueOf());
    														date.setDate(date.getDate() + days);
    														return date;
    													};
    													const date = new Date(tglknx);
    													// }
    													// //else--------------------------------------------------
    													// else{
    													// var tglkn1 = parseInt(leadtime_so.substr(0,2),10);
    													// var blnkn1 = parseInt(leadtime_so.substr(3,2),10);
    													// var thkn1 = parseInt(leadtime_so.substr(6,4),10);
    													// var tglkno = blnkn1+"/"+tglkn1+"/"+thkn1;
    													// var tglknx = new Date(tglkno);
    													// ///
    													// Date.prototype.addDays = function (days) {
    													//     const date = new Date(this.valueOf());
    													//     date.setDate(date.getDate() + days);
    													//     return date;
    													// };
    													// const date = new Date(tglknx);
    													// }

    													dates = parseInt(config);
    													var tglmaxleadtime = date.addDays(dates);
    													var dd = String(tglmaxleadtime.getDate()).padStart(2, '0');
    													var mm = String(tglmaxleadtime.getMonth() + 1).padStart(2, '0');
    													var yyyy = tglmaxleadtime.getFullYear();
    													maxleadtime = dd + '-' + mm + '-' + yyyy;

    													loadingtype = loadtipe.substr(0, 3);
    													materialtype = materialtype.substr(0, 7);
    													dates = parseInt(config);
    													// alert(tipe+materialtype);
    													// if((tipe==materialtype)){
    													if ((val_jkirim == loadingtype || loadingtype == 'FRC') && (tipe == materialtype)) {
    														//rollback request SCM
    														if (rddminplus1 != null || rddminplus1 != '') {
    															var tglrddmin = parseInt(leadtime_so.substr(0, 2), 10);
    															var blnrddmin = parseInt(leadtime_so.substr(3, 2), 10);
    															var thrddmin = parseInt(leadtime_so.substr(6, 4), 10);
    															var tglknorddmin = blnrddmin + "/" + tglrddmin + "/" + thrddmin;
    															var tglknxrddmin = new Date(tglknorddmin);
    															// ///
    															Date.prototype.addDays = function(days) {
    																const date = new Date(this.valueOf());
    																date.setDate(date.getDate() + days);
    																return date;
    															};
    															const date = new Date(tglknxrddmin);

    															datesrddmin = Number(rddminplus1);
    															var tglknx = date.addDays(datesrddmin);
    															var ddtglminleadtimerdd = String(tglknx.getDate()).padStart(2, '0');
    															var mmtglminleadtimerdd = String(tglknx.getMonth() + 1).padStart(2, '0');
    															var yyyytglminleadtimerdd = tglknx.getFullYear();
    															leadtime_so = ddtglminleadtimerdd + '-' + mmtglminleadtimerdd + '-' + yyyytglminleadtimerdd;
    														}
    														//
    														// alert("masuk RDD2");
    														if ((tglx >= tglknx) && (tglx <= tglmaxleadtime)) {
    															//rollback request SCM
    															// if((tglx>=tglminleadtimerdd)&&(tglx<=tglmaxleadtime)){
    															com_tgl.value = tgl;
                                  copytgl(param);
    														} else {
    															if ((tglx >= tglmaxleadtime)) {
    																alert("tanggal terima maximum adalah " + maxleadtime);
    																com_tgl.value = maxleadtime;
                                    copytgl(param);
    														  } else if ((tglx < ttglknx)) {
    																//rollback request SCM
    																// alert("tanggal terima minimum adalah "+rddleadtimemin);
    																// com_tgl.value=rddleadtimemin;
    																alert("tanggal terima minimum adalah " + leadtime_so);
    																// alert("leadtime_so " + leadtime_so + " tglx " + tglx + " tglmaxleadtime " + tglmaxleadtime);
    																com_tgl.value = leadtime_so;
                                    copytgl(param);
    															} else {
    															  com_tgl.value = tgl;
                                    copytgl(param);
    															}
    														}
    													} else {
    														// alert(tglx++tglmaxleadtime);
    														// if((tglx>=tglknx1)&&(tglx<=tglmaxleadtime)){
    														//     com_tgl.value=tgl;
    														// }else{
    														var tglkn1 = parseInt(knplus1.substr(0, 2), 10);
    														var blnkn1 = parseInt(knplus1.substr(3, 2), 10);
    														var thkn1 = parseInt(knplus1.substr(6, 4), 10);
    														var tglkno = blnkn1 + "/" + tglkn1 + "/" + thkn1;
    														var tglknx = new Date(tglkno);
    														// ///
    														Date.prototype.addDays = function(days) {
    															const date = new Date(this.valueOf());
    															date.setDate(date.getDate() + days);
    															return date;
    														};
    														const date = new Date(tglknx);

    														dates = parseInt(config);
    														var tglmaxleadtime = date.addDays(dates);
    														var dd = String(tglmaxleadtime.getDate()).padStart(2, '0');
    														var mm = String(tglmaxleadtime.getMonth() + 1).padStart(2, '0');
    														var yyyy = tglmaxleadtime.getFullYear();
    														maxleadtime = dd + '-' + mm + '-' + yyyy;
    														// alert(tglmaxleadtime+" "+tglx);
    														if ((tglx >= tglmaxleadtime)) {
    															alert("tanggal terima maximum adalah " + maxleadtime);
    															com_tgl.value = maxleadtime;
                                  copytgl(param);
    														} else if ((tglx < ttglknx)) {
    															alert("tanggal terima minimum adalah " + knplus1);
    															com_tgl.value = knplus1;
                                  copytgl(param);
    														} else {
    															com_tgl.value = tgl;
                                  copytgl(param);
    														}

    														var btnsimpan2 = 'save';
    														document.getElementById("simpan").disabled = "";
    														// }
    													}
    													// }

    												}
    											} else {
    												alert("There was a problem while using XMLHTTP1:\n" + reqrdd.statusText);
    												com_tgl.value = '';
    											}
    										}
    									}
    									req.open("GET", strURL, true);
    									req.send(null);
    								}
    								//}
    							} else {
    								//lodingact(1);
    								alert("There was a problem while using XMLHTTP2:\n" + reqs.statusText);
    								com_tgl.value = '';
    							}
    						}
    					}
    					reqs.open("GET", strURL, true);
    					reqs.send(null);
    				}
    				// if((tglx >= tglmaxleadtime))
    				// {
    				//         alert("tanggal maximum adalah "+maxleadtime);
    				//         com_tgl.value=maxleadtime;
    				// } else if (tglx >= ttglknx) { 
    				//         alert("tanggal kirim sesuai mapingan leadtime adalah "+leadtime_so);
    				//         com_tgl.value=leadtime_so;
    				// }else{
    				//     alert("tanggal kirim sesuai mapingan leadtime adalah "+leadtime_so);
    				//     com_tgl.value=leadtime_so;
    				// }
    			} else {
    				if ((tglx >= ttglknx)) {
    					com_tgl.value = tgl;
              copytgl(param);
    				} else {
    					com_tgl.value = knplus1;
              copytgl(param);
    				}
    			}
    		} else {
    			//lodingact(1);
    			alert("There was a problem while using XMLHTTP3:\n" + req.statusText);
    			com_tgl.value = '';
    		}
    	}
    }
    reqrdd.open("GET", strURL, true);
    reqrdd.send(null);
    /////////////////////////////////////
    // }else{
    //     if( (tglx >= ttglknx) )
    // 	{
    //             com_tgl.value=tgl;
    // 	} else { 
    //             com_tgl.value=knplus1; 
    //         }   
    // }
    //penambahan change 3664, plant 7954 tgl tidak boleh melebihi hari H        
    var plant = document.getElementById('plant'+param).value;
    var tglnow = new Date(Date.now());

    loadSISATARGET(param);
    document.getElementById("simpan").disabled = false;

  $(".loader").css("visibility", "hidden");
  $(".loader-page").css("visibility", "hidden");
}

function lodingact(w){
  if(w){
    console.log("loding true");
    $(".loader").css("visibility", "hidden");
    $(".loader-page").css("visibility", "hidden");
  }
  else {
    console.log("loding false");
    $(".loader-page").css("visibility", "visible");
    $(".loader").css("visibility", "visible");
  }
}

var tglkontrakvalid = '';
function loadSISATARGET(obj){
    ////////////////////
    // var jkemasan=document.getElementById('jenis_kemasan');  
    // switch (jkemasan.value) {
    //     case 'CURAH':
    //         tipe = '121-302';
    //         break;
    //     case 'MORTAR ZAK':
    //         tipe = '121-701';
    //         break;
    //     case 'MORTAR CRH':
    //         tipe = '121-702';
    //         break;
    //     case 'M3':
    //         tipe = '121-800';
    //         break;
    //     case 'M3 PALLET1':
    //         tipe = '121-800';
    //         break;
    //     default:
    //         tipe = '121-301';
    // }
    tipe = document.getElementById('material'+obj).value;
    var so_type = document.getElementById('so_type'+obj).textContent;

    ////////////////////
    var com=document.getElementById('org'+obj);
    var dist=document.getElementById('sold_to'+obj);
    var tglkirim=document.getElementById('ppreq'+obj);
    var plant=document.getElementById('plant'+obj);
    var val_plant=plant.textContent;
    var kotatj=document.getElementById('kota'+obj);
    var val_kotatj=kotatj.textContent;
    var pricelist = $("#pricelist"+obj).val();
    if(tglkirim.value!='' && val_kotatj!='' && val_plant!=''){
                if(pricelist == '01' && so_type == 'ZOR' && tipe == '121-301'){
                    console.log("PP yang diinput : material ZAK, tipe SO standart, dan pricelist 01-(Project).");
                }else{
                    if (pricelist != '02' && pricelist != '04' && pricelist != '06' && pricelist != '14' &&  pricelist != '64'  &&  pricelist != '68' && pricelist != '76' && pricelist != '77' && pricelist != '78' && pricelist != '10') {
                        // if (!tglkontrakvalid) {
                        // alert("Silahkan pilih kontrak terlebih dahulu..!!!");
                        // resetForm('0');  
                        // return false;
                        // }
                    }
                }
        if (tglkontrakvalid) {
        // var strURL="loadexplanning3.php?orgin="+com.value+"&soldin="+dist.value+'&tglkirim='+tglkirim.value+'&plant='+val_plant+'&produkin='+tipe+'&kodedistrik='+val_kotatj+'&action=viewsisa&tglkontrak='+tglkontrakvalid;
        }else{
        var strURL="../or_transaksi/loadexplanning3.php?orgin="+com.textContent+"&soldin="+dist.textContent+'&tglkirim='+tglkirim.value+'&plant='+val_plant+'&produkin='+tipe+'&kodedistrik='+val_kotatj+'&action=viewsisa';
        }    
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    // lodingact(0);
                        if (req.readyState == 4) {
                                // only if "OK"
                                if (req.status == 200) {	
                                        // lodingact(1);
                                        if (req.responseText == 'tgldiluarkontrak') {
                                            alert('tanggal kirim tidak boleh melebihi tanggal kontrak..!!!');
                                            document.getElementById('sisa_target'+obj).value=0;
                                            document.getElementById('fsisa_target'+obj).value=0;
                                            $("#tgl_kirim"+obj).val('');
                                        } else{
                                            document.getElementById('ppqtyuom'+obj+'').value='';
                                            document.getElementById('qtycek'+obj+'').value='';
                                            var cobTo2=parseFloat(req.responseText);
                                            
                                             if (isNaN(cobTo2)) {
                                                    cobTo2= 0;
                                            }
                                            //pesan kuota
                                            // add in socc v2.0
                                            var strURL="../or_transaksi/loadexplanning3.php?orgin="+com.value+"&soldin="+dist.value+'&tglkirim='+tglkirim.value+'&plant='+val_plant+'&kodedistrik='+val_kotatj+'&action=cekmapbrand';
                                            // var strURL="loadexplanning3.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=config_umur_so";
                                                var reqs = getXMLHTTP();
                                                if (reqs) {
                                                        reqs.onreadystatechange = function() {
                                                            lodingact(0);
                                                                if (reqs.readyState == 4) {
                                                                        if (reqs.status == 200) { 
                                                                            lodingact(1); 
                                                                                config=reqs.responseText;
                                                                                // alert(config);
                                                                                // alert(cobTo2);
                                                                                // alert(tipe);
                                                                                if(config=="FB"){
                                                                                    if (cobTo2<=0){
                                                                                        alert('kuota fighting brand anda habis, silahkan order main brand terlebih dahulu !!');
                                                                                        $('#ppsched' + obj).prop('disabled', true);
                                                                                        $('#ppreq' + obj).prop('disabled', true);
                                                                                        $('#ppqtyuom' + obj).prop('disabled', true);
                                                                                        $('#centang' + obj).prop('checked', false);
                                                                                        var currentValue = $('#jumlah-edit').val();
                                                                                        var newValue = parseInt(currentValue, 10) - 1;
                                                                                        $('#jumlah-edit').val(newValue);
                                                                                        $('#centang' + obj).remove();
                                                                                    }
                                                                                }else if (cobTo2<=0){
                                                                                    alert('kuota target telah habis.');
                                                                                    $('#ppsched' + obj).prop('disabled', true);
                                                                                    $('#ppreq' + obj).prop('disabled', true);
                                                                                    $('#ppqtyuom' + obj).prop('disabled', true);
                                                                                    $('#centang' + obj).prop('checked', false);
                                                                                    var currentValue = $('#jumlah-edit').val();
                                                                                    var newValue = parseInt(currentValue, 10) - 1;
                                                                                    $('#jumlah-edit').val(newValue);
                                                                                    $('#centang' + obj).remove();
                                                                                }
                                                                        } else {
                                                                                lodingact(1);
                                                                                alert("There was a problem while using XMLHTTP5:\n" + reqs.statusText);
                                                                                $('#ppsched' + obj).prop('disabled', true);
                                                                                $('#ppreq' + obj).prop('disabled', true);
                                                                                $('#ppqtyuom' + obj).prop('disabled', true);
                                                                                $('#centang' + obj).prop('checked', false);
                                                                                var currentValue = $('#jumlah-edit').val();
                                                                                var newValue = parseInt(currentValue, 10) - 1;
                                                                                $('#jumlah-edit').val(newValue);
                                                                                $('#centang' + obj).remove();
                                                                        }
                                                                }               
                                                        }           
                                                        reqs.open("GET", strURL, true);
                                                        reqs.send(null);
                                                }
                                            
                                            // if(cobTo2<=0){
                                            //         alert('kuota fighting brand anda habis, silahkan order main brand terlebih dahulu !!');
                                            //     }
                                            document.getElementById('sisa_target'+obj).value=cobTo2;
                                            document.getElementById('fsisa_target'+obj).value=cobTo2;
                                            var shiptosi = $("#ship_to"+obj).val();
                                            var produksi = $("#material"+obj).val();
                                            // if (shiptosi != '') {
                                            //     ketik_shipto(document.getElementById('ship_to'+obj),obj);
                                            // }
                                            // if (produksi != '') {
                                            //     ketik_produk(document.getElementById('produk'+obj),obj);
                                            // }
                                        }
                                } else {
                                        // lodingact(1);
                                        alert("There was a problem while using XMLHTTP6:\n" + req.statusText);
                                        $('#ppsched' + obj).prop('disabled', true);
                                        $('#ppreq' + obj).prop('disabled', true);
                                        $('#ppqtyuom' + obj).prop('disabled', true);
                                        $('#centang' + obj).prop('checked', false);
                                        var currentValue = $('#jumlah-edit').val();
                                        var newValue = parseInt(currentValue, 10) - 1;
                                        $('#jumlah-edit').val(newValue);
                                        $('#centang' + obj).remove();
                                }
                        }				
                }			
                req.open("GET", strURL, true);
                req.send(null);
        }
    }else{
        alert("Silahkan pilih Plant dan Kota terlebih dahulu..!!!");
    }
}

function getsisasementara() {
	//load sisa target
	var cekrec = document.getElementById('jumlah-edit').value;
	var counterS = 0;
	var arraytarget = new Array();
	var qtytarget = new Array();
	var sisaqtytarget = new Array();
  ////// ngedit kene
	for (var i = 0; i < jumlah_edit_arr.length; i++) {
    var a = jumlah_edit_arr[i];
		// product convertion tot TON
		var qto = parseFloat(document.getElementById('qto' + a + '').value);

		var tgl_kirimval = document.getElementById('ppsched' + a + '').value;
		// if (harianbulanan == 'bulanan') {
		// 	tgl_kirimval = tgl_kirimval.substring(3, 10);
		// }
		console.info('tglkirmval=' + tgl_kirimval);
		var sisalS = document.getElementById('sisa_target' + a + '');
		var ceksisas = parseFloat(sisalS.value);

		arraytarget[counterS] = ceksisas;

		var quantumqtykcek = document.getElementById('qtycek' + a + '');
		var qtycekekm = parseFloat(quantumqtykcek.value);
		if (isNaN(qtycekekm)) {
      qtycekekm = 0;
		}
		// convert to TON
		qtycekekm = qtycekekm * qto;

		console.info('qtytarget=' + qtytarget[counterS]);
		console.info('qtycekemkm=' + qtycekekm);
		if (parseFloat(qtytarget[counterS]) > 0) {
			qtytarget[counterS] = parseFloat(qtytarget[counterS]) + qtycekekm;
		} else {
			qtytarget[counterS] = qtycekekm;
		}
		console.info('qtytargetsetelah=' + qtytarget[counterS]);
    counterS++;
	}

	for (var k in arraytarget) {
		sisaqtytarget[k] = parseFloat(arraytarget[k] - qtytarget[k]);
	}

	for (var y = 0; y < jumlah_edit_arr.length; y++) {
    var yy = jumlah_edit_arr[y];
		for (var ss in sisaqtytarget) {
			var tglcek = document.getElementById('ppreq' + yy + '').value;
			// if (harianbulanan == 'bulanan') {
			// 	tglcek = tglcek.substring(3, 10);
			// }
			if (tglcek == ss) {
				if (!isNaN(sisaqtytarget[ss])) {
					document.getElementById('fsisa_target' + yy + '').value = parseFloat(sisaqtytarget[ss]);
				} else {
					document.getElementById('fsisa_target' + yy + '').value = 0;
				}
			}
		}
	}

}

function copytgl(param){
  var url = "list_so.php";
  var params = 'hitung_tanggal=true&plant='+$("#plant"+param).text()+'&kota='+$("#kota"+param).text()+'&material='+$("#material"+param).val();
  try {
    fetch(window.location.href, {
      method: "POST",
      headers: {
        "Content-type": "application/x-www-form-urlencoded",
      },
      body: params,
    })
    .then(response => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.text();
    })
    .then(data => {
      if (data == "") {
        data = 0;
      }
      var dateString = $("#ppsched"+param).val();

      var dateParts = dateString.split("-");
      var date = new Date(dateParts[2], dateParts[1] - 1, dateParts[0]);

      var currentDate = new Date();

      // if (date.getTime() <= currentDate.getTime()) {
      //   var newDate = new Date(currentDate);
      //   newDate.setDate(newDate.getDate() - parseInt(data));
      var newDate = new Date(date);
        if($("#jenis_kirim"+param).val() !== 'FOT')
          newDate.setDate(newDate.getDate() - parseInt(data));
        else{
          if($("#jenis_kirim_source"+param).val() == 'FRC' || $("#jenis_kirim_source"+param).val() == 'CIF')
          newDate.setDate(newDate.getDate() - parseInt(data));
        }
        // if(newDate.getDay() == 0)
        //   newDate.setDate(newDate.getDate() + 1);
        // else if(newDate.getDay() == 6)
        //   newDate.setDate(newDate.getDate() + 2);

        newDate = ("0" + newDate.getDate()).slice(-2) + "-" +
                  ("0" + (newDate.getMonth() + 1)).slice(-2) + "-" +
                  newDate.getFullYear();
        // $("#ppsched"+param).val(newDate);
        // $("#ppreq"+param).text($("#ppsched"+param).val());
        // }
        $("#ppreq"+param).val(newDate);
    })
    .catch(error => {
      alert("There was a problem while using fetch API:\n" + error.message);
    });
  } catch (error) {
    alert("There was a problem while using fetch API:\n" + error.message);
  }
}

document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Pengajuan Reschedule, Reject, Reopen SO</title>
<script src="https://code.jquery.com/jquery-3.7.0.min.js" integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<style>
  .table-container {
    max-width:100%;
    overflow:scroll;
  }
	.row-ganjil {
		background:whitesmoke;
	}
  .param-find{
    width: 45%;
    padding-left: 80px !important;
  }
  
  .loader-page{
    width:100%;
    height:100%;
    display:block;
    position: fixed;
    left: 0px;
    top: 0px;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .loader {
    margin: 35vh 45vw;
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid rgb(198, 73, 52);
    /* border-bottom: 16px solid red; */
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
  }

  @-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
</head>

<body>

<!-- <div class="loader" style="visibility: hidden;"></div> -->
<div style="visibility:hidden;" class="loader-page">
  <div class="loader"></div>
</div>

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Pengajuan Reschedule, Reject, Reopen SO </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp; </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" enctype="multipart/form-data" action="<? echo $page; ?>"  >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso param-find">No Permintaan Pembelian </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_pp" name="no_pp" disabled/></td>
    </tr>
    <tr>
      <td  class="puso param-find">No. SO </td>
      <td  class="puso">:</td>
      <td ><input type="file" id="no_so" name="no_so"  /><a href="dataxls/filter-so.xls">Download Template</a></td>
    </tr>
    <tr>
      <td  class="puso param-find">Sold To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="sold_to" name="sold_to"  disabled/></td>
    </tr>
    <tr>
      <td  class="puso param-find">Ship To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="ship_to" name="ship_to"  /></td>
    </tr>
    <tr>
      <td  class="puso param-find">Produk </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="produk" name="produk"  /></td>
    </tr>
    <tr>
      <td  class="puso param-find">Distrik </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="distrik" name="distrik"  /></td>
    </tr>
    <tr>
      <td  class="puso param-find">Tanggal PP</td>
      <td  class="puso">:</td>
      <td ><input disabled name="pp_from" type="text" id="pp_from" size=12 onClick="return showCalendar('pp_from');"/>&nbsp; s.d &nbsp;
	<input disabled name="pp_to" type="text" id="pp_to" size=12 onClick="return showCalendar('pp_to');"/></td>
    </tr>
    <tr>
      <td  class="puso param-find">Req. Delivery Date</td>
      <td  class="puso">:</td>
      <td ><input name="req_from" type="text" id="req_from" size=12 onClick="return showCalendar('req_from');" readonly value="<?= date('d-m-Y');?>"/>&nbsp; s.d &nbsp;
	<input name="req_to" type="text" id="req_to" size=12 onClick="return showCalendar('req_to');" readonly value="<?= date('d-m-Y');?>"/></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>

<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<!-- !table -->
<div class="table-hasil">
  <div align="center">
    <table width="100%" align="center" class="adminlist" >
      <tr>
        <th align="left"></th>
      </tr>
    </table>
  </div>
  <div align="center" class="table-container">
    <table width="95%" align="center" class="adminlist" >
      <thead>
        <tr class="quote">
          <td align="center"><strong>&nbsp;&nbsp;Edit</strong></td>
          <td align="center"><strong>Organization</strong></td>
          <td align="center"><strong>Order Type</strong></td>
          <td align="center"><strong>Sold to party</strong></td>
          <td align="center"><strong>Name</strong></td>
          <td align="center"><strong>Ship to</strong></td>
          <td align="center"><strong>Name</strong></td>
          <td align="center" hidden><strong>Kode Prop</strong></td>
          <!-- <td align="center"><strong>Sales District</strong></td> -->
          <td align="center"><strong>District Name</strong></td>
          <td align="center"><strong>Material</strong></td>
          <td align="center"><strong>Description</strong></td>
          <td align="center"><strong>Plant</strong></td>
          <td align="center"><strong>Sales Order</strong></td>
          <td align="center"><strong>SO Create Date</strong></td>
          <td align="center"style="padding: 0 10px;" hidden><strong>SO Req. Dlvr Date</strong></td>
          <td align="center" hidden><strong>SO Sched Dlvr Date</strong></td>
          <td align="center"><strong>SO Order QTY (UOM)</strong></td>
          <td align="center"><strong>Sales Unit</strong></td>
          <td align="center"><strong>SO Order QTY (TON)</strong></td>
          <!-- <td align="center"><strong>Reason for Ejection SO</strong></td> -->
          <td align="center" hidden><strong>SO Description</strong></td>
          <td align="center"><strong>Permintaan Pembelian</strong></td>
          <td align="center"><strong>PP Create Date</strong></td>
          <td align="center"><strong>PP Req. Dlvr Date</strong></td>
          <td align="center"><strong>PP Sched Dlvr Date</strong></td>
          <td align="center"><strong>PP Order QTY (UOM)</strong></td>
          <td align="center"><strong>Sales Unit</strong></td>
          <!-- <td align="center"><strong>PP Order QTY (TON)</strong></td> -->
          <td align="center"><strong>Reason for Rejection</strong></td>
          <!-- <td align="center"><strong>Delivery Block</strong></td> -->
        </tr >
      </thead>
      <tbody>
        <?
          foreach ($datadoc as $value) {
            //  var_dump($value);return;
            print_r($value);
          
          		if((($s+1) % 2) == 0){	 
          			echo "<tr class='row-genap' id='$rowke'>";
          		}
          		else{
          			echo"<tr class='row-ganjil' id='$rowke'>";
          		}
        ?>
        <td align="center"><input type="checkbox" id="<?= 'centang'.$s?>" class="centang-all" value="0" onChange="checkForother(this,'<?=$s?>')"> <?=$s+1?></td>
        <td align="center" id="<?= 'org'.$s?>" name="<?= 'org'.$s?>"><?=$value['VKORG']?></td>
        <td align="center" id="<?= 'so_type'.$s?>"><?=$value['AUART']?></td>
        <input type="hidden" id="<?= "jenis_kirim".$s?>" value="<?=$value['INCO1']?>"/>
        <input type="hidden" id="<?= "pricelist".$s?>" value="<?=$value['POSNR']?>"/>
	      <input type="hidden" value="" id="<?= 'fsisa_target'.$s?>"/>
        <input type="hidden" id="<?= 'sisa_target'.$s?>" value="" />
        <td align="center" id="<?= 'sold_to'.$s?>"><?=$value['KUNNR']?></td>
        <td align="center" id="<?= 'name1'.$s?>"><?=$value['NAME1']?></td>
        <td align="center" id="<?= 'ship_to'.$s?>"><?=$value['KUNNR2']?></td>
        <td align="center"><?=$value['NAME2']?></td>
        <!-- <td align="center"><?=""?></td> -->
        <td align="center" hidden id="<?= 'kota'.$s?>"><?=$value['BZIRK']?></td>
        <td align="center"><?=$value['BZTXT']?></td>
        <td align="center" id="<?= 'material_full'.$s?>"><?=$value['MATNR']?></td>
        <input type="hidden" id="<?= 'material'.$s?>" value="<?= substr($value['MATNR'], 0, 7);?>">
        <td align="center"><?=$value['MAKTX']?></td>
        <input type="hidden" id="<?= 'qto'.$s?>" value="">
        <td align="center" id="<?= 'plant'.$s?>"><?=$value['WERKS']?></td>
        <td align="center" id="<?= 'noso'.$s?>"><?=$value['VBELN']?></td>
        <td align="center"><?= date("d-m-Y", strtotime($value['AUDAT']))?></td>
        <td hidden align="center" id="<?= 'soreq'.$s?>"><?= date("d-m-Y", strtotime($value['VDATU']))?></td>
        <td hidden align="center" id="<?= 'sordd'.$s?>"><?= date("d-m-Y", strtotime($value['EDATU']))?></td>
        <td align="center"><? $value['KWMENG'] = explode(".",$value['KWMENG']);echo $value['KWMENG'][0]?></td>
        <td align="center" id="<?= 'unit'.$s?>"><?=$value['MEINS']?></td>
        <td align="center"><? $value['NTGEW'] = explode(".",$value['NTGEW']);echo $value['NTGEW'][0]?></td>
        <td hidden align="center"><span id="<?= 'status_so'.$s?>"><?
          switch ($value['ABGRU']) {
            case '00':
              echo 'Assicned by System (Internal)';
              break;
            case '01':
              echo 'Delivery date too late';
              break;
            case '02':
              echo 'Poor quality';
              break;
            case '03':
              echo 'Too expensive';
              break;
            case '04':
              echo 'Competitor better';
              break;
            case '05':
              echo 'Guarantee';
              break;
            case '10':
              echo 'Unreasonable request';
              break;
            case '11':
              echo 'Cust.to receive replacement';
              break;
            case '12':
              echo 'Expired Change (manual)';
              break;
            case '50':
              echo 'Transaction is being checked';
              break;
            case '52':
              echo 'Penggantian barang dari Transaksi Return';
              break;
            default:
              echo '';
              break;
            }
          ?></span></td>
        <!-- <td align="center"></td> -->
	      <input type="hidden" id="<?= "tglnya".$s?>" name="tglnya" value="<?=gmdate("d-m-Y",time()+60*60*7);?>" />
        <input type="hidden" id="<?= "tgl1nya".$s?>" name="tgl1nya" value="<?=date("d-m-Y")?>" />
        <input type="hidden" id="<?= "typetruck".$s?>" name="typetruck" value="<?=$value['KVGR1']?>" />
        <input type="hidden" id="<?= "route".$s?>" name="route_code" value="<?=$value['ROUTE']?>" />

        <?
        $no_so_rfc = $value['VBELN'];
        $sql = "SELECT otd.NO_PP, otd.QTY_APPROVE AS QTY_PP, otd.TGL_KIRIM_APPROVE AS TGL_KIRIM_PP, otd.UOM, oth.TGL_PP, otd.TGL_LEADTIME FROM OR_TRANS_DTL otd JOIN OR_TRANS_HDR oth ON otd.NO_PP = oth.NO_PP WHERE otd.NO_SO = '$no_so_rfc'";
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $total_v = 0;
        unset($dataAll);
        while($row=oci_fetch_array($query)){
        ?>
        <td align="center"><span id="<?='nopp'.$s?>"><?=$row['NO_PP']?></span></td>
        <td align="center">
          <span><?= date("d-m-Y", strtotime($row['TGL_PP']))?></span>
        </td>
        <td align="center"><strong>
          <input id="<?='ppsched'.$s?>" name="<?='ppsched'.$s?>" type="text" value="<?= date("d-m-Y", strtotime($row['TGL_KIRIM_PP']))?>" size="6" disabled="disabled" onClick="return showCalendar('<?='ppsched'.$s?>')" onfocusout="settgl(<?=$s?>);"/>
          </strong>
        </td>
        <td align="center"><strong>
          <input type="text" id="<?='ppreq'.$s?>" name="<?='ppreq'.$s?>" value="<?= date("d-m-Y", strtotime($value['EDATU']))?>" size="6" disabled="disabled" readonly/>
          </strong>
        </td>
        <td align="center">
          <span id="<?='ppqtyuom'.$s?>"><?= $row['QTY_PP']?></span>  
        </td>
        <td align="center">
          <span id="<?='ppsalesunit'.$s?>"><?=$row['UOM']?></span>  
        </td>
        <!-- <td align="center"><strong>
          <input id="<?='ppqtyton'.$s?>" name="<?='ppqtyton'.$s?>" type="text" value="<? ?>" size="6" maxlength="5" disabled="disabled" onBlur="javascript:IsNumeric(this)"/>
          </strong>
        </td> -->
        <input type="hidden" id="<?='abgru'.$s?>" value="<?=$value['ABGRU']?>">
        <td align="center"><strong>
        <select id="<?='reason'.$s?>" name="<?='reason'.$s?>" required disabled="disabled">
          <option></option>
          <!-- <? 
            $fungsi->or_reason_reject($value['ABGRU']); 
          ?> -->
          <option value="12" <?if($value['ABGRU']=='12')echo'selected'?>>12 - Expired Change (manual)</option>
          <option value="50" <?if($value['ABGRU']=='50')echo'selected'?>>50 - Transaction is being checked</option>
        </select>
        </td>
        <!-- <td align="center"><strong>
        <select id="<?='delivery-block'.$s?>" name="<?='delivery-block'.$s?>" required disabled="disabled">
          <option></option>
          <? $fungsi->or_delivery_block($value['LIFSK']); ?>
        </select>
        </td> -->
        <!-- <td align="center"><strong>
          <input id="<?=$jam_dat;?>" name="<?=$jam_dat;?>" disabled="disabled" type="text" value="<?=$jam_datang_v[$i]?>" maxlength="2" size="2" onBlur="javascript:IsTime(this)" onChange="javascript:IsTime(this)"/> : 
          <input id="<?=$min_dat;?>" name="<?=$min_dat;?>" type="text" value="<?=$men_datang_v[$i]?>" maxlength="2" size="2" disabled="disabled" onBlur="javascript:IsTime(this)" onChange="javascript:IsTime(this)"/></strong>
        </td>
        <td align="center"><strong>
          <input name="<?=$jam_bkr;?>" id="<?=$jam_bkr;?>" type="text" value="<?=$jam_bongkar_v[$i]?>" maxlength="2" size="2" disabled="disabled" onBlur="javascript:IsTime(this)" onChange="javascript:IsTime(this)"/> : 
          <input id="<?=$min_bkr;?>" name="<?=$min_bkr;?>" type="text" value="<?=$men_bongkar_v[$i]?>" maxlength="2" size="2" disabled="disabled" onBlur="javascript:IsTime(this)" onChange="javascript:IsTime(this)" /></strong>
        </td> -->
      <? }
      $s++;echo "<script>checkboxCount++;</script>";
    } ?>
    </tr>
    </tbody>
    <tr class="quote">
      <td colspan="100" align="center" >
          <input name="simpan" type="submit" class="button" id="simpan" value="Save" onclick=saveiki() style="cursor: pointer;"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <a href="list_so.php" target="isi" class="button">Cancel</a>
        </td>
      </tr>
    </table>
    <input type="hidden" id="jumlah-edit" value="0">
  </div>
</div>
	<!-- !table -->
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
