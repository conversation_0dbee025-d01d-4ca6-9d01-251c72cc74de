<?php
error_reporting(E_ERROR | E_PARSE);
$request_method = $_SERVER["REQUEST_METHOD"];

require_once ("autorisasi.php");
$fautoris= new autorisasi();

global $fautoris;
echo "tes";
switch ($request_method) {
    case 'POST':
        $token_in = trim($_POST['token']);
        $role=$fautoris->login($token_in);
        $jmlData=count($role['dataUserAuto']);
        // echo "oke";
        // die;
        if($role['status']==true && $jmlData>0){

            $user_id = trim($role['dataUserAuto']['USER_ID']);
            $dirr = $_SERVER['PHP_SELF'];
            $rolenn=$fautoris->keamananser($dirr,$user_id); // pembatasan akses ke service

            if ($rolenn==false) {
                $ret = array("status"=>false,"keterangan"=>"Tidak ada akses terhadap service ini");
                echo $hasil = json_encode($ret);
            } else {
                $param["NOPOL"] = $_POST["NOPOL"];
                $param["ORG"] = $_POST["ORG"];
                $param["PLANT"] = $_POST["PLANT"];
                $param["USERNAME"] = $_POST["USERNAME"];
                $param["CONVEYOR"] = $_POST["CONVEYOR"];
                $param["DRIVER"] = $_POST["DRIVER"];
                $param["NO_SIM"] = $_POST["NO_SIM"];
                $param["SPP"] = $_POST["SPP"];

                function formatElement($el) {
                        return sprintf("%010s", $el); 
                    }
                $elements = explode(",", $_POST["NO_SO"]);
                $param["NO_SO"] = array_unique(array_map('formatElement', $elements));

                $param["NO_SO_SBI"] = sprintf("%010s",$_POST["NO_SO_SBI"]);  
                $param["TYPE_SO"] = $_POST["TYPE_SO"];
                if(count($param["NO_SO"]) > 1){
                    $param["QTY"] = explode(",", trim($_POST["QTY"]));
                }else{
                    $param["QTY"] = explode(",", array_sum(explode(",", trim($_POST["QTY"]))));
                }  
                $param["MD_Only"] = ""; // $_POST["MD_Only"];
                $param["CONVEYOR_SBI"] = $_POST["CONVEYOR_SBI"];
                $param["SLoc_SBI"] = ""; // $_POST["SLoc_SBI"];
                $reg = $_POST["REG"]; // null = old, 1 2 = sp, 3 4 5 = old, 6 = ?
                $param["IDCARD"] = $_POST["IDCARD"];
                $param["expeditur_mitra"] = $_POST["EXPEDIITUR_MITRA"];
                $param["expeditur_mitra_nama"] = $_POST["EXPEDIITUR_MITRA_NAMA"];

                if(empty($param['NOPOL']) || empty($param["ORG"]) || empty($param["PLANT"]) || empty($param["USERNAME"]) || empty($param["DRIVER"]) || empty($param["SPP"]) || empty($param["NO_SO"]) || empty($param["QTY"])){ //mandatory
                    $ret = array("status"=>false,"keterangan"=>"Parameter harus lengkap");
                    echo $hasil = json_encode($ret);
                }

                if($reg == '1' || $reg == '2'){
                    $sp = new MatchingSP($fautoris->con_sp(), $fautoris->koneksi());

                    if(strlen($param["NO_SO"][0]) == 12 && substr($param["NO_SO"][0],0,2)=="51"){
                        $param["PO"] = $param["NO_SO"][0];
                        $sp->MatchingPOSTO($param);
                        $_return = $sp->_return;
                        if ($_return["ERROR"] == "") {
                            $pesan = array('Plant Asal: ' => $param["PLANT"], 'Plant Tujuan:' => $_return["PLANT_TO"]);
                            $no_transaksi = array('NO_ANTRI_OPCO' => $_return["NO_TRANSAKSI"], 'DO_OPCO' => $_return["DO_OPCO"]);
                            $ret = array("status"=>true,"keterangan"=>$pesan,"result"=>$no_transaksi);
                        } else {
                            if($_return["NO_DO"]){
                                $rollback = $sp->rollbackDO();
                            }
                            $ret = array("status"=>false,"keterangan"=>$_return["ERROR"].". $rollback [w1]","result"=>'');
                        }
                    } else {
                        $sp->getDataBook($param);
                        if ($sp->_return["ERROR"] == "") {
                            $sp->bookClick();
                            if ($sp->_return["ERROR"] == "") {
                                $sp->generateDOClick();
                                if ($sp->_return["ERROR"] == "") {
                                    $_return = $sp->simpanClick();
                                    if ($sp->_return["ERROR"] == "") {
    
                                        $pesan = 'No DO = ' . $_return["DO_OPCO"] . ' (Plant : '.$param["PLANT"].')';
                                        $do = array('NO_ANTRI' => $_return["ANTRI_MD"],'NO_ANTRI_OPCO' => $_return["ANTRI_OPCO"],'DO_MD' => $_return["DO_MD"], 'DO_Opco' => $_return["DO_OPCO"]);
                                        $ret = array("status"=>true,"keterangan"=>$pesan,"result"=>$do);
    
                                    } else $ret = array("status"=>false,"keterangan"=>$sp->_return["ERROR"]." [w4]","result"=>'');
                                } else $ret = array("status"=>false,"keterangan"=>$sp->_return["ERROR"]." [w3]","result"=>'');
                            } else $ret = array("status"=>false,"keterangan"=>$sp->_return["ERROR"]." [w2]","result"=>'');
                        } else $ret = array("status"=>false,"keterangan"=>$sp->_return["ERROR"]." [w1]","result"=>'');
                    }

                    echo $hasil = json_encode($ret);

                } elseif($reg == '6'){
                    echo $hasil = json_encode(array("status"=>false,"keterangan"=>"REG masih dalam pengembangan","result"=>''));
                } elseif($reg >= 7){
                    echo $hasil = json_encode(array("status"=>false,"keterangan"=>"REG tidak ada","result"=>''));
                } else { // existing

                    $get = new Matching();

                    $lanjut = false;
                    $_truk = $get->getDataTruck($param);
                    if (count($_truk)>0) {
                        $lanjut = true;
                    }
                    if ($lanjut) {
                        $get->cariTruk($param);
                        $get->Antri($param);
                    } else{
                        header("Content-Type: application/json");
                        header("HTTP/1.0 400 Bad Request");
                        $ret = array("status"=>false,"keterangan"=>"no ekspeditur tidak sesuai");
                        echo $hasil = json_encode($ret);
                        return;
                    }

                    if(strtoupper($reg) == '2SMBR'){
                        echo 'masuk matching MD else 124';
                        // echo 'masuk 2smbr<br><br>';
                        if ($get->_antri!="") {
                            $_so = $get->getSOpen2($param);
                        }

                        $smbr = false;
                        // $sbi = false;
                        // $plant_d = $get->data_plant($param, $fautoris->koneksi());  
                        // $LDT = $plant_d['LDT'];

                        if (count($_so)==count($param["NO_SO"])) { // cek apakah ada SO Open.  
                            if (strlen((int) $_so[1]['NOSO2'])==10) {
                                $smbr = true;
                            //     $truk_sbi = $get->Truk_SBI($param);
                            }

                            // KONDISI PENAMBAHAN UNTUK 3 LAYER NON LDT
                            // if ($param["PLANT"]=='7938' || $param["PLANT"]=='79D3') {
                            //     $truk_sbi = $get->Truk_SBI($param);
                            // }
                            // END PENAMBAHAN UNTUK 3 LAYER NON LDT

                            $ada_qty = true;
                            if (count($param["NO_SO"])>1) {                        
                                foreach ($param["QTY"] as $key) {
                                    // echo "Qty ".$key."</br>";
                                    if ($key>0) echo ""; else $ada_qty = false;
                                }
                            }
                            if ($ada_qty) {
                                $lanjut = $get->Matching_MD_SMBR($param, $smbr, $fautoris->koneksi()); //proses matching MD, SBI = false

                                // if ($lanjut == true && $sbi==false) { // matching 7900 sukses
                                //     if ($LDT==1) {
                                //         $lanjut = $get->AutoLDT($param,$_so,"",$fautoris->koneksi());
                                //     }
                                // }
                                $jumline = 0;
                                foreach ($_so as $key => $value) {
                                    $jumline = $jumline + 10;
                                    $kd_soldpd = $value['KUNNR'];
                                    if($kd_soldpd =="0000003808" || $kd_soldpd =="0000004616" || $kd_soldpd =="0000003813" || $kd_soldpd =="0000004410" || $kd_soldpd == "0000003998" || $kd_soldpd == "0000003999" || $kd_soldpd == "0000008998" || $kd_soldpd == "0000380801" || $kd_soldpd == "0000000385" || $kd_soldpd == "0000003819"){
                                        $material = $value['MATNR'];                                     
                                        $org      = $param["ORG"];
                                        $plant    = $param["PLANT"];
                                        $trans    = $get->_antri;
                                        $no_polisi= $param['NOPOL']; 
                                        $distriks = substr($value['BZIRK'], 0, 2);
                                        $distrik_lkp = $value['BZIRK'];

                                        $get->changeBag($trans, $org, $plant, $material, $kd_soldpd,$jumline,$no_polisi,$distriks,$distrik_lkp);
                                    }    
                                }
                
                            } else {
                                array_push($get->_pesan,"Inputan Qty Salah");
                                $lanjut = false;
                                $status = 10;
                            }

                        } else {
                            $status = 10;
                            $lanjut = false;
                        }    

                        if ($lanjut == true) {
                            $status = 40;
                            $lanjut = $get->Matching_SMBR($param,$_so,$fautoris->koneksi());
                        } else $status = 10;
                        
                        // KONDISI PENAMBAHAN UNTUK 3 LAYER NON LDT
                        // if (($param["PLANT"]=='7938' || $param["PLANT"]=='79D3') &&  $lanjut == true ) {
                        //     $status = 40;
                        //     $lanjut = $get->MatchSBI($param,$_so,$truk_sbi,$fautoris->koneksi());
                        // } else $status = 10;
                        // END KONDISI PENAMBAHAN UNTUK 3 LAYER NON LDT

                        if (!$lanjut && $get->_antri!="") {
                            //echo $get->_antri."</br>";
                            //echo 'LANUT';
                            
                            // if ($LDT==1) {
                            //     $get->rollBack($param, $get->_antri,$status,$sbi,$get->_NOLDT,$fautoris->koneksi());
                            // } else {
                            //     $get->rollBack($param, $get->_antri,$status,$sbi);
                            // }
                            $get->rollBack_MD_SMBR($param, $get->_antri, $status);
                        } else {
                            // if ($param["PLANT"] == "7806" || $param["PLANT"] == "7641") {
                            //     $do_md = implode(",", $get->_DO_MD_7000);
                            //     //echo '7000';
                            // }else {
                            //     $do_md = implode(",", $get->_DO_MD);
                            //     //echo 'MD';
                            // }

                            $do_md = implode(",", $get->_DO_MD);

                            function filter_null_values($value) {
                                return $value !== null && $value !== '';
                            }
                            $filtered_array = array_filter($get->_DO_Opco, 'filter_null_values');

                            $my_do_opco = implode(',', $filtered_array);
                            $do_opco = implode(",", $get->_DO_Opco);
                            // if ($param["MD_Only"]=="X") {
                            //     if (strlen($do_md)<7) {
                            //         $get->rollBack($param, $get->_antri,$status,$sbi);
                            //         $lanjut = false;
                            //     }
                            // } else { //echo $param['PLANT'];
                            //     if ($param["ORG"]!='7900' && $param["PLANT"] != "7806" && $param["PLANT"] != "7641"){
                            //         if (strlen($do_opco)<7) {
                            //             $get->rollBack($param, $get->_antri,$status,$sbi);
                            //             $lanjut = false;
                            //             //echo 'ROLLBACK !!!!!!!!!';
                            //         }
                            //     } else {
                            //         if ($LDT==1) {
                            //             if (strlen($do_md)<7) {
                            //                 $get->rollBack($param, $get->_antri,$status,$sbi,$get->_NOLDT,$fautoris->koneksi());
                            //                 $lanjut = false;
                            //             }
                            //         } else {
                            //             if (strlen($do_md)<7 || strlen($do_opco)<7) {
                            //                 $get->rollBack($param, $get->_antri,$status,$sbi);
                            //                 $lanjut = false;
                            //             }
                            //         }    
                            //     }
                            // }
                        }
                    }
                    //************************Penambahan untuk PO STO***************************//
                    else if(strlen($param["NO_SO"][0])== 12 && substr($param["NO_SO"][0],0,2)=="51"){ // matching PO STO
                        echo 'masuk matching MD else 260';
                        //echo "MASUK";
                        if ($get->_antri!="") {
                        $_po = $get->getPOSTO($param);   
                        
                        if (count($_po) >= 1 && $param["QTY"] > 0) { 
                            $ada_qty = true;
                        }else{
                            $ada_qty = false;
                        }
                        
                            if ($ada_qty) {
                                $lanjut = $get->Matching_posto($param, $sbi,$fautoris->koneksi()); 
                            }else {
                                array_push($get->_pesan,"Inputan Qty Salah");
                                $lanjut = false;
                                $status = 10;
                            }
                                if (!$lanjut && $get->_antri!="") {
                                    $get->rollBack($param, $get->_antri,$status,$sbi);
                                    
                                }            
                        }
                    
                    }
                    //************************end Penambahan untuk PO STO***************************//
                    else {
                        echo 'masuk matching MD else';
                        if ($get->_antri!="") {
                            $_so = $get->getSOpen2($param);
                        }

                        $sbi = false;
                        $plant_d = $get->data_plant($param, $fautoris->koneksi());  
                        $LDT = $plant_d['LDT'];

                        // if (count($_so)>0) {
                        // echo count($_so)." - ".count($param["NO_SO"]);
                        if (count($_so)==count($param["NO_SO"])) {        // cek apakah ada SO Open.  
                            if (strlen((int) $_so[1]['NOSO2'])==10) {
                                $sbi = true;
                                $truk_sbi = $get->Truk_SBI($param);
                            }
                            
                            // KONDISI PENAMBAHAN UNTUK 3 LAYER NON LDT
                            if ($param["PLANT"]=='7938' || $param["PLANT"]=='79D3') {
                                $truk_sbi = $get->Truk_SBI($param);
                            }
                            // END PENAMBAHAN UNTUK 3 LAYER NON LDT
                            

                            $ada_qty = true;
                            if (count($param["NO_SO"])>1) {                        
                                foreach ($param["QTY"] as $key) {
                                    // echo "Qty ".$key."</br>";
                                    if ($key>0) echo ""; else $ada_qty = false;
                                }
                            }
                            if ($ada_qty) {
                                $lanjut = $get->Matching($param, $sbi, $fautoris->koneksi()); //proses matching MD, SBI = false

                                if ($lanjut == true && $sbi==false) { // matching 7900 sukses
                                    if ($LDT==1) {
                                        $lanjut = $get->AutoLDT($param,$_so,"",$fautoris->koneksi());
                                        //echo 'JALAN JALAN';
                                        //echo $lanjut;
                                    }
                                }
                                $jumline = 0;
                                foreach ($_so as $key => $value) {
                                    $jumline = $jumline + 10;
                                    $kd_soldpd = $value['KUNNR'];
                                    if($kd_soldpd =="0000003808" || $kd_soldpd =="0000004616" || $kd_soldpd =="0000003813" || $kd_soldpd =="0000004410" || $kd_soldpd == "0000003998" || $kd_soldpd == "0000003999" || $kd_soldpd == "0000008998" || $kd_soldpd == "0000380801" || $kd_soldpd == "0000000385" || $kd_soldpd == "0000003819"){
                                        $material = $value['MATNR'];                                     
                                        $org      = $param["ORG"];
                                        $plant    = $param["PLANT"];
                                        $trans    = $get->_antri;
                                        $no_polisi= $param['NOPOL']; 
                                        $distriks = substr($value['BZIRK'], 0, 2);
                                        $distrik_lkp = $value['BZIRK'];

                                        $get->changeBag($trans, $org, $plant, $material, $kd_soldpd,$jumline,$no_polisi,$distriks,$distrik_lkp);
                                    }    
                                }
                
                            } else {
                                array_push($get->_pesan,"Inputan Qty Salah");
                                $lanjut = false;
                                $status = 10;
                            }

                        } else {
                            $status = 10;
                            $lanjut = false;
                        }    

                        if ($lanjut == true && $sbi == true) {
                            $status = 40;
                            $lanjut = $get->MatchSBI($param,$_so,$truk_sbi,$fautoris->koneksi());
                            //echo 'LANJUT'. $lanjut;
                        } else $status = 10;
                        
                        // KONDISI PENAMBAHAN UNTUK 3 LAYER NON LDT
                        if (($param["PLANT"]=='7938' || $param["PLANT"]=='79D3') &&  $lanjut == true ) {
                            $status = 40;
                            $lanjut = $get->MatchSBI($param,$_so,$truk_sbi,$fautoris->koneksi());
                        } else $status = 10;
                        // END KONDISI PENAMBAHAN UNTUK 3 LAYER NON LDT

                        if (!$lanjut && $get->_antri!="") {
                            //echo $get->_antri."</br>";
                            //echo 'LANUT';
                            
                            if ($LDT==1) {
                                $get->rollBack($param, $get->_antri,$status,$sbi,$get->_NOLDT,$fautoris->koneksi());
                            } else {
                                $get->rollBack($param, $get->_antri,$status,$sbi);
                            }    
                        } else {
                            
                            if ($param["PLANT"] == "7806" || $param["PLANT"] == "7641") {
                                $do_md = implode(",", $get->_DO_MD_7000);
                                //echo '7000';
                            }else {
                                $do_md = implode(",", $get->_DO_MD);
                                //echo 'MD';
                            }
                                
                            //$do_md = implode(",", $get->_DO_MD);

                            function filter_null_values($value) {
                                return $value !== null && $value !== '';
                            }
                            $filtered_array = array_filter($get->_DO_Opco, 'filter_null_values');
                            // $filtered_array = array_filter($get->_DO_Opco);
                            
                            // $filtered_array = array_filter($get->_DO_Opco, function($value) {
                            //     return $value !== null && $value !== '';
                            // });
                            $my_do_opco = implode(',', $filtered_array);
                            $do_opco = implode(",", $get->_DO_Opco);
                            if ($param["MD_Only"]=="X") {
                                if (strlen($do_md)<7) {
                                    $get->rollBack($param, $get->_antri,$status,$sbi);
                                    $lanjut = false;
                                }
                            } else { //echo $param['PLANT'];
                                if ($param["ORG"]!='7900' && $param["PLANT"] != "7806" && $param["PLANT"] != "7641"){
                                    if (strlen($do_opco)<7) {
                                        $get->rollBack($param, $get->_antri,$status,$sbi);
                                        $lanjut = false;
                                        //echo 'ROLLBACK !!!!!!!!!';
                                    }
                                } else {
                                    if ($LDT==1) {
                                        if (strlen($do_md)<7) {
                                            $get->rollBack($param, $get->_antri,$status,$sbi,$get->_NOLDT,$fautoris->koneksi());
                                            $lanjut = false;
                                        }
                                    } else {
                                        if (strlen($do_md)<7 || strlen($do_opco)<7) {
                                            $get->rollBack($param, $get->_antri,$status,$sbi);
                                            $lanjut = false;
                                        }
                                    }    
                                }
                            }
                        }
                    }

                    // query pengecekan mapping plant SO POSTO
                    $conn=$fautoris->koneksi();
                    $strmat = "SELECT PLANT_TUJUAN FROM MAPPING_SO_POSTO_API WHERE PLANT_TUJUAN = '{$param["PLANT"]}' AND ORG_TUJUAN = '{$param["ORG"]}' AND  DELETE_MARK=0";
                    $query=@oci_parse($conn, $strmat);
                    @oci_execute($query);          
                    $cek_plant = oci_fetch_array($query, OCI_ASSOC);  
                
                    if($param["PLANT"] == $cek_plant["PLANT_TUJUAN"]){

                        if ($lanjut && $get->_antri!="") {
                        
                        // =====================Penambahan SO POSTO=================================//
                        
                        if($param["PLANT"] == $cek_plant["PLANT_TUJUAN"]){ // matching PO STO
                            if ($get->_antri!="") {
                            $_so = $get->getSOpen2($param);
                            if (count($_so)==count($param["NO_SO"])) {    
                                $param["PO_REFF"] = $_so[1]['PO_REFF'];
                                //nanti jamgan lupa buat fungsi baru
                                // param detail truck
                                $param["TIPE_TRUK"] = $_truk["TIPE_TRUK"];
                                $param["TIPE_NAME"] = $_truk["TIPE_NAME"];
                                $param["NO_STNK"] = $_truk["NO_STNK"];
                                $param["0000410041"] = $_truk["0000410041"];
                                $param["SYSTEM_BONGKAR"] = $_truk["SYSTEM_BONGKAR"];
                                $param["KAPASITAS"] = $_truk["KAPASITAS"];
                                $param["WARNA_PLAT"] = $_truk["WARNA_PLAT"];
                                $param["NO_RFID"] = $_truk["NO_RFID"];
                                $param["ID_CARD"] = $_truk["ID_CARD"];
                                $param["NO_EXPEDITUR"] = $_truk["NO_EXPEDITUR"];
                                $param["NAMA_EXPEDITUR"] = $_truk["NAMA_EXPEDITUR"];

                                $_po = $get->getPOSTO_MDK($param);   
                                $param["plant_asal"] = $_po[0]['RESWK'];
                            }
                            
                            if (count($_po) >= 1 && $param["QTY"] > 0) { 
                                $ada_qty = true;
                            }else{
                                $ada_qty = false;
                            }
                            
                                if ($ada_qty) {
                                    $lanjut_po = $get->Matching_soposto($param, $sbi,$fautoris->koneksi()); 
                                    // tes
                                }else {
                                    array_push($get->_pesan,"Inputan Qty Salah");
                                    $lanjut_po = false;
                                    $status = 10;
                                }
                                    if (!$lanjut_po) {     
                                        $param["antri_so"] =$get->_antri;
                                        $param["antri_po"] = $get->_antri_po;
                                        $messageRollback = $get->rollBackSOPO($param); //ini rollback SO

                                        $pesan = implode("</br>", $get->_pesan);
                                        $pesanpo = "NO DO PO = ".$get->_pesan_po_do. '(Plant :'.$param["plant_asal"].')';                    
                                        $do = array('NO_ANTRI SO' => $get->_antri, 'NO_ANTRI PO' => $get->_antri_po,  'No. DO PO' => implode(',', $get->_do_po), 'No. DO SO' => $my_do_opco );
                                        $ret = array("status"=>false,"keterangan"=> $pesan,$pesanpo, 'Success Rollback Data',"result"=>$do);
                                        echo $hasil = json_encode($ret);
                                        
                                    }else{
                                        $pesan = implode("</br>", $get->_pesan);                    
                                        $do = array('NO_ANTRI SO' => $get->_antri, 'NO_ANTRI PO' => $get->_antri_po,  'No. DO PO' => implode(',', $get->_do_po), 'No. DO SO' => $my_do_opco );
                                        $ret = array("status"=>true,"keterangan"=>$pesan,"result"=>$do);
                                        echo $hasil = json_encode($ret);
                                    }            
                            }
                        
                        }
                        // =====================End Penambahan SO POSTO============================//
                            
                        } else  {
                            $pesan = implode("</br>", $get->_pesan);
                            $ret = array("status"=>false,"keterangan"=>$pesan);
                            echo $hasil = json_encode($ret);
                        }

                    }else{
                        if ($lanjut && $get->_antri!="") {
                            $pesan = implode("</br>", $get->_pesan);                    
        
                            $do = array('NO_ANTRI' => $get->_antri,'DO_MD' => $do_md, 'DO_Opco' => $do_opco);
                            $ret = array("status"=>true,"keterangan"=>$pesan,"result"=>$do);
                            echo $hasil = json_encode($ret);
                        } else  {
                            $pesan = implode("</br>", $get->_pesan);
                            $ret = array("status"=>false,"keterangan"=>$pesan);
                            echo $hasil = json_encode($ret);
                        }
                    }
                }

            }
        }else{
            echo $hasil = json_encode($role);
        }
        $responseRequest = array (
            'ResponseCode' => "",
            'ResponseMessage' => $hasil
        );

        $byLog = 'match_tmssp';
        $param["NO_SO"] =  json_encode($param["NO_SO"]);
        $param["QTY"] = json_encode($param["QTY"]);
        // $log_servie = $fautoris->log_match_tms($param, $responseRequest, $byLog, $token_in); //penambahan value req & resp
        break;
}

class Matching {

    private $_sapCon;
    private $_data;
    public $_truk;
    public $_antri_po;
    public $_do_po;
    public $_truk_sbi;
    public $_pesan;
    public $_pesan_po;
    public $_pesan_po_do;
    public $_antri;
    public $_DO_MD;
    public $_DO_MD_7000;
    public $_DO_Opco;
    public $_NOLDT;
    public $_so;
    public $_SystemID;

    public function __construct() {
        require_once ("../include/sapclasses/sap.php");
        $this->_sapCon = "../include/sapclasses/logon_data.conf"; 
        //$this->_sapCon = "../include/sapclasses/logon_data_qa.conf"; 
        $this->_pesan = array();
        $this->_pesan_po = array();
        $this->_DO_MD = array();
        $this->_DO_MD_7000 = array();
        $this->_DO_Opco = array();
        $this->_do_po = array();
        $this->_SystemID= 'QASSO';
    }

    function cek_koneksi(){
        $sap = new SAPConnection();
        $sap->Connect($this->_sapCon);
         if ($sap->GetStatus() != 'SAPRFC_OK') {
            $ResponseMessage = 'Gagal koneksi ke SAP';
        } else {
            $ResponseMessage = 'Koneksi ke SAP OK';
        }
        return $ResponseMessage;
    }

    function callAPI($method, $url, $param)
    {
        $content = ($method==='POST') ? json_encode($param): ''; 
        $options = array(
                'http' => array(
                        'header'  => "Content-type: application/json\r\n",
                        'method'  => $method,
                        'content' => $content,
                )
        );        
        $context    = stream_context_create($options);
        $result     = @file_get_contents( $url, false, $context );

        $response   = json_decode($result);
        return json_encode($response);
    }

    function BeratMaterial($param) {
        try {
            $return = true;
            $sap = new SAPConnection();
            $sap->Connect($this->_sapCon);
            $sap->Open();
            $fce = $sap->NewFunction("Z_ZAPPSD_LIST_MATERIAL_SALES");
            if ($fce == false) {
                $sap->PrintStatus();
                exit;
            }
            $fce->I_VKORG = $param["ORG"];
            $fce->I_WERKS = $param["PLANT"];
            $fce->I_MATNR = $kodematerial;
            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->IT_OUT->Reset();
                while ($fce->IT_OUT->Next()) {
                    $qtyM = $fce->IT_OUT->row["NTGEW"] * 1;
                }
            }
            $fce->Close();
            $sap->Close();
            return $qtyM;
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan, $e->getMessage());
            $return = false;
        }
    }

    function getSOpen2($param) {
        try {
            $sap = new SAPConnection();
            $sap->Connect($this->_sapCon);
            $sap->Open();
            $fce = $sap->NewFunction("ZCSD_GET_DETAIL_SO");
            if ($fce == false) {
                $sap->PrintStatus();
                exit;
            }
            
            $fce->I_TRANSACTION_GROUP = "0";
            $fce->I_NO_SORTING = "X";
            
            $fdate = date('Ymd', strtotime(' -30 days')); //'20100101';
            // $fdate = date('Ymd', strtotime(' -60 days')); //'20100101'; Hanya untuk kebutuhan testing merger so
            $tdate = date('Ymd');

            $fce->LR_EDATU->row["SIGN"]     = 'I';
            $fce->LR_EDATU->row["OPTION"]   = 'BT';
            $fce->LR_EDATU->row["LOW"]      = $fdate;
            $fce->LR_EDATU->row["HIGH"]     = $tdate;
            $fce->LR_EDATU->Append($fce->LR_EDATU->row);
            
            $fce->LR_VKORG->row["SIGN"]     = 'I';
            $fce->LR_VKORG->row["OPTION"]   = 'EQ';
            $fce->LR_VKORG->row["LOW"]      = $param["ORG"];
            $fce->LR_VKORG->Append($fce->LR_VKORG->row);
            
            $fce->LR_WERKS->row["SIGN"]     = 'I';
            $fce->LR_WERKS->row["OPTION"]   = 'EQ';
            $fce->LR_WERKS->row["LOW"]      = $param["PLANT"];
            $fce->LR_WERKS->Append($fce->LR_WERKS->row);
            

            foreach ($param["NO_SO"] as $key) {
                $fce->LR_VBELN->row["SIGN"]     = 'I';
                $fce->LR_VBELN->row["OPTION"]   = 'EQ';
                $fce->LR_VBELN->row["LOW"]      = $key;
                $fce->LR_VBELN->Append($fce->LR_VBELN->row);
            }
            
            
            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_OUTPUT->Reset();
                //Display Tables
                $i = 1;
                
                while ($fce->T_OUTPUT->Next()) {

                    $this->_so[$i]["NoSO"] = $fce->T_OUTPUT->row["VBELN"];
                    $this->_so[$i]["NOSO2"] = $fce->T_OUTPUT->row["ZVBELN"];
                    $this->_so[$i]["kg_kemas"] = ($fce->T_OUTPUT->row["NTGEW"] / $fce->T_OUTPUT->row["KWMENG"]) * 1;
                    $this->_so[$i]["POSNR"] = $fce->T_OUTPUT->row["POSNR"];
                    $this->_so[$i]["BZIRK"] = $fce->T_OUTPUT->row["BZIRK"];
                    $this->_so[$i]["BZTXT"] = $fce->T_OUTPUT->row["BZTXT"];
                    $this->_so[$i]["Area"] = $fce->T_OUTPUT->row["BZIRK"] . ' ' . $fce->RETURN_DATA->row["BZTXT"];
                    $this->_so[$i]["KUNNR"] = $fce->T_OUTPUT->row["SOLD_TO_CODE"];
                    $this->_so[$i]["KUNNRTXT"] = $fce->T_OUTPUT->row["SOLDTO_NAME"];
                    $this->_so[$i]["SHIPTO"] = $fce->T_OUTPUT->row["SHIP_TO_CODE"]; //ship_to code
                    $this->_so[$i]["NMSHIPTO"] = $fce->T_OUTPUT->row["SHIPTO_NAME"]; //ship_to name
                    $this->_so[$i]["Distributor"] = ($fce->T_OUTPUT->row["SOLD_TO_CODE"] * 1) . ' ' . $fce->T_OUTPUT->row["SOLDTO_NAME"];
                    $this->_so[$i]["INCO1"] = $fce->T_OUTPUT->row["INCO1"];
                    $this->_so[$i]["STRAS2"] = $fce->T_OUTPUT->row["STRAS"];
                    $this->_so[$i]["PO_REFF"] = $fce->T_OUTPUT->row["ZVBELN"];
                    if ($fce->T_OUTPUT->row["STATUS_ITEM"] == 'C') {
                        $this->_so[$i]["StatusSO"] = 'Incompleted';
                    }else{
                         $this->_so[$i]["StatusSO"] = 'Completed';
                    }
                    if ($fce->T_OUTPUT->row["NETPR"] > 0) {
                        $this->_so[$i]["StatusHarga"] = 'Completed';
                    }else{
                         $this->_so[$i]["StatusHarga"] = 'Incompleted';
                    }
                    if ($fce->T_OUTPUT->row["ZNETPR"] > 0) {
                        $this->_so[$i]["StatusHargaREFF"] = 'Completed';
                    }else{
                         $this->_so[$i]["StatusHargaREFF"] = 'Incompleted';
                    }
                    if ($fce->T_OUTPUT->row["LIFSK"] != '') {
                        $this->_so[$i]["blocking"] = 'Yes';
                    }else{
                         $this->_so[$i]["blocking"] = 'No';
                    }
                    if ($fce->T_OUTPUT->row["ABGRU"] != '') {
                        $this->_so[$i]["Rejection"] = 'Yes';
                    }else{
                         $this->_so[$i]["Rejection"] = 'No';
                    }
                    //$this->_so[$i][""] = ;
                    
                    $this->_so[$i]["MATNR"] = $fce->T_OUTPUT->row["MATNR"];
                    $this->_so[$i]["Matrial"] = $fce->T_OUTPUT->row["ARKTX"];

                    //$this->_so[$i]["NmKapal"] = $fce->T_OUTPUT->row["BNAME"];

                    $qty_so = $fce->T_OUTPUT->row["KWMENG"];
                    $qty_release = $fce->T_OUTPUT->row["QTY_DELIVERY"];
                    $qty_sisa = $qty_so - $qty_release;
                    $QTY_TO = (($qty_sisa)*((1000*($fce->T_OUTPUT->row["NTGEW"]/1000))/$qty_so)/1000);
                    $this->_so[$i]["Qty"] = $qty_so;
                    $this->_so[$i]["QtyRelease"] = $qty_release; //dah di DO
                    $this->_so[$i]["QtySisa"] = $qty_sisa;
                    $this->_so[$i]["QtyTO"] = $QTY_TO;
                    $this->_so[$i]["Satuan"] = $fce->T_OUTPUT->row["VRKME"];
                    $this->_so[$i]["SOStatus"] = $fce->T_OUTPUT->row["STATUS_ITEM"];
                    $this->_so[$i]["LineStatus"] = $fce->T_OUTPUT->row["STATUS_ITEM"];
                    $this->_so[$i]["SOWeight"] = $fce->T_OUTPUT->row["NTGEW"]; //Net Weight of the Item
                    $this->_so[$i]["SatuanWeight"] = $fce->T_OUTPUT->row["GEWEI"]; //Weight Unit
                    $this->_so[$i]["plant"] = $fce->T_OUTPUT->row["WERKS"];
                    $this->_so[$i]["SOType"] = $fce->T_OUTPUT->row["AUART"];
                    $this->_so[$i]["KdType"] = $fce->T_OUTPUT->row["KVGR1"];
                    $this->_so[$i]["NmType"] = $fce->T_OUTPUT->row["VTEXT"];

                    $tgl = $fce->T_OUTPUT->row["EDATU"];
                    $i++;
                }
                //display error
                if (trim($fce->RETURN["TYPE"]) == 'E') {
                    array_push($this->_pesan, 'No So tidak ditemukan');
                    $return = false;
                } 
            }
            $fce->Close();
            $sap->Close();

            return $this->_so;
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan, $e->getMessage());
            $return = false;
        }
    }
    
    public function getPOSTO($param) {
        $sap = new SAPConnection();
        $sap->Connect($this->_sapCon);
        $sap->Open();
        $fce = $sap->NewFunction("Z_ZAPPSD_PO_OPEN_NW");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }
        //Param Export
         $fce->I_BUKRS = $param["ORG"];
         $fce->I_RESWK = $param["PLANT"]; //plant from
         $fce->I_EBELN = substr($param["NO_SO"],0,10);
          
         $fce->I_MODE = "X";
         $fce->I_EINDT_FR = date('Ymd', strtotime("-60 days"));
         $fce->I_EINDT_TO = date('Ymd');
        
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->T_DATA->Reset();
            //Display Tables
            $i = 0;
            while ($fce->T_DATA->Next()) {
                $this->_posto[$i]["EBELN"] = $fce->T_DATA->row["EBELN"]; //Purchasing Document Number
                $this->_posto[$i]["EBELP"] = $fce->T_DATA->row["EBELP"]; //Item Number of Purchasing Document
                //$this->_posto[$i]["BSART"] = $fce->T_DATA->row["BSART"];//Purchasing Document Type
                //$this->_posto[$i]["RESWK"] = $fce->T_DATA->row["RESWK"];//Plant asal
                $this->_posto[$i]["MATNR"] = $fce->T_DATA->row["MATNR"]; //Material Number
                //$this->_posto[$i]["EMATN"] = $fce->T_DATA->row["EMATN"];//Material Number

                $this->_posto[$i]["WERKS"] = $fce->T_DATA->row["WERKS"]; //Plant tujuan
                $this->_posto[$i]["EINDT"] = $fce->T_DATA->row["EINDT"]; //Item Delivery Date
                $this->_posto[$i]["SCH"] = substr($fce->T_DATA->row["EINDT"], 6, 2) . '-' . substr($fce->T_DATA->row["EINDT"], 4, 2) . '-' . substr($fce->T_DATA->row["EINDT"], 0, 4); //Item Delivery Date
                //$this->_posto[$i]["ROUTE"] = $fce->T_DATA->row["ROUTE"];
                $this->_posto[$i]["MAKTX"] = $fce->T_DATA->row["TXZ01"];
                $this->_posto[$i]["MENGE"] = $fce->T_DATA->row["MENGE"] * 1;
                $this->_posto[$i]["MENGE_REL"] = $fce->T_DATA->row["MENGE_REL"] * 1;
                //echo "<br>".$fce->T_DATA->row["MENGE"]. "|".$fce->T_DATA->row["MENGE_REL"];
                $this->_posto[$i]["MEINS"] = $fce->T_DATA->row["MEINS"];
                $this->_posto[$i]["NTGEW"] = $fce->T_DATA->row["NTGEW"];
                $this->_posto[$i]["GEWEI"] = $fce->T_DATA->row["GEWEI"];
                //$this->_posto[$i]["LGORT"] = $fce->T_DATA->row["LGORT"];
                $i++;
            }
            if (trim($fce->RETURN["TYPE"]) == 'E') {
                $laporan .= $fce->RETURN["TYPE"] . " : " . $fce->RETURN["MESSAGE"] . "<br>";
                array_push($this->_pesan, "'".$fce->RETURN["MESSAGE"]."'");
                return false;
            }
            else
                return $this->_posto;
        }else
            return false;
        $fce->Close();
        $sap->Close();
    }


    public function getPOSTO_MDK($param) {
        $sap = new SAPConnection();
        $sap->Connect($this->_sapCon);
        $sap->Open();
        $fce = $sap->NewFunction("Z_ZAPPSD_PO_OPEN_NW");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }
        //Param Export
         $fce->I_BUKRS = $param["ORG"];
         $fce->I_WERKS = $param["PLANT"]; //plant from
         $fce->I_EBELN = substr($param["PO_REFF"],0,10);
          
         $fce->I_MODE = "X";
         $fce->I_EINDT_FR = date('Ymd', strtotime("-60 days"));
         $fce->I_EINDT_TO = date('Ymd');
        
        $fce->Call();
        
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->T_DATA->Reset();
            //Display Tables
            $i = 0;
            while ($fce->T_DATA->Next()) {
                $this->_posto[$i]["EBELN"] = $fce->T_DATA->row["EBELN"]; //Purchasing Document Number
                $this->_posto[$i]["EBELP"] = $fce->T_DATA->row["EBELP"]; //Item Number of Purchasing Document
                //$this->_posto[$i]["BSART"] = $fce->T_DATA->row["BSART"];//Purchasing Document Type
                //$this->_posto[$i]["RESWK"] = $fce->T_DATA->row["RESWK"];//Plant asal
                $this->_posto[$i]["MATNR"] = $fce->T_DATA->row["MATNR"]; //Material Number
                //$this->_posto[$i]["EMATN"] = $fce->T_DATA->row["EMATN"];//Material Number

                $this->_posto[$i]["WERKS"] = $fce->T_DATA->row["WERKS"]; //Plant tujuan
                $this->_posto[$i]["RESWK"] = $fce->T_DATA->row["RESWK"]; //Plant asal
                $this->_posto[$i]["EINDT"] = $fce->T_DATA->row["EINDT"]; //Item Delivery Date
                $this->_posto[$i]["SCH"] = substr($fce->T_DATA->row["EINDT"], 6, 2) . '-' . substr($fce->T_DATA->row["EINDT"], 4, 2) . '-' . substr($fce->T_DATA->row["EINDT"], 0, 4); //Item Delivery Date
                //$this->_posto[$i]["ROUTE"] = $fce->T_DATA->row["ROUTE"];
                $this->_posto[$i]["MAKTX"] = $fce->T_DATA->row["TXZ01"];
                $this->_posto[$i]["MENGE"] = $fce->T_DATA->row["MENGE"] * 1;
                $this->_posto[$i]["MENGE_REL"] = $fce->T_DATA->row["MENGE_REL"] * 1;
                //echo "<br>".$fce->T_DATA->row["MENGE"]. "|".$fce->T_DATA->row["MENGE_REL"];
                $this->_posto[$i]["MEINS"] = $fce->T_DATA->row["MEINS"];
                $this->_posto[$i]["NTGEW"] = $fce->T_DATA->row["NTGEW"];
                $this->_posto[$i]["GEWEI"] = $fce->T_DATA->row["GEWEI"];
                //$this->_posto[$i]["LGORT"] = $fce->T_DATA->row["LGORT"];
                $i++;
            }
            if (trim($fce->RETURN["TYPE"]) == 'E') {
                $laporan .= $fce->RETURN["TYPE"] . " : " . $fce->RETURN["MESSAGE"] . "<br>";
                array_push($this->_pesan, "'".$fce->RETURN["MESSAGE"]."'");
                return false;
            }
            else
                return $this->_posto;
        }else
            return false;
        $fce->Close();
        $sap->Close();
    }

    public function cariTruk($param) {
        $sap = new SAPConnection();
        $sap->Connect($this->_sapCon);
        $sap->Open();
        $fce = $sap->NewFunction("Z_ZAPPSD_SEL_TRNS_HDR2");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }

        $fce->XPARAM["NO_POLISI"] = strtoupper($param['NOPOL']);
        $fce->XDATA_APP["NMORG"] = $param["ORG"];

        $fce->XPARAM["STATUS_TRANS"] = '10';
        $fce->Call();
        // $fce->XPARAM["STATUS_TRANS"] = '20';
        // $fce->Call();

        if ($fce->GetStatus() == SAPRFC_OK) {
          $fce->RETURN_DATA->Reset();
          //Display Tables
          if ($fce->RETURN_DATA->Next()) {
            $antri = $fce->RETURN_DATA->row["NO_TRANSAKSI"];
            $st = $fce->RETURN_DATA->row["STATUS_TRANS"];
            if ($st == "10") {
               $this->delete($param, $antri,"10");
            }
          }
        }
      }


    function getDataTruck($param){
        try
        {
            $return = true;
            $sap = new SAPConnection();
            $sap->Connect($this->_sapCon);
            $sap->Open();
            $fce = $sap->NewFunction("Z_ZAPPSD_SELECT_TRUK2");
            if ($fce == false ) {
               $sap->PrintStatus();
               exit;
            }
            if(strlen($param['NOPOL'])>0){
                $fce->XPARAM["NOPOLISI"]=strtoupper($param['NOPOL']);
            }else{
                array_push($this->_pesan, 'Nomor Polisi harus diisi');
                $return = false;
            }
            
            // $fce->XPARAM["STATUS"]='0';
            $fce->XDATA_APP["NMORG"]=$param["ORG"];
            // $fce->XDATA_APP["NMPLAN"]=$this->User->Plant;

            if($return){
                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $fce->RETURN_DATA->Reset();
                    //Display Tables
                    while ($fce->RETURN_DATA->Next()){
                        // Validation NO EXPEDITUR
                        if ($fce->RETURN_DATA->row["NO_EXPEDITUR"] == "0000410092"){

                            $this->_truk["ORG"]=$fce->RETURN_DATA->row["NMORG"];
                            $this->_truk["TIPE_TRUK"]=$fce->RETURN_DATA->row["VEHICLE_TYPE"];
                            $this->_truk["TIPE_NAME"]=$fce->RETURN_DATA->row["MODE_OFTRANSPORT"];
                            $this->_truk["NO_STNK"]=$fce->RETURN_DATA->row["NOSTNK"];
                            $this->_truk["ID_CARD"]=$fce->RETURN_DATA->row["NO_RFID"];
                            $this->_truk["NO_EXPEDITUR"]=$fce->RETURN_DATA->row["NO_EXPEDITUR"];
                            $this->_truk["NAMA_EXPEDITUR"]=$fce->RETURN_DATA->row["NAMA_EXPEDITUR"];
                            $this->_truk["SYSTEM_BONGKAR"]=$fce->RETURN_DATA->row["SYSTEM_BONGKAR"];
                            $this->_truk["KAPASITAS"]=$fce->RETURN_DATA->row["KAPASITAS"]*1;
                            $this->_truk["WARNA_PLAT"]=$fce->RETURN_DATA->row["WARNA_PLAT"];
                            
                            
                            $this->_truk["NOPOLISI"] = $fce->RETURN_DATA->row["NOPOLISI"];
                            $this->_truk["NMPLAN"] = $fce->RETURN_DATA->row["NMPLAN"];
                            $this->_truk["NMORG"] = $fce->RETURN_DATA->row["NMORG"];
                            $this->_truk["VEHICLE_TYPE"] = $fce->RETURN_DATA->row["VEHICLE_TYPE"];
                            $this->_truk["MODE_OFTRANSPORT"] = $fce->RETURN_DATA->row["MODE_OFTRANSPORT"];
                            $this->_truk["NOMOR_MESIN"] = $fce->RETURN_DATA->row["NOMOR_MESIN"];
                            $this->_truk["ZZRMES"] = $fce->RETURN_DATA->row["ZZRMES"];
                            $this->_truk["NO_RFID"] = $fce->RETURN_DATA->row["NO_RFID"];
                            $this->_truk["QUANTITY_ALLOWED"] = $fce->RETURN_DATA->row["QUANTITY_ALLOWED"];
                            
                        }
                    }
                        
                        if(trim($fce->RETURN["TYPE"])=='E') {
                        array_push($this->_pesan, $fce->RETURN["MESSAGE"]);
                        $return = false;
                    }
                }
            }
            $fce->Close();

            return $this->_truk;
        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            array_push($this->_pesan, $e->getMessage());
            $return = false;
        }
    }

    function Truk_SBI($param)
    {
        // $url_truck = 'https://sip.solusibangunindonesia.com/APIMD/LookupMasterTruck';
        $url_truck = 'https://dev-integrasi-api.sig.id/SIP/APIMD/LookupMasterTruck'; //dev
        //$url = 'http://************/SIP/APIMD/LookupMasterTruck'; //dev
                                              
        $data_truck = array(
            'Token' => 'AM7GV0HFGzlufM4DH',
            'SystemID' => $this->_SystemID, 
            "VehicleNumber"=> strtoupper($param['NOPOL'])
           
        );
        
        $d_truk = json_encode($data_truck);
        $options = array(
            'http' => array(
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => $d_truk,
            )
        );

        // $context = stream_context_create($options);
        // $result = file_get_contents($url, false, $context);
        // $api = new API();
        $result_truck = $this->callAPI('POST', $url_truck, $data_truck);
        // $response = $this->X('POST', $url, $data);
        $response_truck = json_decode($result_truck);
        
        //echo '<pre>',print_r($response_truck),'</pre>';
        
        if ($response_truck->Data == '' || $response_truck->Data == null) {
             $this->_truk_sbi['kdtype'] = 'G4';
             $this->_truk_sbi['nmtype'] = 'Tronton long chassis';
             $this->_truk_sbi['kdexp'] = '';
             $this->_truk_sbi['nmexp'] = '';
        }
        foreach($response_truck->Data as $key) {            
            $this->_truk_sbi['kdexp'] = $key->CARRIER_CODE;
            $this->_truk_sbi['nmexp'] = str_replace(',','.',$key->CARRIER_NAME);
            
            if($key->VEHICLE_TYPE != '' || $key->VEHICLE_TYPE != null){
                $this->_truk_sbi['kdtype'] = $key->VEHICLE_TYPE;
                $this->_truk_sbi['nmtype'] = $key->VEHICLE_NAME;
            }else{
                $this->_truk_sbi['kdtype'] = 'G4';
                $this->_truk_sbi['nmtype'] = 'Tronton long chassis';
            }                      
        }    

        return $this->_truk_sbi;    
    }

    function Antri($param){
       try
        {
                      
            $return = true;
            $sap = new SAPConnection();
            $sap->Connect($this->_sapCon);
            $sap->Open();
            $fce = $sap->NewFunction ("Z_ZAPPSD_INSERT_TRANS_HDR");
            if ($fce == false ) {
               $sap->PrintStatus();
               exit;
            }
            
            $fce->XPARAM["NMORG"]=$param["ORG"];
            $fce->XPARAM["NMPLAN"]=$param["PLANT"];
            $fce->XPARAM["STATUS_TRANS"]='10';
            $fce->XPARAM["TIPE_TRUK"]=$this->_truk["TIPE_TRUK"];
            $fce->XPARAM["EARTX"]=$this->_truk["TIPE_NAME"];
            $fce->XPARAM["NO_POLISI"]=$this->_truk["NOPOLISI"];
            $fce->XPARAM["ID_CARD"]=$this->_truk["ID_CARD"];
            $fce->XPARAM["SYSTEM_BONGKAR"]=$this->_truk["SYSTEM_BONGKAR"];
            $fce->XPARAM["KAPASITAS_MASTER"]=$this->_truk["KAPASITAS"];
            $fce->XPARAM["KAPASITAS"]=$this->_truk["KAPASITAS"];
            $fce->XPARAM["WARNA_PLAT"]=$this->_truk["WARNA_PLAT"];

            $fce->XPARAM["PTGS_ANTRIAN"]=$param["USERNAME"];
            $fce->XPARAM["TGL_ANTRI"]=date('Ymd');
            $fce->XPARAM["JAM_ANTRI"]=date('Hms');
            $fce->XPARAM["NO_EXPEDITUR"]=$this->_truk["NO_EXPEDITUR"];
            $fce->XPARAM["NAMA_EXPEDITUR"]=$this->_truk["NAMA_EXPEDITUR"];

            $fce->XPARAM["TIPE_ANTRI"]='999';
            $fce->XPARAM["JALUR_ANTRI"]='A0';

            $fce->XPARAM["LAST_UPDATED_BY"]=$param["USERNAME"];
            $fce->XPARAM["LAST_UPDATE_DATE"]=date('Ymd');
            $fce->XPARAM["LAST_UPDATE_TIME"]=date('His');

            $fce->XPARAM["ATTRIBUTE2"]=$param["SPP"];
            

            $fce->Call();

            if ($fce->GetStatus() == SAPRFC_OK) {
                if(trim($fce->RETURN["TYPE"])=='E') {
                    array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                    $return = false;
                } else {
                    $this->_antri=$fce->XDATA_RETURN;
                }
            }
            $fce->Close();
            $sap->Close();
            
        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
    }
    
    protected function entriSPPS($param) {
        $return = true;
        try {
            $connection = $this->DataAccess->ConnSAP;

            $fce = $connection->NewFunction("Z_ZAPPSD_UPD_SPPS");
            if ($fce == false) {
                $connection->PrintStatus();
                exit;
            }
            $fce->XDATA_APP["NMORG"] = $param["ORG"];
            $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];

            $fce->XDATA_KEY = $this->_antri;

            $fce->XDATA_UPD["NO_SPPS"] = $param["SPP"];
            $fce->XDATA_UPD["KAPASITAS"] = $this->_truk["KAPASITAS"];
            $fce->XDATA_UPD["NAMA_SUPIR"] = $param["DRIVER"];
            $fce->XDATA_UPD["STATUS_TRANS"] = '20';

            $fce->XDATA_UPD["LAST_UPDATED_BY"] = $param["USERNAME"];
            $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');
            $fce->XDATA_UPD["LAST_UPDATE_TIME"] = date('His');

            $fce->Call();

            if ($fce->GetStatus() == SAPRFC_OK) {
                if (trim($fce->RETURN["TYPE"]) == 'E') {
                    array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                    $return = false;
                } else {
                    $this->KetFinal->Text .= "<br/>Nomor SPPS = $fce->ZSPPS <br/>";
                    $return = true;
                    
                }
            }
            $fce->Close();

            $connection->Close();
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
        
    }


    function Matching($param,$sbi=false,$koneksi) {
        try {
            $start = Time();
            $start = microtime(true);
            $return = true;
            $dsap = new SAPConnection();
            $dsap->Connect($this->_sapCon);
            $dsap->Open();
            $plant_d = $this->data_plant($param, $koneksi); 
            $LDT = $plant_d['LDT'];
            // if ($LDT==1 && $sbi==true) {
                // $lanjut = $this->AutoLDT($param,$this->_so,"",$koneksi);
            // }

            if($param["ORG"] == '7900'){
                if ($sbi) {
                    $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO");
                } else
                 $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO_MD");
                
            }else{
                $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO");
            }
            
            if ($fce == false) {
                $dsap->PrintStatus();
                exit;
            }

            $fce->I_NMORG = $param["ORG"];
            $fce->I_NMPLAN = $param["PLANT"];
            $fce->I_NOTRANS = $this->_antri;
            if ($param["PLANT"]!="7911" && $param["PLANT"]!="7403" && $param["PLANT"]!="79E3" && $param["PLANT"]!="5401" && $param["PLANT"]!="7609" && $param["PLANT"]!="7611" && $param["PLANT"]!="7986" && $param["PLANT"]!="7401" && $param["PLANT"]!="7983" && $param["PLANT"]!="79I4") {
                $fce->I_CVY = $param["CONVEYOR"]==""?"01":$param["CONVEYOR"];
            } else $fce->I_CVY = $param["CONVEYOR"];
            
            $fce->I_USER_ID = $param["USERNAME"];
            $fce->I_TIPEANTRI = "1";
            $fce->I_LASTSTATUS = "10";
            $fce->I_MAX_LOOP1 = "90";
            $fce->I_V2 = "X";
            $fce->I_NAMA_SUPIR = $param["DRIVER"];
            $fce->I_NO_SPPS = $param["SPP"];
            $fce->I_ORGDESC = $this->getNmOrganisasi($param["ORG"]);

            $i = 1;
            
            foreach ($param["NO_SO"] as $item) {
                $index = $i-1;
                $fce->T_TABLE->row["NO_SO"] = $item;
                $fce->T_TABLE->row["POSNR"] = "10";
                $fce->T_TABLE->row["LFART"] = $this->getLFART($this->_so[$i]["SOType"]);
                $fce->T_TABLE->row["ZVSTEL"] = $param["PLANT"];
                $fce->T_TABLE->row["ZQTY_DO"] = "1";
                // $kapal = $item->NmKapal->Text;
                $s_qty = abs($param["QTY"][$index] * 1.0);

                $fce->T_TABLE->row["ZQTY_DO_ITEM"] = $s_qty;
                $fce->T_TABLE->row["ZVENDOR"] = (int) $this->_truk["NO_EXPEDITUR"];
                
                // $fce->T_TABLE->row["JML_JUMBO"] = $item->jmlJumbo_line->text;
                // $fce->T_TABLE->row["TIPE_JUMBO"] = $item->tipeJumbo_line->SelectedValue;

                $fce->T_TABLE->Append($fce->T_TABLE->row);
                $i++;
            }
            // $fce->I_NMKAPAL = $kapal;
            $fce->Call();
            
            if ($fce->RETURN["TYPE"] == 'E') {
                array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                $return = false;
            }

            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_RET_DO->Reset();
                $do_text = ''; $i = 0;
                $cek_plant = $this->data_plant($param, $koneksi);  
                $org =  $cek_plant['COM_MD'];
                $org1 = $cek_plant['PLANT_MD'];
                $opco = $cek_plant['COM_OPCO'];
                $opco1 = $cek_plant['PLANT_OPCO'];
 
                while ($fce->T_RET_DO->Next()) {
                    if ($param["ORG"] == '7900' && $i<count($param["NO_SO"])) {
                        array_push($this->_DO_MD, $fce->T_RET_DO->row["ZVBELN"]);
                        $do_si = $fce->T_RET_DO->row["ZVBELN"];
                    }else if(($param["PLANT"] == '7806' || $param["PLANT"] == '7641') && $i<count($param["NO_SO"])) {
                        array_push($this->_DO_MD_7000, $fce->T_RET_DO->row["ZVBELN"]);
                        //$do_si = $fce->T_RET_DO->row["ZVBELN"];
                    }else {
                        array_push($this->_DO_Opco, $fce->T_RET_DO->row["ZVBELN"]);
                        $do_opco = $fce->T_RET_DO->row["ZVBELN"];
                    }   
                    $do_text = 'No DO = ' . $fce->T_RET_DO->row["ZVBELN"] . ' (Plant : '.$fce->T_RET_DO->row["ZVSTEL"].')';
                    array_push($this->_pesan, $do_text);
                    $i++;
                    if ($do_si!="" && $do_opco!="") {
                        $this->insert_crud_DO($org,$org1,$opco,$opco1,$do_si,$do_opco);
                    }
                    
                }

                $fce->T_RETURN->Reset();
                while ($fce->T_RETURN->Next()) {
                    if ($fce->T_RETURN->row["TYPE"] == "E") {
                        array_push($this->_pesan, $fce->T_RETURN->row["MESSAGE"]);
                        $return = false;
                    }
                }
            }
            
            $eLoop1 = $fce->E_EXIT_LOOP1;
            $eLoop2 = $fce->E_EXIT_LOOP2;

            $t = (microtime(true)-$start);
            
            $fce->Close();

            // $fce = $dsap->NewFunction("Z_ZAPPSD_UPD_SPPS");
            // if ($fce == false) {
            //     $dsap->PrintStatus();
            //     exit;
            // }
            // $fce->XDATA_APP["NMORG"] = $param["ORG"];
            // $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];

            // $fce->XDATA_KEY = $this->_antri;
            // $fce->XDATA_UPD["NAMA_SUPIR"] = $param["DRIVER"];
            // $fce->XDATA_UPD["NO_SPPS"] = $param["SPP"];

            // $fce->XDATA_UPD["LAST_UPDATED_BY"] = $param["USERNAME"];
            // $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');
            // $fce->XDATA_UPD["LAST_UPDATE_TIME"] = date('His');

            // $fce->Call();            
            // $fce->Close();

            $dsap->Close();
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
        return $return;
    }

    function Matching_MD_SMBR($param, $smbr, $koneksi) {
        try {
            $start = Time();
            $start = microtime(true);
            $return = true;
            $dsap = new SAPConnection();
            $dsap->Connect($this->_sapCon);
            $dsap->Open();
            // $plant_d = $this->data_plant($param, $koneksi);
            // $LDT = $plant_d['LDT'];
            // if ($LDT==1 && $sbi==true) {
                // $lanjut = $this->AutoLDT($param,$this->_so,"",$koneksi);
            // }

            if($param["ORG"] == '7900'){
                if ($smbr) {
                    $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO");
                } else
                    $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO_MD");
            } else
                $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO");

            if ($fce == false) {
                $dsap->PrintStatus();
                exit;
            }

            $fce->I_NMORG = $param["ORG"];
            $fce->I_NMPLAN = $param["PLANT"];
            $fce->I_NOTRANS = $this->_antri;
            $fce->I_CVY = $param["CONVEYOR"]==""?"01":$param["CONVEYOR"];
            $fce->I_USER_ID = $param["USERNAME"];
            $fce->I_TIPEANTRI = "1";
            $fce->I_LASTSTATUS = "10";
            $fce->I_MAX_LOOP1 = "90";
            $fce->I_V2 = "X";
            $fce->I_NAMA_SUPIR = $param["DRIVER"];
            $fce->I_NO_SPPS = $param["SPP"];
            $fce->I_ORGDESC = $this->getNmOrganisasi($param["ORG"]);

            $i = 1;
            
            foreach ($param["NO_SO"] as $item) {
                $index = $i-1;
                $fce->T_TABLE->row["NO_SO"] = $item;
                $fce->T_TABLE->row["POSNR"] = "10";
                $fce->T_TABLE->row["LFART"] = $this->getLFART($this->_so[$i]["SOType"]);
                $fce->T_TABLE->row["ZVSTEL"] = $param["PLANT"];
                $fce->T_TABLE->row["ZQTY_DO"] = "1";
                // $kapal = $item->NmKapal->Text;
                $s_qty = abs($param["QTY"][$index] * 1.0);

                $fce->T_TABLE->row["ZQTY_DO_ITEM"] = $s_qty;
                $fce->T_TABLE->row["ZVENDOR"] = (int) $this->_truk["NO_EXPEDITUR"];
                
                // $fce->T_TABLE->row["JML_JUMBO"] = $item->jmlJumbo_line->text;
                // $fce->T_TABLE->row["TIPE_JUMBO"] = $item->tipeJumbo_line->SelectedValue;

                $fce->T_TABLE->Append($fce->T_TABLE->row);
                $i++;
            }
            // $fce->I_NMKAPAL = $kapal;
            $fce->Call();
            
            if ($fce->RETURN["TYPE"] == 'E') {
                array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                $return = false;
            }

            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_RET_DO->Reset();
                $do_text = ''; $i = 0;
                $cek_plant = $this->data_plant($param, $koneksi);  
                $org =  $cek_plant['COM_MD'];
                $org1 = $cek_plant['PLANT_MD'];
                $opco = $cek_plant['COM_OPCO'];
                $opco1 = $cek_plant['PLANT_OPCO'];
 
                while ($fce->T_RET_DO->Next()) {
                    // if ($param["ORG"] == '7900' && $i<count($param["NO_SO"])) {
                    //     array_push($this->_DO_MD, $fce->T_RET_DO->row["ZVBELN"]);
                    //     $do_si = $fce->T_RET_DO->row["ZVBELN"];
                    // }else if(($param["PLANT"] == '7806' || $param["PLANT"] == '7641') && $i<count($param["NO_SO"])) {
                    //     array_push($this->_DO_MD_7000, $fce->T_RET_DO->row["ZVBELN"]);
                    // }else {
                        array_push($this->_DO_MD, $fce->T_RET_DO->row["ZVBELN"]);
                        $do_opco = $fce->T_RET_DO->row["ZVBELN"];
                    // }
                    $do_text = 'No DO = ' . $fce->T_RET_DO->row["ZVBELN"] . ' (Plant : '.$fce->T_RET_DO->row["ZVSTEL"].')';
                    array_push($this->_pesan, $do_text);
                    $i++;
                    if ($do_si!="" && $do_opco!="") {
                        $this->insert_crud_DO($org,$org1,$opco,$opco1,$do_si,$do_opco);
                    }
                    
                }

                $fce->T_RETURN->Reset();
                while ($fce->T_RETURN->Next()) {
                    if ($fce->T_RETURN->row["TYPE"] == "E") {
                        array_push($this->_pesan, $fce->T_RETURN->row["MESSAGE"]);
                        $return = false;
                    }
                }
            }
            
            $eLoop1 = $fce->E_EXIT_LOOP1;
            $eLoop2 = $fce->E_EXIT_LOOP2;

            $t = (microtime(true)-$start);
            
            $fce->Close();

            // $fce = $dsap->NewFunction("Z_ZAPPSD_UPD_SPPS");
            // if ($fce == false) {
            //     $dsap->PrintStatus();
            //     exit;
            // }
            // $fce->XDATA_APP["NMORG"] = $param["ORG"];
            // $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];

            // $fce->XDATA_KEY = $this->_antri;
            // $fce->XDATA_UPD["NAMA_SUPIR"] = $param["DRIVER"];
            // $fce->XDATA_UPD["NO_SPPS"] = $param["SPP"];

            // $fce->XDATA_UPD["LAST_UPDATED_BY"] = $param["USERNAME"];
            // $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');
            // $fce->XDATA_UPD["LAST_UPDATE_TIME"] = date('His');

            // $fce->Call();            
            // $fce->Close();

            $dsap->Close();
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
        return $return;
    }

    function Matching_SMBR($param,$so,$koneksi){
        //$url = 'http://************/splitdoqas'; //qas
        $url = 'https://fios-dev.semenbaturaja.co.id/splitdoqas';
        // $url = 'http://************/splitdo2'; //dev
        $get_opco = $this->data_plant($param, $koneksi);
        $data = array(
            "vbeln"=> $so[1]['NOSO2'],
            "shipPoint"=> $get_opco['PLANT_OPCO'],
            "dlvQty"=> $param["QTY"][0],
            "conveyor"=> $param["CONVEYOR"],
            "noPol"=> $param["NOPOL"],
            "driver"=> $param["DRIVER"],
            "spp"=> $param["SPP"],
            "noSim"=> $param["NO_SIM"],
            "expeditur"=> '410092',
            "namaEpeditur"=> 'SILOG PT',
            "expeditur"=>"410092",
            "namaEpeditur"=>"SILOG PT",
            "expediturMitra"=> $param["expeditur_mitra"],
            "expediturMitraNama"=> $param["expeditur_mitra_nama"],
            "createdBy"=> $param["USERNAME"],
        );

        $d_send = json_encode($data);
        $options = array(
            'http' => array(
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => $d_send,
            )
        );

        $response = $this->callAPIcurl('POST', $url, $data);
         var_dump($response);
        if($response['success'] == 200){
            array_push($this->_DO_Opco, $response['numberDo']);
            return true;
        } else {
            array_push($this->_pesan, $response['messages']);
            return false;
        }
    }

    function callAPIcurl($method, $url, $data){
        $curl = curl_init();

        $rawData = json_encode($data);
        // var_dump($rawData);

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $rawData,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($rawData)
            ),
            // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            // CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2
        ));

        $response = curl_exec($curl);

        if ($response === false) {
            echo "cURL Error: " . curl_error($curl);
            echo "Error Code: " . curl_errno($curl);
        }

        curl_close($curl);

        // You can echo or process the response
        return json_decode($response, true);
    }

    function rollBack_MD_SMBR($param, $antri, $status) {
        $dsap = new SAPConnection();
        $dsap->Connect($this->_sapCon);
        $dsap->Open();
        $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS");
        if ($fce == false) {
          $sap->PrintStatus();
          exit;
        }
        $fce->X_NMORG = $param["ORG"];
        $fce->X_NMPLAN = $param["PLANT"];
        $fce->X_NOTRANS = $antri;
        $fce->X_STATUS = $status;
        
        $fce->X_REASON_CHG = trim("Rollback...");
        
        $fce->X_LAST_UPDATE_DATE = date('Ymd');
        $fce->X_LAST_UPDATE_TIME = date('His');
        $fce->X_LAST_UPDATED_BY = $param["USERNAME"];
        $fce->X_V2 = "X";

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
          if ($fce->RETURN["TYPE"] == "E") {
            array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
            $result = FALSE;
          } else {
            array_push($this->_pesan,"Rollback transaction successful");
            $result = true;
          }
        }
        foreach ($this->_DO_MD as $key => $value) {
            $this->delDO($value);
        }
        foreach ($this->_DO_Opco as $key => $value) {
            $this->delDO($value);
        }
    }

    function Matching_posto($param,$sbi=false,$koneksi) {
        try {
            $start = Time();
            $start = microtime(true);
            $return = true;
            $dsap = new SAPConnection();
            $dsap->Connect($this->_sapCon);
            $dsap->Open();
            $plant_d = $this->data_plant($param, $koneksi); 
        
            $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO_PO");
            
            if ($fce == false) {
                $dsap->PrintStatus();
                exit;
            }
            
            $fce->I_NMORG = $param["ORG"];
            $fce->I_NMPLAN = $param["PLANT"];
            $fce->I_NOTRANS = $this->_antri;
            $fce->I_USER_ID = $param["USERNAME"];
            $fce->I_CVY = $param["CONVEYOR"];
            $fce->I_NAMA_SUPIR = $param["DRIVER"];
            $fce->I_ORGDESC = $this->getNmOrganisasi($param["ORG"]);
            $fce->I_TIPEANTRI = "1";
            $fce->I_LASTSTATUS = '20';

            foreach ($param["NO_SO"] as $item) {
                
                $fce->T_TABLE->row["VKORG"] = $param["ORG"];;
                $fce->T_TABLE->row["WERKS"] = $param["PLANT"];
                $fce->T_TABLE->row["REF_DOC"] = $item;
                $fce->T_TABLE->row["REF_ITEM"] = "000010";
                $fce->T_TABLE->row["LFART"] = 'ZNL';
                $fce->T_TABLE->row["DLV_QTY"] = abs($param["QTY"][0] * 1.0);
                $fce->T_TABLE->row["SALES_UNIT"] = "ZAK";

                $fce->T_TABLE->Append($fce->T_TABLE->row);
            }

            
            $fce->Call();
            
            if ($fce->RETURN["TYPE"] == 'E') {
                array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                $return = false;
            }

            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_RET_DO->Reset();
                $do_text = ''; $i = 0;
                $cek_plant = $this->data_plant($param, $koneksi);  
                
                while ($fce->T_RET_DO->Next()) {
                    
                    $do_text = 'No DO = ' . $fce->T_RET_DO->row["DELIV_NUMB"];
                    array_push($this->_pesan, $do_text);
                    array_push($this->_DO_Opco, $fce->T_RET_DO->row["DELIV_NUMB"]);
                    
                    $i++;
                   
                    
                }

                $fce->T_RETURN->Reset();
                while ($fce->T_RETURN->Next()) {
                    if ($fce->T_RETURN->row["TYPE"] == "E") {
                        // array_push($this->_pesan, $fce->T_RETURN->row["MESSAGE"]);
                        $return = false;
                    }
                }
            }
            
           // $eLoop1 = $fce->E_EXIT_LOOP1;
//            $eLoop2 = $fce->E_EXIT_LOOP2;
//
//            $t = (microtime(true)-$start);
            
            $fce->Close();

            $dsap->Close();
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
        return $return;
    }


    function Matching_soposto($param,$sbi=false,$koneksi) {
        try {
            
            $start = Time();
            $start = microtime(true);
            $return = true;
            $dsap = new SAPConnection();
            $dsap->Connect($this->_sapCon);
            $dsap->Open();
            $plant_d = $this->data_plant($param, $koneksi); 

            //===================START proses insert truck==================// 
            $fce = &$dsap->NewFunction("Z_ZAPPSD_INSERT_TRANS_HDR");
            
            if ($fce == false) {
                $dsap->PrintStatus();
                exit;
            }
            $fce->XPARAM["NMORG"]=$param['ORG'];
            $fce->XPARAM["NMPLAN"]=$param['plant_asal']; //plant from
            $fce->XPARAM["STATUS_TRANS"]='10';
            $fce->XPARAM["TIPE_TRUK"]=$param['TIPE_TRUK'];
            $fce->XPARAM["EARTX"]=$param['TIPE_NAME'];
            $fce->XPARAM["NO_POLISI"]=$param['NOPOL'];
            $fce->XPARAM["ID_CARD"]= $param['ID_CARD'];
            $fce->XPARAM["SYSTEM_BONGKAR"]=$param['SYSTEM_BONGKAR'];// gak ada
            $fce->XPARAM["KAPASITAS_MASTER"]=$param['KAPASITAS']; // gak ada
            $fce->XPARAM["KAPASITAS"]=$param['KAPASITAS']; // gak ada
            $fce->XPARAM["WARNA_PLAT"]=$param['WARNA_PLAT']; // gak ada
            $fce->XPARAM["PTGS_ANTRIAN"]=$param["USERNAME"]; 
            $fce->XPARAM["TGL_ANTRI"]=date('Ymd');
            $fce->XPARAM["JAM_ANTRI"]=date('His');
            $fce->XPARAM["NO_EXPEDITUR"]=$param["NO_EXPEDITUR"]; // gak ada
            $fce->XPARAM["NAMA_EXPEDITUR"]=$param["NAMA_EXPEDITUR"]; // gak ada
            $fce->XPARAM["TIPE_ANTRI"]='999';
            $fce->XPARAM["JALUR_ANTRI"]='A0';
            $fce->XPARAM["LAST_UPDATED_BY"]=$param["USERNAME"]; 
            $fce->XPARAM["LAST_UPDATE_DATE"]=date('Ymd');
            $fce->XPARAM["LAST_UPDATE_TIME"]=date('His');
            $fce->XPASS = 'X';
            $fce->XPARAM["ATTRIBUTE2"]=$param["SPP"];

            $fce->Call();

            if ($fce->GetStatus() == SAPRFC_OK) {
                if (trim($fce->RETURN["TYPE"]) == 'E') {
                    array_push($this->_pesan, "'".$fce->RETURN["MESSAGE"]."'");
                } else {
                    $no_antri_po = $fce->XDATA_RETURN;
                    $this->_antri_po = $fce->XDATA_RETURN;
                }
            }

            $fce->Close();
            $dsap->Close();
            //===================END proses insert truck==================// 
           
             //===================START proses insert SPPS==================// 
            
       
            if($param['DRIVER'] != ''){
                $dsap = new SAPConnection();
                $dsap->Connect($this->_sapCon);
                $dsap->Open();
                $fce = &$dsap->NewFunction("Z_ZAPPSD_UPD_SPPS");
            
                if ($fce == false) {
                    $dsap->PrintStatus();
                    exit;
                }
                
                $fce->XDATA_APP["NMORG"]=$param['ORG'];
                $fce->XDATA_APP["NMPLAN"]=$param['plant_asal'];   
                $fce->XDATA_KEY =  $no_antri_po;
                $fce->XDATA_UPD["KAPASITAS"]=abs($param["QTY"][0] * 1.0);
                $fce->XDATA_UPD["NAMA_SUPIR"]=$param['DRIVER']; 
                $fce->XDATA_UPD["STATUS_TRANS"]='20';
                $fce->XDATA_UPD["LAST_UPDATED_BY"]=$param["USERNAME"]; 
                $fce->XDATA_UPD["LAST_UPDATE_DATE"]=date('Ymd');
                $fce->XDATA_UPD["LAST_UPDATE_TIME"]=date('His');

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    if(trim($fce->RETURN["TYPE"])=='E') {
                        array_push($this->_pesan, "'".$fce->RETURN["MESSAGE"]."'");
                        $return = false;
                    }
                }
                $fce->Close();
           
            }
            $dsap->Close();
            //===================END proses insert SPPS==================// 



            //===================START proses ENTRI PO==================// 
            $dsap = new SAPConnection();
            $dsap->Connect($this->_sapCon);
            $dsap->Open();
            $fce = &$dsap->NewFunction("Z_ZAPPSD_SPLIT_MASSDO_PO");
            
            if ($fce == false) {
                $dsap->PrintStatus();
                exit;
            }
            
            
            $fce->I_NMORG = $param["ORG"];
            $fce->I_NMPLAN = $param["plant_asal"];
            $fce->I_NOTRANS =  $no_antri_po;
            $fce->I_USER_ID = $param["USERNAME"];
            $fce->I_CVY = $param["CONVEYOR"];
            $fce->I_NAMA_SUPIR = $param["DRIVER"];
            $fce->I_ORGDESC = $this->getNmOrganisasi($param["ORG"]);
            $fce->I_TIPEANTRI = "1";
            $fce->I_LASTSTATUS = '20';
            $c = 0;
            foreach ($param["NO_SO"] as $item) {
                
                $fce->T_TABLE->row["VKORG"] = $param["ORG"];;
                $fce->T_TABLE->row["WERKS"] = $param["PLANT"];
                $fce->T_TABLE->row["REF_DOC"] = $param["PO_REFF"];
                $fce->T_TABLE->row["REF_ITEM"] = "000010";
                $fce->T_TABLE->row["LFART"] = 'ZNL';
                $fce->T_TABLE->row["DLV_QTY"] = abs($param["QTY"][$c] * 1.0);
                $fce->T_TABLE->row["SALES_UNIT"] = "ZAK";

                $fce->T_TABLE->Append($fce->T_TABLE->row);
                $c++;
            }

            
            $fce->Call();
            if ($fce->RETURN["TYPE"] == 'E') {
                array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                $return = false;
            }
            

            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_RET_DO->Reset();
                $do_text = ''; $i = 0;
               
                
                while ($fce->T_RET_DO->Next()) {

                    $do_text = 'No DO PO= ' . $fce->T_RET_DO->row["DELIV_NUMB"] . ')';
                    $this->_pesan_po_do = $do_text;
                    
                    $this->_do_po[] = $fce->T_RET_DO->row["DELIV_NUMB"];
                    array_push($this->_pesan, $do_text);
                    array_push($this->_DO_Opco, $fce->T_RET_DO->row["DELIV_NUMB"]);
                    
                    $i++;
                   
                    
                }

                $fce->T_RETURN->Reset();
                while ($fce->T_RETURN->Next()) {
                    if ($fce->T_RETURN->row["TYPE"] == "E") {
                        array_push($this->_pesan_po, "'".$fce->RETURN["MESSAGE"]."'");
                        $return = false;
                    }
                }
            }
            
           // $eLoop1 = $fce->E_EXIT_LOOP1;
//            $eLoop2 = $fce->E_EXIT_LOOP2;
//
//            $t = (microtime(true)-$start);
            
            $fce->Close();

            $dsap->Close();
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
        return $return;
    }

    function MatchSBI($param,$so,$truk_sbi,$koneksi)
    {
        $return = true;
        $cek_plant = $this->data_plant($param, $koneksi);  

        $state = '2';
        $org = ($state === '1') ? $cek_plant['COM_MD'] : $cek_plant['COM_OPCO'];
        $plant = ($state === '1') ? $cek_plant['PLANT_MD'] : $cek_plant['PLANT_OPCO'];
        $LDT = $cek_plant['LDT'];
        
        // PENAMBAHAN KONDISI 3 LAYER NON LDT
        if ($param["PLANT"]=='7938' || $param["PLANT"]=='79D3'){
        
        $plant = $cek_plant['PLANT_SBI_LAYER_2'];
        
        } 
        // END PENAMBAHAN KONDISI 3 LAYER NON LDT
        
        //API SPLIT DO
        if (count($param["NO_SO"])>1) {
            $ada_qty = true;
            foreach ($param["QTY"] as $key) {
                

            }
        }
        
        if ($LDT==0) {
        //    $url = 'https://sip.solusibangunindonesia.com/APIMD/UploadManualGoodsIssue';
           $url = 'https://dev-integrasi-api.sig.id/SIP/APIMD/UploadManualGoodsIssue'; //dev
           //$url = 'http://************/SIP/APIMD/UploadManualGoodsIssue'; //dev
            
           $format_tanggal = date('Y-m-d');
           // echo $format_tanggal ;
           $index = 1;
            $lineitem = 10;
            //$sess_do_si = '';

            foreach ($param["NO_SO"] as $item) {
                $ind = $index-1;
                if ($so[$index]["kg_kemas"] == '40' || $so[$index]["kg_kemas"] == '50') {
                    $qty = $so[$index]["kg_kemas"]*$param["QTY"][$ind]/1000;
                }elseif ($so[$index]["kg_kemas"] == '1000') {
                    $qty = $param["QTY"][$ind];
                }else {
                    $qty = $param["QTY"][$ind];
                }
                $param["CONVEYOR_SBI"] = $param["CONVEYOR_SBI"]==""?"01":$param["CONVEYOR_SBI"];
                if($plant=='I231'){               //kondisi tes khusus untuk SBI rembang dyx
                  $param["CONVEYOR_SBI"] = "31";
                }
                
                // PENAMBAHAN KONDISI 3 LAYER NON LDT
                if ($param["PLANT"]=='7938' || $param["PLANT"]=='79D3'){
                    $sox = $param["NO_SO_SBI"];
                } else{
                    $sox = $so[$index]['NOSO2'];
                }
                if($param["PLANT"]=='7983' || $param["PLANT"]=='7990'){
                    $truk_sbi['kdexp']="5108308";
                }
                // END PENAMBAHAN KONDISI 3 LAYER NON LDT
                
                $data = array(
                    'Token' => 'AM7GV0HFGzlufM4DH',
                    'SystemID' => $this->_SystemID, 
                    "Data"=> array(
                        "ShippingPoint"=> $plant,
                        "LoadingPoint"=> $param["CONVEYOR_SBI"],
                        "TransporterType"=> "0001",//$this->kdtype->Text,
                        "StorLocation"=>$param["SLoc_SBI"],
                        "TruckID"=> $param["NOPOL"],
                        "DriverName"=> $param["DRIVER"],
                        "ShippingType"=> $truk_sbi['kdtype'],
                        "SalesOrderNumber"=> $sox,//$so[$index]['NOSO2'],//PENAMBAHAN KONDISI 3 LAYER NON LDT
                        "SalesOrderNumberDate"=> $format_tanggal,
                        "ItemNumber"=> "000010",
                        "DeliveryQuantity"=> $qty,
                        "SalesUnit"=> "TO",
                        "SalesUnitISO"=>"TO",
                        "GoodsIssueDate"=> $format_tanggal,
                        "CarrierCode"=> $truk_sbi['kdexp']
                    )
                );

                // echo '<pre>',print_r($data),'</pre>';
                $d_send = json_encode($data);
                $options = array(
                    'http' => array(
                        'header' => "Content-type: application/json\r\n",
                        'method' => 'POST',
                        'content' => $d_send,
                    )
                );

                // $context = stream_context_create($options);
                // $result = file_get_contents($url, false, $context);
                // $api = new API();
                $result = $this->callAPI('POST', $url, $data);
                // $response = $this->X('POST', $url, $data);
                $response = json_decode($result);
                $status = $response->Status;
                //$message .= "<br/>API SBI:";
                $message = $response->Message;
                $msg = $response->MessageSAPData;
                $ret_data = $response->Data;
                $status_message = '';
                foreach ($msg as $key => $value) {
                    
                    $status_message .= $value->ZVBELN;
                    if (strlen($value->ZVBELN)>5) {
                        $_DO_Opco = $value->ZVBELN;
                    }
                    if ($value->TYPE == 'E') {
                        array_push($this->_pesan,$value->MESSAGE2);
                        $return = false;
                    }
                     
                }

                // print_r($msg);
                //jika no do sbi tidak kosong
                
                array_push($this->_DO_Opco, $_DO_Opco);

               if ($message == 'SUCCESS') {
                    $org =  $cek_plant['COM_MD'];
                    $org1 = $cek_plant['PLANT_MD'];
                    $opco = $cek_plant['COM_OPCO'];
                    $opco1 = $cek_plant['PLANT_OPCO'];
                    $do_si = $this->_DO_MD[$ind];
                    $do_opco = $_DO_Opco;
                    $this->insert_crud_DO($org,$org1,$opco,$opco1,$do_si,$do_opco);
                    $this->entriSPPSsbi($param,$do_opco,$lineitem);            
               }            
                $lineitem = $lineitem+10;
               $index++;

               $user = $param["USERNAME"];
                $field_names = array(
                    'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
                );
                $field_data = array(
                    "$d_send", "$result", "$user", "", "$message", json_encode($msg), "SYSDATE"
                );
                $tablename = "ZMD_LOG_SBI";
                $sukses = $this->insert($koneksi, $field_names, $field_data, $tablename);

           }
        } else {
            $return = $this->AutoLDT($param,$so,$plant,$koneksi);
            //echo 'matching SBI';
             //echo $return;
        }
        return $return;

    }

    function getLFART($SOType){
        $lfart = array(array('so'=>'ZEX','do'=>'ZLFE'),
                       array('so'=>'ZFC','do'=>'ZLC'),
                       array('so'=>'ZOR','do'=>'ZLF'),
                       array('so'=>'ZPR','do'=>'ZLFP'));
        $ada = false;
        for($zxz=0;$zxz<count($lfart);$zxz++){
            if($lfart[$zxz]['so']==$SOType){
                $DOType=$lfart[$zxz]['do'];
                $ada = true;
                return $DOType;
            }
        }
        if($ada)return $DOType;
        else return false;
    }

    function insert_crud_DO($org,$org1,$opco,$opco1,$do_si,$do_opco){
       
        //$result = false;
        try{

            $sap = new SAPConnection();
            $sap->Connect($this->_sapCon);
            $sap->Open();
            $fce = $sap->NewFunction ("ZSD_CRUD_MAP_DO_MD");
            if ($fce == false ) {
               $sap->PrintStatus();
               exit;
            }
            $fce->X_FLAG= "I";
            $fce->X_PARAM->row["MANDT"] = '';
            $fce->X_PARAM->row["VKORG1"] = $org;
            $fce->X_PARAM->row["WERKS1"] = $org1;
            $fce->X_PARAM->row["VBELN1"] = $do_si; //do si
            $fce->X_PARAM->row["VKORG2"] = $opco;
            $fce->X_PARAM->row["WERKS2"] = $opco1;
            $fce->X_PARAM->row["VBELN2"] = $do_opco; // do sbi
            $fce->X_PARAM->Append($fce->X_PARAM->row);

            $fce->Call(); 
             //echo '<pre>',print_r($msg),'</pre>';
            if ($fce->GetStatus() == SAPRFC_OK) {
               
                $fce->T_RETURN->Reset();
                $ada_error = false;
                
                if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_RETURN->Reset();
                //Display Tables
                $i = 1;
                
                while ($fce->T_RETURN->Next()) {
                    if ($fce->T_RETURN->row["TYPE"] == "E") {
                        $ada_error = true;
                    }
                  }
                }
            }
            
            $fce->Close();
            $sap->Close();
           
            
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $return = false;
        }
     }

     function entriSPPSsbi($param,$do_sbi,$lineitem) {
        $result = false;
        try {
            $sap = new SAPConnection();
            $sap->Connect($this->_sapCon);
            $sap->Open();
            $fce = $sap->NewFunction ("Z_ZAPPSD_UPD_SPPS");
            if ($fce == false ) {
               $sap->PrintStatus();
               exit;
            }
            $fce->XDATA_APP["NMORG"] = $param["ORG"];
            $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];

            $fce->XDATA_KEY = $this->_antri;
            $fce->XPOSNR = $lineitem;
            $fce->XDATA_UPD["NAMA_SUPIR"] = $param["DRIVER"];
            $fce->XDATA_UPD["NO_SPPS"] = $do_sbi;

            $fce->XDATA_UPD["LAST_UPDATED_BY"] = $param["USERNAME"];
            $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');
            $fce->XDATA_UPD["LAST_UPDATE_TIME"] = date('His');

            $fce->Call();

            if ($fce->GetStatus() == SAPRFC_OK) {
                if (trim($fce->RETURN["TYPE"]) == 'E') {
                    // array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
                    // $result = FALSE;
                } else {
                    // array_push($this->_pesan,"Nomor SPPS = $fce->ZSPPS");
                    // $result = TRUE;
                }
            }
            $fce->Close();

            $sap->Close();
            $result = TRUE;
        } catch (Exception $e) { // an exception is raised if a query fails will be raised
            array_push($this->_pesan,$e->getMessage());
            $result = FALSE;
        }
        return $result;
    }

    function data_plant($param, $koneksi){
        $conn=$koneksi;
        $strmat = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$param["PLANT"]}' AND COM_MD = '{$param["ORG"]}' AND  DEL=0";
        $query=@oci_parse($conn, $strmat);
        @oci_execute($query);
        $cek_plant = oci_fetch_array($query, OCI_ASSOC);
        return $cek_plant;
    }


    function AutoLDT($param,$so,$plant_ldt,$koneksi) {
        $return = true;      
        $plant_d = $this->data_plant($param, $koneksi); 
        if ($plant_ldt=="") { 
            $plant_ldt = $plant_d['PLANT_SBI_LAYER_2'];
        } 
        $param["NO_SO_SBI"] = explode(",", $param["NO_SO_SBI"]);
        $jumlah_so = count($param["NO_SO"]);

        if ($jumlah_so > 1) { // multiple
            // $url = 'https://dev-sip.solusibangunindonesia.com/APIMD/CreateLDTVer2'; // dev sbi
            // $url = 'https://dev-integrasi-api.sig.id/apimd/createldtver2'; //dev   
            $url = 'https://integrasi-api.sig.id/apimd/createldtver2/dev'; //devprod                     
        } else { // single
            // $url = 'https://integrasi-api.sig.id/SIP/APIMD/CreateLDT'; // prod
            // $url = 'https://dev-integrasi-api.sig.id/SIP/APIMD/CreateLDT'; //dev
            $url = 'https://integrasi-api.sig.id/apimd/createldt/dev'; //devprod
        } 
        // $url = 'https://sip.solusibangunindonesia.com/APIMD/CreateLDT';
        // $url = 'https://dev-integrasi-api.sig.id/SIP/APIMD/CreateLDTVer2'; // new sincro
        // $url = 'https://dev-integrasi-api.sig.id/sip/ampimd/createldtver2'; // new sincro

        $index = 1;
        $index_qty = 1;

        $data = array(
            'Token'     => '974CB593-6710-4BF7-BF53-A6DADE1F55C9',
            'SystemID'  => $this->_SystemID,
            'Data'      => array()
        );

        // echo "<pre>";
        // print_r($param);
        // echo "</pre>";
        // echo "<pre>";
        // print_r($so);
        // echo "</pre>";
        // echo "<br>";

        if ($jumlah_so > 1) { // multiple
            foreach ($param["NO_SO"] as $index => $item) {               
                $ind = $index;
                $ind_qty = $index_qty-1;
                // echo "index_qty : ".$index_qty."<br>";
                // echo "ind_qty : ".$ind_qty."<br>";
                // echo "so_index_qty : ".$so[$index_qty]["kg_kemas"]."<br>";
                // echo "so_ind_qty : ".$so[$ind_qty]["kg_kemas"]."<br>";
                // echo "so_ind : ".$so[$ind]["kg_kemas"]."<br>";
                // echo "param_index_qty : ".$param["QTY"][$ind_qty]."<br>";
                // echo "param_ind_qty : ".$param["QTY"][$ind_qty]."<br>";
                // echo "param_ind : ".$param["QTY"][$ind]."<br>";

                // if ($so[$index_qty]["kg_kemas"] == '40' || $so[$index_qty]["kg_kemas"] == '50') {
                //     $qty = $so[$index_qty]["kg_kemas"] * $param["QTY"][$ind_qty] / 1000;
                // } elseif ($so[$index_qty]["kg_kemas"] == '1000') {
                //     $qty = $param["QTY"][$ind_qty];
                // } else {
                //     $qty = $param["QTY"][$ind_qty];
                // }
                
                // echo "index : ".$ind."<br>";
                if ($so[$index_qty]["kg_kemas"] == '40' || $so[$index_qty]["kg_kemas"] == '50') {
                    $qty = $so[$index_qty]["kg_kemas"] * $param["QTY"][$ind] / 1000;
                } elseif ($so[$index_qty]["kg_kemas"] == '1000') {
                    $qty = $param["QTY"][$ind];
                } else {
                    $qty = $param["QTY"][$ind];
                }
                
                $no_so_sbi = isset($param["NO_SO_SBI"][$ind]) ? $param["NO_SO_SBI"][$ind] : '';

                // OLD
                // if ($param["PLANT"] == '7806' || $param["PLANT"] == '7641'){
                //      $do_md = implode(",", $this->_DO_MD_7000);
                // }else{
                //      $do_md = implode(",", $this->_DO_MD);
                // }

                // NEW do number
                $do_md_array = ($param["PLANT"] == '7806' || $param["PLANT"] == '7641') 
                    ? $this->_DO_MD_7000 
                    : $this->_DO_MD;
                $do_md = isset($do_md_array[$index]) ? $do_md_array[$index] : '';
                
                //  $do_md = implode(",", $this->_DO_MD);
                //  echo "<pre>$do_md</pre>";
                $skrg = date("Y-m-d");//date("Y-m-d", strtotime('+ 1 days'));
                $skrg_t = date("Y-m-d H:i:s");

                $data["Data"][] = array(
                    'NoAntri'=> $this->_antri,
                    'DONumber'=> $do_md,
                    'SalesOrderNumberMD'=> $item,
                    'SalesOrderNumberSBI'=> $no_so_sbi,
                    'DoQty'=> $qty,
                    'DriverName'=> $param["DRIVER"],
                    'DriverLicense'=> $param["NO_SIM"],
                    'VehicleNumber'=> $param["NOPOL"],
                    'PlantMD'=> $param["PLANT"],
                    'PlantSBI'=> $plant_ldt,
                    'DeliveryDate'=> $skrg,
                    'PlanCheckInPlant'=> $skrg_t
                ); 
            } // end foreach     
            
            // echo "<pre>";
            // print_r($data);
            // echo "</pre>";
            // echo "<br>";  
        
        } else { //single
            $ind = $index-1;
             if ($so[$index]["kg_kemas"] == '40' || $so[$index]["kg_kemas"] == '50') {
                 $qty = $so[$index]["kg_kemas"]*$param["QTY"][$ind]/1000;
             }elseif ($so[$index]["kg_kemas"] == '1000') {
                 $qty = $param["QTY"][$ind];
             }else {
                 $qty = $param["QTY"][$ind];
             }

            if ($param["PLANT"] == '7806' || $param["PLANT"] == '7641'){
                 $do_md = implode(",", $this->_DO_MD_7000);
            }else{
                 $do_md = implode(",", $this->_DO_MD);
            }
            $skrg = date("Y-m-d");
            $skrg_t = date("Y-m-d H:i:s");

            $data["Data"] = array(
                'NoAntri'=> $this->_antri,
                'DONumber'=> $do_md,
                'SalesOrderNumberMD'=> $param["NO_SO"][0],
                'SalesOrderNumberSBI'=> $param["NO_SO_SBI"][0],
                'DoQty'=> $qty,
                'DriverName'=> $param["DRIVER"],
                'DriverLicense'=> $param["NO_SIM"],
                'VehicleNumber'=> $param["NOPOL"],
                'PlantMD'=> $param["PLANT"],
                'PlantSBI'=> $plant_ldt,
                'DeliveryDate'=> $skrg,
                'PlanCheckInPlant'=> $skrg_t
            );
        }

         $d_send = json_encode($data);
            $options = array(
                'http' => array(
                    'header' => "Content-type: application/json\r\n",
                    'method' => 'POST',
                    'content' => $d_send,
            )
         ); 

        // echo "<pre>$d_send</pre>";
        // exit;
        // return $d_send;
        $result = $this->callAPI('POST', $url, $data);
        // echo "111<pre>$result</pre>";
        $response = json_decode($result);
        // echo "221<pre>$response</pre>";
        $status = $response->Status;
        // echo "Status: $status<br>";
        $message = $response->Message;
        // echo "Message: $message<br>";
        $msg = $response->MessageDetail;
        // echo "Detail: $msg<br>";
        // echo "lanjut";
        $ret_data = $response->Data;
        array_push($this->_pesan,$msg);
        $lineitem=10;
        $org =  $plant_d['COM_MD'];
        $org1 = $plant_d['PLANT_MD'];
        $opco = $plant_d['COM_OPCO'];
        $opco1 = $plant_d['PLANT_OPCO'];
        $do_si = $do_md;

        $no_ldt = $ret_data->LDTNumber;
        $do_opco = $no_ldt;
        $this->_NOLDT=$no_ldt;
        $this->insert_crud_DO($org,$org1,$opco,$opco1,$do_si,$do_opco);
        $this->entriSPPSsbi($param,$no_ldt,$lineitem);

        $user = $param["USERNAME"];
        $field_names = array(
            'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
        );
        $res = substr($result,0,1900);
        $field_data = array(
            "$d_send", substr($result,0,1900), "$user", "", substr($message,0,50), substr($msg,0,900), "SYSDATE"
        );
        $tablename = "ZMD_LOG_SBI";
        $sukses = $this->insert($koneksi, $field_names, $field_data, $tablename);
        
         //print_r($result);

        if ($status==1) {
            $return = true;
            //echo 'Benar';
        } else {
            array_push($this->_pesan,"Gagal create LDT");
            $return = false;
        //echo 'Salah';
        }
        
        return $return;
    }

    function cancel_ldt($param, $ldt, $koneksi) {
        // $url = 'https://sip.solusibangunindonesia.com/APIMD/CancelLDT';
        // $url = 'https://integrasi-api.sig.id/SIP/APIMD/CancelLDT';
        $url = 'https://dev-integrasi-api.sig.id/SIP/APIMD/CancelLDT'; //dev
        //$url = 'http://************/SIP/APIMD/CancelLDT'; //dev
        

         $data = array('Token'=> '974CB593-6710-4BF7-BF53-A6DADE1F55C9',
                  'SystemID' => $this->_SystemID, 
                  'Data'=> array (
                          'LDTNumber'=> $ldt)
                  );
         $d_send = json_encode($data);
         $options = array(
            'http' => array(
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => $d_send,
            )
         );

        $result = $this->callAPI('POST', $url, $data);
        $response = json_decode($result);
        $status = $response->Status;
        $message = $response->Message;
        $msg = $response->MessageDetail;
        $ret_data = $response->Data;
        array_push($this->_pesan,$message);

         $user = $param["USERNAME"];
        $field_names = array(
            'SEND_PARAM', 'RETURN_PARAM', 'USER_SAVE', 'NO_PP', 'PESAN', 'PESAN_DETAIL', 'TGL'
        );
        $field_data = array(
            "$d_send", "$result", "$user", "", "$message", "$msg", "SYSDATE"
        );
        $tablename = "ZMD_LOG_SBI";
        $sukses = $this->insert($koneksi, $field_names, $field_data, $tablename);

    }

    function rollBack($param, $antri,$status,$sbi=false,$noLDT="",$koneksi="") {
        $dsap = new SAPConnection();
        $dsap->Connect($this->_sapCon);
        $dsap->Open();
        if(($param["ORG"] == '7900') && ($param["MD_Only"]!='X' && $status!="10" && $sbi==false) ){
            $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS_MD");
        } else {
            $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS");
        }
        if ($fce == false) {
          $sap->PrintStatus();
          exit;
        }
        $fce->X_NMORG = $param["ORG"];
        $fce->X_NMPLAN = $param["PLANT"];
        $fce->X_NOTRANS = $antri;
        $fce->X_STATUS = $status;
        
        $fce->X_REASON_CHG = trim("Rollback...");
        
        $fce->X_LAST_UPDATE_DATE = date('Ymd');
        $fce->X_LAST_UPDATE_TIME = date('His');
        $fce->X_LAST_UPDATED_BY = $param["USERNAME"];
        $fce->X_V2 = "X";

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
          if ($fce->RETURN["TYPE"] == "E") {
            array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
            $result = FALSE;
          } else {
            array_push($this->_pesan,"Rollback transaction successful");
            $result = true;
          }
        }
        if ($noLDT!="") {
            $this->cancel_ldt($param, $noLDT, $koneksi);
        }


        foreach ($this->_DO_MD as $key => $value) {
            $this->delDO($value);
        }

        foreach ($this->_DO_Opco as $key => $value) {
            $this->delDO($value);
        }
        

    }

    function rollBackSOPO($param) {
        $dsap = new SAPConnection();
        $dsap->Connect($this->_sapCon);
        $dsap->Open();

        //===================START proses insert truck==================// 
        $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS");
        
        if ($fce == false) {
            $dsap->PrintStatus();
            exit;
        }
        $fce->X_NMORG = $param['ORG'];
        $fce->X_NMPLAN = $param['PLANT'];
        $fce->X_NOTRANS = $param['antri_so'];
        $fce->X_STATUS = '40';
        
        $fce->X_REASON_CHG = 'rollback data';
        
        $fce->X_LAST_UPDATE_DATE = date('Ymd');
        $fce->X_LAST_UPDATE_TIME = date('His');
        $fce->X_LAST_UPDATED_BY = $param['USERNAME'];
        $fce->X_V2 = "X";
    
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
          if ($fce->RETURN["TYPE"] == "E") {
           $message = $fce->RETURN["MESSAGE"];
          } else {
           $message = "Data di Rollback";
          }
        }
        // =====================================================
        // delete transaction PO
        $dsap = new SAPConnection();
        $dsap->Connect($this->_sapCon);
        $dsap->Open();

        //===================START proses insert truck==================// 
        $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS");
        
        if ($fce == false) {
            $dsap->PrintStatus();
            exit;
        }
        $fce->X_NMORG = $param['ORG'];
        $fce->X_NMPLAN = $param['plant_asal'];
        $fce->X_NOTRANS =$param['antri_po'];
        $fce->X_STATUS = '20';
        
        $fce->X_REASON_CHG = 'rollback data';
        
        $fce->X_LAST_UPDATE_DATE = date('Ymd');
        $fce->X_LAST_UPDATE_TIME = date('His');
        $fce->X_LAST_UPDATED_BY =  $param['USERNAME'];
        $fce->X_V2 = "X";
    
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
          if ($fce->RETURN["TYPE"] == "E") {
            
            $message = $fce->RETURN["MESSAGE"];
          } else {
            
            $message = $fce->RETURN["MESSAGE"];
          }
        }
        return $message;
        
        $fce->Close();
        $dsap->Close();

    }


    function delete($param, $antri,$status) {
        $dsap = new SAPConnection();
        $dsap->Connect($this->_sapCon);
        $dsap->Open();
        // if($param["ORG"] == '7900'){
        //     $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS_MD");
        // } else {
            $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_TRANS");
        // }
        if ($fce == false) {
          $sap->PrintStatus();
          exit;
        }
        $fce->X_NMORG = $param["ORG"];
        $fce->X_NMPLAN = $param["PLANT"];
        $fce->X_NOTRANS = $antri;
        $fce->X_STATUS = $status;
        
        $fce->X_REASON_CHG = trim("Delete Antri...");
        
        $fce->X_LAST_UPDATE_DATE = date('Ymd');
        $fce->X_LAST_UPDATE_TIME = date('His');
        $fce->X_LAST_UPDATED_BY = $param["USERNAME"];
        $fce->X_V2 = "X";

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
          if ($fce->RETURN["TYPE"] == "E") {
            // array_push($this->_pesan,$fce->RETURN["MESSAGE"]);
            // $result = FALSE;
          } else {
            // array_push($this->_pesan,"Rollback transaction successful");
            // $result = true;
          }
        }
        

      }


    function getNmOrganisasi($orgn_code)
    {
        $toOrg=array('2000'=>'PT.SEMEN INDONESIA','3000'=>'PT.SEMEN PADANG','4000'=>'PT.SEMEN TONASA','7000'=>'PT Semen Indonesia', '7900'=>'MD Semen Indonesia');
        if(strlen($toOrg[$orgn_code])>0)return $toOrg[$orgn_code];
        else return false;
    }

    function insert($conn,$field_names,$field_data,$tablename)
    {
        $query = "INSERT INTO $tablename ($field_names[0]";
        for($k=1;$k< count($field_names);$k++)
        {
            $query.=', '."$field_names[$k]";
        }
        $query.=") VALUES ('$field_data[0]'";
        for($k=1;$k< count($field_data);$k++)
        {
            list($tang,$gal)=split("_",$field_data[$k]);
            if($tang=="instgl")
                $query.=', '."TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";  
            else if($field_data[$k]=='SYSDATE') 
                $query.=', '."$field_data[$k]";
                        else if ($tang == "TO" && substr($gal, 0, 4) == "Date")
                        $query.=', ' . "$field_data[$k]";
            else    
            $query.=', '."'$field_data[$k]'";
        }
            $query.=')';
            //echo"<br>". $query;
            
        $sqlquery= oci_parse($conn, $query);
        $result = oci_execute($sqlquery);
        return $result;
    }

    function delDO($do)
    {
        $dsap = new SAPConnection();
        $dsap->Connect($this->_sapCon);
        $dsap->Open();
        $fce = &$dsap->NewFunction("Z_ZAPPSD_DEL_DO");
        if ($fce == false) {
          $sap->PrintStatus();
          exit;
        }
        $fce->I_VBELN = trim($do);
        $fce->Call();

        $fce->T_RETURN->Reset();
        while ($fce->T_RETURN->Next()) {
            $e = $fce->T_RETURN->row['TYPE'] == 'E';
            $msg = $fce->T_RETURN->row['MESSAGE'];            
        }
        $fce->Close();

    }

    function changeBag($trans , $org, $plant, $material,$kd_soldpd,$posnr,$no_polisi,$distriks,$distrik_lkp){
      
        try
        {
            if((($kd_soldpd == '0000003998') and ($org=='5000')) or (($kd_soldpd == '0000003999') and ($org=='5000'))){
                if ($material=="121-301-0060"){ // kantong eco brand padang
                    $kantong = "121-400-0235";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC50KG;H;SP";
                } else if($material=="121-301-0050"){
                    $kantong = "121-400-0024";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC40KG;H;SP";
                }  else if($material=="121-301-5024"){
                    $kantong = "121-400-6054"; 
                    $nm_kantong = "CEMENT BAG;PST;KRAFT;2P;PCC40KG;DYX;SBI";
                } // kantong DYX Rembang

            }else if((($kd_soldpd == '0000003998') and ($org=='7000')) or (($kd_soldpd == '0000003999') and ($org=='7000'))){
                if ($material=="121-301-0060"){ // kantong eco brand padang
                    $kantong = "121-400-0235";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC50KG;H;SP";
                } else if($material=="121-301-0050"){
                    $kantong = "121-400-0024";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC40KG;H;SP";
                } 
            }

            if($kd_soldpd == '0000003813'){ //MULTI ICS REMBANG 79E3-3813-5401
                if ($material=="121-301-0060"){ // kantong eco brand padang
                    $kantong = "121-400-0235";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC50KG;H;SP";
                } else if($material=="121-301-0050"){
                    $kantong = "121-400-0024";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC40KG;H;SP";
                } 
            } else if($kd_soldpd == '0000003808' || $kd_soldpd == '0000380801'){ // MULTI ICS TUBAN-PADANG 79C1-3808-7403
                if ($material=="121-301-0060"){ // kantong eco brand padang
                    $kantong = "121-400-0235";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC50KG;H;SP";
                } else if($material=="121-301-0050"){
                    $kantong = "121-400-0024";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;PCC40KG;H;SP";
                } 
            } else if($kd_soldpd == '0000004616' || $kd_soldpd == '0000004410' || $kd_soldpd == '0000004999'){ // kantong eco brannd tonasa
                if (($material=="121-301-0050" && $kd_soldpd =='0000004616' && $distriks == "25") || ($material=="121-301-0050" && $kd_soldpd =='0000004616' && $distriks == "23")){ // tonasa jatim-jateng
                    $kantong = "121-400-2059";
                    $nm_kantong = "CEMENT BAG;PASTED;WOVEN;1P;PCC40 ST";
                } else if ($material=="121-301-0060" || $material =="121-301-5006"){
                    $kantong = "121-400-2060";
                    $nm_kantong = "CEMENT BAG;PASTED;WOVEN;2P;PCC50 ST";
                } else {
                    $kantong = "121-400-2059";
                    $nm_kantong = "CEMENT BAG;PASTED;WOVEN;1P;PCC40 ST";
                }  
        //    }else if($kd_soldpd == '0000000385' && $plant == '7403'){ // 7401 & 385
        //         if ($material=="121-301-5005"){
        //             $kantong = "121-400-6080";
        //             $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;MASONRY40KG;SBI"; 
        //      }
           }else if(($kd_soldpd == '0000003819' && $plant == '7611') || ($kd_soldpd == '0000000385' && $plant == '7611')){ // tambahan plant 7611 Padang & Dyx
                if ($material=="121-301-0050"){
                    $kantong = "121-400-0024";
                    $nm_kantong = "CEMENT BAG,PASTED:WV;STD;1P;PCC40 SP"; //Brand SEMEN PADANG
                }else if($material=="121-301-0060"){
                    $kantong = "121-400-0235";
                    $nm_kantong = "CEMENT BAG,PASTED:WV;STD;1P;PCC50 SP";
                }else if($material=="121-301-5024"){
                    $kantong = "121-400-6054";
                    $nm_kantong = "CEMENT BAG;PST;KRAFT;2P;PCC40KG;DYX;SBI"; //Brand DYNAMIX
                }else if($material=="121-301-5023"){
                    $kantong = "121-400-6055";
                    $nm_kantong = "CEMENT;BAG;PASTED;KRAFT;2PLY;PCC;50KGSBI";
                }   
                
            }else if($kd_soldpd == '0000000385' || $plant == '7412' || $kd_soldpd == '0000008998'){ // kantong eco brannd tonasa
                if ($material=="121-301-5005"){
                    $kantong = "121-400-6069";
                    $nm_kantong = "CEMENT BAG;WOVEN;PASTED;1P;40KG;DYNAMIX";
                }else if($material=="121-301-5064"){
                    $kantong = "121-400-6080";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;MASONRY40KG;SBI";
                }else if($material=="121-301-5071"){
                    $kantong = "121-400-2131";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;MASONRY50KG;SBI";
                }else if($material=="121-301-5024"){
                    $kantong = "121-400-6054";
                    $nm_kantong = "CEMENT BAG;PST;KRAFT;2P;PCC40KG;DYX;SBI";
                }else if($material=="121-301-5025"){
                    $kantong = "121-400-6070"; 
                    $nm_kantong = "CEMENT BAG;WOVEN;PASTED;1P;50KG;DYNAMIX";
                }
            }else if($kd_soldpd == '0000000385' && $plant == '7401'){ 
                if ($material=="121-301-5064"){
                    $kantong = "121-400-6080";
                    $nm_kantong = "CEMENT BAG;PST;WV;STD;1P;MASONRY40KG;SBI";
                }
            }

            
            $connection = new SAPConnection();
            $connection->Connect($this->_sapCon);
            $connection->Open();
            $fce = &$connection->NewFunction("Z_ZAPPSD_GR_UPD_KTG");
            if ($fce == false) {
              $sap->PrintStatus();
              exit;
            }

            $fce->XPARAM["NMORG"] = $org;
            $fce->XPARAM["NMPLAN"] = $plant; 
            $fce->XPARAM["NO_TRANSAKSI"]=$trans;
            $fce->XPARAM["NO_KTG_ACT"]=$kantong;
            $fce->XPARAM["KTG_DESC_ACT"]=$nm_kantong;
            $fce->XPARAM["POSNR"]=$posnr;//'000010';//$this->FData[$idtl]["POSNR"];
            $fce->XPARAM["CHG_KTG"]='20';

            $fce->XPARAM["LAST_UPDATE_DATE"]=date('Ymd');
            $fce->XPARAM["LAST_UPDATE_TIME"]=date('His');
            $fce->XPARAM["LAST_UPDATED_BY"]=$this->User->ID;
            $fce->Call();
             
        //if ($fce->GetStatus() == SAPRFC_OK) {
             //  $fce->RETURN->Reset();
                //Display Tables
             //   $i=0;
             //   while ( $fce->RETURN->Next() ){
             //       $this->report= $fce->RETURN->row["TYPE"].': '.$fce->RETURN->row["MESSAGE"];
              //      $fce->RETURN->row["MESSAGE"];
             //       $i++;
             //   }
            //}
            
            $fce->Close();
            $connection->Close();
            
            // return "Change bag sukses.";
        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            echo $e;
        }
    }

}


class MatchingSP {

    private $conn;
    private $conncsms;
    private $sap;
    private $_sapCon;
    private $_dtlso;
    public $_cekAntri;
    public $_truk;
    public $_sopir;
    public $_Dkartu;
    public $_return;

    public function __construct($conn, $conncsms) {
        require_once ("../include/sapclasses/sap.php");
        $this->_sapCon = "../include/sapclasses/logon_data.conf";
        //$this->_sapCon = "../include/sapclasses/logon_data_qa.conf";
        $this->conn = $conn;
        $this->conncsms = $conncsms;
        
        $this->sap = new SAPConnection();
        $this->sap->Connect($this->_sapCon);
        $this->sap->Open();
    }

    function getDataBook($param){
        $this->_dtlso["NOPOL"] = $param["NOPOL"];
       // $this->_dtlso["NOPOL"] = $this->formatnopolisi($param["NOPOL"]);
        $this->_dtlso["USERNAME"] = $param["USERNAME"];
        $this->_dtlso["ORG"] = $param["ORG"];
        $this->_dtlso["ORG_DESC"] = "PT. SEMEN PADANG";
        $this->_dtlso["PLANT"] = $param["PLANT"];
        // $this->_dtlso["NMPLAN_NAME"] = $fce->T_DATA->row["NMWERKS"]; // w?	
        $this->_dtlso["DRIVER"] = $param["DRIVER"];
        $this->_dtlso["NO_SIM"] = $param["NO_SIM"];
        $this->_dtlso["QTY_DO"] = $param["QTY"][0];
        $this->_dtlso["NO_SO"] = $param["NO_SO"][0];
        $this->_dtlso["SPP"] = $param["SPP"];
        $this->_dtlso["EXPEDITUR"] = "0000410092";
        $this->_dtlso["EXPEDITUR_NAME"] = "SEMEN INDONESIA LOGISTIC, PT";
        $this->_dtlso["IDCARD"] = $param["IDCARD"];


        //// SIMPAN LOG TRANSAKSI di LOG_TIME_TRANS
        $cekbooking = $this->DBselect($this->conn, 'NO_BOOKING', 'LOG_ANTRI', "NO_BOOKING = '".$this->_dtlso["SPP"]."' AND DELETE_MARK = '0' AND STATUS_ANTRI = '0'");
        // $ = $this->DataAccess->querySelect('LOG_ANTRI', array('NO_BOOKING'), array('DELETE_MARK' => '0', 'STATUS_ANTRI' => '1', 'NO_BOOKING' => $fce->T_DATA->row["NO_BOOKING"]));
        if($cekbooking == ''){
            $tgl_jam = date('Y-m-d H:i:s');
            $HdrColumn = array('NO_BOOKING','WAKTU_START','STATUS');
            $HdrValue = array($this->_dtlso["SPP"],$tgl_jam,'ANTRISODO');
            $this->DBinsert($this->conn, $HdrColumn, $HdrValue, 'LOG_TIME_TRANS');
        }else{
            return $this->_return["ERROR"] .= "No Booking ".$this->_dtlso["SPP"]." telah digunakan sebelum nya";
            // echo "No Booking ".$this->_dtlso["SPP"]." telah digunakan sebelum nya";
            // die;
        }


        //// GET DATA BOOKING/SO Z_ZAPPSD_SOVSTRUK
        $fce = $this->sap->NewFunction("ZCSD_GET_DETAIL_SO");
        if ($fce == false) {
            $this->sap->PrintStatus();
            exit;
        }
        $fce->I_TRANSACTION_GROUP = '0';
        $fce->LR_VKORG->row["SIGN"] = "I";
        $fce->LR_VKORG->row["OPTION"] = "EQ";
        $fce->LR_VKORG->row["LOW"] = trim($this->_dtlso["ORG"]);
        $fce->LR_VKORG->Append($fce->LR_VKORG->row);

        $fce->LR_WERKS->row["SIGN"] = "I";
        $fce->LR_WERKS->row["OPTION"] = "EQ";
        $fce->LR_WERKS->row["LOW"] = trim($this->_dtlso["PLANT"]);
        $fce->LR_WERKS->Append($fce->LR_WERKS->row);

        $fce->LR_EDATU->row["SIGN"] = "I";
        $fce->LR_EDATU->row["OPTION"] = "BT";
        $fce->LR_EDATU->row["LOW"] = date('Ymd', strtotime(' -30 days'));
        $fce->LR_EDATU->row["HIGH"] = date('Ymd');
        $fce->LR_EDATU->Append($fce->LR_EDATU->row);

        $fce->LR_VBELN->row["SIGN"] = "I";
        $fce->LR_VBELN->row["OPTION"] = "EQ";
        $fce->LR_VBELN->row["LOW"] = $this->_dtlso["NO_SO"];
        $fce->LR_VBELN->Append($fce->LR_VBELN->row);
        // var_dump($fce);
        $fce->Call();

        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->T_OUTPUT->Reset();
            if($fce->RETURN["TYPE"] == "E")
                return $this->_return["ERROR"] .= $fce->RETURN['MESSAGE'];

            while ($fce->T_OUTPUT->Next()) {
                $this->_dtlso["TIPESO"] = $fce->T_OUTPUT->row["AUART"]; // ZOR
                $this->_dtlso["ORDER_DATE"] = $fce->T_OUTPUT->row["EDATU"]; // ZOR
                $this->_dtlso["MATERIAL"] = $fce->T_OUTPUT->row["MATNR"]; // 121-301-0050
                $this->_dtlso["DISTRIK"] = $fce->T_OUTPUT->row["BZIRK"]; // 251001
                $this->_dtlso["ALAMAT"] = $fce->T_OUTPUT->row["STRAS"]; // 251001
                $this->_dtlso["NAMA_KOTA"] = $fce->T_OUTPUT->row["BZTXT"]; // 251001
                $this->_dtlso["KOTA"] = $fce->T_OUTPUT->row["BZIRK"]; // 251001
                $this->_dtlso["INV"] = $fce->T_OUTPUT->row["PRODH"];
                $this->_dtlso["NAMA_PROVINSI"] = $fce->T_OUTPUT->row["BEZEI"]; // 251001
                $this->_dtlso["PROVINSI"] = $fce->T_OUTPUT->row["VKBUR"]; // 1025
                $this->_dtlso["TRANSAKSI"] = $fce->T_OUTPUT->row["INCO1"]; // FOT
                $this->_dtlso["TIPE_TRANSAKSI"] = "SHP-".$fce->T_OUTPUT->row["INCO1"]; // SHP-FOT
                $this->_dtlso["NAMA_TRANSAKSI"] = $fce->T_OUTPUT->row["BEZEI_ROUTE"]; // Route Darat - FOT
                $this->_dtlso["LINE"] = $fce->T_OUTPUT->row["POSNR"]; // 10
                $this->_dtlso["SOLD_TO_CODE"] = $fce->T_OUTPUT->row["SOLD_TO_CODE"]; // 10
                $this->_dtlso["SOLDTO_NAME"] = $fce->T_OUTPUT->row["SOLDTO_NAME"]; // 10
                $this->_dtlso["SHIP_TO_CODE"] = $fce->T_OUTPUT->row["SHIP_TO_CODE"]; // 10
                $this->_dtlso["SHIPTO_NAME"] = $fce->T_OUTPUT->row["SHIPTO_NAME"]; // 10
            }
        } else {
            return $this->_return["ERROR"] .= $fce->RETURN["TYPE"]." : ".$fce->RETURN["MESSAGE"];
            // echo $fce->RETURN["TYPE"]." : ".$fce->RETURN["MESSAGE"];
            // die;
        }

        //// UOM Z_ZAPPSD_SEL_MAT_PLANT
        $fce = $this->sap->NewFunction("Z_ZAPPSD_SEL_MAT_PLANT");
        if ($fce == false) {
            $this->sap->PrintStatus();
            exit;
        }

        $fce->XWERKS = $this->_dtlso["PLANT"];
        $fce->XMATNR = $this->_dtlso["MATERIAL"];
        if($this->_dtlso["ORG"] =="3000" and $this->_dtlso["PLANT"] == "3201"){
            $fce->XMTART = "ZBSJ";
        }else{
            $fce->XMTART = "ZBJD";
        }
        $fce->XMATNR = $this->_dtlso["MATERIAL"];                
        $fce->LR_XMATNR->row["SIGN"] = 'I';
        $fce->LR_XMATNR->row["OPTION"] = 'EQ';
        $fce->LR_XMATNR->row["LOW"] = $this->_dtlso["MATERIAL"];
        $fce->LR_XMATNR->Append($fce->LR_XMATNR->row);
        
        $fce->LR_PLANT->row["SIGN"] = 'I';
        $fce->LR_PLANT->row["OPTION"] = 'CP';
        $fce->LR_PLANT->row["LOW"] = $this->_dtlso["PLANT"];
        $fce->LR_PLANT->Append($fce->LR_PLANT->row);

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->RETURN_DATA->Reset();
            while ($fce->RETURN_DATA->Next()) {
                $this->_dtlso["UOM"] = $fce->RETURN_DATA->row["MEINS"];
            }
        }


        if ($this->_dtlso["UOM"] == "TO"){
            $this->_dtlso["CURAH_OR_BAG"] = '10';
        }else{
            $this->_dtlso["CURAH_OR_BAG"] = '20';
        }


        $this->getDataTruk();
        if($this->_truk[0]["TIDAKADAOPCO"] == '1'){
            $hasilcekcek .= "DAFTARKAN TRUK NYA DI SEMEN PADANG TERLEBIH DAHULU";
            return $this->_return["ERROR"] = "DAFTARKAN TRUK NYA DI SEMEN PADANG TERLEBIH DAHULU";
            // die;
        }elseif($this->_truk[0]["DAFTAR"] == '0'){
            $this->insertTruk();
            $hasilcekcek .= "Truk UPDATE<br />";
            $DATATRUKADA = 1;
        }elseif($this->_truk[0]["LENGKAP"] == '0'){
            ###TRUK LENGKAP
            $hasilcekcek .= "Truk sudah ada<br />";
            $DATATRUKADA = 1;
        }
        if($DATATRUKADA > 0){
            $this->getDataSopir();
            if($this->_sopir[0]["SOPIRTIDAKADA"] == '0'){
                $hasilcekcek .= "DAFTARKAN SOPIR NYA DI SEMEN PADANG TERLEBIH DAHULU";
                return $this->_return["ERROR"] = "DAFTARKAN SOPIR NYA DI SEMEN PADANG TERLEBIH DAHULU";
                // die;
            }elseif($this->_sopir[0]["DAFTARSOPIR"] == '0'){
                $this->insertSopir();
                $hasilcekcek .= "Sopir UPDATE<br />";
                $SIMPANMASUK = 1;
            }elseif($this->_sopir[0]["SOPIRLENGKAP"] == '0'){
                ###TRUK LENGKAP
                $hasilcekcek .= "Sopir sudah ada<br />";
                $SIMPANMASUK = 1;
            }
        }

        // var_dump($this->_truk);
        // var_dump($this->_sopir);

        //// CEK MAPPING KANTONG
        if($SIMPANMASUK > 0){
            if($SALES_UNIT != 'TO'){
                if ($this->_dtlso["TIPESO"] == 'ZNL'){ $tipe_order = 'ZNL';}
                elseif ($this->_dtlso["TIPESO"] == 'ZLFP') { $tipe_order = 'ZLFP'; }
                else { $tipe_order = 'ALL'; }

                $rowkantong = $this->DBselect($this->conn, 'KANTONG, KANTONG_DESC', 'TB_MASTER_KANTONG', 
                "MATERIAL = '".$this->_dtlso["MATERIAL"]."' AND DELIVERY_TYPE='$tipe_order' AND PLANT='".$this->_dtlso["PLANT"]."' AND ID_DISTRIK = '".$this->_dtlso["DISTRIK"]."'");

                if($rowkantong['KANTONG'] != ''){
                    $this->_dtlso["NO_KTG"] = $rowkantong['KANTONG'];
                    $this->_dtlso["KTG_DESC"] = $rowkantong['KANTONG_DESC'];
                    $this->_dtlso["UOM_KTG"] = 'LBR';
                    $cek_jamuat = 1;
                }else{
                    $this->_return["ERROR"] .= "Lakukan Daftar kantong dengan ".$this->_dtlso["DISTRIK"]."-".$this->_dtlso["PLANT"]."-".$tipe_order."-".$this->_dtlso["MATERIAL"];
                }
            }else{
                $cek_jamuat = 1;
            }
        }

        //// CEK JAM MUAT DAN ANTRI
        if($cek_jamuat == 1){
            // $checkjam = $this->DBselect($this->conn, 'AKTIVASI_ANTRISO', 'TB_PLANT', "KODE_PLANT='".$this->_dtlso["PLANT"]."'");
            // if($checkjam == '1'){
            // 	$Ajam = substr($JAM_ANTRI,-6,2).':';
            // 	$Amenit = substr($JAM_ANTRI,-4,2).':';
            // 	$Adetik = substr($JAM_ANTRI,-2,2);

            // 	$Njam = substr($JAM_ANTRI_END,-6,2).':';
            // 	$Nmenit = substr($JAM_ANTRI_END,-4,2).':';
            // 	$Ndetik = substr($JAM_ANTRI_END,-2,2);
                
            // 	$tgl_scheduler = date('d-m-Y',strtotime($TGL_SCHEDULE));
            // 	$sekarang = strtotime(date('d-m-Y H:i:s'));
            // 	$awal = strtotime($tgl_scheduler.' '.$Ajam.$Amenit.$Adetik);
            // 	$akhir = strtotime($tgl_scheduler.' '.$Njam.$Nmenit.$Ndetik);
            // 	$jam_checkin_awal1 = strtotime($Ajam.$Amenit.$Adetik);
            // 	$jam_checkin_akhir1 = strtotime($Njam.$Nmenit.$Ndetik);
                
            // 	if($jam_checkin_awal1 > $jam_checkin_akhir1){
            // 		$checkin_awal = date('d-m-Y H:i:s', strtotime("-1 days", strtotime($awal))); //checkin_awal
            // 	}else{
            // 		$checkin_awal = $awal;
            // 	}
                
            // 	if($sekarang >= $checkin_awal && $sekarang <= $akhir){
            // 		$cek_errorantri = 1;
            // 	}ELSE{
            // 		$cek_errorantri = 0;
            // 		$this->cengenet->IsValid = false;
            // 		$this->cengenet->Text = "Bukan Waktunya Check in, waktu check in adalah $tgl_scheduler $Ajam$Amenit$Adetik - $tgl_scheduler $Njam$Nmenit$Ndetik dan sekarang tanggal ".date('d-m-Y H:i:s'); 
            // 	}
            // }else{
                $cek_errorantri = 1;
            // }
        }

        if($cek_errorantri == 1){
            $checkjam = $this->DBselect($this->conn, 'NO_DO, STATUS_ANTRI, TIPE_ANTRI, NODAFTAR, ANTRI', 'LOG_ANTRI', "NO_BOOKING = '".$this->_dtlso["SPP"]."' AND DELETE_MARK = '0' AND STATUS_ANTRI = '0'");
            // $cekerorantri = $this->DataAccess->querySelect('LOG_ANTRI', array('NO_DO','STATUS_ANTRI','TIPE_ANTRI','NODAFTAR','ANTRI'), array('NO_BOOKING' => "'" . $this->Noboooking->Text . "'", 'DELETE_MARK' => '0', 'STATUS_ANTRI' => '0'));	
            if($cekerorantri["NO_DO"] != '' AND $cekerorantri["STATUS_ANTRI"] == 0 AND $cekerorantri["TIPE_ANTRI"] != '' AND $cekerorantri["NODAFTAR"] != ''){
                // $this->_cekAntri["NO_DO"] = $this->AutoNum($cekerorantri["NO_DO"],10);
                // echo "<br>melbu kene 1<br>";die;
                $this->_cekAntri["NO_DO"] = $cekerorantri["NO_DO"];
                $this->_cekAntri["NO_ANTRI"] = $cekerorantri["ANTRI"];
                $this->_cekAntri["TIPE_ANTRI"] = $cekerorantri["TIPE_ANTRI"];
                $this->_cekAntri["NO_DAFTAR"] = $cekerorantri["NODAFTAR"];
                // echo "<br>no_daftar1 ".$this->_cekAntri["NO_DAFTAR"];
                $this->_cekAntri["STATUSEROR"] = '1';
                return true;
            }elseif($cekerorantri["NO_DO"] != '' AND $cekerorantri["STATUS_ANTRI"] == 0 AND $cekerorantri["TIPE_ANTRI"] == '' AND $cekerorantri["NODAFTAR"] == ''){
                // echo "<br>no_daftar01 ";
                // $this->_cekAntri["NO_DO"] = $this->AutoNum($cekerorantri["NO_DO"],10);
                // echo "<br>melbu kene 2<br>";die;
                $this->_cekAntri["NO_DO"] = $cekerorantri["NO_DO"];
                $this->_cekAntri["STATUSEROR"] = '2';
                return true;
            }else{
                // echo "<br>no_daftar02 ";
                // echo "<br>melbu kene 3<br>";die;
                $this->_cekAntri["STATUSEROR"] = '0';
                return true;
            }
        }


        $fce->Close();
        // $this->sap->Close();
    }

    function bookClick(){
        if (($this->_dtlso["PLANT"] == '3401')||($this->_dtlso["PLANT"] == '7912')||($this->_dtlso["PLANT"] == '79H6')||($this->_dtlso["PLANT"] == '79M6')){
            if($this->_cekAntri["STATUSEROR"] == 0){
                $this->_cekAntri["NO_DAFTAR"] = rand();
                $this->buatAntriClick();
                return $this->_return;
                // if($this->_dtlso["UOM"] == 'ZAK'){
                //     if($this->_dtlso["TIPE_TRANSAKSI"] <> 'SHP-FOT'){
                //         if ($this->getKartu()){
                //             $this->simpando->Visible = true;
                //             $this->simpan->Visible = false;
                //             return true;					
                //         }else {			
                //             $this->simpando->Visible = false;
                //             $this->simpan->Visible = false;
                //         }
                //     }
                // }
            }elseif($this->_cekAntri["STATUSEROR"] == 1){
                if ($this->UpdateDOShipment()){	
                    // $this->PnlBtnTruk->Visible = true;
                    // $this->simpando->Visible = false;
                    // $this->simpan->Visible = true;
                    return $this->_return;
                }
            }elseif($this->_cekAntri["STATUSEROR"] == 2){
                $this->_cekAntri["NO_DAFTAR"] = rand();
                $this->buatAntriClick();
                if ($this->UpdateDOShipment()){	
                    // $this->PnlBtnTruk->Visible = true;
                    // $this->simpando->Visible = false;
                    // $this->simpan->Visible = true;
                    return $this->_return;
                }
            }
        }else{ ### UNTUK PLANT != 3401
            $ceklocksystem = $this->DBselect($this->conn, 'LOCK_SYSTEM', 'TB_PLANT', "KODE_PLANT = '".$this->_dtlso["PLANT"]."' AND DELETE_MARK=0");
            $LOCK_SYSTEM = explode(';', $ceklocksystem["LOCK_SYSTEM"]);
            if($this->_cekAntri["STATUSEROR"] == 0){
                if($LOCK_SYSTEM[0] == '1'){
                    // $this->proximitynum->Visible = true;
                    // $this->atau->Visible = true;
                    // $this->nodaftar->Text = rand();
                    // $this->simpan->Visible = false;
                    $this->_cekAntri["NO_DAFTAR"] = rand();
                    $this->getKartuIDcard();
                    return true;
                }elseif($LOCK_SYSTEM[0] == '0'){
                    // $this->nodaftar->Text = rand();
                    // $this->simpan->Visible = false;
                    $this->buatAntriClick();
                    $this->_cekAntri["NO_DAFTAR"] = rand();
            }
            }elseif($this->_cekAntri["STATUSEROR"] == 1){
                if($LOCK_SYSTEM[0] == '1'){
                    // $this->proximitynum->Visible = true;
                    // $this->atau->Visible = true;
                    $this->getKartuIDcard();
                }elseif($LOCK_SYSTEM[0] == '0'){
                    // if ($this->UpdateDOShipment()){
                        // $this->PnlBtnTruk->Visible = true;
                        // $this->simpando->Visible = false;
                        // $this->simpan->Visible = true;					
                    // }
                }
            }elseif($this->_cekAntri["STATUSEROR"] == 2){	
                if($LOCK_SYSTEM[0] == '1'){
                    // $this->proximitynum->Visible = true;
                    // $this->atau->Visible = true;
                    $this->getKartuIDcard();
                }elseif($LOCK_SYSTEM[0] == '0'){					
                    if ($this->UpdateDOShipment()){
                        $this->buatAntriClick();
                        $this->_cekAntri["NO_DAFTAR"] = rand();
                        $this->nodaftar->Text = rand();
                        // $this->PnlBtnTruk->Visible = true;
                        // $this->simpando->Visible = false;
                        // $this->simpan->Visible = true;
                    }	
                }
            }
        }
    }

    function generateDOClick(){
        $datetime = date('d-m-Y H:i:s');
        $tanngal_now = date('d-m-Y');
        if(!empty($_SERVER['HTTP_CLIENT_IP'])){
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        }elseif(!empty($_SERVER['HTTP_X_FORWARDED_FOR'])){
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        }else{
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        }
        $lognoantri = $this->_cekAntri["NO_ANTRI"];
        $LOGNO_BOOKING = $this->_dtlso["SPP"];
        $LOG_USERID=$this->_dtlso["USERNAME"];
        $LOG_NOPOLISI=$this->_dtlso["NOPOL"];
        // $laporan = '';
        ### AMBIL DATA SO
        $NO_SO = $this->_dtlso["NO_SO"];
        $LINE = $this->_dtlso["LINE"];
        $NMPLAN = $this->_dtlso["PLANT"];
        $ITEM_QTY = $this->_dtlso["QTY_DO"]; // qty do (inputan)
        $SALES_UNIT = $this->_dtlso["UOM"];
        $SO_TYPE = $this->_dtlso["TIPESO"]; // zor
        $ORDER_DATE = $this->_dtlso["ORDER_DATE"];
        $ALAMAT = $this->_dtlso["ALAMAT"]; // 
        $NO_KTG = $this->_dtlso["NO_KTG"]; // kode kantong 121-400-0024
        $KTG_DESC = $this->_dtlso["KTG_DESC"]; // kantong deskripsi
        $VENDOR_NO = $this->_dtlso["EXPEDITUR"];
        $VENDOR_NAME = $this->_dtlso["EXPEDITUR_NAME"]; //"SEMEN INDONESIA LOGISTIC, PT"

        ### PEGECEKAN DO YANG BELUM ANTRI
        $fceCEKDO = $this->sap->NewFunction("ZCSD_DO_FOR_SO");
        if ($fceCEKDO == false ) {
            $dataCEKDO->PrintStatus();
            exit;
        }
        $fceCEKDO->REFERENCE_DOC->row["SIGN"] = 'I';
        $fceCEKDO->REFERENCE_DOC->row["OPTION"] = 'EQ';
        $fceCEKDO->REFERENCE_DOC->row["LOW"] = $NO_SO;
        $fceCEKDO->REFERENCE_DOC->Append($fceCEKDO->REFERENCE_DOC->row);
        $fceCEKDO->Call();
        if ($fceCEKDO->GetStatus() == SAPRFC_OK ) {
            ### LIST DO
            $fceCEKDO->RESULT->Reset();
            while ( $fceCEKDO->RESULT->Next() ){
                if($fceCEKDO->RESULT->row["STATUS_TRANS"] == "" AND $fceCEKDO->RESULT->row["ORDER_QTY"] == $ITEM_QTY){ 
                    $this->_dtlso["ID_LOG"] = $this->DBselect($this->conn, 'ID', 'LOG_ANTRI', "NO_DO = '".$this->AutoNum($fceCEKDO->RESULT->row["NO_DO"],10)."' AND DELETE_MARK='0'");
                    // $this->_data_log = $this->DataAccess->querySelect('LOG_ANTRI', array('ID'), array('DELETE_MARK' => '0', 'NO_DO' => $this->AutoNum($fceCEKDO->RESULT->row["NO_DO"],10)));
                    if($this->_dtlso["ID_LOG"] == ""){
                        $NODO = $fceCEKDO->RESULT->row["NO_DO"];
                    }
                    // echo ",<br>";
                    // var_dump($this->_dtlso["ID_LOG"]);
                }
            }
        }
        if($NODO != ''){
            // echo "nodoe ga kosong ".$NODO;
            ### INSERT LOG ANTRI
            $LOG_field_names=array('IP','NO_BOOKING','STATUS_ANTRI','NO_DO','ANTRI','DELETE_MARK','TGL_ANTRI','NOPOLISI','PLANT');
            $LOG_field_data=array("$ipaddress","$LOGNO_BOOKING","0",$this->AutoNum($NODO,10),"$lognoantri","0","$datetime","$LOG_NOPOLISI","$NMPLAN");
            $LOG_tablename="LOG_ANTRI";
            $this->DBinsert($this->conn, $LOG_field_names, $LOG_field_data, $LOG_tablename);
            // $this->DataAccess->OraInsert($LOG_field_names,$LOG_field_data,$LOG_tablename);
            
            $this->_cekAntri["NO_DO"] = $this->AutoNum($NODO,10);
            // $this->no_do->Text = $this->AutoNum($NODO,10);
            // $this->simpan->Visible = true;
            // $this->simpando->Visible = false;
                            
            $nilai = array('TIPE_ANTRI' => "'".$this->_cekAntri["TIPE_ANTRI"]."'" ,'NODAFTAR' => "'".$this->nodaftar->Text."'");
            $kondisi = array('NO_BOOKING' => "'" . $LOGNO_BOOKING . "'" ,'DELETE_MARK' => '0' ,'STATUS_ANTRI' => '0', 'NO_DO' => "'" . $this->AutoNum($NODO,10) . "'");
            $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
            // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
            return true;
        }else{
            // echo "nodoe kosong";
            ### SPLIT DO
            if ($this->_dtlso["ORG"] == '7900') {
                $fce = $this->sap->NewFunction ("ZCSD056_2SPLIT_DO3_MD");
            }else{
                $fce = $this->sap->NewFunction ("ZCSD056_2SPLIT_DO3");
            }
            if ($fce == false ) {
                $this->sap->PrintStatus();
                exit;
            }

            ### FILTER DATA
            $fce->I_SO_DOC = $NO_SO; ### NOMOR SO
            $fce->I_SO_POSNR = $this->linenum($LINE);

            $fce->T_TABLE->row["ZVSTEL"] = $NMPLAN;
            $fce->T_TABLE->row["ZQTY_DO"] = 1;
            $fce->T_TABLE->row["ZQTY_DO_ITEM"] = $ITEM_QTY;
            $fce->T_TABLE->row["ZVENDOR"] = '';
            $fce->T_TABLE->Append($fce->T_TABLE->row);

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK ) {
                ### DO TERSPLIT
                $fce->T_RETURN->Reset();
                while ( $fce->T_RETURN->Next() ){
                    if($this->_dtlso["ORG"] == '7900'){
                        if($fce->T_RETURN->row["VKORG"] == '7900'){
                            $NODO = $fce->T_RETURN->row["ZVBELN"];
                            $plantdo = $fce->T_RETURN->row["ZVSTEL"];
                            $comdo = $fce->T_RETURN->row["VKORG"];
                            ### INSERT LOG ANTRI
                            $LOG_field_names=array('IP','NO_BOOKING','STATUS_ANTRI','NO_DO','ANTRI','DELETE_MARK','TGL_ANTRI','NOPOLISI','PLANT');
                            $LOG_field_data=array("$ipaddress","$LOGNO_BOOKING","0",$this->AutoNum($NODO,10),"$lognoantri","0","$datetime","$LOG_NOPOLISI","$NMPLAN");
                            $LOG_tablename="LOG_ANTRI";
                            $this->DBinsert($this->conn, $LOG_field_names, $LOG_field_data, $LOG_tablename);
                            // $this->DataAccess->OraInsert($LOG_field_names,$LOG_field_data,$LOG_tablename);
                        }
                    }else{
                        $NODO = $fce->T_RETURN->row["ZVBELN"];
                        $plantdo = $fce->T_RETURN->row["ZVSTEL"]; 
                        ### INSERT LOG ANTRI
                        $LOG_field_names=array('IP','NO_BOOKING','STATUS_ANTRI','NO_DO','ANTRI','DELETE_MARK','TGL_ANTRI','NOPOLISI','PLANT');
                        $LOG_field_data=array("$ipaddress","$LOGNO_BOOKING","0",$this->AutoNum($NODO,10),"$lognoantri","0","$datetime","$LOG_NOPOLISI","$NMPLAN");
                        $LOG_tablename="LOG_ANTRI";
                        $this->DBinsert($this->conn, $LOG_field_names, $LOG_field_data, $LOG_tablename);
                        // $this->DataAccess->OraInsert($LOG_field_names,$LOG_field_data,$LOG_tablename);
                    }
                }
                ### MESSAGE DO
                $fce->T_MSGRETURN->Reset();
                while ($fce->T_MSGRETURN->Next()) {
                    $splitnya=trim($fce->T_MSGRETURN->row["TYPE"]);
                    if (($splitnya == "S") or ($splitnya == "I")) {			
                        $dapat = 'M';
                    }else{
                        $pesan = $fce->T_MSGRETURN->row["MESSAGE"];
                        $keterangan1 = stristr($pesan,"NOK");
                        $keterangan2 = stristr($pesan,"ZOR");
                        if($keterangan1 != ''){
                            $this->_return["ERROR"] .= " Kredit limit tidak mencukupi <br />";
                            // echo "Kredit limit tidak mencukupi<br />";
                        }elseif($keterangan2 != ''){
                            $this->_return["ERROR"] .= " Sisa Qty SO tidak mencukupi untuk Split <br />";
                            // echo "Sisa Qty SO tidak mencukupi untuk Split<br />";
                        }else{
                            $this->_return["ERROR"] .= $pesan."<br />";
                            // echo $pesan."<br />";
                        }					
                        $salah=$salah+1;
                    }
                }
                if ($dapat == 'M') {
                    $fceUPDATE = '';
                    if ($this->_dtlso["ORG"] == '7900') {
                        $company = $comdo;
                        $fceUPDATE = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_DO_SHIPMENT2MD");
                    }else{
                        $company = $this->_dtlso["ORG"];
                        $fceUPDATE = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_DO_SHIPMENT2");
                    }
                    if ($fceUPDATE == false) {
                        $this->sap->PrintStatus();
                        exit;
                    }
                    $fceUPDATE->X_VKORG = $company;
                    $fceUPDATE->X_WERKS = $plantdo;
                    $fceUPDATE->X_LFART = $this->getLFART($SO_TYPE);
                    $fceUPDATE->X_VBELN = $this->AutoNum($NODO,10);

                    $fceUPDATE->Call();
                    $returntemp = array();
                    if ($fceUPDATE->GetStatus() == SAPRFC_OK) {
                        if ($fceUPDATE->RETURN["TYPE"] == "S") {
                            $fceUPDATE->RETURN_DATA->Reset();
                            while ($fceUPDATE->RETURN_DATA->Next()) {
                                $returntemp = $fceUPDATE->RETURN_DATA->row;
                                $order_date=$returntemp['ORDER_DATE'];
                                $thn=substr($order_date,0,4);
                                $bln=(substr($order_date,4,2));
                                $tgl=substr($order_date,6,3);
                                $tgl_order=$tgl.'-'.$bln.'-'.$thn;
                                $jj=$j+2;
                                $tanggal=$tgl_sch[$jj];	

                                // echo "$SALES_UNIT";
                                if($SALES_UNIT != 'TO'){
                                    $fcep = $this->sap->NewFunction ("Z_ZAPPSD_UPDATE_DO");
                                    if ($fcep == false ) {
                                        $this->sap->PrintStatus();
                                        #echo "Koneksi ke RFC terputus!, mohon coba kembali beberapa saat lagi";
                                        exit;
                                    }
                                    
                                    $fcep->NO_ORDER =  $this->AutoNum($returntemp['ORDER_NO'],10);
                                    $fcep->ITEM_KANTONG = $NO_KTG;
                                    $fcep->KANTONG_DESC = $KTG_DESC;
                                    $fcep->Call();
                                    if ($fcep->GetStatus() == SAPRFC_OK ) {
                                        if(trim($fcep->RETURN["TYPE"]) == 'S'){
                                            $field_names=array('ORDER_ID','LINE_ID','ORDER_NO','DISTRIBUTOR','DISTRIBUTOR_ID','QTY_1','TIPE_SEMEN','ITEM_NO','INV_CLASS_NAME','QTY_2','STATUS_ASSIGN','INV_CLASS','ORDER_UM1','ORDER_UM2','PROP_TUJUAN','ORGN_CODE','ATTRIBUTE_CATEGORY','OP_ORDR_TYPE','KOTA_TUJUAN','ORDER_DATE','PRESALES_ORD_NO','PLANT','INCOTERM','DISTRIK','SHIPTO','ALAMAT','HARGA_SATUAN','TGL_SCEDULE','KD_SHIPTO','KODE_KANTONG','KANTONG','KD_EXP','NM_EXP');
                                            $field_data=array("$returntemp[ORDER_NO]","$returntemp[LINE]","$returntemp[ORDER_NO]","$returntemp[SOLD_TO_PARTY]","$returntemp[SOLD_TO_CODE]","$returntemp[ORDER_QTY]","$returntemp[ITEM_DESC]","$returntemp[ITEM_NO]","$returntemp[INV_CLASS_NAME]","$returntemp[JUMLAH_SEMEN]","1","$returntemp[KODE_INV_CLASS]","$returntemp[ORDER_UM]","$returntemp[UM_JUMLAH_SEMEN]","$returntemp[PROP_TUJUAN]","$returntemp[NMORG]","$returntemp[ATT_CATEGORY]","$returntemp[LFART_DWN]","$returntemp[KOTA_TUJUAN_NAME]","instgl_$tgl_order","$returntemp[SALES_ORD_NO]","$returntemp[NMPLAN]","$returntemp[INCOTERM]","$returntemp[DISTRIK]","$returntemp[SHIP_TO_PARTY]","$ALAMAT","","$tgl_order","$returntemp[SHIP_TO_CODE]","$NO_KTG","$KTG_DESC","$VENDOR_NO","$VENDOR_NAME");
                                            $tablename="TB_DO_DATA";
                                            $this->DBinsert($this->conn, $field_names, $field_data, $tablename);
                                            // $this->DataAccess->OraInsert($field_names,$field_data,$tablename);	
                                            $this->_cekAntri["NO_DO"] = $this->AutoNum($NODO,10);
                                            // $this->no_do->Text =  $this->AutoNum($NODO,10);
                                            // $this->simpan->Visible = true;
                                            // $this->simpando->Visible = false;
                                            
                                            $nilai = array('TIPE_ANTRI' => "'".$this->_cekAntri["TIPE_ANTRI"]."'" ,'NODAFTAR' => "'".$this->_cekAntri["NO_DAFTAR"]."'");
                                            $kondisi = array('NO_BOOKING' => "'" . $LOGNO_BOOKING . "'" ,'DELETE_MARK' => '0' ,'STATUS_ANTRI' => '0', 'NO_DO' => "'" . $this->AutoNum($NODO,10) . "'");
                                            $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                                            // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);

                                            // echo "<br>returntempORG: " . $returntemp['NMORG'];
                                            if($returntemp['NMORG'] == '7900'){
                                                // echo "DO MD Sukses Tercreate - ".$this->AutoNum($returntemp['ORDER_NO'],10)."<br />";
                                                $this->_return["DO_MD"] = $this->AutoNum($returntemp['ORDER_NO'],10);
                                            }else{
                                                // echo "DO OPCO Sukses Tercreate - ".$this->AutoNum($returntemp['ORDER_NO'],10)."<br />";
                                                $this->_return["DO_OPCO"] = $this->AutoNum($returntemp['ORDER_NO'],10);
                                            }
                                            // return true;
                                        } else {
                                            echo $fce->RETURN["TYPE"] . " : " . $fce->RETURN["MESSAGE"] . "<br>" ;
                                        }
                                    } else {
                                        echo $fcep->PrintStatus();
                                    }                            
                                }else{
                                    $field_names=array('ORDER_ID','LINE_ID','ORDER_NO','DISTRIBUTOR','DISTRIBUTOR_ID','QTY_1','TIPE_SEMEN','ITEM_NO','INV_CLASS_NAME','QTY_2','STATUS_ASSIGN','INV_CLASS','ORDER_UM1','ORDER_UM2','PROP_TUJUAN','ORGN_CODE','ATTRIBUTE_CATEGORY','OP_ORDR_TYPE','KOTA_TUJUAN','ORDER_DATE','PRESALES_ORD_NO','PLANT','INCOTERM','DISTRIK','SHIPTO','ALAMAT','HARGA_SATUAN','TGL_SCEDULE','KD_SHIPTO','KODE_KANTONG','KANTONG','KD_EXP','NM_EXP');
                                    $field_data=array("$returntemp[ORDER_NO]","$returntemp[LINE]","$returntemp[ORDER_NO]","$returntemp[SOLD_TO_PARTY]","$returntemp[SOLD_TO_CODE]","$returntemp[ORDER_QTY]","$returntemp[ITEM_DESC]","$returntemp[ITEM_NO]","$returntemp[INV_CLASS_NAME]","$returntemp[JUMLAH_SEMEN]","1","$returntemp[KODE_INV_CLASS]","$returntemp[ORDER_UM]","$returntemp[UM_JUMLAH_SEMEN]","$returntemp[PROP_TUJUAN]","$returntemp[NMORG]","$returntemp[ATT_CATEGORY]","$returntemp[LFART_DWN]","$returntemp[KOTA_TUJUAN_NAME]","instgl_$tgl_order","$returntemp[SALES_ORD_NO]","$returntemp[NMPLAN]","$returntemp[INCOTERM]","$returntemp[DISTRIK]","$returntemp[SHIP_TO_PARTY]","$ALAMAT","","$tgl_order","$returntemp[SHIP_TO_CODE]","$returntemp[ITEM_KANTONG]","$returntemp[KANTONG_DESC]","$VENDOR_NO","$VENDOR_NAME");
                                    $tablename="TB_DO_DATA";
                                    $this->DBinsert($this->conn, $field_names, $field_data, $tablename);
                                    // $this->DataAccess->OraInsert($field_names,$field_data,$tablename);	
                                    $this->no_do->Text =  $this->AutoNum($NODO,10);
                                    // $this->simpan->Visible = true;
                                    // $this->simpando->Visible = false;								
                                    
                                    $nilai = array('TIPE_ANTRI' => "'".$this->typeAntri->Text."'" ,'NODAFTAR' => "'".$this->nodaftar->Text."'");
                                    $kondisi = array('NO_BOOKING' => "'" . $LOGNO_BOOKING . "'" ,'DELETE_MARK' => '0' ,'STATUS_ANTRI' => '0', 'NO_DO' => "'" . $this->AutoNum($NODO,10) . "'");
                                    $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                                    // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                                    
                                    if($returntemp['NMORG'] == '7900'){
                                        $this->laporan->Text .= "DO MD Sukses Tercreate - ".$this->AutoNum($returntemp['ORDER_NO'],10)."<br />";
                                    }else{
                                        $this->laporan->Text .= "DO OPCO Sukses Tercreate - ".$this->AutoNum($returntemp['ORDER_NO'],10)."<br />";
                                    }
                                    
                                    // return true;
                                    
                                }
                            }
                        }else{							
                            $salah = 'D';
                            // echo "DO gagal di Update Ke table ZAPPSD_DATA_DO ".$NO_SO." <br />";
                            // echo "Mohon dilakukan pengecekan SO OPCO / MD  <br /> untuk QTY atau Kredit Limits <br />";
                            $this->_return["ERROR"] .= "DO gagal di Update Ke table ZAPPSD_DATA_DO ".$NO_SO." <br>";
                            $this->_return["ERROR"] .= "Mohon dilakukan pengecekan SO OPCO / MD <br> untuk QTY atau Kredit Limits <br>";
                            // echo $fceUPDATE->RETURN["TYPE"].": ".$fceUPDATE->RETURN["MESSAGE"]."<br>";
                        }
                    }else{						
                        $salah = 'D';
                        echo "DO gagal di Update Ke table ZAPPSD_DATA_DO <br />";
                    }				
                }else{
                    $salah = 'D';
                    $this->_return["ERROR"] .= "DO gagal di Generate <br />";					
                    // echo "DO gagal di Generate <br />";					
                }
            } else {
                echo $fce->PrintStatus();
            }    

            if ($salah == 'D'){
                if($this->_dtlso["ORG"] == '3000'){
                    $fceBOLNR = $this->sap->NewFunction ("Z_ZCSD_IO052_CLEAR_BOLNR");
                    if ($fceBOLNR == false ) {
                        $this->sap->PrintStatus();
                        exit;
                    }
                    $fceBOLNR->I_VBELN = $this->AutoNum($NODO,10);
                    $fceBOLNR->Call();
                    if ($fceBOLNR->GetStatus() == SAPRFC_OK ) {
                        $fceCOMMIT = $this->sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                        if ($fceDELDO == false ) {
                            $this->sap->PrintStatus();
                            exit;
                        }
                        $fceCOMMIT->Call();
                    }
                    //----------------------------------------------
                    //running rfc void do
                    $fceDELDO = $this->sap->NewFunction ("Z_ZCSD_DEL_DO");
                    if ($fceDELDO == false ) {
                        $this->sap->PrintStatus();
                        exit;
                    }
                    $fceDELDO->VBELN = $this->AutoNum($NODO,10);
                    $fceDELDO->Call();
                    if ($fceDELDO->GetStatus() == SAPRFC_OK ) {
                        $fceDELDO->RETURN->Reset();
                        while ( $fceDELDO->RETURN->Next() ){
                            $error=$fceDELDO->RETURN->row["TYPE"];
                            $msg=$fceDELDO->RETURN->row["MESSAGE"];
                            
                            ### LOG ANTRI
                            $nilai = array('DELETE_MARK' => 1);
                            $kondisi = array('NO_DO' => "'" . $this->AutoNum($NODO,10) . "'");
                            $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                            // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                            
                            // $this->cengenet->IsValid = false;
                            $this->_return["ERROR"] .= $error.': '.$msg.' / QTY SO anda Tidak Mencukupi / DO gagal Terbentuk (E1)';
                            // echo $error.' : '.$msg.' / QTY SO anda Tidak Mencukupi / DO gagal Terbentuk (E1)';
                        }
                        //Commit Transaction
                        $fceCOMMIT = $this->sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                        $fceCOMMIT->Call();
                        die;
                    }
                }else{
                    $DOKEDUA = '';
                    $VBELN1 = '';
                    $VOIDDO = '';
                    $FCEDOMD = $this->sap->NewFunction ("ZCSD_CEK_REFF_TRNS_MD");
                    if ($FCEDOMD == false ) {
                        $this->sap->PrintStatus();
                        exit;
                    }
                    $FCEDOMD->I_VKORG = $this->_dtlso["ORG"];
                    $FCEDOMD->I_WERKS = $this->_dtlso["PLANT"];
                    $FCEDOMD->I_NO_DO = $this->AutoNum($NODO,10);
                    $FCEDOMD->Call();
                    if ($FCEDOMD->GetStatus() == SAPRFC_OK ) {				
                        $VBELN1 = $FCEDOMD->WA_LIKP1['VBELN'];
                        $DOKEDUA = $FCEDOMD->WA_LIKP2["VBELN"];
                    }
                    if($VBELN1 != ''){
                        $cekdo_vbeln1 = $this->DBselect($this->conn, 'ORDER_ID', 'TB_DO_DATA', "DELETE_MARK = 0 AND ORDER_ID = $VBELN1");
                        // $cekdo_vbeln1 = $this->DataAccess->querySelect('TB_DO_DATA', array('ORDER_ID'), array('DELETE_MARK' => 0,'ORDER_ID' => $VBELN1));
                        $CEKDO_VBELN1 = $cekdo_vbeln1[0]['ORDER_ID'];
                        if($CEKDO_VBELN1 == ''){
                            $VOIDDO = 'V1';
                        }else{
                            $cekdo_vbeln2 = $this->DBselect($this->conn, 'ORDER_ID', 'TB_DO_DATA', "DELETE_MARK = 0 AND ORDER_ID = $DOKEDUA");
                            // $cekdo_vbeln2 = $this->DataAccess->querySelect('TB_DO_DATA', array('ORDER_ID'), array('DELETE_MARK' => 0,'ORDER_ID' => $DOKEDUA));
                            $CEKDO_DOKEDUA = $cekdo_vbeln2[0]['ORDER_ID'];
                            if($CEKDO_DOKEDUA == ''){
                                $VOIDDO = 'V1';
                            }else{
                                ###DO LENGKAP KEDUANYA
                            }
                        }
                    }else{
                        ### LOG ANTRI
                        $nilai = array('DELETE_MARK' => 1);
                        $kondisi = array('NO_DO' => "'" . $this->AutoNum($NODO,10) . "'");
                        $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                        // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                    }
                    
                    if($VOIDDO == 'V1'){
                        $DELDO1 = '';
                        $fceBOLNR = $this->sap->NewFunction ("Z_ZCSD_IO052_CLEAR_BOLNR");
                        if ($fceBOLNR == false ) {
                            $this->sap->PrintStatus();
                            exit;
                        }
                        $fceBOLNR->I_VBELN =  $this->AutoNum($VBELN1,10);
                        $fceBOLNR->Call();
                        if ($fceBOLNR->GetStatus() == SAPRFC_OK ) {
                            $fceCOMMIT = $this->sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                            $fceCOMMIT->Call();
                        }
                        $fceDELDO = $this->sap->NewFunction ("Z_ZCSD_DEL_DO");
                        if ($fceDELDO == false ) {
                            $this->sap->PrintStatus();
                            exit;
                        }
                        $fceDELDO->VBELN = $this->AutoNum($VBELN1,10);
                        $fceDELDO->Call();
                        if ($fceDELDO->GetStatus() == SAPRFC_OK ) {
                            $fceDELDO->RETURN->Reset();
                            while ( $fceDELDO->RETURN->Next() ){
                                $error=$fceDELDO->RETURN->row["TYPE"];
                                $msg=$fceDELDO->RETURN->row["MESSAGE"];
                                $this->_return["ERROR"] .= "<br />DO rollback". $VBELN1;
                                
                                ### LOG ANTRI
                                $nilai = array('DELETE_MARK' => 1);
                                $kondisi = array('NO_DO' => "'" . $this->AutoNum($VBELN1,10) . "'");
                                $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                                // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                                
                                $DELDO1 = 'S';
                            }
                            //Commit Transaction
                            $fceCOMMIT = $this->sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                            $fceCOMMIT->Call();
                        }
                        if($DELDO1 == 'S'){
                            $fceBOLNR = $this->sap->NewFunction ("Z_ZCSD_IO052_CLEAR_BOLNR");
                            if ($fceBOLNR == false ) {
                                $this->sap->PrintStatus();
                                exit;
                            }
                            $fceBOLNR->I_VBELN = $this->AutoNum($DOKEDUA,10);
                            $fceBOLNR->Call();
                            if ($fceBOLNR->GetStatus() == SAPRFC_OK ) {
                                $fceCOMMIT = $this->sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                                $fceCOMMIT->Call();
                            }
                            //----------------------------------------------
                            //running rfc void do
                            $fceDELDO = $this->sap->NewFunction ("Z_ZCSD_DEL_DO");
                            if ($fceDELDO == false ) {
                                $dsapDELDO->PrintStatus();
                                exit;
                            }
                            $fceDELDO->VBELN = $this->AutoNum($DOKEDUA,10);
                            $fceDELDO->Call();
                            if ($fceDELDO->GetStatus() == SAPRFC_OK ) {
                                $fceDELDO->RETURN->Reset();
                                while ( $fceDELDO->RETURN->Next() ){
                                    $error=$fceDELDO->RETURN->row["TYPE"];
                                    $msg=$fceDELDO->RETURN->row["MESSAGE"];
                                    $this->_return["ERROR"] .= "<br />DO rollback". $DOKEDUA;
                                    // echo "<br />DO rollback". $DOKEDUA;
                                    ### LOG ANTRI
                                    $nilai = array('DELETE_MARK' => 1);
                                    $kondisi = array('NO_DO' => "'" . $this->AutoNum($DOKEDUA,10) . "'");
                                    $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                                    // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);

                                    // $this->cengenet->IsValid = false;
                                    $this->_return["ERROR"] .= "<br>".$error.': '.$msg.' / QTY SO anda Tidak Mencukupi / DO gagal Terbentuk (E2)';
                                    // echo "<br>".$error.' : '.$msg.' / QTY SO anda Tidak Mencukupi / DO gagal Terbentuk (E2)';
                                    // die;
                                }
                                //Commit Transaction
                                $fceCOMMIT = $this->sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                                $fceCOMMIT->Call();
                            }
                        }
                    }
                }
                // die;
            }
        }
        return $this->_return;
    }

    function simpanClick(){
        // $new_id=$this->DataAccess->new_daftar_number(); // ganti kode dibawah
        $new_id = $this->DBselect($this->conn, 'TB_DAFTAR_SEQ.NEXTVAL', 'SYS.DUAL');

        $NOBOOKING = $this->_dtlso["SPP"];
        $NOPOLISI = $this->_dtlso["NOPOL"];
        $NOSIM = $this->_dtlso["NO_SIM"];
        $SOPIR = $this->_dtlso["DRIVER"];
        $RFID = $this->_truk[0]["NO_RFID"];
        // $priority = $this->priority->Text;
        $priority = 0;
        $STNK = $this->_truk[0]["NO_STNK"];
        $JAM = date('h:i');
        $datetime = date('d-m-Y H:i:s');
        $laporan = '';
        
        $jam_schedule_1 = $JAM;
        $jam_schedule_2 = date('H:i',strtotime('+7 hour',strtotime(date('H:i'))));

        // $this->_data_log = $this->DataAccess->querySelect('LOG_ANTRI', array('ID', 'NO_DO'), array('DELETE_MARK' => '0', 'STATUS_ANTRI' => '0', 'NO_BOOKING' => $NOBOOKING));
        $data_log = $this->DBselect($this->conn, 'ID, NO_DO', 'LOG_ANTRI', "NO_BOOKING = '".$NOBOOKING."' AND DELETE_MARK = '0' AND STATUS_ANTRI = '0'");
        $log_doID = $data_log["ID"];
        $NODO = $data_log["NO_DO"];
        
        $VENDOR_NO = $this->_dtlso["EXPEDITUR"];
        $VENDOR_NAME = $this->_dtlso["EXPEDITUR_NAME"];
        $ORDER_DATE = date("d-M-Y", strtotime($this->_dtlso["ORDER_DATE"]));
        $ITEM_UM = $this->_dtlso["UOM"];
        $ITEM_QTY = $this->_dtlso["QTY_DO"];
        $INV_CLASS = $this->_dtlso["INV"];
        $SO_TYPE = $this->_dtlso["TIPESO"];				

        // $IDDATETIME = $item2->IDDATETIME->Text; // date dari sovstruk untuk update sovstruk
        $NO_SO = $this->_dtlso["NO_SO"];
        $POSNR = $this->_dtlso["LINE"];
        $NOPOLISIUP = $this->_dtlso["NOPOL"];
        $ORDER_DATEUP = date("d.m.Y", strtotime($this->_dtlso["ORDER_DATE"]));

        if($log_doID != '' AND $NODO == $this->AutoNum($this->_cekAntri["NO_DO"],10)){
            // if($this->UpdateDOShipment()){ ### PENGECEKAN DATA DO
                ###AMBIL DATA DO
                $fceDO = $this->sap->NewFunction("Z_ZAPPSD_SELECT_DO_SHIP_N1");
                if ($fceDO == false) {
                    $dsapDO->PrintStatus();
                    exit;
                }
                ### PARAMETER
                $fceDO->XPARAM["ORDER_NO"] = $this->AutoNum($this->_cekAntri["NO_DO"],10);
                $fceDO->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                $fceDO->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];

                $fceDO->Call();
                if ($fceDO->GetStatus() == SAPRFC_OK) {
                    ## DISPLAY DATA
                    $fceDO->RETURN_DATA2->Reset();
                    while ($fceDO->RETURN_DATA2->Next()) {
                        $DO_ORDER_NO = $fceDO->RETURN_DATA2->row["ORDER_NO"];
                        $DO_ITEM_DESC = $fceDO->RETURN_DATA2->row["ITEM_DESC"];
                        $DO_ITEM_NO = $fceDO->RETURN_DATA2->row["ITEM_NO"];
                        $DO_ORDER_QTY = $fceDO->RETURN_DATA2->row["ORDER_QTY"];
                        $DO_JUMLAH_SEMEN = $fceDO->RETURN_DATA2->row["JUMLAH_SEMEN"];
                        $DO_LINE = $fceDO->RETURN_DATA2->row["LINE"];
                        $DO_ORDER_UM = $fceDO->RETURN_DATA2->row["ORDER_UM"];
                        $DO_KODE_INV_CLASS = $fceDO->RETURN_DATA2->row["KODE_INV_CLASS"];
                        $DO_KOTA_TUJUAN_NAME = $fceDO->RETURN_DATA2->row["KOTA_TUJUAN_NAME"];
                        $DO_NMPLAN = $fceDO->RETURN_DATA2->row["NMPLAN"];
                        $DO_SALES_ORD_NO = $fceDO->RETURN_DATA2->row["SALES_ORD_NO"];
                        $DO_DISTRIK = $fceDO->RETURN_DATA2->row["DISTRIK"];
                        $DO_SHIP_TO_PARTY = $fceDO->RETURN_DATA2->row["SHIP_TO_PARTY"];
                        $DO_SOLD_TO_CODE = $fceDO->RETURN_DATA2->row["SOLD_TO_CODE"];
                        $DO_PROP_TUJUAN = $fceDO->RETURN_DATA2->row["PROP_TUJUAN"];
                        $DO_INCOTERM = $fceDO->RETURN_DATA2->row["INCOTERM"];
                        $DO_USERID = $this->_dtlso["USERNAME"];
                        $DO_USERORG = $this->_dtlso["ORG"];

                        $data_Daftar = $this->DBselect($this->conn, 'NO_BOOKING', 'TB_DAFTAR', "DELETE_MARK = '0' AND NO_BOOKING = '$NOBOOKING'");
                        // $data_Daftar = $this->DataAccess->querySelect('TB_DAFTAR', array('NO_BOOKING'), array('DELETE_MARK' => 0,'NO_BOOKING' => $NOBOOKING));

                        if($data_Daftar['NO_BOOKING'] == ''){
                            ### INSERT TB DAFTAR
                            $DETAIL_field_names=array('DAFTAR_ID','NO_DO','TIPE_SEMEN','NO_ITEM','QTY_1','QTY_2','PENALTY_MARK','ACTIVE_MARK','DELETE_MARK','LINE_ID','ORDER_ID','ORDER_UM1','ORDER_UM2','INV_CLASS','PROP_TUJUAN','ORGN_CODE','KOTA_TUJUAN','FROM_WHSE','NO_SO','DISTRIK','SHIPTO','TIPESO','TRUK_ID');
                            $DETAIL_field_data=array($new_id["NEXTVAL"],"$DO_ORDER_NO","$DO_ITEM_DESC","$DO_ITEM_NO","$DO_ORDER_QTY","$DO_JUMLAH_SEMEN","0","0","0","$DO_LINE","$DO_ORDER_NO","$DO_ORDER_UM","KG","$INV_CLASS","$DO_PROP_TUJUAN","$DO_USERORG","$DO_KOTA_TUJUAN_NAME","$DO_NMPLAN","$DO_SALES_ORD_NO","$DO_DISTRIK","$DO_SHIP_TO_PARTY","$SO_TYPE","$NOPOLISI");
                            $DETAIL_tablename="TB_DAFTAR_DETAIL";
                            $this->DBinsert($this->conn, $DETAIL_field_names, $DETAIL_field_data, $DETAIL_tablename);
                            // $this->DataAccess->OraInsert($DETAIL_field_names,$DETAIL_field_data,$DETAIL_tablename); 
                            // $this->laporan->Text .= "Sukses daftar detail <br />";
                            
                            $DAFTAR_field_names=array('ID','NO_BOOKING','USER_ID','TGL_BOOKING','TGL_SCHEDULE','JAM_SCHEDULE_1','JAM_SCHEDULE_2','ACTIVE_MARK','DELETE_MARK','NO_ANTRIAN','JALUR_ANTRIAN_ID','PENALTY_MARK','DISTRIBUTOR_ID','SHIP_QTY','JAM_PASS','LAST_UPDATE_DATE','LAST_UPDATED_BY','PROP_TUJUAN','KOTA_TUJUAN','FROM_WHSE','TIPE_TRANSAKSI','DISTRIK','KD_EXP','NM_EXP','STATUS_CHECKIN','TRUK_ID','NO_STNK','SUPIR','SIM','RFID','PRIORITY','STATUS_UPLOAD','USER_CHECKIN','TGL_CHECK_IN','CETAK','USER_CHECK_IN');
                            $DAFTAR_field_data=array($new_id["NEXTVAL"],"$NOBOOKING","999","SYSDATE","$ORDER_DATE","$jam_schedule_1","$jam_schedule_2","0","0","0","0","0","$DO_SOLD_TO_CODE","$ITEM_QTY","$JAM","SYSDATE","999","$DO_PROP_TUJUAN","$DO_KOTA_TUJUAN_NAME","$DO_NMPLAN","SHP-$DO_INCOTERM","$DO_DISTRIK","$VENDOR_NO","$VENDOR_NAME","1","$NOPOLISI","$STNK","$SOPIR","$NOSIM","$RFID","$priority","1","$DO_USERID","SYSDATE","1","999");
                            $DAFTAR_tablename="TB_DAFTAR";
                            $this->DBinsert($this->conn, $DAFTAR_field_names, $DAFTAR_field_data, $DAFTAR_tablename);
                            // $this->DataAccess->OraInsert($DAFTAR_field_names,$DAFTAR_field_data,$DAFTAR_tablename);
                            // $this->laporan->Text .= "Sukses daftar <br />";
                            
                            // $sukses = 1;
                        // }else{
                            // $sukses = 1;
                        }
                    }				
                } else{
                    $fceDO->PrintStatus();
                }
                
                // if($sukses == '1'){
                    /*
                    * update by Mujib 01-07-2015, requested by Suhermon
                    * hardcoding vehicle type to 300
                    * old: $vtype = $this->TruckType($dsap, $_REQUEST['nopol']);
                    */            
                    $vtype = '300';
                    $ceklocksystem = $this->DBselect($this->conn, 'LOCK_SYSTEM', 'TB_PLANT', "KODE_PLANT = '".$this->_dtlso["PLANT"]."' AND DELETE_MARK=0");
                    $CEKLOCK_SYSTEM = explode(';', $ceklocksystem["LOCK_SYSTEM"]);
                    // $ceklocksystem = $this->DataAccess->querySelect('TB_PLANT', array('LOCK_SYSTEM'), array('KODE_PLANT' => $this->User->Plant));
                    $fce = '';
                    #### TAMBAHA MD
                    if($this->_dtlso["ORG"] == '7900'){
                        $fce = $this->sap->NewFunction("ZAPPSD_INSERT_TRANSAKSI2_N3_MD");
                    }ELSE{
                        $fce = $this->sap->NewFunction("Z_ZAPPSD_INSERT_TRANSAKSI2_N3");
                    }
                    #### TAMBAHA MD
                    if ($fce == false) {
                        $this->sap->PrintStatus();
                        exit;
                    }

                    $laporan = '';

                    ### HEADER ENTRI
                    $fce->I_NOCEK = 'X'; ### Tidak Pengecekan Sopir
                    $fce->XPARAM["NMORG"] = $this->_dtlso["ORG"];
                    $fce->XPARAM["NMPLAN"] = $this->_dtlso["PLANT"];
                    $fce->XPARAM["NO_BOOKING"] = $NOBOOKING;
                    $fce->XPARAM["ATTRIBUTE2"] = $NOBOOKING;
                    $fce->XPARAM["STATUS_TRANS"] = '20'; //10 for daftar antri, 2 for timb masuk, 3 gdg kantong, 4 bg counter, 5 timb keluar, 6closing.

                    $fce->XPARAM["NO_ADJ"] = $this->_cekAntri["NO_DAFTAR"]; //sementara
                    $fce->XPARAM["JALUR_ANTRI"] = $this->_cekAntri["NO_ANTRI"]; //sementara
                    $fce->XPARAM["TIPE_ANTRI"] = $this->_cekAntri["TIPE_ANTRI"]; //sementara
                    $fce->XPARAM["JALUR_CONVEYOR"] = ''; //sementara dari Boomer						

                    $fce->XPARAM["NO_POLISI"] = $NOPOLISI;
                    $fce->XPARAM["NO_STNK"] = $this->_truk[0]["NO_STNK"];
                    $fce->XPARAM["NAMA_SUPIR"] = $SOPIR;
                    $fce->XPARAM["NO_SIM"] = $NOSIM;					

                    $fce->XPARAM["TGL_ANTRI"] = date('Ymd');
                    $fce->XPARAM["JAM_ANTRI"] = date('His');
                    $fce->XPARAM["PTGS_ANTRIAN"] = $this->_dtlso["USERNAME"];
                    $fce->XPARAM["STS_ANTRI_PRINT"] = '0';
                    $fce->XPARAM["CNT_PRINT_ANTRI"] = '0';
                    $fce->XPARAM["STS_MASUK_PRINT"] = '0';
                    $fce->XPARAM["CNT_PRINT_MASUK"] = '0';
                    $fce->XPARAM["STS_KELUAR_PRINT"] = '0';
                    $fce->XPARAM["CNT_PRINT_KELUAR"] = '0';
                    $fce->XPARAM["NO_EXPEDITUR"] = $VENDOR_NO;
                    $fce->XPARAM["NAMA_EXPEDITUR"] = $VENDOR_NAME;

                    if($CEKLOCK_SYSTEM[0] == '1'){
                        $fce->XPARAM["ID_CARD"] = $this->_dtlso["IDCARD"]; // proximitynum w?
                        $fce->XPARAM["TIPE_TRUK"] = '';
                    }elseif($CEKLOCK_SYSTEM[0] == '0'){
                        $fce->XPARAM["ID_CARD"] = $RFID;
                        $fce->XPARAM["TIPE_TRUK"] = $vtype; //$this->nopol->Text;
                    }
                    
                    $fce->XPARAM["DEL"] = '0'; //delete mark
                    $fce->XPARAM["SHP_POINT"] = $this->_dtlso["PLANT"];
                    
                    $fce->XPARAM["LAST_UPDATE_DATE"] = date('Ymd');
                    $fce->XPARAM["LAST_UPDATE_TIME"] = date('His');
                    $fce->XPARAM["LAST_UPDATED_BY"] = $this->_dtlso["USERNAME"];

                    ### DETAIL ANTRI
                    $jmlBerat = 0;
                    $jmlBeratKg = 0;
                    $jmlKantongAct = 0;
                    // foreach ($this->DtGrid->Items as $item) {
                    $fce->XPARAM_LINE->row["NMORG"] = $this->_dtlso["ORG"];
                    $fce->XPARAM_LINE->row["ORG_DESC"] = $this->_dtlso["ORG_DESC"];
                    $fce->XPARAM_LINE->row["NMPLAN"] = $this->_dtlso["PLANT"];
                    // $fce->XPARAM_LINE->row["PLAN_DESC"] = $item->NMPLAN_NAME->Text; w?

                    $fce->XPARAM_LINE->row["PLANT_RCV"] = $fceDO->RETURN_DATA2->row["NMPLAN2_CODE"];
                    $fce->XPARAM_LINE->row["PLANT_RCV_NAME"] = $fceDO->RETURN_DATA2->row["NMPLAN2_NAME"];
                    $fce->XPARAM_LINE->row["NO_DO"] = $this->_cekAntri["NO_DO"]; //NO DO
                    $fce->XPARAM_LINE->row["LINE"] = $this->_dtlso["LINE"]; //POSNR
                    $fce->XPARAM_LINE->row["NO_SO"] = $this->_dtlso["NO_SO"]; //NO SO
                    // $fce->XPARAM_LINE->row["NO_PO"] = $item->NO_PO->Text; //NO PO w?
                    // $fce->XPARAM_LINE->row["LINE_PO"] = $item->LINE_PO->Text; //FROM_WHSE
                    $fce->XPARAM_LINE->row["STATUS_TRANS"] = '20'; // STATUS TRANSAKSI
                    $fce->XPARAM_LINE->row["FROM_WHSE"] = $fceDO->RETURN_DATA2->row["FROM_WHSE"]; //FROM_WHSE w?
                    $fce->XPARAM_LINE->row["TO_WHSE"] = $fceDO->RETURN_DATA2->row["TO_WHSE"]; //TO_WHSE
                    $fce->XPARAM_LINE->row["ITEM_NO"] = $fceDO->RETURN_DATA2->row["ITEM_NO"]; //KODE MATERIAL
                    $fce->XPARAM_LINE->row["ITEM_DESC"] = $fceDO->RETURN_DATA2->row["ITEM_DESC"]; //NAMA MATERIAL
                    $fce->XPARAM_LINE->row["ITEM_QTY"] = $fceDO->RETURN_DATA2->row["ORDER_QTY"]; //ORDER_QTY
                    $fce->XPARAM_LINE->row["TOTAL_QTY"] = $fceDO->RETURN_DATA2->row["JUMLAH_SEMEN"];//BERAT DO DALAM KG
                    $fce->XPARAM_LINE->row["TOTAL_UM"] = $fceDO->RETURN_DATA2->row["UM_JUMLAH_SEMEN"]; //UM JUMLAH SEMEN
                    $fce->XPARAM_LINE->row["ITEM_UM"] = $fceDO->RETURN_DATA2->row["ORDER_UM"];//UM MATERIAL
                    $fce->XPARAM_LINE->row["LSTEL"] = $fceDO->RETURN_DATA2->row["LSTEL"];
                    $fce->XPARAM_LINE->row["ORDER_DATE"] = $this->_dtlso["ORDER_DATE"]; //TGL SCHEDULE
                    $fce->XPARAM_LINE->row["NO_ORA"] = $fceDO->RETURN_DATA2->row["NO_ORA"];//
                    $fce->XPARAM_LINE->row["LFART"] = $fceDO->RETURN_DATA2->row["LFART"];  //TIPE TRANSAKSI
                    $fce->XPARAM_LINE->row["INCOTERM"] = $this->_dtlso["TRANSAKSI"]; //INCOTERM
                    $fce->XPARAM_LINE->row["INCOTERM_DESC"] = $this->_dtlso["NAMA_TRANSAKSI"]; //NAMA INCOTERM
                    $fce->XPARAM_LINE->row["SOLD_TO_CODE"] = $this->_dtlso["SOLD_TO_CODE"]; //KODE SOLD TO PARTY
                    $fce->XPARAM_LINE->row["SHIP_TO_CODE"] = $this->_dtlso["SHIP_TO_CODE"]; //KODE SHIP TO PARTY
                    $fce->XPARAM_LINE->row["SOLD_TO_PARTY"] = $this->_dtlso["SOLDTO_NAME"]; //NAMA SOLD TO PARTY
                    $fce->XPARAM_LINE->row["SHIP_TO_PARTY"] = $this->_dtlso["SHIPTO_NAME"]; //NAMA SHIP TO PARTY

                    if ($this->_dtlso["UOM"] != 'TO') {
                        $fce->XPARAM_LINE->row["NO_KTG"] = $fceDO->RETURN_DATA2->row["ITEM_KANTONG"];//$item->NO_KTG->Text; //KODE KANTONG
                        $fce->XPARAM_LINE->row["KTG_DESC"] = $fceDO->RETURN_DATA2->row["KANTONG_DESC"]; //NAMA KANTONG 
                        $fce->XPARAM_LINE->row["KTG_SHORT"] = $fceDO->RETURN_DATA2->row["KANTONG_SHORTDES"];//

                        $fce->XPARAM_LINE->row["NO_KTG_ACT"] = $fceDO->RETURN_DATA2->row["ITEM_KANTONG"];//$item->NO_KTG->Text; //KODE KANTONG
                        $fce->XPARAM_LINE->row["KTG_DESC_ACT"] = $fceDO->RETURN_DATA2->row["KANTONG_DESC"];//$item->KTG_DESC->Text; //NAMA KANTONG
                        $fce->XPARAM_LINE->row["JML_KTG_ACT"] = $fceDO->RETURN_DATA2->row["ORDER_QTY"];//

                        $fce->XPARAM_LINE->row["CHG_KTG"] = '10'; //DEFAULT TETAP
                        $fce->XPARAM_LINE->row["NO_KTG_ALT"] = $fceDO->RETURN_DATA2->row["KANTONG_ALT_II"];//
                        $fce->XPARAM_LINE->row["KTG_DESC_ALT"] = $fceDO->RETURN_DATA2->row["KANTONG_ALT_DESC"];//
                        $fce->XPARAM_LINE->row["KTG_SHORT_ALT"] = $fceDO->RETURN_DATA2->row["ALT_SHORT_DESC"]; //
                        $fce->XPARAM_LINE->row["UOM_KTG"] = $fceDO->RETURN_DATA2->row["UOM_ITEM_KANTONG"]; //
                    }
                    $fce->XPARAM_LINE->row["KODE_PROP"] = $fceDO->RETURN_DATA2->row["KODE_PROP"];//KODE PROPINSI
                    $fce->XPARAM_LINE->row["PROP_NAME"] = $fceDO->RETURN_DATA2->row["PROP_TUJUAN"];//NAMA PROPINSI
                    $fce->XPARAM_LINE->row["KODE_KOTA"] = $fceDO->RETURN_DATA2->row["KODE_KOTA_TUJUAN"];//KODE KOTA
                    $fce->XPARAM_LINE->row["KOTA_NAME"] = $this->_dtlso["NAMA_KOTA"]; //NAMA KOTA
                    $fce->XPARAM_LINE->row["DISTRIK"] = $this->_dtlso["KOTA"]; //DISTRIK
                    
                    $fce->XPARAM["BZIRK"] = $this->_dtlso["KOTA"]; //distrik terakhir yang diambil
                    $fce->XPARAM["BZTXT"] = $this->_dtlso["NAMA_KOTA"]; //distrik terakhir yang diambil

                    $fce->XPARAM_LINE->row["ATT_CATEGORY"] = $fceDO->RETURN_DATA2->row["ATT_CATEGORY"]; //
                    $fce->XPARAM_LINE->row["INV_CLASS"] = $fceDO->RETURN_DATA2->row["KODE_INV_CLASS"]; //INV CLASS
                    $fce->XPARAM_LINE->row["INV_CLASS_NAME"] = $fceDO->RETURN_DATA2->row["INV_CLASS_NAME"]; //NAMA INV CLASS 
                    $fce->XPARAM_LINE->row["UMREZ"] = $fceDO->RETURN_DATA2->row["UMREZ"]; //
                    $fce->XPARAM_LINE->row["ROUTE"] = $fceDO->RETURN_DATA2->row["ROUTE"]; //ROUTE

                    if($this->_dtlso["PLANT"] == '3401'){	
                        if($fceDO->RETURN_DATA2->row["ORDER_UM"] == 'ZAK'){
                            //Add by mujib 28 dec 2012 for rfid sms center ==============================				
                            $luarsumbar = false;
                            if (trim($fceDO->RETURN_DATA2->row["KODE_PROP"]) != '' && trim($fceDO->RETURN_DATA2->row["KODE_PROP"]) != '1012')//sales
                                $luarsumbar = true;
                            elseif (trim($fceDO->RETURN_DATA2->row["KODE_PROP"]) == '' && trim($fceDO->RETURN_DATA2->row["KODE_PROP"]) != '1210')//sto
                                $luarsumbar = true;

                            if ($luarsumbar) {
                                $fce->XPARAM["KODE_CHECKPOINT"] = $this->_dtlso["PLANT"]; //Check point seharusnya
                                $fce->XPARAM["NAMA_CHECKPOINT"] = 'Belum CheckPoint'; //Check point dilokasi
                            }//End Add by mujib 28 dec 2012 =============================================
                        }

                        $fce->XPARAM_LINE->row["VENDOR_NO"] = $VENDOR_NO;
                        $fce->XPARAM_LINE->row["VENDOR_NAME"] = $VENDOR_NAME;						
                    }else{
                        $fce->XPARAM_LINE->row["VENDOR_NO"] =  $VENDOR_NO;
                        $fce->XPARAM_LINE->row["VENDOR_NAME"] =  $VENDOR_NAME;			
                    }
                    
                    $fce->XPARAM_LINE->row["CREATE_DATE"] = date('Ymd');
                    $fce->XPARAM_LINE->row["CREATE_TIME"] = date('H:i:s');
                    $fce->XPARAM_LINE->row["CREATED_BY"] = $this->_dtlso["USERNAME"];
                    $fce->XPARAM_LINE->row["DEL"] = '0';

                    $fce->XPARAM_LINE->Append($fce->XPARAM_LINE->row);
                    $jmlBerat = $jmlBerat + (int) $fceDO->RETURN_DATA2->row["ORDER_QTY"];
                    $jmlBeratKg = $jmlBeratKg + (int) $fceDO->RETURN_DATA2->row["JUMLAH_SEMEN"];

                    $lfart = $fceDO->RETURN_DATA2->row["LFART"];
                    $lstel = $fceDO->RETURN_DATA2->row["LSTEL"];
                    $isCurah = $this->_dtlso["CURAH_OR_BAG"];
                    if ($isCurah == '20') {// khusus zak
                        $jmlKantongAct = $jmlBerat;
                    }
                    $actNoKantong = $fceDO->RETURN_DATA2->row["ITEM_KANTONG"];
                    $actKantongDsc = $fceDO->RETURN_DATA2->row["KANTONG_DESC"];
                    // }
                    if ($isCurah == '20') {
                        $fce->XPARAM["NO_KTG"] = $actNoKantong;
                        $fce->XPARAM["KTG_DESC"] = $actKantongDsc;
                        $fce->XPARAM["NO_KTG_ACT"] = $actNoKantong;
                        $fce->XPARAM["KTG_DESC_ACT"] = $actKantongDsc;
                        $fce->XPARAM["JML_KTG_ACT"] = $jmlKantongAct;
                        $fce->XPARAM["CHG_KTG"] = '10'; //TETAP
                    }

                    $fce->XPARAM["TOTAL_QTY"] = $jmlBeratKg;

                    $fce->XPARAM["ORDER_TYPE"] = $lfart;
                    $fce->XPARAM["LOADING_POINT"] = $lstel;
                    $fce->XPARAM["BERAT_TOTAL_DO"] = $jmlBerat; //$qty hit dr DO;	
                    $fce->XPARAM["CURAH_OR_BAG"] = $isCurah;

                    // var_dump($fce);
                    // echo "<br>last call";return;
                    $fce->Call();
                    
                    $ada_error = false;
                    if ($fce->GetStatus() == SAPRFC_OK and ! $ada_error) {
                        #### TAMBAHA MD
                        if (trim($fce->RETURN["TYPE"]) == 'E') {
                            $this->_return["ERROR"] .= $fce->RETURN["TYPE"] . ": " . $fce->RETURN["MESSAGE"] . "<br>" ;
                            // echo "<br>".$fce->RETURN["TYPE"] . " : " . $fce->RETURN["MESSAGE"] . "<br>" ;
                            $this->_return["ERROR"] .= "TIDAK BERHASIL DISIMPAN ,SILAHKAN DI COBA LAGI..";
                            $ada_error = true;
                            $sukses_antri = '0';
                        }else{
                            if($this->_dtlso["ORG"] == '7900'){
                                $fce->RETURN_NO_TRANSAKSI->Reset();
                                while ($fce->RETURN_NO_TRANSAKSI->Next()) {	
                                    $notrx = $fce->RETURN_NO_TRANSAKSI->row['NO_TRANSAKSI'];
                                    // echo "Antrian ".$fce->RETURN_NO_TRANSAKSI->row['NMORG'] ." sukses di buat dengan nomor :" . $notrx . "<br>";
                                    if($fce->RETURN_NO_TRANSAKSI->row['NMORG'] == '7900'){
                                        $this->_return["ANTRI_MD"] = $notrx;
                                        $notrans = $notrx;

                                        // $this->cetak->Visible = true;
                                        // $urlnyo = $this->Service->constructUrl('shp.op.print.prnt_daftar', array('trxnum' => $notrx));
                                        // $this->cetak->PopupUrl = $urlnyo;
                                        $sukses_antri = '1';
                                    } else {
                                        $this->_return["ANTRI_OPCO"] = $notrx;
                                    }
                                }
                            }else{
                                if (strlen($fce->XDATA_RETURN) > 0) {
                                    echo "Antrian sukses di buat dengan nomor :" . $fce->XDATA_RETURN . "<br>";
                                    $notrx = $fce->XDATA_RETURN;
                                    $notrans = $notrx;

                                    // $this->cetak->Visible = true;
                                    // $urlnyo = $this->Service->constructUrl('shp.op.print.prnt_daftar', array('trxnum' => $notrx));
                                    // $this->cetak->PopupUrl = $urlnyo;
                                    $sukses_antri = '1';
                                }
                            }
                        }
                        #### TAMBAHA MD
                    }
                    
                    // $this->laporan->Text .= $laporan;

                    // $fce->Close();
                    // $dsap->Close();
                    
                    if ($sukses_antri == 1) {							
                        ### LOG ANTRI
                        $nilai = array('STATUS_ANTRI' => 1,'LAST_UPDATE_BY' => "'".$this->_dtlso["USERNAME"]."'",'LAST_UPDATE_AT' => "'".$datetime."'");
                        $kondisi = array('ID' => "'" . $log_doID . "'");
                        $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
                        // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                        
                        #### UPDATE STATUS BOOKING #####
                        // $fceUPDATEANTRI = $this->sap->NewFunction ("ZUPDATE_ZAPPSD_SOVSTRUK");
                        // if ($fceUPDATEANTRI == false ) {
                        //     $this->sap->PrintStatus();
                        //     exit;
                        // }
                        // $fceUPDATEANTRI->ORG = $this->_dtlso["ORG"];
                        // $fceUPDATEANTRI->PLANT = $this->_dtlso["PLANT"];
                        // $fceUPDATEANTRI->EDATU = $ORDER_DATEUP;
                        // $fceUPDATEANTRI->NO_SO = $NO_SO;
                        // $fceUPDATEANTRI->POSNR = $POSNR;
                        // $fceUPDATEANTRI->EXPEDITUR = $VENDOR_NO;
                        // $fceUPDATEANTRI->NO_POLISI = $NOPOLISIUP;
                        // // $fceUPDATEANTRI->IDDATETIME = $IDDATETIME;
                        // $fceUPDATEANTRI->UPDATE_BY = 'antri_semenpadang';
                        // $fceUPDATEANTRI->STATUS = '1';
                        // $fceUPDATEANTRI->NO_TRANSAKSI = $notrans;
                        // $fceUPDATEANTRI->DEL_MARK =0;
                        // $fceUPDATEANTRI->Call();
                        // if($fceUPDATEANTRI->GetStatus() == SAPRFC_OK ) {
                        //     if(trim($fceUPDATEANTRI->E_MSG["TYPE"]) == 'S'){
                        //         $this->laporan->Text .= "Update Status Booking Sukses";
                        //     }
                        // }						
                        // $this->simpan->Visible = false;

                    }
                // }
            // }
        }else{
            // $this->cengenet->IsValid = false;
            $this->_return["ERROR"] .= "Lakukan Antri Ulang untuk No Booking berikut ".$NOBOOKING;
            // echo "Lakukan Antri Ulang untuk No Booking berikut ".$NOBOOKING;
        }
        return $this->_return;
    }

    function insertKartuProx(){
        try
            {
                $fce = $this->sap->NewFunction ("Z_ZAPPSD_INSERT_KARTU");
                if ($fce == false ) {
                $this->sap->PrintStatus();
                exit;
                }
            
                //Param Export
                $panjang=strlen(strval($this->_dtlso["IDCARD"]));
                if($panjang==1)$newid='000000'.$this->dtlso["IDCARD"];
                if($panjang==2)$newid='00000'.$this->dtlso["IDCARD"];
                if($panjang==3)$newid='0000'.$this->dtlso["IDCARD"];
                if($panjang==4)$newid='000'.$this->dtlso["IDCARD"];
                if($panjang==5)$newid='00'.$this->dtlso["IDCARD"];
                if($panjang==6)$newid='0'.$this->dtlso["IDCARD"];
                if($panjang==7)$newid=$this->dtlso["IDCARD"];
                $this->dtlso["IDCARD"]=$newid;
                $fce->XPARAM["IDCARD"] = $this->dtlso["IDCARD"];
                $fce->XPARAM["NMORG"] = $this->_dtlso["ORG"];
                $fce->XPARAM["NMPLAN"] = $this->_dtlso["PLANT"];
                $fce->XPARAM["STATUS"] = '10';
                $fce->XPARAM["DEL"] = '0';
                $fce->XPARAM["CREATED_BY"] = $this->_dtlso["USERNAME"];
                $fce->XPARAM["CREATE_DATE"] = date('Ymd');
                    
                //Execute Function
                $fce->Call();
                $fce->Close();			
                $dsap->Close();
                
            }
            catch(Exception $e) // an exception is raised if a query fails will be raised
            {
                $this->_return["ERROR"] .= $e->getMessage();
            }
    }

    function getKartuIDcard(){
        $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_KARTU");
        if ($fce == false) {
            $this->sap->PrintStatus();
            exit;
        }

        if($this->_dtlso["ORG"] == '7900'){
            // $this->_koneksi = $this->DataAccess->getConnOraEx();
            // $sql= "SELECT PLANT_OPCO,COM_OPCO FROM ZMD_MAPPING_PLANT
            // WHERE PLANT_MD = '$PLANT' AND  DEL=0";
            // $query = oci_parse($this->_koneksi, $sql);
            // oci_execute($query);
            // $row=oci_fetch_assoc($query);
            $row = $this->DBselect($this->conncsms, 'PLANT_OPCO, COM_OPCO', 'ZMD_MAPPING_PLANT', "PLANT_MD = '".$this->_dtlso["PLANT"]."' AND  DEL=0");
        }
        //Param Export
        $panjang = strlen(strval($this->_dtlso["IDCARD"]));
        if ($panjang == 1)
            $newid = '000000' . $this->_dtlso["IDCARD"];
        if ($panjang == 2)
            $newid = '00000' . $this->_dtlso["IDCARD"];
        if ($panjang == 3)
            $newid = '0000' . $this->_dtlso["IDCARD"];
        if ($panjang == 4)
            $newid = '000' . $this->_dtlso["IDCARD"];
        if ($panjang == 5)
            $newid = '00' . $this->_dtlso["IDCARD"];
        if ($panjang == 6)
            $newid = '0' . $this->_dtlso["IDCARD"];
        if ($panjang == 7)
            $newid = $this->_dtlso["IDCARD"];
        $this->_dtlso["IDCARD"] = $newid;
        $fce->XPARAM["IDCARD"] = $this->_dtlso["IDCARD"];
        // $fce->XPARAM["STATUS"] = '10';
        if($this->_dtlso["ORG"] == '7900'){
            $fce->XDATA_APP["NMORG"] = $row["COM_OPCO"];
            $fce->XDATA_APP["NMPLAN"] = $row["PLANT_OPCO"];
            $i=0;
            $this->_Dkartu=array();
        }else{
            $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
            $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];
        }
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->RETURN_DATA->Reset();
            while ($fce->RETURN_DATA->Next()) {
                IF($this->_dtlso["ORG"] == '7900'){
                    $this->_Dkartu[$i]["KARNMORG"] = $fce->RETURN_DATA->row["NMORG"];				
                    $this->_Dkartu[$i]["NMPLAN"] = $fce->RETURN_DATA->row["NMPLAN"];				
                    $this->_Dkartu[$i]["IDCARD"] = $fce->RETURN_DATA->row["IDCARD"];	
                    $status = $fce->RETURN_DATA->row["STATUS"];
                }else{
                    $status = $fce->RETURN_DATA->row["STATUS"];
                }
            }
        }	
        if ($this->_dtlso["IDCARD"] == ''){
            // $this->proximitynum->focus();
            // $this->PnlBtnTruk->Visible = false;	
            // $this->cengenet->IsValid = false;
            // $this->cengenet->Text = "Mohon Entri Id Card" ;
            $this->_return["ERROR"] .= "Mohon Entri Id Card";
        }else{
            if ($status == '10') {
                if(!$this->_cekAntri["STATUSEROR"]){
                    return true;
                }elseif($this->_cekAntri["STATUSEROR"] == 0){
                    if($this->_dtlso["ORG"] == '7900'){
                        $this->buatAntriClick();
                        return $this->_Dkartu;
                    }else{
                        $this->buatAntriClick();
                        return true;
                    }
                }elseif($this->_cekAntri["STATUSEROR"] == 1){
                    if($this->_dtlso["ORG"] == '7900'){
                        $this->buatAntriClick();
                        return $this->_Dkartu;
                    }else{
                        if ($this->UpdateDOShipment()){	
                            // $this->PnlBtnTruk->Visible = true;
                            // $this->simpando->Visible = false;
                            // $this->simpan->Visible = true;					
                        }	
                    }
                }elseif($this->_cekAntri["STATUSEROR"] == 2){
                    if($this->_dtlso["ORG"] == '7900'){
                        $this->buatAntriClick();
                        return $this->_Dkartu;
                    }else{
                        if ($this->UpdateDOShipment()){	
                            $this->buatAntriClick();						
                            $this->_cekAntri["NO_DAFTAR"] = rand();
                            // $this->nodaftar->Text = rand();
                            // $this->PnlBtnTruk->Visible = true;
                            // $this->simpando->Visible = false;
                            // $this->simpan->Visible = true;
                        }
                    }
                }
            } elseif ($status == '20') {
                // $this->PnlBtnTruk->Visible = false;	
                // $this->cengenet->IsValid = false;
                // $this->cengenet->Text = "Status Kartu Masih Digunakan..";
                $this->_return["ERROR"] .= "Status Kartu Masih Digunakan..";
                return false;
            }else{
                // $this->PnlBtnTruk->Visible = false;	
                // $this->cengenet->IsValid = false;
                // $this->cengenet->Text = "Kartu Belum di daftarkan pada plant ".$this->User->Plant;
                $this->_return["ERROR"] .= "Kartu Belum di daftarkan pada plant ".$this->_dtlso["PLANT"];
                return false;
            }
        }
        $fce->Close();
    }

    function UpdateDOShipment(){ ### OTOMATIS UPDATE
        ###AMBIL DATA DO
        $fceDO = $this->sap->NewFunction("Z_ZAPPSD_SELECT_DO_SHIP_N1");
        if ($fceDO == false) {
            $this->sap->PrintStatus();
            exit;
        }
        ### PARAMETER
        $fceDO->XPARAM["ORDER_NO"] = $this->AutoNum($this->_cekAntri["NO_DO"],10);
        $fceDO->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
        $fceDO->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];
        
        $fceDO->Call();
        if ($fceDO->GetStatus() == SAPRFC_OK) {				
            ### MESSAGE DATA Z DATA DO
            if(trim($fceDO->RETURN["TYPE"]) == 'E'){
                $UpdateDO = '1';
                // $this->laporan->Text .= $this->AutoNum($this->no_do->Text,10).' - '.trim($fceDO->RETURN["MESSAGE"]).' Proses Update Data DO Otomatis . . . <br />';
            }else{
                // $this->_data_do_cek = $this->DataAccess->querySelect('TB_DO_DATA', array('ORDER_NO'), array('ORDER_NO' => $this->AutoNum($this->no_do->Text,10)));
                $data_do_cek = $this->DBselect($this->conn, 'ORDER_NO', 'TB_DO_DATA', "ORDER_NO = ".$this->AutoNum($this->_cekAntri["NO_DO"],10)."");
                $CEK_NODO = $data_do_cek['ORDER_NO'];
                if($CEK_NODO == ''){
                    $UpdateDO = '1';
                    echo  $this->AutoNum($this->_cekAntri["NO_DO"],10).' - '.trim($fceDO->RETURN["MESSAGE"]).' Proses Update Data DO Oracle Otomatis . . . <br />';
                }else{			
                    $LOGNO_BOOKING=$this->_dtlso["SPP"];
                    $nilai = array('TIPE_ANTRI' => "'".$this->_cekAntri["TIPE_ANTRI"]."'" ,'NODAFTAR' => "'".$this->_cekAntri["NO_DAFTAR"]."'");
                    $kondisi = array('NO_BOOKING' => "'" . $LOGNO_BOOKING . "'" ,'DELETE_MARK' => '0' ,'STATUS_ANTRI' => '0', 'NO_DO' => "'" . $this->AutoNum($this->_cekAntri["NO_DO"],10) . "'");
                    $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                    echo  $this->AutoNum($this->_cekAntri["NO_DO"],10).' - Data DO Lengkap pada System <br />';
                    return true;
                }					
            }
        }
        
        if($UpdateDO == '1'){
            $LOGNO_BOOKING=$this->_dtlso["SPP"];
            $LOG_USERID=$this->_dtlso["USENAME"];
            $LOG_NOPOLISI=$this->_dtlso["NOPOL"];

            $VENDOR_NO = $this->_dtlso["EXPEDITUR"];
            $VENDOR_NAME = $this->_dtlso["EXPEDITUR_NAME"];
            $ORDER_DATE = $this->_dtlso["ORDER_DATE"];
            $ITEM_QTY = $this->_dtlso["QTY_DO"];
            $INV_CLASS = $this->_dtlso["INV"];
            $SO_TYPE = $this->_dtlso["TYPESO"];
            $ALAMAT = $this->_dtlso["ALAMAT"];
            $NO_KTG = $this->_dtlso["NO_KTG"];
            $KTG_DESC = $this->_dtlso["KTG_DESC"];
            $SALES_UNIT = $this->_dtlso["UOM"];

            #### TAMBAHA MD
            $fceUPDATE = '';
            if ($this->_dtlso["ORG"] == '7900') {
                $fceUPDATE = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_DO_SHIPMENT2MD");
            }else{
                $fceUPDATE = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_DO_SHIPMENT2");
            }
            #### TAMBAHA MD
            if ($fceUPDATE == false) {
                $this->sap->PrintStatus();
                exit;
            }

            $fceUPDATE->X_VKORG = $this->_dtlso["ORG"];
            $fceUPDATE->X_WERKS = $this->_dtlso["PLANT"];
            $fceUPDATE->X_LFART = $this->getLFART($SO_TYPE);
            $fceUPDATE->X_VBELN = $this->AutoNum($this->_cekAntri["NO_DO"],10);

            $fceUPDATE->Call();
            $returntemp = array();
            if ($fceUPDATE->GetStatus() == SAPRFC_OK) {
                if ($fceUPDATE->RETURN["TYPE"] == "S") {
                    $fceUPDATE->RETURN_DATA->Reset();
                    while ($fceUPDATE->RETURN_DATA->Next()) {
                        $returntemp = $fceUPDATE->RETURN_DATA->row;
                        $order_date=$returntemp['ORDER_DATE'];
                        $thn=substr($order_date,0,4);
                        $bln=(substr($order_date,4,2));
                        $tgl=substr($order_date,6,3);
                        $tgl_order=$tgl.'-'.$bln.'-'.$thn;
                        $jj=$j+2;
                        $tanggal=$tgl_sch[$jj];	
                        
                        if($SALES_UNIT != 'TO'){
                            $fcep = $this->sap->NewFunction ("Z_ZAPPSD_UPDATE_DO");
                            if ($fcep == false ) {
                                $this->sap->PrintStatus();
                                #echo "Koneksi ke RFC terputus!, mohon coba kembali beberapa saat lagi";
                                exit;
                            }

                            $fcep->NO_ORDER = $this->AutoNum($this->_cekAntri["NO_DO"],10);
                            $fcep->ITEM_KANTONG = $NO_KTG;
                            $fcep->KANTONG_DESC = $KTG_DESC;
                            $fcep->Call();
                            if ($fcep->GetStatus() == SAPRFC_OK ) {
                                if(trim($fcep->RETURN["TYPE"]) == 'S'){
                                    $field_names=array('ORDER_ID','LINE_ID','ORDER_NO','DISTRIBUTOR','DISTRIBUTOR_ID','QTY_1','TIPE_SEMEN','ITEM_NO','INV_CLASS_NAME','QTY_2','STATUS_ASSIGN','INV_CLASS','ORDER_UM1','ORDER_UM2','PROP_TUJUAN','ORGN_CODE','ATTRIBUTE_CATEGORY','OP_ORDR_TYPE','KOTA_TUJUAN','ORDER_DATE','PRESALES_ORD_NO','PLANT','INCOTERM','DISTRIK','SHIPTO','ALAMAT','HARGA_SATUAN','TGL_SCEDULE','KD_SHIPTO','KODE_KANTONG','KANTONG','KD_EXP','NM_EXP');
                                    $field_data=array("$returntemp[ORDER_NO]","$returntemp[LINE]","$returntemp[ORDER_NO]","$returntemp[SOLD_TO_PARTY]","$returntemp[SOLD_TO_CODE]","$returntemp[ORDER_QTY]","$returntemp[ITEM_DESC]","$returntemp[ITEM_NO]","$returntemp[INV_CLASS_NAME]","$returntemp[JUMLAH_SEMEN]","1","$returntemp[KODE_INV_CLASS]","$returntemp[ORDER_UM]","$returntemp[UM_JUMLAH_SEMEN]","$returntemp[PROP_TUJUAN]","$returntemp[NMORG]","$returntemp[ATT_CATEGORY]","$returntemp[LFART_DWN]","$returntemp[KOTA_TUJUAN_NAME]","instgl_$tgl_order","$returntemp[SALES_ORD_NO]","$returntemp[NMPLAN]","$returntemp[INCOTERM]","$returntemp[DISTRIK]","$returntemp[SHIP_TO_PARTY]","$ALAMAT","","$tgl_order","$returntemp[SHIP_TO_CODE]","$NO_KTG","$KTG_DESC","$VENDOR_NO","$VENDOR_NAME");
                                    $tablename="TB_DO_DATA";
            $this->DBinsert($this->conn, $field_names, $LOG_field_data, $tablename);
            // $this->DataAccess->OraInsert($field_names,$field_data,$tablename);
                                    
                                    $nilai = array('TIPE_ANTRI' => "'".$this->typeAntri->Text."'" ,'NODAFTAR' => "'".$this->nodaftar->Text."'");
                                    $kondisi = array('NO_BOOKING' => "'" . $LOGNO_BOOKING . "'" ,'DELETE_MARK' => '0' ,'STATUS_ANTRI' => '0', 'NO_DO' => "'" . $this->AutoNum($this->no_do->Text,10) . "'");
            $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
            // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                                    
                                    $this->laporan->Text .= 'Sukses '.$this->AutoNum($this->no_do->Text,10).' - '.$fceUPDATE->RETURN["MESSAGE"].'<br />';
                                    return true;
                                }
                            }
                        }else{
                            $field_names=array('ORDER_ID','LINE_ID','ORDER_NO','DISTRIBUTOR','DISTRIBUTOR_ID','QTY_1','TIPE_SEMEN','ITEM_NO','INV_CLASS_NAME','QTY_2','STATUS_ASSIGN','INV_CLASS','ORDER_UM1','ORDER_UM2','PROP_TUJUAN','ORGN_CODE','ATTRIBUTE_CATEGORY','OP_ORDR_TYPE','KOTA_TUJUAN','ORDER_DATE','PRESALES_ORD_NO','PLANT','INCOTERM','DISTRIK','SHIPTO','ALAMAT','HARGA_SATUAN','TGL_SCEDULE','KD_SHIPTO','KODE_KANTONG','KANTONG','KD_EXP','NM_EXP');
                            $field_data=array("$returntemp[ORDER_NO]","$returntemp[LINE]","$returntemp[ORDER_NO]","$returntemp[SOLD_TO_PARTY]","$returntemp[SOLD_TO_CODE]","$returntemp[ORDER_QTY]","$returntemp[ITEM_DESC]","$returntemp[ITEM_NO]","$returntemp[INV_CLASS_NAME]","$returntemp[JUMLAH_SEMEN]","1","$returntemp[KODE_INV_CLASS]","$returntemp[ORDER_UM]","$returntemp[UM_JUMLAH_SEMEN]","$returntemp[PROP_TUJUAN]","$returntemp[NMORG]","$returntemp[ATT_CATEGORY]","$returntemp[LFART_DWN]","$returntemp[KOTA_TUJUAN_NAME]","instgl_$tgl_order","$returntemp[SALES_ORD_NO]","$returntemp[NMPLAN]","$returntemp[INCOTERM]","$returntemp[DISTRIK]","$returntemp[SHIP_TO_PARTY]","$ALAMAT","","$tgl_order","$returntemp[SHIP_TO_CODE]","$returntemp[ITEM_KANTONG]","$returntemp[KANTONG_DESC]","$VENDOR_NO","$VENDOR_NAME");
                            $tablename="TB_DO_DATA";
            $this->DBinsert($this->conn, $field_names, $LOG_field_data, $tablename);
            // $this->DataAccess->OraInsert($field_names,$field_data,$tablename);

                            $nilai = array('TIPE_ANTRI' => "'".$this->typeAntri->Text."'" ,'NODAFTAR' => "'".$this->nodaftar->Text."'");
                            $kondisi = array('NO_BOOKING' => "'" . $LOGNO_BOOKING . "'" ,'DELETE_MARK' => '0' ,'STATUS_ANTRI' => '0', 'NO_DO' => "'" . $this->AutoNum($this->no_do->Text,10) . "'");
            $this->DBupdate($this->conn, 'LOG_ANTRI', $nilai, $kondisi);
            // $this->DataAccess->queryUpdate('LOG_ANTRI', $nilai, $kondisi);
                            
                            $this->laporan->Text .= 'Sukses '.$this->AutoNum($this->_cekAntri["NO_DO"],10).' - '.$fceUPDATE->RETURN["MESSAGE"].'<br />';
                            return true;
                        }
                    }
                }else{
                    // $this->cengenet->IsValid = false;
                    echo "No Booking $LOGNO_BOOKING ini harus dilakukan cancel DO";
                    return false;
                }
            }
        }
    }

    function buatAntriClick(){
        if (($this->_dtlso["PLANT"] == '3401')||($this->_dtlso["PLANT"] == '7912')||($this->_dtlso["PLANT"] == '79H6')||($this->_dtlso["PLANT"] == '79M6')){
        ### UNTUK PLANT 3401
            if($this->_dtlso["UOM"] == 'ZAK'){ ### UNTUK 3401 = ZAK
                //$this->noantri->Text="A001";
                //$inv ='10013020'; // untuk pcc
                //$lfart = 'ZLF'; // untuk do
                // ambil nilai untuk mulai antrian
                // ambil tipe antrian berdasarkan :
                // lfart untuk menentukan tipe DO (sales Atau STO)
                // PPC dan SMC gabung jalur H kode invclass
                // Tipe I Luar Sumbar (STO DAN SALES) => A,B,C,D kode inv
                // PCC (UNTUK STO DAN SALES) => E,F,G,K kode inv
                // SUMBANGAN dan KAPAL( STO TYPE I ) => L	lfart dan kode inv
                // Tipe I sumbar ( SALES ) => I,J	kode inv dan lfart
                //CHANGED PER 15 AJN 2011
                // TIPE 1 JALUR UNUTK OPC SUMBAR DAN NON SUMBAR INV 100 110 200 (A B)
                // TIPE 2 JALUR UNTUK PCC SUMBAR INV 100 130 200 DAN PROP 1012 (SUMBAR)
                // TIPE 3 JALUR UNTUK PCC LUAR SUMBAR INV 100 130 200 PROP != 1012
                // TIPE 4 JALUR SMC DAN PPC 100 140 200 N 100 120 200
                // TIPE 5 JALUR KAPAL DAN SUMBANGAN LFART = ZNL AND ZLC KOTA = 3801
                // TIPE 6 TYPE LAINNYA OTHERS
                if (($this->_dtlso["PLANT"] == '3401')||($this->_dtlso["PLANT"] == '7912')||($this->_dtlso["PLANT"] == '79H6')||($this->_dtlso["PLANT"] == '79M6'))
                {// Jika PPI  Z_ZAPPSD_INSERT_SY_SYS_DO Z_ZAPPSD_SELECT_JALUR
                    $lfart = $this->getLFART($this->_dtlso["TIPESO"]); //ex. tipedo: zor
                    $inv = $this->_dtlso["INV"]; //ex: 100130200
                    $prop = $this->_dtlso["PROVINSI"]; //ex: 1025
                    $kode_kota = $this->_dtlso["DISTRIK"]; //ex: 251001

                    if ($inv != "" and $lfart != "") {
                        if ($lfart == "ZLC" or ( $lfart == "ZNL" and $kode_kota == "3801"))
                            $tipe = "005";
                        elseif ($lfart == "ZLC" or ( $lfart == "ZNL" and $kode_kota == "7975"))
                            $tipe = "005";
                        elseif ($inv == "100110200")
                            $tipe = "001";
                        elseif ($inv == "100130200" and $prop == "1012")
                            $tipe = "002";
                        elseif ($inv == "100130200" and $prop != "1012")
                            $tipe = "003";
                        elseif ($inv == "100140200" or $inv == "100120200")
                            $tipe = "004";
                        else
                            $tipe = "006";
                        
                        // if($this->priority->Text=='1'){$tipe = "007";}; // can't find priority exclude 0
                    }else {
                        $tipe = "SALAH";
                    }
                    
                    if ($tipe == "SALAH") {
                        // $this->PnlBtnTruk->Visible = false;
                        // $this->rfid->focus();
                        // $this->cengenet->IsValid = false;
                        // $this->cengenet->Text = "TIPE ANTRIAN BELUM TERDAFTAR.. ";
                        $this->_return["ERROR"] .= "TIPE ANTRIAN BELUM TERDAFTAR.. ";
                        // echo "TIPE ANTRIAN BELUM TERDAFTAR.. ";
                        // die;
                    } else {
                        $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_SY_SYS_DO");
                        if ($fce == false) {
                            $this->sap->PrintStatus();
                            exit;
                        }

                        //Param Export
                        $fce->XPARAM["TYPE_DOC"] = $tipe;
                        $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                        $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];

                        //Execute Function
                        $fce->Call();

                        if ($fce->GetStatus() == SAPRFC_OK) {
                            $fce->RETURN_DATA->Reset();
                            //Display Tables
                            while ($fce->RETURN_DATA->Next()) {
                                $assign = $fce->RETURN_DATA->row["LAST_ASSIGN"];
                            }
                        }
                        $assign += 1;

                        $fce->Close();

                        $no_antri = $this->no_antrian($assign, $tipe);
                        $cekpas = substr($no_antri, 0, -2);

                        $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_JALUR");
                        if ($fce == false) {
                            $dsap->PrintStatus();
                            exit;
                        }

                        //Param Export
                        $fce->XPARAM["JALUR"] = $cekpas;
                        $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                        $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];

                        //Execute Function
                        $fce->Call();

                        if ($fce->GetStatus() == SAPRFC_OK) {
                            $fce->RETURN_DATA->Reset();
                            //Display Tables
                            while ($fce->RETURN_DATA->Next()) {
                                $status_jalur = $fce->RETURN_DATA->row["STATUS"];
                                $maks_jalur = $fce->RETURN_DATA->row["MAKS"];
                                $antri_jalur = $fce->RETURN_DATA->row["ANTRI"];
                                $jumlah_jalur = $fce->RETURN_DATA->row["JUMLAH"];
                            }
                        }
                        $fce->Close();
                        //$dsap->Close();
                        if ($status_jalur == "0") {

                            $fce = $this->sap->NewFunction("Z_ZAPPSD_CREATE_ANTRI");
                            if ($fce == false) {
                                $dsap->PrintStatus();
                                exit;
                            }
                            $antri_jalur+=1;
                            $jumlah_jalur+=1;
                            if ($maks_jalur == $jumlah_jalur) {
                                $status_jalur = '10';
                            }

                            $fce->XDATA_KEY = $cekpas;
                            $fce->XTYPE_DOC = $tipe;

                            $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                            $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];

                            $fce->XDATA_UPD["STATUS"] = $status_jalur;
                            $fce->XDATA_FLAG["STATUS"] = 'X';

                            $fce->XDATA_UPD["ANTRI"] = $antri_jalur;
                            $fce->XDATA_FLAG["ANTRI"] = 'X';
                            $fce->XDATA_UPD["JUMLAH"] = $jumlah_jalur;
                            $fce->XDATA_FLAG["JUMLAH"] = 'X';

                            $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');
                            $fce->XDATA_UPD["LAST_UPDATED_BY"] =  $this->_dtlso["USERNAME"];

                            $fce->XDATA_FLAG["LAST_UPDATE_DATE"] = 'X';
                            $fce->XDATA_FLAG["LAST_UPDATED_BY"] = 'X';
                            $fce->Call();

                            if ($fce->GetStatus() == SAPRFC_OK) {
                                if (trim($fce->RETURN["TYPE"]) == 'E') {
                                    $this->_return["ERROR"] .= $fce->RETURN["TYPE"] . ": " . $fce->RETURN["MESSAGE"] . "<br>";
                                    // echo  $fce->RETURN["TYPE"] . " : " . $fce->RETURN["MESSAGE"] . "<br>";
                                    // die;
                                } else {
                                    $fce->Close();

                                    $fce = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_SY_SYS_DO");
                                    if ($fce == false) {
                                        $dsap->PrintStatus();
                                        exit;
                                    }

                                    //Param Export
                                    $fce->XDATA_KEY = $tipe;
                                    $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                                    $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];

                                    $fce->XDATA_UPD["LAST_ASSIGN"] = $assign;
                                    $fce->XDATA_FLAG["LAST_ASSIGN"] = 'X';
                                    //Execute Function
                                    $fce->Call();

                                    $fce->Close();
                                    if($this->_cekAntri["STATUSEROR"] == 2){
                                        $this->_cekAntri["NO_DAFTAR"] = $assign;
                                        // echo "<br>no_daftar2 ".$this->_cekAntri["NO_DAFTAR"];
                                        $this->_return["ERROR"] .= " tidak dapat melanjutkan simpan DO";
                                        // echo "tidak dapat melanjutkan simpan DO";die;
                                        // $this->simpando->Visible = false;
                                        // $this->simpan->Visible = false;
                                        $this->_cekAntri["NO_ANTRI"] = $no_antri;
                                        $this->_cekAntri["TIPE_ANTRI"] = $tipe;
                                    }else{
                                        $this->_cekAntri["NO_DAFTAR"] = $assign;
                                        // echo "<br>no_daftar3 ".$this->_cekAntri["NO_DAFTAR"];
                                        // $this->simpando->Visible = true;
                                        // $this->simpan->Visible = false;
                                        $this->_cekAntri["NO_ANTRI"]  = $no_antri;
                                        $this->_cekAntri["TIPE_ANTRI"] = $tipe;
                                    }
                                }
                            }

                        } else {
                            // $this->PnlBtnTruk->Visible = false;
                            // $this->rfid->focus();
                            // $this->cengenet->IsValid = false;
                            // echo "JALUR ANTRIAN " . $cekpas . " TELAH PENUH..";
                            // die;
                            return $this->_return["ERROR"] = "JALUR ANTRIAN " . $cekpas . " TELAH PENUH..";
                        }
                    }
                } else {
                    // $this->simpan->Visible = true;
                    $this->_cekAntri["NO_ANTRI"] = 'A0';
                    $this->_cekAntri["TIPE_ANTRI"] = '999';
                }
            }else{ ### UNTUK 3401 = CURAH OR BAG
                if($this->proximitynum->Text != ''){
                    if($this->_cekAntri["STATUSEROR"] == 2){
                        if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                            $this->insertKartuProx();
                            $this->laporan->Text .= "Data Kartu UPDATE<br />";
                        }
                        if ($this->UpdateDOShipment()){
                            $this->_cekAntri["NO_DAFTAR"] = rand();
                            $this->_cekAntri["NO_ANTRI"] = 'X0';
                            $this->_cekAntri["TIPE_ANTRI"] = '999';
                            // $this->nodaftar->Text = rand();
                            // $this->noantri->Text='X0';
                            // $this->typeAntri->Text='999';				
                            // $this->PnlBtnTruk->Visible = true;
                            // $this->simpando->Visible = false;
                            // $this->simpan->Visible = true;
                        }					
                    }elseif($this->_cekAntri["STATUSEROR"] == 1){
                        if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                            $this->insertKartuProx();
                            // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                        }
                        if ($this->UpdateDOShipment()){
                            $this->_cekAntri["NO_ANTRI"] = 'X0';
                            $this->_cekAntri["TIPE_ANTRI"] = '999';
                            // $this->noantri->Text='X0';
                            // $this->typeAntri->Text='999';
                            // $this->simpando->Visible = false;
                            // $this->simpan->Visible = true;					
                        }
                    }else{
                        if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                            $this->insertKartuProx();
                            // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                        }
                        $this->_cekAntri["NO_ANTRI"] = 'X0';
                        $this->_cekAntri["TIPE_ANTRI"] = '999';
                        // $this->simpando->Visible = true;
                        // $this->simpan->Visible = false;
                        // $this->noantri->Text = 'X0';
                        // $this->typeAntri->Text = '999';
                    }					
                }else{
                    if($this->_cekAntri["STATUSEROR"] == 2){
                        $this->_cekAntri["NO_ANTRI"] = 'X0';
                        $this->_cekAntri["TIPE_ANTRI"] = '999';
                        // $this->simpando->Visible = false;
                        // $this->simpan->Visible = false;
                        // $this->noantri->Text='X0';
                        // $this->typeAntri->Text='999';					
                    }else{
                        $this->_cekAntri["NO_ANTRI"] = 'X0';
                        $this->_cekAntri["TIPE_ANTRI"] = '999';
                        // $this->simpando->Visible = true;
                        // $this->simpan->Visible = false;
                        // $this->noantri->Text = 'X0';
                        // $this->typeAntri->Text = '999';
                    }					
                }
                if($this->_cekAntri["STATUSEROR"] == 2){
                    if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                        $this->insertKartuProx();
                        // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                    }
                    $this->_cekAntri["NO_ANTRI"] = 'X0';
                    $this->_cekAntri["TIPE_ANTRI"] = '999';
                    // $this->simpando->Visible = false;
                    // $this->simpan->Visible = false;
                    // $this->noantri->Text='X0';
                    // $this->typeAntri->Text='999';
                }else{
                    if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                        $this->insertKartuProx();
                        // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                    }
                    $this->_cekAntri["NO_ANTRI"] = 'X0';
                    $this->_cekAntri["TIPE_ANTRI"] = '999';
                    // $this->simpando->Visible = true;
                    // $this->simpan->Visible = false;
                    // $this->noantri->Text='X0';
                    // $this->typeAntri->Text='999';
                }
            }
        }else{ ### UNTUK PLANT != 3401
            /*
            $this->_jarak= $this->DataAccess->querySelect('TB_PLANT', array('KODE_PLANT','LANGITUDE','LATITUDE','RADIUS','AKTIVASI_CEK_RADIUS'), array('KODE_PLANT' => $this->User->Plant));
            $lati=$this->_jarak[0]["LANGITUDE"];
            $lngi=$this->_jarak[0]["LATITUDE"];
            $radius=$this->_jarak[0]["RADIUS"];
            $aktivasi=$this->_jarak[0]["AKTIVASI_CEK_RADIUS"];
            
            $this->_api= $this->DataAccess->querySelect('TB_API', array('KODE','NM_API','ALAMAT','ID_USERNAME','API_KEY'), array('DELETE_MARK' => '1' ,'APLIKASI' => 'EPOOOL', 'KODE' => 'EP13'));
            $id_user=$this->_api[0]["ID_USERNAME"];
            $api_key=$this->_api[0]["API_KEY"];
            $alamat=$this->_api[0]["ALAMAT"];
            
            $incot=$this->inco->Text;
            if ( ($aktivasi=='3') or ($aktivasi=='1' and $incot == 'SHP-STO') or ($aktivasi=='2' and $incot == 'SHP-FRC')){	
                
                $data_detail = array(
                    'id_username' => $id_user,
                    'api_key' => $api_key,
                    'no_plat' => array( $this->nopol->Text)
                    //'no_plat' => array('BA-8069-BU')
                );
                
                $res = $this->get_content($alamat,json_encode($data_detail));
                $myArray = json_decode($res, true);
                $kode=$myArray['code'];
                $no_plat=$myArray['data_truck'][0]['no_plat'];
                $lng=$myArray['data_truck'][0]['lng'];
                $lat=$myArray['data_truck'][0]['lat'];
                $lokasi=$myArray['data_truck'][0]['alamat'];
                
                if($kode==1){
                    ## cara penggunaannya
                    ## contoh ada 2 koordinat (latitude dan longitude)
                    //$a =$this->_jarak[0]["LATITUDE"].",".$this->_jarak[0]["LANGITUDE"];
                    $a = '"'.$lati.','.$lngi.'"';
                    $b = '"'.$lat.','.$lng.'"';
                    $nilai=distHaversine($a, $b);
                    $nilai_format = number_format($nilai);
                    
                    if ($nilai_format >=  $this->_jarak[0]["RADIUS"]){
                        $this->PnlBtnTruk->Visible = false;	
                        $this->cengenet->IsValid = false;
                        $this->cengenet->Text = "Mobil ".$no_plat." berada pada jarak  ".$nilai_format." M dari Post Pendaftaran, Mobil bisa di daftarkan Jika mobil berada pada jarak ".$this->_jarak[0]["RADIUS"]. "M Dari Pos Pendaftaran" ;		
                    } else {
                        $this->laporan->Text=  "Mobil ".$no_plat." berada pada jarak  ".$nilai_format." M dari Post Pendaftaran" ;
                    }
                }else{
                    $this->PnlBtnTruk->Visible = false;	
                    $this->cengenet->IsValid = false;
                    $this->cengenet->Text = "Mobil ".$no_plat." Belum Menggunakan GPS, Untuk pemuatan FRC Harus menggunakan GPS";					
                }
            }
            $this->PnlBtnTruk->Visible = true;
            */
            if($this->proximitynum->Text != ''){
                if($this->_cekAntri["STATUSEROR"] == 2){
                    if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                        $this->insertKartuProx();
                        // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                    }
                    if ($this->UpdateDOShipment()){
                        $this->_cekAntri["NO_DAFTAR"] = rand();
                        $this->_cekAntri["NO_ANTRI"] = 'X0';
                        $this->_cekAntri["TIPE_ANTRI"] = '999';
                        // $this->nodaftar->Text = rand();
                        // $this->noantri->Text='X0';
                        // $this->typeAntri->Text='999';
                        // $this->PnlBtnTruk->Visible = true;
                        // $this->simpando->Visible = false;
                        // $this->simpan->Visible = true;
                    }
                }elseif($this->_cekAntri["STATUSEROR"] == 1){
                    if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                        $this->insertKartuProx();
                        // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                    }
                    if ($this->UpdateDOShipment()){
                        $this->_cekAntri["NO_ANTRI"] = 'X0';
                        $this->_cekAntri["TIPE_ANTRI"] = '999';
                        // $this->noantri->Text='X0';
                        // $this->typeAntri->Text='999';
                        // $this->simpando->Visible = false;
                        // $this->simpan->Visible = true;
                    }
                }else{
                    if($this->_Dkartu[0]["KARNMORG"] != $this->_dtlso["ORG"] AND $this->_Dkartu[0]["NMPLAN"] != $this->_dtlso["PLANT"]){
                        $this->insertKartuProx();
                        // $this->laporan->Text .= "Data Kartu UPDATE<br />";
                    }
                    $this->_cekAntri["NO_ANTRI"] = 'X0';
                    $this->_cekAntri["TIPE_ANTRI"] = '999';
                    // $this->simpando->Visible = true;
                    // $this->simpan->Visible = false;
                    // $this->noantri->Text = 'X0';
                    // $this->typeAntri->Text = '999';
                }				
            }else{
                $this->PnlBtnTruk->Visible=true;
                if($this->_cekAntri["STATUSEROR"] == 2){
                    $this->_cekAntri["NO_ANTRI"] = 'X0';
                    $this->_cekAntri["TIPE_ANTRI"] = '999';
                    // $this->simpando->Visible = false;
                    // $this->simpan->Visible = false;
                    // $this->noantri->Text='X0';
                    // $this->typeAntri->Text='999';					
                }else{
                    $this->_cekAntri["NO_ANTRI"] = 'X0';
                    $this->_cekAntri["TIPE_ANTRI"] = '999';
                    // $this->simpando->Visible = true;
                    // $this->simpan->Visible = false;
                    // $this->noantri->Text='X0';
                    // $this->typeAntri->Text='999';
                }
            }
        }
    }

    function MatchingPOSTO($param){
        $_dtldo = array();
        $_insertdo = array();
        $_truk = array();
        $_sopir = array();

        //// DATA TRUK
        $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_TRUK");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC select truck";
            // exit;
        }
        // $fce->XPARAM["NOPOLISI"] = $this->formatnopolisi($param["NOPOL"]);
        $fce->XPARAM["NOPOLISI"] = $param["NOPOL"];
        $fce->XPARAM["STATUS"] = '0';
        $fce->XDATA_APP["NMORG"] = $param["ORG"];
        $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] = "Error truck: ".$fce->RETURN["MESSAGE"];
            }
            $fce->RETURN_DATA->Reset();
            while ($fce->RETURN_DATA->Next()) {
                $_truk["MANDT"] = $fce->RETURN_DATA->row["MANDT"];
                $_truk["ID"] = $fce->RETURN_DATA->row["ID"];
                $_truk["NOPOLISI"] = $fce->RETURN_DATA->row["NOPOLISI"];
                $_truk["NOSTNK"] = $fce->RETURN_DATA->row["NOSTNK"];
                $_truk["NMPLAN"] = $fce->RETURN_DATA->row["NMPLAN"];
                $_truk["NMORG"] = $fce->RETURN_DATA->row["NMORG"];
                $_truk["NAMA_KAPAL"] = $fce->RETURN_DATA->row["NAMA_KAPAL"];
                $_truk["VEHICLE_TYPE"] = $fce->RETURN_DATA->row["VEHICLE_TYPE"];
                $_truk["MODE_OFTRANSPORT"] = $fce->RETURN_DATA->row["MODE_OFTRANSPORT"];
                $_truk["TRAYEK"] = $fce->RETURN_DATA->row["TRAYEK"];
                $_truk["TAHUNPEMBUATAN"] = $fce->RETURN_DATA->row["TAHUNPEMBUATAN"];
                $_truk["DATE_MULAI_EXP"] = $fce->RETURN_DATA->row["DATE_MULAI_EXP"];
                $_truk["JAM_EXPIRE"] = $fce->RETURN_DATA->row["JAM_EXPIRE"];
                $_truk["JAM_MULAI_EXP"] = $fce->RETURN_DATA->row["JAM_MULAI_EXP"];
                $_truk["BERAT_TRUK"] = $fce->RETURN_DATA->row["BERAT_TRUK"];
                $_truk["NOMOR_MESIN"] = $fce->RETURN_DATA->row["NOMOR_MESIN"];
                $_truk["STATUS"] = $fce->RETURN_DATA->row["STATUS"];
                $_truk["KAPASITAS"] = $fce->RETURN_DATA->row["KAPASITAS"];
                $_truk["LOADINGCAPASITY"] = $fce->RETURN_DATA->row["LOADINGCAPASITY"];
                $_truk["UNLOADING_CPASTY"] = $fce->RETURN_DATA->row["UNLOADING_CPASTY"];
                $_truk["NO_EXPEDITUR"] = $fce->RETURN_DATA->row["NO_EXPEDITUR"];
                $_truk["NAMA_EXPEDITUR"] = $fce->RETURN_DATA->row["NAMA_EXPEDITUR"];
                $_truk["NO_RFID"] = $fce->RETURN_DATA->row["NO_RFID"];
                $_truk["WARNA_PLAT"] = $fce->RETURN_DATA->row["WARNA_PLAT"];
                $_truk["STATUSKEPEMILKAN"] = $fce->RETURN_DATA->row["STATUSKEPEMILKAN"];
                $_truk["QUANTITY_ALLOWED"] = $fce->RETURN_DATA->row["QUANTITY_ALLOWED"];
                $_truk["NO_TANGKI"] = $fce->RETURN_DATA->row["NO_TANGKI"];
                $_truk["HIDROLIK"] = $fce->RETURN_DATA->row["HIDROLIK"];
                $_truk["PANJANG_KAPAL"] = $fce->RETURN_DATA->row["PANJANG_KAPAL"];
                $_truk["DRAUGH_KAPAL"] = $fce->RETURN_DATA->row["DRAUGH_KAPAL"];
                $_truk["SYSTEM_BONGKAR"] = $fce->RETURN_DATA->row["SYSTEM_BONGKAR"];
                $_truk["JUMLAH_CRANE"] = $fce->RETURN_DATA->row["JUMLAH_CRANE"];
                $_truk["JUMLAH_PALKA"] = $fce->RETURN_DATA->row["JUMLAH_PALKA"];
                $_truk["KAPASITAS_CRANE"] = $fce->RETURN_DATA->row["KAPASITAS_CRANE"];
                $_truk["KECEPATAN_KAPAL"] = $fce->RETURN_DATA->row["KECEPATAN_KAPAL"];
                $_truk["JENIS_BAK"] = $fce->RETURN_DATA->row["JENIS_BAK"];
                $_truk["LAST_TRANSAKSI"] = $fce->RETURN_DATA->row["LAST_TRANSAKSI"];
                $_truk["DEL"] = $fce->RETURN_DATA->row["DEL"];
                $_truk["CREATE_BY"] = $fce->RETURN_DATA->row["CREATE_BY"];
                $_truk["CREATED_DATE"] = $fce->RETURN_DATA->row["CREATED_DATE"];
                $_truk["LAST_UPDATED_BY"] = $fce->RETURN_DATA->row["LAST_UPDATED_BY"];
                $_truk["LAST_UPDATE_DATE"] = $fce->RETURN_DATA->row["LAST_UPDATE_DATE"];
                $_truk["FLAG"] = $fce->RETURN_DATA->row["FLAG"];
                $_truk["SAP"] = $fce->RETURN_DATA->row["SAP"];
                $_truk["OBJNR"] = $fce->RETURN_DATA->row["OBJNR"];
                $_truk["EQKTX"] = $fce->RETURN_DATA->row["EQKTX"];
                $_truk["JMUAT"] = $fce->RETURN_DATA->row["JMUAT"];
                $_truk["FLEET_NUM"] = $fce->RETURN_DATA->row["FLEET_NUM"];
                $_truk["ZZJBI"] = $fce->RETURN_DATA->row["ZZJBI"];
                $_truk["ZZRMES"] = $fce->RETURN_DATA->row["ZZRMES"];
                $_truk["DRIVER_NAME"] = $fce->RETURN_DATA->row["DRIVER_NAME"];
                $_truk["DRIVER_LICENSE"] = $fce->RETURN_DATA->row["DRIVER_LICENSE"];
                $_truk["PALLET"] = $fce->RETURN_DATA->row["PALLET"];
                $_truk["VENDORGPS"] = $fce->RETURN_DATA->row["VENDORGPS"];
                $_truk["NM_VENDORGPS"] = $fce->RETURN_DATA->row["NM_VENDORGPS"];
                $_truk["SUBVEND_CODE"] = $fce->RETURN_DATA->row["SUBVEND_CODE"];
                $_truk["SUBVEND_NAME"] = $fce->RETURN_DATA->row["SUBVEND_NAME"];
                $_truk["MUATAN_TRUK"] = $fce->RETURN_DATA->row["MUATAN_TRUK"];
            }
        }
        $fce->Close();

        //// DATA SOPIR
        $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_SOPIR");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC select sopir";
            // exit;
        }
        $fce->XPARAM["NAMA_SOPIR"]=strtoupper($param["DRIVER"]);
        $fce->XPARAM["NO_SIM"] = $param["NO_SIM"];
        $fce->XPARAM["STATUS"] = '0';
        $fce->XDATA_APP["NMORG"] = $param["ORG"];
        $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] = "Error sopir: ".$fce->RETURN["MESSAGE"];
            }
            $fce->RETURN_DATA->Reset();
            while ($fce->RETURN_DATA->Next()) {
                $_sopir["ID"] = $fce->RETURN_DATA->row["ID"];
                $_sopir["NO_SIM"] = $fce->RETURN_DATA->row["NO_SIM"];
                $_sopir["NMPLAN"] = $fce->RETURN_DATA->row["NMPLAN"];
                $_sopir["NMORG"] = $fce->RETURN_DATA->row["NMORG"];
                $_sopir["NAMA_SOPIR"] = $fce->RETURN_DATA->row["NAMA_SOPIR"];
                $_sopir["STATUS"] = $fce->RETURN_DATA->row["STATUS"];
                $_sopir["DEL"] = $fce->RETURN_DATA->row["DEL"];
            }
        }
        $fce->Close();

        //// CEK KARTU
        if($param["PLANT"] == '3409'){
            $this->_dtlso["ORG"] = $param["ORG"];
            $this->_dtlso["PLANT"] = $param["PLANT"];
            $this->_dtlso["IDCARD"] = sprintf('%07d', $param["IDCARD"]);
            if($this->_dtlso["IDCARD"] == '0000000')
                return $this->_return["ERROR"] = "Error IDCARD kosong";
            elseif($this->getKartuIDcard()) // sementara ini dicoba untuk 3409
                $_truk["NO_RFID"] = $param["IDCARD"];
            else
                return $this->_return["ERROR"] = "Error kartu: ".$this->_return["ERROR"];
        }

        //// SPLIT DO
        $fce = $this->sap->NewFunction("ZCSD008_SPLIT_DO");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC split do";
            // exit;
        }
        $fce->T_VBELN->row["SIGN"] = "I";
        $fce->T_VBELN->row["OPTION"] = "EQ";
        $fce->T_VBELN->row["LOW"] = $param["PO"];
        $fce->T_VBELN->Append($fce->T_VBELN->row);
        $fce->T_SPLIT->row["NO"] = '1';
        $fce->T_SPLIT->row["IDX"] = '1';
        $fce->T_SPLIT->row["JUMLAH"] = '1';
        $fce->T_SPLIT->row["QTY"] = $param["QTY"][0];
        $fce->T_SPLIT->row["EKPD"] = '0000410092';
        $fce->T_SPLIT->Append($fce->T_SPLIT->row);

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] = "Error Split DO: ".$fce->RETURN["MESSAGE"];
            }
            $fce->T_DATA->Reset();
            while ($fce->T_DATA->Next()) {
                $_dtldo["NO_DO"] = $fce->T_DATA->row["VBELN"];
                $this->_return["NO_DO"] = $_dtldo["NO_DO"];
            }
        }
        $fce->Close();

        // prevent delay split do
        sleep(3);

        //// INSERT TB DO
        $fce = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_DO_SHIPMENT");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC update do";
            // exit;
        }
        $fce->X_VKORG = $param["ORG"];//"sales organization";
        $fce->X_WERKS = $param["PLANT"];//"plant";
        $fce->X_LFART = 'ZNL';//"tipe transaksi";
        $fce->ZX_ONLYNEW = 'X';
        $fce->X_VBELN = $_dtldo["NO_DO"];
        $fce->T_BLDAT->row["SIGN"] = "I";
        $fce->T_BLDAT->row["OPTION"] = "BT";
        $fce->T_BLDAT->row["LOW"] = date('Ymd', strtotime(' -2 days'));
        $fce->T_BLDAT->row["HIGH"] = date('Ymd');
        $fce->T_BLDAT->Append($fce->T_BLDAT->row);

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] = "Error Insert DO(1): ".$fce->RETURN["MESSAGE"];
            }
            $fce->RETURN_DATA->Reset();
            while ($fce->RETURN_DATA->Next()) {
                if($fce->RETURN_DATA->row["ORDER_NO"] == $_dtldo["NO_DO"]){
                    $_insertdo["ORDER_NO"] = $fce->RETURN_DATA->row["ORDER_NO"];
                    $_insertdo["SOLD_TO_PARTY"] = $fce->RETURN_DATA->row["SOLD_TO_PARTY"];
                    $_insertdo["SOLD_TO_CODE"] = $fce->RETURN_DATA->row["SOLD_TO_CODE"];
                    $_insertdo["ORDER_QTY"] = $fce->RETURN_DATA->row["ORDER_QTY"];
                    $_insertdo["ITEM_DESC"] = $fce->RETURN_DATA->row["ITEM_DESC"];
                    $_insertdo["ITEM_NO"] = $fce->RETURN_DATA->row["ITEM_NO"];
                    $_insertdo["INV_CLASS_NAME"] = $fce->RETURN_DATA->row["INV_CLASS_NAME"];
                    $_insertdo["JUMLAH_SEMEN"] = $fce->RETURN_DATA->row["JUMLAH_SEMEN"];
                    $_insertdo["KODE_INV_CLASS"] = $fce->RETURN_DATA->row["KODE_INV_CLASS"];
                    $_insertdo["ORDER_UM"] = $fce->RETURN_DATA->row["ORDER_UM"];
                    $_insertdo["UM_JUMLAH_SEMEN"] = $fce->RETURN_DATA->row["UM_JUMLAH_SEMEN"];
                    $_insertdo["PROP_TUJUAN"] = $fce->RETURN_DATA->row["PROP_TUJUAN"];
                    $_insertdo["NMORG"] = $fce->RETURN_DATA->row["NMORG"];
                    $_insertdo["ATT_CATEGORY"] = $fce->RETURN_DATA->row["ATT_CATEGORY"];
                    $_insertdo["LFART_DWN"] = $fce->RETURN_DATA->row["LFART_DWN"];
                    $_insertdo["DISTRIK"] = $fce->RETURN_DATA->row["DISTRIK"];
                    $_insertdo["KOTA_TUJUAN_NAME"] = $fce->RETURN_DATA->row["KOTA_TUJUAN_NAME"];
                    $_insertdo["ORDER_DATE"] = $fce->RETURN_DATA->row["ORDER_DATE"];
                    $_insertdo["SALES_ORD_NO"] = $fce->RETURN_DATA->row["SALES_ORD_NO"];
                    $_insertdo["NMPLAN"] = $fce->RETURN_DATA->row["NMPLAN"];
                    $_insertdo["FROM_WHSE"] = $fce->RETURN_DATA->row["FROM_WHSE"];
                    $_insertdo["FROM_WHSE_NAME"] = $fce->RETURN_DATA->row["FROM_WHSE_NAME"];
                    $_insertdo["NMPLAN2_CODE"] = $fce->RETURN_DATA->row["NMPLAN2_CODE"];
                    $_insertdo["NMPLAN2_NAME"] = $fce->RETURN_DATA->row["NMPLAN2_NAME"];
                    $_insertdo["NO_ORA"] = $fce->RETURN_DATA->row["NO_ORA"];
                    $_insertdo["INCOTERM"] = $fce->RETURN_DATA->row["INCOTERM"];
                    $_insertdo["SHIP_TO_PARTY"] = $fce->RETURN_DATA->row["SHIP_TO_PARTY"];
                    $_insertdo["EXPEDITUR_NO"] = $fce->RETURN_DATA->row["EXPEDITUR_NO"];
                    $_insertdo["EXPEDITUR_NAME"] = $fce->RETURN_DATA->row["EXPEDITUR_NAME"];
                } else 
                    return $this->_return["ERROR"] = "Error Insert DO(2): ".$fce->RETURN["MESSAGE"];
            }
        }
        $fce->Close();

        //// UPDATE DO KANTONG
        $kantong = $this->DBselect($this->conn, 'KANTONG, KANTONG_DESC', 'TB_MASTER_KANTONG', "PLANT = '".$param["PLANT"]."' AND ID_DISTRIK = '".$_insertdo["DISTRIK"]."' AND DELIVERY_TYPE = 'ZNL' AND MATERIAL = '".$_insertdo["ITEM_NO"]."'");

        if(!$kantong){
            return $this->_return["ERROR"] = "Lakukan Daftar kantong dengan ".$_insertdo["DISTRIK"]."-".$param["PLANT"]."-ZNL-".$_insertdo["ITEM_NO"];
        }

        $fce = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_DO");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC update kantong";
            // exit;
        }
        $fce->NO_ORDER = $_dtldo["NO_DO"];
        $fce->ITEM_KANTONG = $kantong['KANTONG'];
        $fce->KANTONG_DESC = $kantong['KANTONG_DESC'];

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] = "Error Update DO: ".$fce->RETURN["MESSAGE"];
            }
        }
        $fce->Close();

        //// GET DATA DO
        $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_DO_SHIP_N1");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC select do";
            // exit;
        }

        // $fce->XPARAM["ORDER_NO"] = '74019096';
        // $fce->XDATA_APP["NMORG"] = '3000';
        // $fce->XDATA_APP["NMPLAN"] = '3401';
        $fce->XPARAM["ORDER_NO"] = $_dtldo["NO_DO"];
        $fce->XDATA_APP["NMORG"] = $param["ORG"];
        $fce->XDATA_APP["NMPLAN"] = $param["PLANT"];
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                // echo $fce->RETURN["MESSAGE"];
                return $this->_return["ERROR"] .= "Error Get DO: ".$fce->RETURN["MESSAGE"];
            }
            $fce->RETURN_DATA2->Reset();
            while ($fce->RETURN_DATA2->Next()) {
                $_dtldo["ORDER_NO"] = $fce->RETURN_DATA2->row["ORDER_NO"];
                $_dtldo["LINE"] = $fce->RETURN_DATA2->row["LINE"];
                $_dtldo["ID_GENERATED"] = $fce->RETURN_DATA2->row["ID_GENERATED"];
                $_dtldo["PLANT"] = $fce->RETURN_DATA2->row["NMPLAN"];
                $_dtldo["NMPLAN_NAME"] = $fce->RETURN_DATA2->row["NMPLAN_NAME"];
                $_dtldo["NO_ORA"] = $fce->RETURN_DATA2->row["NO_ORA"];
                $_dtldo["ORDER_DATE"] = $fce->RETURN_DATA2->row["ORDER_DATE"];
                $_dtldo["ORDER_TIME"] = $fce->RETURN_DATA2->row["ORDER_TIME"];
                $_dtldo["PO_NO"] = $fce->RETURN_DATA2->row["PO_NO"];
                $_dtldo["PO_LINE"] = $fce->RETURN_DATA2->row["PO_LINE"];
                $_dtldo["LFART"] = $fce->RETURN_DATA2->row["LFART"];
                $_dtldo["INCOTERM"] = $fce->RETURN_DATA2->row["INCOTERM"];
                $_dtldo["INCOTERM_DESC"] = $fce->RETURN_DATA2->row["INCOTERM_DESC"];
                $_dtldo["ORDER_UM"] = $fce->RETURN_DATA2->row["ORDER_UM"];
                $_dtldo["UM_JUMLAH_SEMEN"] = $fce->RETURN_DATA2->row["UM_JUMLAH_SEMEN"];
                $_dtldo["JUMLAH_SEMEN"] = $fce->RETURN_DATA2->row["JUMLAH_SEMEN"];
                $_dtldo["SOLD_TO_CODE"] = $fce->RETURN_DATA2->row["SOLD_TO_CODE"];
                $_dtldo["SOLD_TO_PARTY"] = $fce->RETURN_DATA2->row["SOLD_TO_PARTY"];
                $_dtldo["SHIP_TO_CODE"] = $fce->RETURN_DATA2->row["SHIP_TO_CODE"];
                $_dtldo["SHIP_TO_PARTY"] = $fce->RETURN_DATA2->row["SHIP_TO_PARTY"];
                $_dtldo["LSTEL"] = $fce->RETURN_DATA2->row["LSTEL"];
                $_dtldo["ITEM_NO"] = $fce->RETURN_DATA2->row["ITEM_NO"];
                $_dtldo["ITEM_DESC"] = $fce->RETURN_DATA2->row["ITEM_DESC"];
                $_dtldo["KODE_INV_CLASS"] = $fce->RETURN_DATA2->row["KODE_INV_CLASS"];
                $_dtldo["INV_CLASS_NAME"] = $fce->RETURN_DATA2->row["INV_CLASS_NAME"];
                $_dtldo["SALES_ORD_NO"] = $fce->RETURN_DATA2->row["SALES_ORD_NO"];
                $_dtldo["TO_WHSE"] = $fce->RETURN_DATA2->row["TO_WHSE"];
                $_dtldo["TO_WHSE_NAME"] = $fce->RETURN_DATA2->row["TO_WHSE_NAME"];
                $_dtldo["ITEM_KANTONG"] = $fce->RETURN_DATA2->row["ITEM_KANTONG"];
                $_dtldo["KANTONG_DESC"] = $fce->RETURN_DATA2->row["KANTONG_DESC"];
                $_dtldo["KANTONG_SHORTDES"] = $fce->RETURN_DATA2->row["KANTONG_SHORTDES"];
                $_dtldo["UOM_ITEM_KANTONG"] = $fce->RETURN_DATA2->row["UOM_ITEM_KANTONG"];
                $_dtldo["ORDER_QTY"] = $fce->RETURN_DATA2->row["ORDER_QTY"];
                $_dtldo["EXT_PRICE"] = $fce->RETURN_DATA2->row["EXT_PRICE"];
                $_dtldo["NET_PRICE"] = $fce->RETURN_DATA2->row["NET_PRICE"];
                $_dtldo["KODE_PROP"] = $fce->RETURN_DATA2->row["KODE_PROP"];
                $_dtldo["PROP_TUJUAN"] = $fce->RETURN_DATA2->row["PROP_TUJUAN"];
                $_dtldo["FROM_WHSE"] = $fce->RETURN_DATA2->row["FROM_WHSE"];
                $_dtldo["FROM_WHSE_NAME"] = $fce->RETURN_DATA2->row["FROM_WHSE_NAME"];
                $_dtldo["STORAGE_CLASS"] = $fce->RETURN_DATA2->row["STORAGE_CLASS"];
                $_dtldo["ATT_CATEGORY"] = $fce->RETURN_DATA2->row["ATT_CATEGORY"];
                $_dtldo["EXPEDITUR_NO"] = $fce->RETURN_DATA2->row["EXPEDITUR_NO"];
                $_dtldo["EXPEDITUR_NAME"] = $fce->RETURN_DATA2->row["EXPEDITUR_NAME"];
                $_dtldo["ORG"] = $fce->RETURN_DATA2->row["NMORG"];
                $_dtldo["PLANT_TUJUAN"] = $fce->RETURN_DATA2->row["NMPLAN2_CODE"];
                $_dtldo["NMPLAN2_NAME"] = $fce->RETURN_DATA2->row["NMPLAN2_NAME"];
                $_dtldo["KANTONG_ALT_II"] = $fce->RETURN_DATA2->row["KANTONG_ALT_II"];
                $_dtldo["KANTONG_ALT_DESC"] = $fce->RETURN_DATA2->row["KANTONG_ALT_DESC"];
                $_dtldo["ALT_SHORT_DESC"] = $fce->RETURN_DATA2->row["ALT_SHORT_DESC"];
                $_dtldo["KODE_KOTA_TUJUAN"] = $fce->RETURN_DATA2->row["KODE_KOTA_TUJUAN"];
                $_dtldo["KOTA_TUJUAN_NAME"] = $fce->RETURN_DATA2->row["KOTA_TUJUAN_NAME"];
                $_dtldo["NMPLAN_DWN"] = $fce->RETURN_DATA2->row["NMPLAN_DWN"];
                $_dtldo["LFART_DWN"] = $fce->RETURN_DATA2->row["LFART_DWN"];
                $_dtldo["COMPLETE_DATE"] = $fce->RETURN_DATA2->row["COMPLETE_DATE"];
                $_dtldo["DEL"] = $fce->RETURN_DATA2->row["DEL"];
                $_dtldo["LAST_UPDATE_DATE"] = $fce->RETURN_DATA2->row["LAST_UPDATE_DATE"];
                $_dtldo["LAST_UPDATED_BY"] = $fce->RETURN_DATA2->row["LAST_UPDATED_BY"];
                $_dtldo["ROUTE"] = $fce->RETURN_DATA2->row["ROUTE"];
                $_dtldo["UMREZ"] = $fce->RETURN_DATA2->row["UMREZ"];
                $_dtldo["ZOPEN"] = $fce->RETURN_DATA2->row["ZOPEN"];
                $_dtldo["DISTRIK"] = $fce->RETURN_DATA2->row["DISTRIK"];
            }
        }
        $fce->Close();

        //// ANTRI
        //$this->noantri->Text="A001";
        //$inv ='10013020'; // untuk pcc
        //$lfart = 'ZLF'; // untuk do
        // ambil nilai untuk mulai antrian
        // ambil tipe antrian berdasarkan :
        // lfart untuk menentukan tipe DO (sales Atau STO)
        // PPC dan SMC gabung jalur H kode invclass
        // Tipe I Luar Sumbar (STO DAN SALES) => A,B,C,D kode inv
        // PCC (UNTUK STO DAN SALES) => E,F,G,K kode inv
        // SUMBANGAN dan KAPAL( STO TYPE I ) => L	lfart dan kode inv
        // Tipe I sumbar ( SALES ) => I,J	kode inv dan lfart
        //CHANGED PER 15 AJN 2011
        // TIPE 1 JALUR UNUTK OPC SUMBAR DAN NON SUMBAR INV 100 110 200 (A B)
        // TIPE 2 JALUR UNTUK PCC SUMBAR INV 100 130 200 DAN PROP 1012 (SUMBAR)
        // TIPE 3 JALUR UNTUK PCC LUAR SUMBAR INV 100 130 200 PROP != 1012
        // TIPE 4 JALUR SMC DAN PPC 100 140 200 N 100 120 200
        // TIPE 5 JALUR KAPAL DAN SUMBANGAN LFART = ZNL AND ZLC KOTA = 3801
        // TIPE 6 TYPE LAINNYA OTHERS
        if ($_dtldo["PLANT"] == '3401' || $_dtldo["PLANT"] == '7912' || $_dtldo["PLANT"] == '79H6' || $_dtldo["PLANT"] == '79M6'){
            $lfart = $_dtldo["LFART"]; //ex: ZNL
            $inv = $_dtldo["KODE_INV_CLASS"]; //ex: 100130200
            $prop = $_dtldo["KODE_PROP"]; //ex: 1025
            $plant_to = $_dtldo["PLANT_TUJUAN"]; //ex: 3801

            if ($inv != "" and $lfart != "") {
                //echo " in ";
                if ($lfart == "ZLC" or ( $lfart == "ZNL" and $plant_to == "3801"))
                        $tipe = "005";
                    elseif ($lfart == "ZLC" or ( $lfart == "ZNL" and $plant_to == "7975"))
                        $tipe = "005";
                    elseif ($inv == "100110200")
                        $tipe = "001";
                    elseif ($mat_no =="121-301-0050" and $inv == "100130200" ) 
                        $tipe = "004";  
                    elseif ($mat_no !="121-301-0050" and $inv == "100130200" and $prop == "1012")
                        $tipe = "002";
                    elseif ($mat_no !="121-301-0050" and $inv == "100130200" and $prop != "1012")
                        $tipe = "003";
                    elseif ($inv == "100140200" or $inv == "100120200")
                        $tipe = "004";
                    else
                        $tipe = "006";
                // if($this->priority->Text=='1'){$tipe = "007";}; // can't find priority exclude 0
            }else {
                $tipe = "SALAH";
            }

            if ($tipe == "SALAH") {
                return $this->_return["ERROR"] .= "TIPE ANTRIAN BELUM TERDAFTAR.. ";
            } else {
                $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_SY_SYS_DO");
                if ($fce == false) {
                    // $this->sap->PrintStatus();
                    return $this->_return["ERROR"] = "Can't connect to RFC select assign";
                    // exit;
                }

                //Param Export
                $fce->XPARAM["TYPE_DOC"] = $tipe;
                $fce->XDATA_APP["NMORG"] = $_dtldo["ORG"];
                $fce->XDATA_APP["NMPLAN"] = $_dtldo["PLANT"];

                //Execute Function
                $fce->Call();

                if ($fce->GetStatus() == SAPRFC_OK) {
                    $fce->RETURN_DATA->Reset();
                    //Display Tables
                    while ($fce->RETURN_DATA->Next()) {
                        $assign = $fce->RETURN_DATA->row["LAST_ASSIGN"];
                    }
                }
                $assign += 1;

                $fce->Close();

                $no_antri = $this->no_antrian($assign, $tipe);
                $cekpas = substr($no_antri, 0, -2);

                $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_JALUR");
                if ($fce == false) {
                    // $dsap->PrintStatus();
                    // exit;
                    return $this->_return["ERROR"] = "Can't connect to RFC select jalur";
                }

                //Param Export
                $fce->XPARAM["JALUR"] = $cekpas;
                $fce->XDATA_APP["NMORG"] = $_dtldo["ORG"];
                $fce->XDATA_APP["NMPLAN"] = $_dtldo["PLANT"];

                //Execute Function
                $fce->Call();

                if ($fce->GetStatus() == SAPRFC_OK) {
                    $fce->RETURN_DATA->Reset();
                    //Display Tables
                    while ($fce->RETURN_DATA->Next()) {
                        $status_jalur = $fce->RETURN_DATA->row["STATUS"];
                        $maks_jalur = $fce->RETURN_DATA->row["MAKS"];
                        $antri_jalur = $fce->RETURN_DATA->row["ANTRI"];
                        $jumlah_jalur = $fce->RETURN_DATA->row["JUMLAH"];
                    }
                }
                $fce->Close();
                //$dsap->Close();
                if ($status_jalur == "0") {

                    $fce = $this->sap->NewFunction("Z_ZAPPSD_CREATE_ANTRI");
                    if ($fce == false) {
                        // $dsap->PrintStatus();
                        // exit;
                        return $this->_return["ERROR"] = "Can't connect to RFC create antri";
                    }
                    $antri_jalur+=1;
                    $jumlah_jalur+=1;
                    if ($maks_jalur == $jumlah_jalur) {
                        $status_jalur = '10';
                    }

                    $fce->XDATA_KEY = $cekpas;
                    $fce->XTYPE_DOC = $tipe;

                    $fce->XDATA_APP["NMORG"] = $_dtldo["ORG"];
                    $fce->XDATA_APP["NMPLAN"] = $_dtldo["PLANT"];

                    $fce->XDATA_UPD["STATUS"] = $status_jalur;
                    $fce->XDATA_FLAG["STATUS"] = 'X';

                    $fce->XDATA_UPD["ANTRI"] = $antri_jalur;
                    $fce->XDATA_FLAG["ANTRI"] = 'X';
                    $fce->XDATA_UPD["JUMLAH"] = $jumlah_jalur;
                    $fce->XDATA_FLAG["JUMLAH"] = 'X';

                    $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date('Ymd');
                    $fce->XDATA_UPD["LAST_UPDATED_BY"] =  $_dtldo["USERNAME"];

                    $fce->XDATA_FLAG["LAST_UPDATE_DATE"] = 'X';
                    $fce->XDATA_FLAG["LAST_UPDATED_BY"] = 'X';
                    $fce->Call();

                    if ($fce->GetStatus() == SAPRFC_OK) {
                        if (trim($fce->RETURN["TYPE"]) == 'E') {
                            return $this->_return["ERROR"] .= "Error Antri: ".$fce->RETURN["MESSAGE"] . "<br>";
                        } else {
                            $fce->Close();

                            $fce = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_SY_SYS_DO");
                            if ($fce == false) {
                                // $dsap->PrintStatus();
                                // exit;
                                return $this->_return["ERROR"] = "Can't connect to RFC update assign";
                            }

                            $fce->XDATA_KEY = $tipe;
                            $fce->XDATA_APP["NMORG"] = $_dtldo["ORG"];
                            $fce->XDATA_APP["NMPLAN"] = $_dtldo["PLANT"];

                            $fce->XDATA_UPD["LAST_ASSIGN"] = $assign;
                            $fce->XDATA_FLAG["LAST_ASSIGN"] = 'X';

                            $fce->Call();
                            $fce->Close();

                            $_dtldo["NO_DAFTAR"] = $assign;
                            $_dtldo["NO_ANTRI"]  = $no_antri;
                            $_dtldo["TIPE_ANTRI"] = $tipe;
                        }
                    }

                } else {
                    return $this->_return["ERROR"] .= "JALUR ANTRIAN " . $cekpas . " TELAH PENUH..";
                }
            }
        } else {
            $_dtldo["NO_ANTRI"] = 'A0';
            $_dtldo["TIPE_ANTRI"] = '999';
        }
        // ^ returning
        // $_dtldo["NO_DAFTAR"] = $assign;
        // $_dtldo["NO_ANTRI"]  = $no_antri;
        // $_dtldo["TIPE_ANTRI"] = $tipe;

        //// SAVE DATA
        $fce = $this->sap->NewFunction("Z_ZAPPSD_INSERT_TRANSAKSI2_N3");
        if ($fce == false) {
            // $this->sap->PrintStatus();
            return $this->_return["ERROR"] = "Can't connect to RFC save data";
            // exit;
        }
        // header gr entri
            $fce->XPARAM["NMORG"] = $_dtldo["ORG"];
            $fce->XPARAM["NMPLAN"] = $_dtldo["PLANT"];

            $fce->XPARAM["NO_BOOKING"] = $param["SPP"];
            $fce->XPARAM["STATUS_TRANS"] = '20'; //10 for daftar antri, 20 for timb masuk, 30 gdg kantong, 40 bg counter, 50 timb keluar, 60 closing.

            $fce->XPARAM["NO_ADJ"] = $_dtldo["NO_DAFTAR"]; // $this->nodaftar->Text;
            $fce->XPARAM["JALUR_ANTRI"] = $_dtldo["NO_ANTRI"]; // $this->noantri->Text;
            $fce->XPARAM["TIPE_ANTRI"] = $_dtldo["TIPE_ANTRI"]; // $this->typeAntri->Text;
            $fce->XPARAM["JALUR_CONVEYOR"] = '';
            if($_dtldo["PLANT"] == '3401'){
                $fce->XPARAM["TIPE_TRUK"] = '300'; // $this->nopol->Text; code asalnya dihardcode 300
            }else{
                $fce->XPARAM["TIPE_TRUK"] = ''; // untuk 3409
            }
            $fce->XPARAM["ID_CARD"] = $_truk["NO_RFID"];

            $fce->XPARAM["NO_POLISI"] = $_truk["NOPOLISI"];
            $fce->XPARAM["NO_STNK"] = $_truk["NOSTNK"];
            $fce->XPARAM["NAMA_SUPIR"] = $_sopir["NAMA_SOPIR"];
            $fce->XPARAM["NO_SIM"] = $_sopir["NO_SIM"];

            // $fce->XPARAM["NO_EXPEDITUR"] = "0000410092";
            // $fce->XPARAM["NAMA_EXPEDITUR"] = "SEMEN INDONESIA LOGISTIC, PT";
            $fce->XPARAM["NO_EXPEDITUR"] = $_truk["NO_EXPEDITUR"];;
            $fce->XPARAM["NAMA_EXPEDITUR"] = $_truk["NAMA_EXPEDITUR"];;

            $fce->XPARAM["TGL_ANTRI"] = date('Ymd');
            $fce->XPARAM["JAM_ANTRI"] = date('His');
            $fce->XPARAM["PTGS_ANTRIAN"] = $param["USERNAME"];
            $fce->XPARAM["STS_ANTRI_PRINT"] = '0';
            $fce->XPARAM["CNT_PRINT_ANTRI"] = '0';
            $fce->XPARAM["STS_MASUK_PRINT"] = '0';
            $fce->XPARAM["CNT_PRINT_MASUK"] = '0';
            $fce->XPARAM["STS_KELUAR_PRINT"] = '0';
            $fce->XPARAM["CNT_PRINT_KELUAR"] = '0';
            $fce->XPARAM["DEL"] = '0'; //delete mark
            $fce->XPARAM["SHP_POINT"] = $_dtldo["PLANT"];

        //detail do entri
            $jmlBerat = 0;
            $jmlBeratKg = 0;
            $jmlKantongAct = 0;

            $fce->XPARAM_LINE->row["NMORG"] = $_dtldo["ORG"];
            $fce->XPARAM_LINE->row["ORG_DESC"] = "PT. SEMEN PADANG";
            $fce->XPARAM_LINE->row["NMPLAN"] = $_dtldo["PLANT"];
            $fce->XPARAM_LINE->row["PLAN_DESC"] = $_dtldo["NMPLAN_NAME"];

            $fce->XPARAM_LINE->row["PLANT_RCV"] = $_dtldo["PLANT_TUJUAN"];
            $fce->XPARAM_LINE->row["PLANT_RCV_NAME"] = $_dtldo["NMPLAN2_NAME"];
            $fce->XPARAM_LINE->row["NO_DO"] = $_dtldo["NO_DO"];
            $fce->XPARAM_LINE->row["LINE"] = $_dtldo["LINE"];
            $fce->XPARAM_LINE->row["NO_SO"] = $_dtldo["SALES_ORD_NO"];
            $fce->XPARAM_LINE->row["NO_PO"] = $_dtldo["PO_NO"];
            $fce->XPARAM_LINE->row["LINE_PO"] = $_dtldo["PO_LINE"]; //FROM_WHSE
            $fce->XPARAM_LINE->row["STATUS_TRANS"] = '20';
            $fce->XPARAM_LINE->row["FROM_WHSE"] = $_dtldo["FROM_WHSE"]; //FROM_WHSE
            $fce->XPARAM_LINE->row["TO_WHSE"] = $_dtldo["TO_WHSE"]; //TO_WHSE
            $fce->XPARAM_LINE->row["ITEM_NO"] = $_dtldo["ITEM_NO"];
            $fce->XPARAM_LINE->row["ITEM_DESC"] = $_dtldo["ITEM_DESC"];
            $fce->XPARAM_LINE->row["ITEM_QTY"] = $_dtldo["ORDER_QTY"]; //ORDER_QTY
            $fce->XPARAM_LINE->row["TOTAL_QTY"] = $_dtldo["JUMLAH_SEMEN"]; //BERAT DO DALAM KG
            $fce->XPARAM_LINE->row["TOTAL_UM"] = $_dtldo["UM_JUMLAH_SEMEN"];
            $fce->XPARAM_LINE->row["ITEM_UM"] = $_dtldo["ORDER_UM"];
            $fce->XPARAM_LINE->row["LSTEL"] = $_dtldo["LSTEL"];
            $fce->XPARAM_LINE->row["ORDER_DATE"] = $_dtldo["ORDER_DATE"];
            $fce->XPARAM_LINE->row["NO_ORA"] = $_dtldo["NO_ORA"];
            $fce->XPARAM_LINE->row["LFART"] = $_dtldo["LFART"];
            $fce->XPARAM_LINE->row["INCOTERM"] = $_dtldo["INCOTERM"];
            $fce->XPARAM_LINE->row["INCOTERM_DESC"] = $_dtldo["INCOTERM_DESC"];
            $fce->XPARAM_LINE->row["SOLD_TO_CODE"] = $_dtldo["SOLD_TO_CODE"];
            $fce->XPARAM_LINE->row["SHIP_TO_CODE"] = $_dtldo["SHIP_TO_CODE"];
            $fce->XPARAM_LINE->row["SOLD_TO_PARTY"] = $_dtldo["SOLD_TO_PARTY"];
            $fce->XPARAM_LINE->row["SHIP_TO_PARTY"] = $_dtldo["SHIP_TO_PARTY"];

            if ($_dtldo["ORDER_UM"] != 'TO') {
                $_dtldo["CURAH_OR_BAG"] = '20';
                $fce->XPARAM_LINE->row["NO_KTG"] = $_dtldo["ITEM_KANTONG"];
                $fce->XPARAM_LINE->row["KTG_DESC"] = $_dtldo["KANTONG_DESC"];
                $fce->XPARAM_LINE->row["KTG_SHORT"] = $_dtldo["KANTONG_SHORTDES"];

                $fce->XPARAM_LINE->row["NO_KTG_ACT"] = $_dtldo["ITEM_KANTONG"];
                $fce->XPARAM_LINE->row["KTG_DESC_ACT"] = $_dtldo["KANTONG_DESC"];
                $fce->XPARAM_LINE->row["JML_KTG_ACT"] = $_dtldo["ORDER_QTY"];

                $fce->XPARAM_LINE->row["CHG_KTG"] = '10'; // DEFAULT TETAP
                $fce->XPARAM_LINE->row["NO_KTG_ALT"] = $_dtldo["KANTONG_ALT_II"];
                $fce->XPARAM_LINE->row["KTG_DESC_ALT"] = $_dtldo["KANTONG_ALT_DESC"];
                $fce->XPARAM_LINE->row["KTG_SHORT_ALT"] = $_dtldo["ALT_SHORT_DESC"];
                $fce->XPARAM_LINE->row["UOM_KTG"] = $_dtldo["UOM_ITEM_KANTONG"];
            } else {
                $_dtldo["CURAH_OR_BAG"] = '10';
            }
            $fce->XPARAM_LINE->row["KODE_PROP"] = $_dtldo["KODE_PROP"];
            $fce->XPARAM_LINE->row["PROP_NAME"] = $_dtldo["PROP_TUJUAN"];
            $fce->XPARAM_LINE->row["KODE_KOTA"] = $_dtldo["KODE_KOTA_TUJUAN"];
            $fce->XPARAM_LINE->row["DISTRIK"] = $_dtldo["DISTRIK"];
            $fce->XPARAM["BZIRK"] = $_dtldo["DISTRIK"]; //distrik terakhir yang diambil
            $fce->XPARAM["BZTXT"] = $_dtldo["KOTA_TUJUAN_NAME"]; //distrik terakhir yang diambil
            $fce->XPARAM_LINE->row["KOTA_NAME"] = $_dtldo["KOTA_TUJUAN_NAME"];
            //Add by mujib 28 dec 2012 for rfid sms center ==============================				
            $luarsumbar = false;
            if (trim($_dtldo["KODE_PROP"]) != '' && trim($_dtldo["KODE_PROP"]) != '1012')//sales
                $luarsumbar = true;
            elseif (trim($_dtldo["KODE_PROP"]) == '' && trim($_dtldo["KODE_KOTA_TUJUAN"]) != '1210')//sto
                $luarsumbar = true;

            if ($luarsumbar) {
                $fce->XPARAM["KODE_CHECKPOINT"] = $_dtldo["PLANT"]; //Check point seharusnya
                $fce->XPARAM["NAMA_CHECKPOINT"] = 'Belum CheckPoint'; //Check point dilokasi
            }//End Add by mujib 28 dec 2012 =============================================

            $fce->XPARAM_LINE->row["ATT_CATEGORY"] = $_dtldo["ATT_CATEGORY"];
            $fce->XPARAM_LINE->row["INV_CLASS"] = $_dtldo["KODE_INV_CLASS"];
            $fce->XPARAM_LINE->row["INV_CLASS_NAME"] = $_dtldo["INV_CLASS_NAME"];
            $fce->XPARAM_LINE->row["UMREZ"] = $_dtldo["UMREZ"];
            $fce->XPARAM_LINE->row["ROUTE"] = $_dtldo["ROUTE"];

            // if ($_dtldo["LFART"] == "ZLF") {
            //     $fce->XPARAM_LINE->row["VENDOR_NO"] = '99999';
            //     $fce->XPARAM_LINE->row["VENDOR_NAME"] = $this->ekspeditur->Text;
            // } else {
            //     $fce->XPARAM_LINE->row["VENDOR_NO"] = '';
            //     $fce->XPARAM_LINE->row["VENDOR_NAME"] = $item->VENDOR_NAME->Text;
            // }
            $fce->XPARAM_LINE->row["VENDOR_NO"] = $_truk["NO_EXPEDITUR"];
            $fce->XPARAM_LINE->row["VENDOR_NAME"] = $_truk["NAMA_EXPEDITUR"];
            $fce->XPARAM_LINE->row["CREATE_DATE"] = date('Ymd');
            $fce->XPARAM_LINE->row["CREATE_TIME"] = date('H:i:s');
            $fce->XPARAM_LINE->row["CREATED_BY"] = $param["USERNAME"];
            $fce->XPARAM_LINE->row["DEL"] = '0';

            $fce->XPARAM_LINE->Append($fce->XPARAM_LINE->row);
            $jmlBerat = $jmlBerat + (int) $_dtldo["ORDER_QTY"];
            $jmlBeratKg = $jmlBeratKg + (int) $_dtldo["JUMLAH_SEMEN"];

            $lfart = $_dtldo["LFART"];
            $lstel = $_dtldo["LSTEL"];
            if ($_dtldo["CURAH_OR_BAG"] == '20') {// khusus zak
                $jmlKantongAct = $jmlBerat;
            }
            $actNoKantong =$_dtldo["ITEM_KANTONG"];
            $actKantongDsc = $_dtldo["KANTONG_DESC"];

            if ($_dtldo["CURAH_OR_BAG"] == '20') {
                $fce->XPARAM["NO_KTG"] = $actNoKantong;
                $fce->XPARAM["KTG_DESC"] = $actKantongDsc;
                $fce->XPARAM["NO_KTG_ACT"] = $actNoKantong;
                $fce->XPARAM["KTG_DESC_ACT"] = $actKantongDsc;
                $fce->XPARAM["JML_KTG_ACT"] = $jmlKantongAct;
                $fce->XPARAM["CHG_KTG"] = '10'; //TETAP
            }

            $fce->XPARAM["TOTAL_QTY"] = $jmlBeratKg;

            $fce->XPARAM["ORDER_TYPE"] = $lfart;
            $fce->XPARAM["LOADING_POINT"] = $lstel;
            $fce->XPARAM["BERAT_TOTAL_DO"] = $jmlBerat; //$qty hit dr DO;	
            $fce->XPARAM["CURAH_OR_BAG"] = $_dtldo["CURAH_OR_BAG"];

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] .= "Error Matching: ".$fce->RETURN["MESSAGE"];
            }
            // $fce->XDATA_RETURN->Reset();
            if(strlen($fce->XDATA_RETURN) > 0) {
                $this->_return["NO_TRANSAKSI"] = $fce->XDATA_RETURN;
            }
        }
        $fce->Close();

        //// update vehicle?
        if ($_dtldo["PLANT"] == '3401') {
            $fce = $this->sap->NewFunction("Z_ZAPPSD_UPDATE_VEHICLE");
            if ($fce == false) {
                // $this->sap->PrintStatus();
                $this->_return["ERROR"] = "Can't connect to RFC update vehicle";
            }
            $fce->XID = $_truk["ID"];
            $fce->XDATA_APP['NMORG'] = $_dtldo["ORG"];
            $fce->XDATA_APP['NMPLAN'] = $_dtldo["PLANT"];
            
            $fce->XDATA_UPD["NO_RFID"] = $_truk["NO_RFID"];
            $fce->XDATA_UPD["LAST_UPDATED_BY"] = $param["USERNAME"];
            $fce->XDATA_UPD["LAST_UPDATE_DATE"] = date("Ymd");
            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                if(trim($fce->RETURN["TYPE"])=='E') {
                    $this->_return["ERROR"] = "Error Update Truck: ".$fce->RETURN["MESSAGE"];
                }
            }
            $fce->Close();
        }

        $this->_return["PLANT_TO"] = $_dtldo["PLANT_TUJUAN"];
        $this->_return["DO_OPCO"] = $_dtldo["NO_DO"];
        return;
    }

    function rollbackDO(){
        $fce = $this->sap->NewFunction("Z_ZAPPSD_DEL_DO");
        if ($fce == false) {
            return $this->_return["ERROR"] = "Can't connect to RFC rollback (DO ".$this->_return["NO_DO"].")";
        }

        $fce->I_VBELN = $this->_return["NO_DO"];

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            if(trim($fce->RETURN["TYPE"])=='E') {
                return $this->_return["ERROR"] = "Error rollback ".$this->_return["NO_DO"].": ".$fce->RETURN["MESSAGE"];
            }
            $fce->T_RETURN->Reset();
            while ($fce->T_RETURN->Next()) {
                return $this->_return["ERROR"] = " Transaction failed, DO ".$this->_return["NO_DO"]." rollback";
            }
        }
    }

    function no_antrian($brp, $tipe){
        if ($tipe == "001") {// untuk OPC Sumbar N Luar Sumbar
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 2; // jumlah jalur yang ada
            $total_jalur = 2; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        } else if ($tipe == "002") {// unutk PCC Sumbar
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 2; // jumlah jalur yang ada
            $total_jalur = 2; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        } else if ($tipe == "003") {// unutk jalur PCC Luar Sumbar
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 5; // jumlah jalur yang ada
            $total_jalur = 5; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        } else if ($tipe == "004") {// unutk SMC Dan PPC
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 1; // jumlah jalur yang ada
            $total_jalur = 1; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        } else if ($tipe == "005") {// unutk KAPAL N Sumbangan
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 2; // jumlah jalur yang ada
            $total_jalur = 2; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        } else if ($tipe == "006") {// Others If Not Maintained
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 2; // jumlah jalur yang ada
            $total_jalur = 2; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        } 
        else if ($tipe == "007") {// Others If Not Maintained
            $jumlah_antrian = 11; //jumlah truk per jalur
            $jumlah_jalur = 2; // jumlah jalur yang ada
            $total_jalur = 2; //total semua jalur mulai dari indeks 0-...
            $jalan = true;
        }        
        else {
            $jalan = false;
        }
        $kali_jalur = $total_jalur * $jumlah_antrian;
        $kali_jalur_plus = $kali_jalur + 1;
        $antri_plus = $jumlah_antrian + 1;

        //===========================================$indeks=array(1=>"A0","B0","C0","D0","A1","B2","B3");
        if ($jalan) {
            for ($q = 0; $q <= $jumlah_antrian; $q++) {
                for ($w = 1; $w <= $jumlah_jalur; $w++) {
                    if ($tipe == "001") {//
                        if ($w == 1) {
                            $as = "A";
                        } elseif ($w == 2) {
                            $as = "B";
                        }
                    } else if ($tipe == "002") {//
                        if ($w == 1) {
                            $as = "C";
                        } elseif ($w == 2) {
                            $as = "D";
                        }
                    } else if ($tipe == "003") {//
                        if ($w == 1) {
                            $as = "E";
                        } elseif ($w == 2) {
                            $as = "F";
                        } elseif ($w == 3) {
                            $as = "G";
                        } elseif ($w == 4) {
                            $as = "H";
                        } elseif ($w == 5) {
                            $as = "I";
                        }
                    } else if ($tipe == "004") {//
                        if ($w == 1) {
                            $as = "J";
                        }
                    } else if ($tipe == "005") {//
                        if ($w == 1) {
                            $as = "K";
                        } elseif ($w == 2) {
                            $as = "L";
                        }
                    } else if ($tipe == "006") {//
                        if ($w == 1) {
                            $as = "K";
                        } elseif ($w == 2) {
                            $as = "L";
                        }
                    }
                    else if ($tipe == "007") {//
                        if ($w == 1) {
                            $as = "M";
                        } elseif ($w == 2) {
                            $as = "N";
                        }
                    }
                    
                    $val = $as . $q;
                    $indeks[] = $val;
                }
            }
            //$brp=$_POST['brp'];
            if ($jumlah_antrian == "" or $jumlah_antrian == null or $jumlah_antrian == 0)
                $jumlah_antrian = 1;
            $kali = floor($brp / $jumlah_antrian); //jumlah antrian
            $sisa = $brp % $jumlah_antrian;

            if ($kali < 1 and $brp < $kali_jalur_plus) {//jumlah antrian  + 1
                $var = $brp;
                $end = 1;
            } else if ($kali >= 1 and $kali <= $total_jalur) {//total jalur
                if ($kali == $total_jalur and $sisa > 0) {
                    $kali = 0;
                    $var = $sisa;
                    $end = 1;
                } else {
                    $ke = $kali * $jumlah_antrian;
                    $kali-=1; //0
                    $var = $jumlah_antrian;
                    $end = $brp % $jumlah_antrian; //4
                }
            } elseif ($kali > $total_jalur) {
                $brp2 = $brp;
                while ($kali > $total_jalur) {
                    $brp2 = $brp2 - $kali_jalur;
                    $kali = floor($brp2 / $jumlah_antrian);
                }
                if ($kali < 1) {
                    //$kali=1;
                    $var = $brp2;
                    $end = 1;
                } else if ($kali >= 1 and $kali <= $total_jalur) {//total jalur 
                    if ($kali == $total_jalur and $sisa > 0) {
                        $kali = 0;
                        $var = $sisa;
                        $end = 1;
                    } else {

                        $ke = $kali * $jumlah_antrian;
                        $kali-=1; //0
                        $var = $jumlah_antrian;
                        $end = $brp % $jumlah_antrian; //4
                    }
                }
            }
            //$indeks=array(array());
            $no = 1;
            for ($j = 0; $j <= $kali; $j++) {
                $a = $j;
                $hasil1 = $indeks[$a];
                //echo " <br/> $hasil1 <br/><br/>";
                for ($k = 1; $k <= $var; $k++) {
                    //for($k=1;$k<=$end;$k++){
                    if ($k <= 9) {
                        $nul = "0" . $k;
                    } else {
                        $nul = $k;
                    }
                    if ($ke == $no && $sisa > 0) {
                        $b = $a + 1;
                        $hasil1 = $indeks[$b];
                        //echo " ulang ke ";
                        //echo " $nul <br/>";
                        //echo "<br/> $hasil1 <br/><br/>";
                        $k = 0;
                        $var = $end;
                        $no+=1;
                    } else {
                        //echo " ulang ke ";
                        //echo " $nul <br/>";
                        // $brp-=1;
                        $no+=1;
                    }
                    $hasil2 = $nul;
                    //}
                }
            }
        }
        $hasil = $hasil1 . $hasil2;
        return $hasil;		
    }

    function linenum($kode){
        $panjang=strlen(strval($kode));
        if($panjang==1)$linenum='00000'.$kode;
        if($panjang==2)$linenum='0000'.$kode;
        if($panjang==3)$linenum='000'.$kode;
        if($panjang==4)$linenum='00'.$kode;
        if($panjang==5)$linenum='0'.$kode;
        if($panjang==6)$linenum=$kode;
        return $linenum;
    }

    function getLFART($SOType){
        $lfart = array(array('so' => 'ZEX', 'do' => 'ZLFE'),
            array('so' => 'ZFC', 'do' => 'ZLC'),
            array('so' => 'ZOR', 'do' => 'ZLF'),
            array('so' => 'ZPR', 'do' => 'ZLFP'));
        $ada = false;
        for ($zxz = 0; $zxz < count($lfart); $zxz++) {
            if ($lfart[$zxz]['so'] == $SOType) {
                $DOType = $lfart[$zxz]['do'];
                $ada = true;
                return $DOType;
            }
        }
        if ($ada)
            return $DOType;
        else
            return false;
    }

    function getDataTruk(){
        try
        {
            $fce = $this->sap->NewFunction("Z_ZAPPSD_SELECT_TRUK");
            if ($fce == false) {
                $this->sap->PrintStatus();
                exit;
            }

            $fce->XPARAM["NOPOLISI"]=strtoupper($this->_dtlso["NOPOL"]);
            // $fce->XPARAM["NOSTNK"]=$stnk;
            $fce->XPARAM["STATUS"]='0';
            if($this->_dtlso["ORG"] == '7900'){
                $row = $this->DBselect($this->conncsms, 'PLANT_OPCO, COM_OPCO', 'ZMD_MAPPING_PLANT', "PLANT_MD = '".$this->_dtlso["PLANT"]."' AND  DEL=0");
                // $sql= "SELECT PLANT_OPCO,COM_OPCO FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '".$this->_dtlso["PLANT"]."' AND  DEL=0";

                $fce->XDATA_APP["NMORG"] = $row["COM_OPCO"];
                $fce->XDATA_APP["NMPLAN"] = $row["PLANT_OPCO"];
            }else{
                $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];
            }

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->RETURN_DATA->Reset();
                $i=0;
                $truk=array();
                while ($fce->RETURN_DATA->Next()){  
                    if($this->_dtlso["ORG"] == '7900'){
                        if($fce->RETURN_DATA->row["NMORG"] == $row["COM_OPCO"]){
                            $this->_truk[$i]["DAFTAR"] = 0;
                            $this->_truk[$i]["CORG"] = $fce->RETURN_DATA->row["NMORG"];
                            $this->_truk[$i]["TIPE_TRUK"] = $fce->RETURN_DATA->row["VEHICLE_TYPE"];
                            $this->_truk[$i]["TIPE_NAME"] = $fce->RETURN_DATA->row["MODE_OFTRANSPORT"];
                            $this->_truk[$i]["NO_STNK"] = $fce->RETURN_DATA->row["NOSTNK"];
                            $this->_truk[$i]["ID_CARD"] = $fce->RETURN_DATA->row["NO_RFID"];
                            $this->_truk[$i]["SYSTEM_BONGKAR"] = $fce->RETURN_DATA->row["SYSTEM_BONGKAR"];
                            $this->_truk[$i]["NO_EXPEDITUR"] = $fce->RETURN_DATA->row["NO_EXPEDITUR"];
                            $this->_truk[$i]["NAMA_EXPEDITUR"] = $fce->RETURN_DATA->row["NAMA_EXPEDITUR"];
                            $this->_truk[$i]["KAPASITAS"] = $fce->RETURN_DATA->row["KAPASITAS"]*1;
                            $this->_truk[$i]["WARNA_PLAT"] = $fce->RETURN_DATA->row["WARNA_PLAT"];									
                            $this->_truk[$i]["NOPOLISI"] = $fce->RETURN_DATA->row["NOPOLISI"];
                            $this->_truk[$i]["NMPLAN"] = $fce->RETURN_DATA->row["NMPLAN"];
                            $this->_truk[$i]["NMORG"] = $fce->RETURN_DATA->row["NMORG"];
                            $this->_truk[$i]["VEHICLE_TYPE"] = $fce->RETURN_DATA->row["VEHICLE_TYPE"];
                            $this->_truk[$i]["MODE_OFTRANSPORT"] = $fce->RETURN_DATA->row["MODE_OFTRANSPORT"];
                            $this->_truk[$i]["NOMOR_MESIN"] = $fce->RETURN_DATA->row["NOMOR_MESIN"];
                            $this->_truk[$i]["ZZRMES"] = $fce->RETURN_DATA->row["ZZRMES"];
                            $this->_truk[$i]["NO_RFID"] = $fce->RETURN_DATA->row["NO_RFID"];
                            $this->_truk[$i]["QUANTITY_ALLOWED"] = $fce->RETURN_DATA->row["QUANTITY_ALLOWED"];
                        }else{
                            $this->_truk[$i]["TIDAKADAOPCO"] = '1';
                        }
                    }else{
                        $this->_truk[$i]["LENGKAP"] = '0';
                    }	
                    
                }
                if(trim($fce->RETURN["TYPE"])=='E') {
                    // echo $fce->RETURN["MESSAGE"];
                    return $this->_return["ERROR"] = "Truk ".$fce->RETURN["MESSAGE"];
                }
            }
            $fce->Close();
            // return $this->_truk;
        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            echo $e->getMessage();
        }
    }

    function insertTruk(){
        try
        {
            $fce = $this->sap->NewFunction ("Z_ZAPPSD_INSERT_TRUK");
            if ($fce == false ) {
                $dsap->PrintStatus();
                exit;
            }
            
            $fce->XPARAM["NOPOLISI"] = $this->_truk[0]["NOPOLISI"];
            $fce->XPARAM["NOSTNK"] = $this->_truk[0]["NO_STNK"];
            $fce->XPARAM["NMORG"] = $this->_dtlso["ORG"];
            $fce->XPARAM["NMPLAN"] = $this->_dtlso["PLANT"];
            $fce->XPARAM["VEHICLE_TYPE"] = $this->_truk[0]["VEHICLE_TYPE"];
            $fce->XPARAM["MODE_OFTRANSPORT"] = $this->_truk[0]["MODE_OFTRANSPORT"];
            $fce->XPARAM["NOMOR_MESIN"] = $this->_truk[0]["NOMOR_MESIN"];
            $fce->XPARAM["ZZRMES"] = $this->_truk[0]["ZZRMES"];
            $fce->XPARAM["STATUS"] = '0';
            $fce->XPARAM["KAPASITAS"] = $this->_truk[0]["KAPASITAS"];
            $fce->XPARAM["NO_EXPEDITUR"] = $this->_truk[0]["NO_EXPEDITUR"];
            $fce->XPARAM["NAMA_EXPEDITUR"] = $this->_truk[0]["NAMA_EXPEDITUR"];
            $fce->XPARAM["NO_RFID"] = $this->_truk[0]["ID_CARD"];
            $fce->XPARAM["QUANTITY_ALLOWED"] = $this->_truk[0]["QUANTITY_ALLOWED"];
            $fce->XPARAM["SYSTEM_BONGKAR"] = $this->_truk[0]["SYSTEM_BONGKAR"];
            $fce->XPARAM["WARNA_PLAT"] = $this->_truk[0]["WARNA_PLAT"];
            $fce->XPARAM["DEL"] = '0';
            $fce->XPARAM["SAP"] = '0';
            $fce->XPARAM["CREATE_BY"] = $this->_dtlso["USERNAME"];
            $fce->XPARAM["CREATE_DATE"] = date("Ymd");
            
            $fce->Call();
            // echo "berhasil insert truk";
            $fce->Close();
            
        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            echo $e->getMessage();
        }
    }

    function getDataSopir(){
        try
        {
            $fce = $this->sap->NewFunction ("Z_ZAPPSD_SELECT_SOPIR");
            if ($fce == false ) {
            $this->sap->PrintStatus();
            exit;
            }

            $fce->XPARAM["NAMA_SOPIR"]=strtoupper($this->_dtlso["DRIVER"]);
            $fce->XPARAM["STATUS"] = '0';

            if($this->_dtlso["ORG"] == '7900'){
                $row = $this->DBselect($this->conncsms, 'PLANT_OPCO, COM_OPCO', 'ZMD_MAPPING_PLANT', "PLANT_MD = '".$this->_dtlso["PLANT"]."' AND  DEL=0");
                $fce->XDATA_APP["NMORG"] = $row["COM_OPCO"];
                $fce->XDATA_APP["NMPLAN"] = $row["PLANT_OPCO"];
            }else{
                $fce->XDATA_APP["NMORG"] = $this->_dtlso["ORG"];
                $fce->XDATA_APP["NMPLAN"] = $this->_dtlso["PLANT"];
            }

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->RETURN_DATA->Reset();
                $i=0;
                $this->_sopir=array();
                while ($fce->RETURN_DATA->Next()){  
                    if($this->_dtlso["ORG"] == '7900'){
                        if($fce->RETURN_DATA->row["NMORG"] == $row["COM_OPCO"]){
                            // echo "<br>".$fce->RETURN_DATA->row["NMORG"]." w ".$row["COM_OPCO"];
                            // echo "<br>".$fce->RETURN_DATA->row["NMPLAN"]." w ".$row["PLANT_OPCO"];
                            // echo "<br>".$fce->RETURN_DATA->row["NAMA_SOPIR"]." w ".$this->_dtlso["DRIVER"];
                            if($fce->RETURN_DATA->row["NMORG"] == $row["COM_OPCO"] AND $fce->RETURN_DATA->row["NMPLAN"] == $row["PLANT_OPCO"] AND $fce->RETURN_DATA->row["NAMA_SOPIR"] == $this->_dtlso["DRIVER"]){
                                $SONMPLAN = $fce->RETURN_DATA->row["NMPLAN"];
                                $SONMORG = $fce->RETURN_DATA->row["NMORG"];
                                $SONAMA_SOPIR = $fce->RETURN_DATA->row["NAMA_SOPIR"];
                                $SONO_SIM = $fce->RETURN_DATA->row["NO_SIM"];
                                $this->_sopir[$i]["SOPIRLENGKAP"] = '0';
                            }else{
                                // echo "masuk else1";
                                $this->_sopir[$i]["SOPIRTIDAKADA"] = '0';
                            }
                        }else{
                            // echo "masuk else2";
                            $this->_sopir[$i]["SOPIRTIDAKADA"] = '0';
                        }
                    }else{
                        // echo "masuk else3";
                        $this->_sopir[$i]["SOPIRLENGKAP"] = '0';
                    }
                }
                if($SONMPLAN != ''){
                    $this->sopir->Text=$SONAMA_SOPIR;;			
                    $this->nosim->Text=$SONO_SIM;
                    $this->_sopir[$i]["DAFTARSOPIR"] = '0';
                    $this->_sopir[$i]["NMPLAN"] = $SONMPLAN;
                    $this->_sopir[$i]["SONMORG"] = $SONMORG;
                    $this->_sopir[$i]["NAMA_SOPIR"] = $SONAMA_SOPIR;
                    $this->_sopir[$i]["SIM"] = $SONO_SIM;
                }
                
                // if(trim($fce->RETURN["TYPE"])=='E') echo "Sopir ".$fce->RETURN["MESSAGE"];
                if(trim($fce->RETURN["TYPE"])=='E') return $this->_return["ERROR"] = "Sopir ".$fce->RETURN["MESSAGE"];
            }

            $fce->Close();
            // return $this->_sopir;
        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            echo $e->getMessage();
        }
    }

    function insertSopir(){
        try
        {
            $fce = $this->sap->NewFunction ("Z_ZAPPSD_INSERT_SOPIR");
            if ($fce == false ) {
                $this->sap->PrintStatus();
                exit;
            }

            //Param Export
            $fce->XPARAM["NAMA_SOPIR"] = strtoupper($this->_dtlso["DRIVER"]);
            $fce->XPARAM["NO_SIM"] = strtoupper($this->_dtlso["NO_SIM"]);
            $fce->XPARAM["NMORG"] = $this->_dtlso["ORG"];
            $fce->XPARAM["NMPLAN"] = $this->_dtlso["PLANT"];
            $fce->XPARAM["STATUS"] = '0';
            $fce->XPARAM["DEL"] = '0';
            $fce->XPARAM["CREATED_BY"] = $this->_dtlso["USERNAME"];
            $fce->XPARAM["CREATE_DATE"] = date('Ymd');

            //Execute Function
            $fce->Call();
            $fce->Close();

        }
        catch(Exception $e) // an exception is raised if a query fails will be raised
        {
            $this->laporan->Text=$e->getMessage();
        }
    }

    function DBselect($conn,$columns, $table, $whereClause = ''){

        // Construct the SQL statement
        $sql = "SELECT $columns FROM $table";

        // Append the WHERE clause if provided
        if (!empty($whereClause)) {
            $sql .= " WHERE $whereClause";
        }

        // echo $sql;
        // $query = @oci_parse($conn, $sql);
        // @oci_execute($query);

        // Prepare and execute the SQL query
        $query = oci_parse($conn, $sql);
        if (!$query) {
            $e = oci_error($conn);
            echo $sql;
            throw new Exception('<br>Failed to parse SQL query: ' . $e['message']);
        }

        $r = oci_execute($query);
        if (!$r) {
            $e = oci_error($query);
            echo $sql;
            throw new Exception('<br>Failed to execute SQL query: ' . $e['message']);
        }

        // Fetch the results
        $results = oci_fetch_array($query, OCI_ASSOC);

        return $results;
    }

    // function DBinsert($conn, $columns, $values, $table) {
    //     // Join columns array to form the columns part of the SQL statement
    //     $columnsString = implode(', ', $columns);

    //     // Sanitize and join values array to form the values part of the SQL statement
    //     $sanitizedValues = array();
    //     foreach ($values as $value) {
    //         $sanitizedValues[] = $value;
    //     }
    //     $valuesString = implode(', ', $sanitizedValues);

    //     // Construct the SQL statement
    //     $sql = "INSERT INTO $table ($columnsString) VALUES ($valuesString)";
    //     $query=@oci_parse($conn, $sql);
    //     if (!$query) {
    //         $e = oci_error($conn);
    //         echo $sql;
    //         throw new Exception('<br>Failed to parse SQL query: ' . $e['message']);
    //     }
        
    //     $r = oci_execute($query);
    //     if (!$r) {
    //         $e = oci_error($query);
    //         echo $sql;
    //         throw new Exception('<br>Failed to execute SQL query: ' . $e['message']);
    //     }

    //     // $return = oci_fetch_array($query, OCI_ASSOC);
    //     // return $return;
    // }

    function DBinsert($conn, $field_names, $field_data, $tablename){
        $query = "INSERT INTO $tablename ($field_names[0]";
        for($k=1;$k< count($field_names);$k++)
        {
                $query.=', '."$field_names[$k]";
        }
        $query.=") VALUES ('$field_data[0]'";
        for($k=1;$k< count($field_data);$k++)
        {
                list($tang,$gal)=split("_",$field_data[$k]);
                if($tang=="instgl")
                        $query.=', '."TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
                else if($field_data[$k]=='SYSDATE')
                        $query.=', '."$field_data[$k]";
                else
                $query.=', '."'$field_data[$k]'";
        }
                $query.=')';

        $parse=@oci_parse($conn, $query);
        if (!$parse) {
            $e = oci_error($conn);
            echo $query;
            throw new Exception('<br>Failed to parse SQL query: ' . $e['message']);
        }
        
        $r = oci_execute($parse);
        if (!$r) {
            $e = oci_error($parse);
            echo $query;
            throw new Exception('<br>Failed to execute SQL query: ' . $e['message']);
        }

        // $return = oci_fetch_array($query, OCI_ASSOC);
        // return $return;
    }

    function DBupdate($conn, $table, $values, $conditions){
        // Construct the SET part of the SQL statement
        $setClauses = array();
        foreach ($values as $column => $value) {
            $setClauses[] = "$column = " . $value;
        }
        $setString = implode(', ', $setClauses);
    
        // Construct the WHERE part of the SQL statement
        $whereClauses = array();
        foreach ($conditions as $column => $value) {
            $whereClauses[] = "$column = " . $value;
        }
        $whereString = implode(' AND ', $whereClauses);
    
        // Construct the SQL statement
        $sql = "UPDATE $table SET $setString WHERE $whereString";
        // echo $sql;
        
        // Prepare and execute the SQL query
        $query = oci_parse($conn, $sql);
        if (!$query) {
            $e = oci_error($conn);
            echo $sql;
        throw new Exception('<br>Failed to parse SQL query: ' . $e['message']);
        }
    
        $r = oci_execute($query);
        if (!$r) {
            $e = oci_error($query);
            echo $sql;
            throw new Exception('<br>Failed to execute SQL query: ' . $e['message']);
        }
    
        return true;
    }   

    function formatnopolisi($string){
        $string = strtoupper(trim($string));  

        $pattern = '/^([A-Z]{1,3})(\s|-)*([1-9][0-9]{0,3})(\s|-)*([A-Z]{0,3}|[1-9][0-9]{1,2})$/i';
        if (preg_match($pattern, $string)) {
            return trim(strtoupper(preg_replace($pattern, '$1-$3-$5', $string)));
        }

        // militer dan kepolisian
        $pattern = '/^([0-9]{1,5})(\s|-)*([0-9]{2}|[IVX]{1,5})*/';
        if (preg_match($pattern, $string)) {
            return trim(strtoupper(preg_replace($pattern, '$1-$3', $string)));
        }
        return null;
    }

    function AutoNum($numint,$pjg){
        $val="";
        $jmlchar=strlen(strval($numint));
        for($i=0;$i<($pjg-$jmlchar);$i++){
        $val .= "0";
        }
        $val .= strval($numint)."";
        return $val; 
    }
}


?>
