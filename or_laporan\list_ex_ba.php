<?

session_start();
include ('../include/my_fungsi.php');

$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

//$importtargetVolume='upload_customer_replenishment_report.php?act=update';
$cancelUrl='list_ex_ba.php?';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

$ckp = basename($_SERVER['SCRIPT_NAME']);

if ($ckp != 'login.php') {
    if (empty($_SESSION['user_id'])) {
        echo '<script type="text/javascript">';
        echo 'window.location.href = "https://csms.sig.id/sdonline/login.php";';
        echo '</script>';
        exit;
    }
}



?>
<!DOCTYPE html>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>List EX BA </title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>

<div align="center">   
    <table id="dg" title="EX BA " class="easyui-datagrid" style="width:100%;height:350px">
        <thead>
            <tr>
                <th field="ID" width="5%">ID</th>
                <th field="NO_BA_EX" width="10%">NO_BA_EX</th>
                <th field="NO_BA_SAP" width="10%">NO_BA_SAP</th>
				<th field="NO_PAJAK_EX" width="10%">NO_PAJAK_EX</th>
                <th field="TOTAL_INV" width="7%">TOTAL_INV</th>
				<th field="PAJAK_INV" width="3%">PAJAK_INV</th>
                <th field="NO_VENDOR" width="10%">NO_VENDOR</th>
                <th field="NAMA_VENDOR" width="10%">NAMA_VENDOR</th>
				<th field="KLAIM_KTG" width="5%">KLAIM_KTG</th>
                <th field="KLAIM_SEMEN" width="5%">KLAIM_SEMEN</th>
                <th field="PDPKS" width="3%">PDPKS</th>
				<th field="NO_BA" width="3%">NO_BA</th>
                <th field="TGL_INVOICE" width="5%">TGL_INVOICE</th>
                <th field="ONGKOS_PARKIR" width="5%">ONGKOS_PARKIR</th>
				<th field="ALAMAT_VENDOR" width="5%">ALAMAT_VENDOR</th>
                <th field="DISTRIK_VENDOR" width="5%">DISTRIK_VENDOR</th>
                <th field="NO_REKENING" width="5%">NO_REKENING</th>
				<th field="BANK" width="5%">BANK</th>
                <th field="CURR" width="5%">CURR</th>
                <th field="ALAT_BAYAR" width="5%">ALAT_BAYAR</th>
				<th field="KETERANGAN" width="5%">KETERANGAN</th>
                <th field="KET_BAYAR" width="5%">KET_BAYAR</th>
                <th field="NO_PP" width="5%">NO_PP</th>
				<th field="KEPALA" width="5%">KEPALA</th>
                <th field="ATASAN" width="5%">ATASAN</th>
                <th field="VERIFIKASI" width="5%">VERIFIKASI</th>
				<th field="UNIT" width="5%">UNIT</th>
                <th field="LOKASI" width="5%">LOKASI</th>
                <th field="TGL_APPROVED" width="5%">TGL_APPROVED</th>
				<th field="TGL_PAYMENT" width="5%">METERAI_SPDS_DOC_URL</th>
                <th field="PDPKK" width="5%">PDPKK</th>
                <th field="DELETE_MARK" width="5%">DELETE_MARK</th>
				<th field="LAST_UPDATE_DATE" width="5%">LAST_UPDATE_DATE</th>
                <th field="LAST_UPDATED_BY" width="5%">LAST_UPDATED_BY</th>
                <th field="BANK_CABANG" width="5%">BANK_CABANG</th>
				<th field="TGL_PAJAK_EX" width="5%">TGL_PAJAK_EX</th>
                <th field="ORG" width="5%">ORG</th>
                <th field="TOTAL_INVOICE" width="5%">TOTAL_INVOICE</th>
				<th field="ACCOUNTING_DOC" width="5%">ACCOUNTING_DOC</th>
                <th field="TAHUN" width="5%">TAHUN</th>
                <th field="BVTYP" width="5%">BVTYP</th>
				<th field="FLAG_TRN" width="5%">FLAG_TRN</th>
                <th field="TGL_TERMIN" width="5%">TGL_TERMIN</th>
                <th field="TERMIN" width="5%">TERMIN</th>
				<th field="TGL_BA" width="5%">TGL_BA</th>
                <th field="EKSPEDISI" width="5%">EKSPEDISI</th>
                <th field="NPWP_VENDOR" width="5%">NPWP_VENDOR</th>
				<th field="SKBP" width="5%">SKBP</th>
				<th field="APPROVE_KASI" width="5%">APPROVE_KASI</th>
				<th field="APPROVE_KABIRO" width="5%">APPROVE_KABIRO</th>
				<th field="APPROVE_KASI_ON" width="5%">APPROVE_KASI_ON</th>
				<th field="APPROVE_KABIRO_ON" width="5%">APPROVE_KABIRO_ON</th>
				<th field="EMAIL_KASI" width="5%">EMAIL_KASI</th>
				<th field="EMAIL_KABIRO" width="5%">EMAIL_KABIRO</th>
				<th field="TGL_APPROVE_KASI" width="5%">TGL_APPROVE_KASI</th>
				<th field="TGL_APPROVE_KABIRO" width="5%">TGL_APPROVE_KABIRO</th>
				<th field="APPROVE_KASI_BY" width="5%">APPROVE_KASI_BY</th>
				<th field="APPROVE_KABIRO_BY" width="5%">APPROVE_KABIRO_BY</th>
				<th field="STATUS_DOKUMEN" width="5%">STATUS_DOKUMEN</th>
				<th field="NO_KWITANSI" width="5%">NO_KWITANSI</th>
				<th field="CREATE_DATE" width="5%">CREATE_DATE</th>
				<th field="NO_BAF" width="5%">NO_BAF</th>
				<th field="SKBP" width="5%">SKBP</th>
				<th field="PAST_ACC_DOC" width="5%">PAST_ACC_DOC</th>
				<th field="PAST_MIR" width="5%">PAST_MIR</th>
				<th field="COUNT_SPJ" width="5%">COUNT_SPJ</th>
				<th field="STATUS_BA" width="5%">STATUS_BA</th>
				<th field="FILENAME" width="5%">FILENAME</th>
				<th field="ALASAN_REJECT" width="5%">ALASAN_REJECT</th>
				<th field="TIPE_ALASAN" width="5%">TIPE_ALASAN</th>
				<th field="SIGN_LLX_1" width="5%">SIGN_LLX_1</th>
				<th field="SIGN_LLY_1" width="5%">SIGN_LLY_1</th>
				<th field="SIGN_URX_1" width="5%">SIGN_URX_1</th>
				<th field="SIGN_URY_1" width="5%">SIGN_URY_1</th>
				<th field="SIGN_PAGE_1" width="5%">SIGN_PAGE_1</th>
				<th field="SIGN_LLX_2" width="5%">SIGN_LLX_2</th>
				<th field="SIGN_LLY_2" width="5%">SIGN_LLY_2</th>
				<th field="SIGN_URX_2" width="5%">SIGN_URX_2</th>
				<th field="SIGN_URY_2" width="5%">SIGN_URY_2</th>
				<th field="SIGN_PAGE_2" width="5%">SIGN_PAGE_2</th>
				<th field="SIGN_ORDER_ID_1" width="5%">SIGN_ORDER_ID_1</th>
				<th field="SIGN_ORDER_ID_2" width="5%">SIGN_ORDER_ID_2</th>
				<th field="SIGN_STATUS_1" width="5%">SIGN_STATUS_1</th>
				<th field="SIGN_STATUS_2" width="5%">SIGN_STATUS_2</th>
				<th field="SIGN_TOKEN_1" width="5%">SIGN_TOKEN_1</th>
				<th field="SIGN_TOKEN_2" width="5%">SIGN_TOKEN_2</th>
				<th field="ID_USER_APPROVAL" width="5%">ID_USER_APPROVAL</th>
			</tr>
        </thead>
    </table>
    <div id="toolbar">
<!--        <a href="upload_customer_replenishment_report.php?act=update" title="Import Exel" class="easyui-linkbutton c6" iconCls="icon-edit" target="_blank">Upload Data</a>-->
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editAct()">Edit</a>
        <label>No BA :</label>    
		<input id="cari_spjtrn" name="cari_spjtrn" class="easyui-textbox" >
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="newSearch" style="width:80px">Search</a>
        </div>
    
    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px"
         closed="true" buttons="#dlg-buttons">
        <div class="ftitle">EX BA INVOICE</div>
        <form id="fm" method="post" novalidate>
         <div class="fitem">
                <label>ID:</label>
                <input name="ID" id="ID" style="width:650px" class="easyui-textbox" required="true" readonly>
            </div>
			<div class="fitem">
                <label>SIGN_STATUS_2:</label>
                <input name="SIGN_STATUS_2" id="SIGN_STATUS_2" style="width:650px" class="easyui-textbox" >
            </div>
			<div class="fitem">
                <label>STATUS BA :</label>
                <input name="STATUS_BA" id="STATUS_BA" style="width:650px" class="easyui-textbox" required="true">
			</div>
		</form>
    </div>
    
    <div id="dlg-buttons">  
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
    </div>
</div>

<script type="text/javascript">
 $(function(){
    $("#dg").datagrid({
            url:'list_ex_ba_act.php?act=show',
            singleSelect:true,
            pagination:true, 
            pageList:[5,10,20,30,40,50,100,200,300],
            pageSize:10,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
    $('#dg').datagrid('enableFilter');
    
 }); 
$("#newSearch").click(function() {   
    var w_no_shp_trn = $('#cari_spjtrn').textbox('getValue');
   
	    if(w_no_shp_trn==""  ){
               $.messager.show({
                    title: 'Error',
                    msg: 'Isi Filter terlebih dahulu'
                });
          }else{   
         //$('#sold_to, #shipto1, #sold_to_opco, #shipto_opco1').val("")
           $('#dg').datagrid('load',{
                WTRNCODE: w_no_shp_trn,
              
           }); // reload the user data
          }  
        });

function editAct(){
var row = $('#dg').datagrid('getSelected');
if (row){
    $('#dlg').dialog('open').dialog('setTitle','Edit District');
//    var idnh = row.ID;
    $('#SIGN_STATUS_2').textbox('setValue', row.SIGN_STATUS_2);
	$('#STATUS_BA').textbox('setValue', row.STATUS_BA);
	$('#ID').textbox('setValue', row.ID);

    
    url = "list_ex_ba_act.php?act=update&NO_BA="+row.NO_BA;
}else{
        alert("Pilih baris data yang ingin di edit terlebih dahulu !");
    }
}

function saveAct(){
$('#fm').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
        } else {
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        }
    }
});
}

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
