<?
include ('fungsi.php');
class kpl_fungsi extends fungsi 
{

//	var $kpl_username = "APPSGG";
//	var $kpl_password = "sgmerdeka99";
//	var $kpl_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';

    	var $kpl_username = "dev";
	var $kpl_password = "semeru2";
//	var $kpl_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
	var $kpl_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = DEVSGG)(SERVER = DEDICATED)))';

	public function kpl_koneksi()
	{
		$conn = oci_connect($this->kpl_username, $this->kpl_password, $this->kpl_db );//dev
                //$oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_210.php');//prod
                //$conn = oci_connect($oraConfig['username_conn'], $oraConfig['password_conn'], $oraConfig['db']);//prod
		if (!$conn)
			return false;
		else
		 return $conn;
	}
	function kpl_vehicle_type($ting){
		$k=array(1 => 'Nama 100', 'Nama 200', 'Nama 300', 'Nama 400', 'Nama 500');
		$nama=array(1 => '100', '200', '300','400','500');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function kpl_order_type($ting){
		$k=array(1 => 'ZOR', 'ZPR', 'ZFC', 'ZEX');
		$nama=array(1 => 'Sales Standart', 'Sales Project', 'Sales FOC','Sales Export');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}

	function kpl_jns_plant($ting){
		$k=array(1 => 'PABRIK', 'GUDANG');
		$nama=array(1 => 'PABRIK', 'GUDANG');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function kpl_tipecharter($ting){ 	
		$k=array(1 => '401','402','403','404','409','410');
		$nama=array(1 => 'VOYAGE CHARTER BAG','VOYAGE CHARTER CURAH','TIME CHARTER','NON CHARTER','FREIGHT CHARTER','GROSS TIME CHARTER');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function kpl_jenis($ting){
		$k=array(1 => 'KM','VM','PLM','KLM','TK','LCT');
		$nama=array(1 => 'KAPAL MOTOR','MOTOR VESEL','PERAHU LAYAR MOTOR','KAPAL LAYAR MOTOR','TONGKANG','LCT');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function kpl_incoterm($ting){
		$k=array(1 => 'FRC','FOB','CNF','CIF');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}

	function kpl_bayar($ting){
		$k=array(1 => 'KREDIT','TUNAI');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function kpl_dermaga($port,$dermg){
		$conn = $this->kpl_koneksi();
		$sql= "SELECT * FROM KPL_DERMAGA_V WHERE KD_PORT='$port'";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$a =0;
		while ($row = oci_fetch_array($query)){
		$a += 1;
		$kode[$a]= $row['ID'];
		$nama[$a]=$row['NM_DERMG'];
		$daftar[$a]=$nama[$a];
		}
		$daftar[0]="--Pilih Dermaga--";
		$kode[0]="";
		for($x=0;$x<=$a;$x++)
		{
		echo("<option value='$kode[$x]' title='$daftar[$x]'");
		if($kode[$x] == $dermg){echo("selected");}
		echo(">$daftar[$x]</option>");
		}
	}
	function kpl_gangguan($ting){
		$conn = $this->kpl_koneksi();
		$sql= "SELECT * FROM KPL_GANGGUAN WHERE DELETE_MARK ='0'";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$a =0;
		while ($row = oci_fetch_array($query)){
		$a += 1;
		$kode[$a]= $row['ID'];
		$nama[$a]=$row['KETERANGAN'];
		$daftar[$a]=$nama[$a];
		}
		$daftar[0]="--Pilih Gangguan--";
		$kode[0]="";
		for($x=0;$x<=$a;$x++)
		{
		echo("<option value='$daftar[$x]' title='$daftar[$x]'");
		if($daftar[$x] == $ting){echo("selected");}
		echo(">$daftar[$x]</option>");
		}
	}

	function kpl_produk($idnya){
		$conn = $this->kpl_koneksi();
		$sql= "SELECT ID,KODE_PRODUK,NAMA_PRODUK FROM OR_TRANS_APP WHERE FLAG_KAPAL='$idnya' AND DELETE_MARK ='0'";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$a =0;
		while ($row = oci_fetch_array($query)){
		$a += 1;
		$kodenya[$a]= $row['ID'];
		$kode[$a]= $row['KODE_PRODUK'];
		$nama[$a]=$row['NAMA_PRODUK'];
		$daftar[$a]=$nama[$a];
		}
		$daftar[0]="--Pilih Produk--";
		$kode[0]="";

		for($x=0;$x<=$a;$x++)
		{
		echo("<option value='$kode[$x]' title='$daftar[$x]' label='$kodenya[$x]'");
		if($kode[$x] == $port){echo("selected");}
		echo(">$daftar[$x]</option>");
		}
	}
	function kpl_produk_po($idnya){
		$conn = $this->kpl_koneksi();
		$sql= "SELECT ID,KD_MAT,NM_MAT FROM KPL_TRANS_DTL WHERE ID_HDR='$idnya' AND DELETE_MARK ='0'";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$a =0;
		while ($row = oci_fetch_array($query)){
		$a += 1;
		$kodenya[$a]= $row['ID'];
		$kode[$a]= $row['KD_MAT'];
		$nama[$a]=$row['NM_MAT'];
		$daftar[$a]=$nama[$a];
		}
		$daftar[0]="--Pilih Produk--";
		$kode[0]="";

		for($x=0;$x<=$a;$x++)
		{
		echo("<option value='$kode[$x]' title='$daftar[$x]' label='$kodenya[$x]'");
		if($kode[$x] == $port){echo("selected");}
		echo(">$daftar[$x]</option>");
		}
	}

	function kpl_cari_so2($org,$noso,$posnr)
	{
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_SO_QTY");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$new_number = $posnr*10;
		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='00000'.$new_number;
		if($panjang==2)$new_number_ok='0000'.$new_number;
		if($panjang==3)$new_number_ok='000'.$new_number;
		if($panjang==4)$new_number_ok='00'.$new_number;
		if($panjang==5)$new_number_ok='0'.$new_number;
		if($panjang==6)$new_number_ok=$new_number;

		$fce->XVKORG = $user_org; //org
		$fce->I_VBELN = $noso; // sold to
		$fce->I_POSNR = $new_number_ok; // sold to
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$qty_so= $fce->E_RESULT["JML_SO"];
			$qty_do= $fce->E_RESULT["JML_DO"];
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $qty_do;
	}
	
	function kpl_cari_so($org,$noso)
	{
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_SO_QTY");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$new_number = $posnr*10;
		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='00000'.$new_number;
		if($panjang==2)$new_number_ok='0000'.$new_number;
		if($panjang==3)$new_number_ok='000'.$new_number;
		if($panjang==4)$new_number_ok='00'.$new_number;
		if($panjang==5)$new_number_ok='0'.$new_number;
		if($panjang==6)$new_number_ok=$new_number;

		$fce->XVKORG = $user_org; //org
		$fce->I_VBELN = $noso; // sold to
		$fce->I_POSNR = $new_number_ok; // sold to
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$qty_so= $fce->E_RESULT["JML_SO"];
			$qty_do= $fce->E_RESULT["JML_DO"];
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $qty_do;
	}


	function kpl_cari_vendor($org,$vendor)
	{
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZCSD_VENDOR");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		if ($org=="2000")$ktokk="4100";
		if ($org=="3000")$ktokk="4200";
		if ($org=="4000")$ktokk="4300";

	$panjang=strlen(strval($vendor));
		if($panjang==1)$vendor='000000000'.$vendor;
		if($panjang==2)$vendor='00000000'.$vendor;
		if($panjang==3)$vendor='0000000'.$vendor;
		if($panjang==4)$vendor='000000'.$vendor;
		if($panjang==5)$vendor='00000'.$vendor;
		if($panjang==6)$vendor='0000'.$vendor;
		if($panjang==7)$vendor='000'.$vendor;
		if($panjang==8)$vendor='00'.$vendor;
		if($panjang==9)$vendor='0'.$vendor;

		$fce->XKTOKK = $ktokk;
		$fce->XDLGRP = "";
		$fce->XLIFNR = $vendor;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
				$lifnr= $fce->RETURN_DATA->row["LIFNR"];
				$nama= str_replace('"','',$fce->RETURN_DATA->row["NAME1"]);
				$kota=$fce->RETURN_DATA->row["ORT01"];
				$alamat=$fce->RETURN_DATA->row["STRAS"];

		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $alamat;
	}
	function kpl_plant($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPP_SELECT_SYSPLAN");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
		$fce->XPARAM = '2000';
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
			$plantcode[]= $fce->RETURN_DATA->row["WERKS"];
			$plantdesc[]= $fce->RETURN_DATA->row["NAME1"];
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($plantcode);$x++)
		{
		echo("<option value='$plantcode[$x]' title='$plantdesc[$x]' ");
		if($plantdesc[$x] == $ting){echo("selected");}
		echo(">$plantcode[$x]"." - "."$plantdesc[$x]</option>");
		}
	
	}

	public function kpl_clearsessi_all()
	{
		$this->kpl_clearsessi_waktu_kirim();
	}
	public function kpl_clearsessi_waktu_kirim()
	{
		unset($_SESSION['kpl_asal_waktu']);
		unset($_SESSION['kpl_nama_asal_waktu']);
		unset($_SESSION['kpl_tujuan_waktu']);
		unset($_SESSION['kpl_nama_tujuan_waktu']);
		unset($_SESSION['kpl_incoterm_waktu']);
		unset($_SESSION['kpl_nama_incoterm_waktu']);
		unset($_SESSION['kpl_vehicle_type_waktu']);
		unset($_SESSION['kpl_waktu_waktu']);
		unset($_SESSION['kpl_keterangan_waktu']);
		unset($_SESSION['kpl_tgl_mulai_waktu']);
		unset($_SESSION['kpl_tgl_selesai_waktu']);
	}
	function kpl_new_number($conn)
	{
		$sql="SELECT KPL_TRANS_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];
		/*
		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='000000000'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		*/
		return $new_number;

	}
	function nospm($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$nospm='00000'.$kode;
		if($panjang==2)$nospm='0000'.$kode;
		if($panjang==3)$nospm='000'.$kode;
		if($panjang==4)$nospm='00'.$kode;
		if($panjang==5)$nospm='0'.$kode;
		if($panjang==6)$nospm=$kode;
		return $nospm;
	}
	
	function sapcode($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$sapcode='000000000'.$kode;
		if($panjang==2)$sapcode='00000000'.$kode;
		if($panjang==3)$sapcode='0000000'.$kode;
		if($panjang==4)$sapcode='000000'.$kode;
		if($panjang==5)$sapcode='00000'.$kode;
		if($panjang==6)$sapcode='0000'.$kode;
		if($panjang==7)$sapcode='000'.$kode;
		if($panjang==8)$sapcode='00'.$kode;
		if($panjang==9)$sapcode='0'.$kode;
		if($panjang==10)$sapcode=$kode;
		return $sapcode;
	}
	function linenum($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$linenum='00000'.$kode;
		if($panjang==2)$linenum='0000'.$kode;
		if($panjang==3)$linenum='000'.$kode;
		if($panjang==4)$linenum='00'.$kode;
		if($panjang==5)$linenum='0'.$kode;
		if($panjang==6)$linenum=$kode;
		return $linenum;
	}
	function qtyso($qty)
	{
		$panjang=strlen(strval($qty));
		if($panjang==1)$qtyso='000000000000'.$qty;
		if($panjang==2)$qtyso='00000000000'.$qty;
		if($panjang==3)$qtyso='0000000000'.$qty;
		if($panjang==4)$qtyso='000000000'.$qty;
		if($panjang==5)$qtyso='00000000'.$qty;
		if($panjang==6)$qtyso='0000000'.$qty;
		if($panjang==7)$qtyso='000000'.$qty;
		if($panjang==8)$qtyso='00000'.$qty;
		if($panjang==9)$qtyso='0000'.$qty;
		if($panjang==10)$qtyso='000'.$qty;
		if($panjang==11)$qtyso='00'.$qty;
		if($panjang==12)$qtyso='0'.$qty;
		return $qtyso;
	}
	function kpl_status($status)
	{
	if($status==0)$hasil="ANTRI";
	else if($status==1)$hasil="MATCHING";
	else if($status==2)$hasil="TENDER";
	else if($status==3)$hasil="SANDAR";
	else if($status==4)$hasil="MULAI MUAT";
	else if($status==5)$hasil="SELESAI MUAT";
	else if($status==6)$hasil="BERLAYAR";
	else if($status==7)$hasil="SAMPAI TUJUAN";
	else if($status==8)$hasil="SANDAR BONGKAR";
	else if($status==9)$hasil="MULAI BONGKAR";
	else if($status==10)$hasil="SELESAI BONGKAR";
	else if($status==11)$hasil="BERLAYAR KEMBALI";
	return $hasil;
	}
	function kpl_statuslist($ting){
		$k=array(1 => '0','1','2','3','4','5','6','7','8','9','10');
		$nama=array(1 => 'ANTRI','MATCHING','TENDER','SANDAR','MULAI MUAT','SELESAI MUAT','BERLAYAR','SAMPAI TUJUAN','SANDAR BONGKAR','MULAI BONGKAR','SELESAI BONGKAR');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x] - $nama[$x]</option>");
			}
	}

}
?>
