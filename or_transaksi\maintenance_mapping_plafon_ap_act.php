<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'kode_region';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$periode = date('Y-m', strtotime(htmlspecialchars($_REQUEST['periode'])));
$distrik_ret = htmlspecialchars($_REQUEST['distrik_ret']);
$target = htmlspecialchars($_REQUEST['target']);
$target_alloc = htmlspecialchars($_REQUEST['target_alloc']);

$delete = htmlspecialchars($_REQUEST['delete']);
$id = htmlspecialchars($_REQUEST['id']);
$created_by = htmlspecialchars($user_name);
$UPDATE_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 4;
                    for ($row = 3; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                $messageLog = array();

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    $mapLog = array();
                    // Skip baris yang kosong
                    if (empty($row[1]) && empty($row[2])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[1]) || empty($row[2])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        $mapLog['baris'] = ($rowNumber - 2);
                        $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " memiliki data yang tidak lengkap. ";
                        array_push($messageLog, $mapLog);
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkDuplicateData($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3],$row[4])) {
                        $check_plafon = checkSisaPlafon($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3],$row[4]);
                        if ($check_plafon['status']) {
                            $messageRows['failedUpdate'][] = $rowNumber;
                            $mapLog['baris'] = ($rowNumber - 2);
                            $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " Gagal update, SNOP akhir tidak boleh kurang dari Target SPC (".$check_plafon['target_spc']."). ";
                            array_push($messageLog, $mapLog);
                            continue;
                        }else {
                            if (update($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3], $row[4], $created_by)) {
                                $messageRows['successUpdate'][] = $rowNumber;
                                $mapLog['baris'] = ($rowNumber - 2);
                                $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " berhasil update data. ";
                                array_push($messageLog, $mapLog);
                                continue;
                            } else {
                                $messageRows['system'][] = $rowNumber;
                                $mapLog['baris'] = ($rowNumber - 2);
                                $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " gagal diinputkan karena kesalahan sistem. ";
                                array_push($messageLog, $mapLog);
                                continue;
                            }
                        }
                    }

                    // Jika tidak ada masalah, lakukan upload
                    if (insert($conn, date('Y-m', strtotime($row[1])), $row[2], $row[3], $row[4], $created_by)) {
                        $messageRows['success'][] = $rowNumber;
                        $mapLog['baris'] = ($rowNumber - 2);
                        $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " berhasil diinputkan. ";
                        array_push($messageLog, $mapLog);
                    } else {
                        $messageRows['system'][] = $rowNumber;
                        $mapLog['baris'] = ($rowNumber - 2);
                        $mapLog['message'] = "Baris ke-" . ($rowNumber - 2) . " gagal diinputkan karena kesalahan sistem. ";
                        array_push($messageLog, $mapLog);
                    }
                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris yang sukses di update
                if (!empty($messageRows['successUpdate'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['successUpdate']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diupdate. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }
                
                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['failedUpdate'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['failedUpdate']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " Gagal Update, Target SNOP Lebih Kecil dari SPC. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage, 'log' => $messageLog));
            }
        }
        break;        
        case 'show' :
        {
            $filt_periode = $filter_periode;
            $filt = array('periode' => $filt_periode);
            displayData($conn, $filt);
        }
        break;
        case 'add':
        {
            if (checkDuplicateData($conn,$periode, $distrik_ret, $target)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            } else {
                if (insert($conn,$periode,$distrik_ret, $target, $target_alloc, $created_by)) {
                    echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                } else {
                    echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                }
            }
        }
        break;
        case 'edit' :
        {
            $check_plafon = checkSisaPlafon($conn, $periode, $distrik_ret, $target, $target_alloc);
            if ($check_plafon['status']) {
                echo json_encode(array('errorMsg' => 'SNOP akhir tidak boleh kurang dari Target SPC ('.$check_plafon['target_spc'].')!'));
            }else {
                $sqlcek= "UPDATE MAPPING_PLAFON_TARGET set PERIODE = '$periode', DISTRIK_RET = '$distrik_ret', TARGET = '$target', TARGET_ALLOC = '$target_alloc', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
                $querycek= oci_parse($conn, $sqlcek);
                $return=oci_execute($querycek);
                if ($return){
                    echo json_encode(array('success'=>true,'info'=>"Edit data success"));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "UPDATE MAPPING_PLAFON_TARGET set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'multipleDel' :
        {

            $value = ($_POST['data']);
            $list = array();
            $gagal = 0;
            $sukses= 0;
            $i=0; 
            
            while($i < count($value)){
                $idDlt = $value[$i]['ID'];          
                $sql = "UPDATE MAPPING_PLAFON_TARGET set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $idDlt ";
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
    
                if($result){ 
                    $sukses=$sukses+1; 
                }else{ 
                    $gagal=$gagal+1; 
                }
    
                array_push($list, $ID);

                $i++;
            }  
            
            if ($result){
                $keterangan = array('success'=>"Data Berhasil Di Delete : ".$sukses.", gagal : ".$gagal." ! ");
            } else {
                $keterangan = array('errorMsg'=>"Data Gagal Di Delete : ".$gagal." ! ");
            }
            // }
            echo json_encode($keterangan);

        }
        break;
    }
}

function checkSisaPlafon($conn, $periode, $distrik_ret, $target, $target_alloc){
    $sql_count = "SELECT
	mpt.*,
	COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_AKHIR,
	COALESCE(spc.TARGET, 0) AS TARGET_SPC,
	COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(spc.TARGET, 0) AS SISA_PLAFON,
	spc.SEGMEN,
	TO_CHAR(mpt.CREATED_AT, 'DD-MON-YYYY') AS CREATED_AT_F,
	TO_CHAR(mpt.UPDATED_AT, 'DD-MON-YYYY') AS UPDATED_AT_F
FROM
	MAPPING_PLAFON_TARGET mpt
LEFT JOIN (
	SELECT
		to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
		mmw.DISTRIK_RET,
		CASE
			WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
			WHEN mmw.KODE_REGION IN ('1', '2', '6')
			AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
			ELSE mms3.SEGMEN
		END AS SEGMEN,
			sum(tb1.TARGET) AS TARGET
	FROM
			ZSD_TARGET_HARIAN_NEW_BRAND tb1
	LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
			tb1.DISTRIK = mmw.KODE_DISTRIK
	LEFT JOIN
    (
		SELECT
				DISTINCT
        mms.BRAND,
				mms.DISTRIK_RET,
				mms.SEGMEN
		FROM
				MASTER_MAPPING_SEGMEN mms
		JOIN (
			SELECT
					BRAND,
					DISTRIK_RET,
					MAX(PERIODE) AS MAX_PERIODE
			FROM
					MASTER_MAPPING_SEGMEN
			WHERE
					DEL_MARK = '0'
			GROUP BY
					BRAND,
					DISTRIK_RET
                        ) latest
                        ON
				mms.BRAND = latest.BRAND
			AND mms.DISTRIK_RET = latest.DISTRIK_RET
			AND mms.PERIODE = latest.MAX_PERIODE
		WHERE
				mms.DEL_MARK = '0' ) mms1 ON
			mmw.KODE_REGION IN ('3', '4', '5')
		AND tb1.BRAND = mms1.BRAND
		AND mmw.DISTRIK_RET = mms1.DISTRIK_RET
	LEFT JOIN
                            (
		SELECT
					DISTINCT
        mms.BRAND,
					mms.KD_PROP,
					mms.KD_DISTRIK,
					mms.SEGMEN
		FROM
					MASTER_MAPPING_SEGMEN mms
		JOIN (
			SELECT
						BRAND,
						KD_PROP,
						KD_DISTRIK,
						MAX(PERIODE) AS MAX_PERIODE
			FROM
						MASTER_MAPPING_SEGMEN
			WHERE
						DEL_MARK = '0'
			GROUP BY
						BRAND,
						KD_PROP,
						KD_DISTRIK
                                ) latest
                                    ON
					mms.BRAND = latest.BRAND
			AND mms.KD_PROP = latest.KD_PROP
			AND mms.KD_DISTRIK = latest.KD_DISTRIK
			AND mms.PERIODE = latest.MAX_PERIODE
		WHERE
					mms.DEL_MARK = '0') mms2 ON
				mmw.KODE_REGION IN ('1', '2', '6')
		AND tb1.BRAND = mms2.BRAND
		AND mmw.KODE_PROVINSI = mms2.KD_PROP
		AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK
	LEFT JOIN
                            (
		SELECT
						DISTINCT
        mms.BRAND,
						mms.KD_PROP,
						mms.SEGMEN
		FROM
						MASTER_MAPPING_SEGMEN mms
		JOIN (
			SELECT
							BRAND,
							KD_PROP,
							MAX(PERIODE) AS MAX_PERIODE
			FROM
							MASTER_MAPPING_SEGMEN
			WHERE
							DEL_MARK = '0'
				AND KD_DISTRIK IS NULL
			GROUP BY
							BRAND,
							KD_PROP,
							KD_DISTRIK
                                ) latest
                                    ON
						mms.BRAND = latest.BRAND
			AND mms.KD_PROP = latest.KD_PROP
			AND mms.PERIODE = latest.MAX_PERIODE
		WHERE
						mms.DEL_MARK = '0'
			AND KD_DISTRIK IS NULL) mms3 ON
					mmw.KODE_REGION IN ('1', '2', '6')
		AND tb1.BRAND = mms3.BRAND
		AND mmw.KODE_PROVINSI = mms3.KD_PROP
	WHERE
						tb1.DEL_MARK = '0'
		AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$periode'
		AND CASE
								WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
			WHEN mmw.KODE_REGION IN ('1', '2', '6')
			AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
			ELSE mms3.SEGMEN
		END = 'FB'
	GROUP BY
								to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
								mmw.DISTRIK_RET,
								CASE
									WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
			WHEN mmw.KODE_REGION IN ('1', '2', '6')
			AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
			ELSE mms3.SEGMEN
		END
	ORDER BY
									to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') DESC) spc ON
	mpt.PERIODE = spc.PERIODE
	AND mpt.DISTRIK_RET = spc. DISTRIK_RET
WHERE
	mpt.DEL_MARK = '0'
	AND mpt.DISTRIK_RET IS NOT NULL
	AND mpt.PERIODE = '$periode'
	AND mpt.ISTRIK_RET = '$distrik_ret' ";

    // echo $sql_count;
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $t_akhir_after_upload = (($target) + ($target_alloc)); 
    $result['status'] = (($target) + ($target_alloc)) < $row_count['TARGET_SPC'] && $row_count['SEGMEN'] == "FB";
    $result['gap_spc'] = $t_akhir_after_upload - $row_count['TARGET_SPC'];
    $result['target_akhir'] = $t_akhir_after_upload;
    // $result['status'] = (($row_count['SISA_PLAFON'] - $row_count['TARGET_ALLOC']) + $target_alloc) < 0;
    // $result['status'] = ($row_count['TARGET'] + $target_alloc) > $row_count['TARGET_SPC'];
    $result['target_spc'] = $row_count['TARGET_SPC'];

    return $result;
}

function displayData($conn, $filt){
    $org = $_SESSION['user_org'];
    if($conn){
        $periode = isset($filt['periode']) && $filt['periode'] ? $filt['periode'] : date('Y-m');
        $sql1 = "SELECT
	mpt.*,
	COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) AS TARGET_PLAFON,
	COALESCE(spc.TARGET, 0) AS TARGET_SPC,
	COALESCE(mpt.TARGET, 0) + COALESCE(mpt.TARGET_ALLOC, 0) - COALESCE(spc.TARGET, 0) AS SISA_PLAFON,
	spc.SEGMEN,
	TO_CHAR(mpt.CREATED_AT, 'DD-MON-YYYY') AS CREATED_AT_F,
	TO_CHAR(mpt.UPDATED_AT, 'DD-MON-YYYY') AS UPDATED_AT_F
FROM
	MAPPING_PLAFON_TARGET mpt
LEFT JOIN (
	SELECT
		to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') AS PERIODE,
		mmw.DISTRIK_RET,
		CASE
			WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
			WHEN mmw.KODE_REGION IN ('1', '2', '6')
				AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
				ELSE mms3.SEGMEN
			END AS SEGMEN,
			sum(tb1.TARGET) AS TARGET
		FROM
			ZSD_TARGET_HARIAN_NEW_BRAND tb1
		LEFT JOIN MAPPING_MASTER_WILAYAH mmw ON
			tb1.DISTRIK = mmw.KODE_DISTRIK
		LEFT JOIN
    (
			SELECT
				DISTINCT
        mms.BRAND,
				mms.DISTRIK_RET,
				mms.SEGMEN
			FROM
				MASTER_MAPPING_SEGMEN mms
			JOIN (
				SELECT
					BRAND,
					DISTRIK_RET,
					MAX(PERIODE) AS MAX_PERIODE
				FROM
					MASTER_MAPPING_SEGMEN
				WHERE
					DEL_MARK = '0'
				GROUP BY
					BRAND,
					DISTRIK_RET
                        ) latest
                        ON
				mms.BRAND = latest.BRAND
				AND mms.DISTRIK_RET = latest.DISTRIK_RET
				AND mms.PERIODE = latest.MAX_PERIODE
			WHERE
				mms.DEL_MARK = '0' ) mms1 ON
			mmw.KODE_REGION IN ('3', '4', '5')
				AND tb1.BRAND = mms1.BRAND
				AND mmw.DISTRIK_RET = mms1.DISTRIK_RET
			LEFT JOIN
                            (
				SELECT
					DISTINCT
        mms.BRAND,
					mms.KD_PROP,
					mms.KD_DISTRIK,
					mms.SEGMEN
				FROM
					MASTER_MAPPING_SEGMEN mms
				JOIN (
					SELECT
						BRAND,
						KD_PROP,
						KD_DISTRIK,
						MAX(PERIODE) AS MAX_PERIODE
					FROM
						MASTER_MAPPING_SEGMEN
					WHERE
						DEL_MARK = '0'
					GROUP BY
						BRAND,
						KD_PROP,
						KD_DISTRIK
                                ) latest
                                    ON
					mms.BRAND = latest.BRAND
					AND mms.KD_PROP = latest.KD_PROP
					AND mms.KD_DISTRIK = latest.KD_DISTRIK
					AND mms.PERIODE = latest.MAX_PERIODE
				WHERE
					mms.DEL_MARK = '0') mms2 ON
				mmw.KODE_REGION IN ('1', '2', '6')
					AND tb1.BRAND = mms2.BRAND
					AND mmw.KODE_PROVINSI = mms2.KD_PROP
					AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK
				LEFT JOIN
                            (
					SELECT
						DISTINCT
        mms.BRAND,
						mms.KD_PROP,
						mms.SEGMEN
					FROM
						MASTER_MAPPING_SEGMEN mms
					JOIN (
						SELECT
							BRAND,
							KD_PROP,
							MAX(PERIODE) AS MAX_PERIODE
						FROM
							MASTER_MAPPING_SEGMEN
						WHERE
							DEL_MARK = '0'
							AND KD_DISTRIK IS NULL
						GROUP BY
							BRAND,
							KD_PROP,
							KD_DISTRIK
                                ) latest
                                    ON
						mms.BRAND = latest.BRAND
						AND mms.KD_PROP = latest.KD_PROP
						AND mms.PERIODE = latest.MAX_PERIODE
					WHERE
						mms.DEL_MARK = '0'
						AND KD_DISTRIK IS NULL) mms3 ON
					mmw.KODE_REGION IN ('1', '2', '6')
						AND tb1.BRAND = mms3.BRAND
						AND mmw.KODE_PROVINSI = mms3.KD_PROP
					WHERE
						tb1.DEL_MARK = '0'
						AND to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') = '$periode'
							AND CASE
								WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
								WHEN mmw.KODE_REGION IN ('1', '2', '6')
									AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
									ELSE mms3.SEGMEN
								END = 'FB'
							GROUP BY
								to_char(tb1.TANGGAL_TARGET, 'YYYY-MM'),
								mmw.DISTRIK_RET,
								CASE
									WHEN mmw.KODE_REGION IN ('3', '4', '5') THEN mms1.SEGMEN
									WHEN mmw.KODE_REGION IN ('1', '2', '6')
										AND mmw.KODE_DISTRIK = mms2.KD_DISTRIK THEN mms2.SEGMEN
										ELSE mms3.SEGMEN
									END
								ORDER BY
									to_char(tb1.TANGGAL_TARGET, 'YYYY-MM') DESC) spc ON
	mpt.PERIODE = spc.PERIODE
	AND mpt.DISTRIK_RET = spc.DISTRIK_RET
WHERE
	mpt.DEL_MARK = '0'
    AND mpt.DISTRIK_RET IS NOT NULL
	AND mpt.PERIODE = '$periode' ";
        
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID'] = $row['ID'];
            $result[$i]['PERIODE'] = $row['PERIODE'];
            $result[$i]['DISTRIK_RET'] = $row['DISTRIK_RET'];
            $result[$i]['SEGMEN'] = $row['SEGMEN'];
            $result[$i]['TARGET'] = $row['TARGET'];
            $result[$i]['TARGET_F'] = number_format($row['TARGET'], 0, ',', '.');
            $result[$i]['TARGET_PLAFON'] = $row['TARGET_PLAFON'];
            $result[$i]['TARGET_PLAFON_F'] = number_format($row['TARGET_PLAFON'], 0, ',', '.');
            $result[$i]['TARGET_ALLOC'] = $row['TARGET_ALLOC'] ? $row['TARGET_ALLOC'] : 0;
            $result[$i]['TARGET_ALLOC_F'] = number_format($row['TARGET_ALLOC'], 0, ',', '.');
            $result[$i]['TARGET_SPC'] = $row['TARGET_SPC'];
            $result[$i]['TARGET_SPC_F'] = number_format($row['TARGET_SPC'], 0, ',', '.');
            $result[$i]['SISA_PLAFON'] = $row['SISA_PLAFON'];
            $result[$i]['SISA_PLAFON_F'] = number_format($row['SISA_PLAFON'], 0, ',', '.');
            $result[$i]['CREATED_AT'] = $row['CREATED_AT_F'] == null ? '-' : $row['CREATED_AT_F'];
            $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? '-' : $row['CREATED_BY'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT_F'] == null ? '-' : $row['UPDATED_AT_F'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? '-' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn, $periode, $distrik_ret, $target, $target_alloc, $created_by){
    $sqlcek= "INSERT INTO MAPPING_PLAFON_TARGET (PERIODE, DISTRIK_RET, TARGET, TARGET_ALLOC, created_at, created_by, DEL_MARK) values ('".date('Y-m', strtotime($periode))."','".$distrik_ret."', '".$target."','".$target_alloc."',  SYSDATE, '".$created_by."', '0')";
    
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

function update($conn,$periode,$distrik_ret, $target, $target_alloc, $created_by){

    $sqlcek= "UPDATE MAPPING_PLAFON_TARGET set TARGET_ALLOC = '$target_alloc', TARGET = '$target', UPDATED_AT = SYSDATE, UPDATED_BY = '$created_by' where PERIODE = '$periode' AND DISTRIK_RET = '$distrik_ret' ";

    // echo $sqlcek;
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn, $periode, $distrik_ret, $target) {
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPPING_PLAFON_TARGET
                WHERE 
                    PERIODE = '$periode'
                    AND DISTRIK_RET = '$distrik_ret'
                    AND DEL_MARK = '0'
                ";
    
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    
    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function adjustRowNumber($num) {
    return $num - 2;
}



?>
