<?php
session_start();
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
    header("Pragma: public");
}

HeaderingExcel('template_upload_pod.xls');

// Creating a workbook
$workbook = new Workbook("-");
$format_bold = &$workbook->add_format();
$format_bold->set_bold();
$worksheet1 = &$workbook->add_worksheet('Data Upload');

$fields = array(
    'NO_SHIPMENT', 'QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR',
    'TOTAL_KTG_RUSAK', 'TOTAL_KTG_REZAK', 'TOTAL_KLAIM_KTG', 'TOTAL_SEMEN_RUSAK',
    'PDPKS', 'KETERANGAN_POD', 'GEOFENCE_POD'
);

// Header Sheet 1: Data Upload
for ($i = 0; $i < count($fields); $i++) {
    $worksheet1->write(0, $i, $fields[$i], $format_bold);
}

// Header Sheet 2: Contoh Data
$worksheet2 = &$workbook->add_worksheet('Contoh Data');
for ($i = 0; $i < count($fields); $i++) {
    $worksheet2->write(0, $i, $fields[$i], $format_bold);
}

// Data Sheet 2
$example_data = array(
    'S000000036','0', '0', '2025-07-03 00:00:00', '2025-07-03 00:00:00',
    '0', '0', '0', '0',
    '0', 'INFORMASI POD', '-6,7011737, 111,6255767'
);

for ($i = 0; $i < count($fields); $i++) {
    $worksheet2->write_string(1, $i, $example_data[$i]);
}

$workbook->close();
?>
