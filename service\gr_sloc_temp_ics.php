<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':

    $dateFrom = isset($_POST['date_from']) ? $_POST['date_from'] : date('Y-m-d');
    $dateTo = isset($_POST['date_to']) ? $_POST['date_to'] : date('Y-m-d');
    $strSo = "SELECT
                OR_TRANS_DTL.NO_SO,
                OR_TRANS_DTL.PLANT,
                OR_TRANS_DTL.KODE_PRODUK,
                OR_TRANS_DTL.NAMA_PRODUK,
                OR_TRANS_DTL.NAMA_KAPAL,
                OR_TRANS_HDR.SOLD_TO
              FROM
                OR_TRANS_HDR
              LEFT JOIN OR_TRANS_DTL ON
                OR_TRANS_HDR.NO_PP = OR_TRANS_DTL.NO_PP
              WHERE
                OR_TRANS_HDR.NOTE = 'ics_pp'
                AND OR_TRANS_HDR.ORG IN ('PTSC', 'ID50', '1000')
                AND OR_TRANS_DTL.STATUS_LINE = 'APPROVE'
                -- AND (OR_TRANS_HDR.CREATE_DATE BETWEEN TO_DATE('".date('Y-m-d',strtotime('-5 days'))."', 'YYYY-MM-DD') AND TO_DATE('".date('Y-m-d')."', 'YYYY-MM-DD'))
                AND (TO_CHAR(OR_TRANS_HDR.CREATE_DATE, 'YYYY-MM-DD') BETWEEN '".date('Y-m-d',strtotime('2025-06-01'))."' AND '".date('Y-m-d',strtotime('2025-06-17'))."')";
                // AND (TO_CHAR(OR_TRANS_HDR.CREATE_DATE, 'YYYY-MM-DD') BETWEEN '".date('Y-m-d',strtotime('-5 days'))."' AND '".date('Y-m-d')."')";
  
    $querySo = @oci_parse($fautoris->koneksi(), $strSo);
    @oci_execute($querySo);
    $listSo = array();
    $i=0;
    while($row=oci_fetch_array($querySo)){
        $listSo[$i] = $row;
        $listMapSo[$row['NO_SO']] = $row;
        $i++;
    }
    // $rowSo = oci_fetch_array($querySo, OCI_ASSOC);    
    // $listSo['NO_SO'] = $rowSo["NO_SO"];
    // $listSo['PLANT'] = $rowSo["PLANT"];
    echo "<br> QUERY =====> <br><pre>";
    print_r($strSo);
    echo "</pre>";

    $strmap = "SELECT * FROM MAPPING_PO_ICS WHERE COMPANY_CODE = 'PTSC' AND SHIPPING_POINT = 'I300' AND  FLAG_DEL='X'";
    
    $querymap = @oci_parse($fautoris->koneksi(), $strmap);
    @oci_execute($querymap);
    $rowmap = oci_fetch_array($querymap, OCI_ASSOC);    
    $plant_tujuan = $rowmap["SOLD_TO_PARTY"];
    $plant_source = $rowmap["PLANT"];

    $strSoldto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '".sprintf("%010s", $plant_tujuan)."' AND  DEL='0' ";
    
    $querySoldto = @oci_parse($fautoris->koneksi(), $strSoldto);
    @oci_execute($querySoldto);
    $rowSoldto = oci_fetch_array($querySoldto, OCI_ASSOC);    
    $soldtoOpco = $rowSoldto["SOLD_TO_OPCO"];

    echo "<br> =====> MAPPING SOLDTO <pre>";
    print_r($strSoldto);
    echo "</pre>";
    $lookUpGi = array();

    ///// VERSI BY SOLD TO//////////
    $url = 'https://integrasi-api.sig.id/apimd/lookupgoodsissuebyrange/dev';//$results['URL'];
    $data = array(
        "Token" =>"aSsMx7GV0HFGzlufM4DH",// $results['TOKEN'],
        "Plant" =>$plant_source,
        "DistributionChannel" => null,
        "SoldTo" => $soldtoOpco,
        "VehicleNumber" => null,
        "StartGoodsIssueDate" => $dateFrom,
        "EndGoodsIssueDate" => $dateTo,
        'SystemID' => 'QASSO'
    );

    $options = array(
        'http' => array(
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data),
        )
    );

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context); 
    
    $response = json_decode($result);   
    echo "<br> =====> Response SBI <pre>";
    print_r($response);
    echo "</pre>";
    exit;
    if (isset($response->Data) && count($response->Data) != 0) {
      $responseLGi = json_decode(json_encode($response->Data), true);
      foreach ($responseLGi as $kk => $vv) {
        if (isset($listMapSo[$vv['SONo']])) {
          $vv['MATERIAL'] = isset($listMapSo[$vv['SONo']]['KODE_PRODUK']) ? $listMapSo[$vv['SONo']]['KODE_PRODUK'] : "";
          $vv['MATERIAL_DESC'] = isset($listMapSo[$vv['SONo']]['NAMA_PRODUK']) ? $listMapSo[$vv['SONo']]['NAMA_PRODUK'] : "";
          $vv['NAMA_KAPAL'] = isset($listMapSo[$vv['SONo']]['NAMA_KAPAL']) ? $listMapSo[$vv['SONo']]['NAMA_KAPAL'] : "";
          array_push($lookUpGi, $vv);
        }
      }
    }

    ///////// VERSI BY SO /////////////
    // foreach ($listSo as $k => $v) {
    //   $url = 'https://integrasi-api.sig.id/apimd/lookupgoodsissuevercc/dev';//$results['URL'];
    //   // $url = 'https://integrasi-api.sig.id/apimd/lookupgoodsissuevercc/dev';//$results['URL'];    
    //   $data = array(
    //       "Token" =>"aSsMx7GV0HFGzlufM4DH",// $results['TOKEN'],
    //       "Plant" =>$v['PLANT'],
    //       "DistributionChannel" => null,
    //       "SONumber" => $v['NO_SO'],
    //       "VehicleNumber" => null,
    //       "StartGoodsIssueDate" => '2025-06-01',
    //       "EndGoodsIssueDate" => '2025-06-30',
    //       // "StartGoodsIssueDate" => date('Y-m-d',strtotime('-5 days')),
    //       // "EndGoodsIssueDate" => date('Y-m-d'),
    //       'SystemID' => 'QASSO'
    //   );
  
    //   $options = array(
    //       'http' => array(
    //           'header' => "Content-type: application/json\r\n",
    //           'method' => 'POST',
    //           'content' => json_encode($data),
    //       )
    //   );
  
    //   $context = stream_context_create($options);
    //   $result = file_get_contents($url, false, $context); 
      
    //   $response = json_decode($result);   
  
    //   if (isset($response->Data) && count($response->Data) != 0) {
    //     $responseLGi = json_decode(json_encode($response->Data), true);
    //     foreach ($responseLGi as $kk => $vv) {
    //       $vv['MATERIAL'] = $v['KODE_PRODUK'];
    //       $vv['MATERIAL_DESC'] = $v['NAMA_PRODUK'];
    //       $vv['NAMA_KAPAL'] = $v['NAMA_KAPAL'];
    //       array_push($lookUpGi, $vv);
    //     }
    //   }
    //   // $lookUpGi = $response->Data;
    // }

    echo "<br> =====> Data Lookup GI <pre>";
    print_r($lookUpGi);
    echo "</pre>";
    exit;

    if (count($lookUpGi) > 0) {
      
      $countGi = 0;
      foreach ($lookUpGi as $k => $v) {
        $firstDigit = substr((string)$plant_tujuan, 0, 1);
        $bukrs = $firstDigit . '000';

        // echo " <br> =====> GI DATE (".$k.") <pre>";
        // print_r($v["GoodsIssueDateStr"]);
        // echo "</pre>";
        
        // echo " <br> =====> GI DATE FORMATED (".$k.") <pre>";
        // print_r(date('Y', strtotime($v["GoodsIssueDateStr"])));
        // echo "</pre>";

        // echo " <br> =====> Data Map Lookup GI (".$k.") <pre>";
        // print_r($v);
        // echo "</pre>";

        $param["GI_NUMBER"] = '4900000181';
        $param["GI_YEAR"] = date('Y', strtotime($v["GoodsIssueDateStr"]));
        $param["ORG"] = 'PTSC';
        $param["PLANT"] = $plant_tujuan;
        $param["PLANT_SOURCE"] = $v["Plant"];
        $param["MATERIAL"] = $v['MATERIAL'];
        $param["MATERIAL_DESC"] = $v['MATERIAL_DESC'];
        $param["BUDAT"] = date('Ymd');
        $param["BLDATE"] = date('Ymd');
        $param["DO_NUMBER"] = $v["DONo"];
        $param["SO_NUMBER"] = $v["SONo"];
        $param["DO_QTY"] = $v["DOQty"];
        $param["MEINS"] = 'TO';
        $param["NOPOL"] = $v["VehicleNumber"];
        $param["NAMA_KAPAL"] = $v['NAMA_KAPAL'];
        $param["BUKRS"] = $bukrs;
        $param["PO_NUMBER"] = $v['LDTNo'];

        // echo "<pre>";
        // print_r($param);
        // echo "</pre>";
        
        $get = new gr_sloc_temp_ics();
        $result = $get->sv_data($param);
    
        if ($result["TYPE"]) {
          $transferStock = $get->sv_transfer_stock($fautoris->koneksi(),$param);
          if ($transferStock["TYPE"] == "S") {
            $get->dataLogIcs($fautoris->koneksi(),$param);
            $countGi++;
          } 
          // $message = $result["MESSAGE"] ? $result["MESSAGE"] : "Success GR Process";
          // $responseRequest = array(
          //   'ResponseCode' => 200,
          //   'responseMessage' => $message
          // );
        }
        // else{
        //   $responseRequest = array(
        //     'ResponseCode' => 500,
        //     'responseMessage' => "Failed GR Process"
        //   );
        // }
          
        $byLog = 'gr_sloc_temp_ics';
        $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, 'Scheduller');
      }
  
      $message = "Success Process ".$countGi." GI";
    } else {
      $message = "Data GI Not Found";
    }
    

    $responseRequest = array(
      'responseCode' => 200,
      'responseMessage' => $message
    );
    header('Content-Type: application/json');
    echo json_encode($responseRequest);
    
    break;
}

class gr_sloc_temp_ics
{

  private $_basePath;
  private $_sapCon;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function sv_data($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCMM_ICS_GRPROCESS");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc($fce, $param)
  {
    $fce->I_INPUT["MBLNR_GI"] = $param["GI_NUMBER"];
    $fce->I_INPUT["MJAHR_GI"] = $param["GI_YEAR"];
    $fce->I_INPUT["VKORG"] = $param["ORG"];
    $fce->I_INPUT["KUNNR"] = $param["PLANT"];
    $fce->I_INPUT["VSTEL"] = $param["PLANT_SOURCE"];
    $fce->I_INPUT["MATNR"] = $param["MATERIAL"];
    $fce->I_INPUT["ARKTX"] = $param["MATERIAL_DESC"];
    $fce->I_INPUT["BUDAT"] = $param["BUDAT"];
    $fce->I_INPUT["BLDAT"] = $param["BLDATE"];
    $fce->I_INPUT["VBELN"] = $param["DO_NUMBER"];
    $fce->I_INPUT["POSNR"] = "10";
    $fce->I_INPUT["VGBEL"] = $param["SO_NUMBER"];
    $fce->I_INPUT["VGPOS"] = "10";
    $fce->I_INPUT["LFIMG"] = $param["DO_QTY"];
    $fce->I_INPUT["MEINS"] = $param["MEINS"];
    $fce->I_INPUT["NOPOL"] = $param["NOPOL"];
    $fce->I_INPUT["BNAME"] = $param["NAMA_KAPAL"];
    $fce->I_INPUT["BUKRS"] = $param["BUKRS"];
    $fce->I_INPUT["EBELN"] = $param["PO_NUMBER"];
    $fce->I_INPUT["EBELP"] = "10";
    
    $fce->I_LGORT = "IC01";
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->E_RETURN->Reset();
      $return = $fce->E_MESSAGE;
    }
    return $return;
  }
  
  function sv_transfer_stock($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      $fce = $sap->NewFunction("ZCMM_ICS_TRF_STOCK");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc_transfer_stock($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc_transfer_stock($fce, $param)
  {
    $fce->I_VKORG = "PTSC";
    $fce->I_WERKS = $param['PLANT_SOURCE'];
    $fce->I_NO_TRANSAKSI = $param['GI_NUMBER'];
    $fce->I_NO_DO = $param['DO_NUMBER'];
    $fce->I_LFIMG = $param['DO_QTY'];
    $tgl_terima = date('Ymd');
    $fce->I_BUDAT = $tgl_terima;
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->E_RETURN->Reset();
      $return = $fce->E_MESSAGE;

      while ($fce->E_RETURN->Next()) {
            
        $return['TYPE'] = $fce->E_RETURN->row["TYPE"];
        $return['MESSAGE'] = $fce->E_RETURN->row["MESSAGE"];
      }
    }
    return $return;
  }

  function dataLogIcs($conn, $data) {
        $sqlInsert = "INSERT INTO LOG_ICS_SBI 
                        (GI_NUMBER,
                        GI_YEAR,
                        ORG,
                        PLANT,
                        PLANT_SOURCE,
                        MATERIAL,
                        MATERIAL_DESC,
                        BUDAT,
                        BLDATE,
                        DO_NUMBER,
                        SO_NUMBER,
                        DO_QTY,
                        MEINS,
                        NOPOL,
                        NAMA_KAPAL,
                        BUKRS,
                        PO_NUMBER,
                        LOG_DATE) 
                      VALUES (:GI_NUMBER,
                              :GI_YEAR,
                              :ORG,
                              :PLANT,
                              :PLANT_SOURCE,
                              :MATERIAL,
                              :MATERIAL_DESC,
                              :BUDAT,
                              :BLDATE,
                              :DO_NUMBER,
                              :SO_NUMBER,
                              :DO_QTY,
                              :MEINS,
                              :NOPOL,
                              :NAMA_KAPAL,
                              :BUKRS,
                              :PO_NUMBER,
                              SYSDATE)";
        
        $stmtInsert = oci_parse($conn, $sqlInsert);
        oci_bind_by_name($stmtInsert, ':GI_NUMBER', $data['GI_NUMBER']);
        oci_bind_by_name($stmtInsert, ':GI_YEAR', $data['GI_YEAR']);
        oci_bind_by_name($stmtInsert, ':ORG', $data['ORG']);
        oci_bind_by_name($stmtInsert, ':PLANT', $data['PLANT']);
        oci_bind_by_name($stmtInsert, ':PLANT_SOURCE', $data['PLANT_SOURCE']);
        oci_bind_by_name($stmtInsert, ':MATERIAL', $data['MATERIAL']);
        oci_bind_by_name($stmtInsert, ':MATERIAL_DESC', $data['MATERIAL_DESC']);
        oci_bind_by_name($stmtInsert, ':BUDAT', $data['BUDAT']);
        oci_bind_by_name($stmtInsert, ':BLDATE', $data['BLDATE']);
        oci_bind_by_name($stmtInsert, ':DO_NUMBER', $data['DO_NUMBER']);
        oci_bind_by_name($stmtInsert, ':SO_NUMBER', $data['SO_NUMBER']);
        oci_bind_by_name($stmtInsert, ':DO_QTY', $data['DO_QTY']);
        oci_bind_by_name($stmtInsert, ':MEINS', $data['MEINS']);
        oci_bind_by_name($stmtInsert, ':NOPOL', $data['NOPOL']);
        oci_bind_by_name($stmtInsert, ':NAMA_KAPAL', $data['NAMA_KAPAL']);
        oci_bind_by_name($stmtInsert, ':BUKRS', $data['BUKRS']);
        oci_bind_by_name($stmtInsert, ':PO_NUMBER', $data['PO_NUMBER']);
        oci_execute($stmtInsert);
    }
}
