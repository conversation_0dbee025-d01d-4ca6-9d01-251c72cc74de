<?php 

session_start();
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
	header("HTTP/1.1 401 Unauthorized");
	echo json_encode(array(
			'status' => 'error',
			'message' => 'Unauthorized: Session expired or not logged in'
	));
	exit;
}
require_once('../security_helper.php');
sanitize_global_input();
include ('../include/ex_fungsi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$action=$_POST['action'];
if (!$action) {
	$action = $_GET['action'];
}

$data = array();
switch ($action) {
	case 'listeasyui':
		$sqlget = "SELECT ID,NAMA_DOKUMEN FROM EX_INVOICE_DOKUMEN_INV WHERE DELETE_MARK = 0";
		$query = oci_parse($conn, $sqlget);
		oci_execute($query);
		while ($dataget = oci_fetch_assoc($query)) {
			array_push($data, $dataget);
		}
		break;
	case 'savedata':
		$id = $_POST['ID'];
		$nama_dokumen = $_POST['NAMA_DOKUMEN'];
		$user_name=$_SESSION['user_name'];
		if ($id) {
			$field_names=array('NAMA_DOKUMEN','UPDATED_BY','UPDATED_AT');
		    $field_data=array("$nama_dokumen","$user_name","SYSDATE"); 
		    $tablename="EX_INVOICE_DOKUMEN_INV";
		    if($fungsi->update($conn,$field_names,$field_data,$tablename,array('ID'),array($id))){
			    $data = array('success'=>true);
		    }else{
	    		$data = array('errorMsg'=>'Data Gagal diupdate!!!');
		    }
		}else{
			$field_names=array('NAMA_DOKUMEN','CREATED_BY','CREATED_AT');
		    $field_data=array("$nama_dokumen","$user_name","SYSDATE"); 
		    $tablename="EX_INVOICE_DOKUMEN_INV";
		    if($fungsi->insert($conn,$field_names,$field_data,$tablename)){
	    		$data = array('success'=>true);
		    }else{
	    		$data = array('errorMsg'=>'Data Gagal disimpan!!!');
		    }
		}
		break;
	case 'deletedata':
		$id = $_POST['id'];
		$user_name=$_SESSION['user_name'];
		$field_names=array('DELETE_MARK','DELETED_BY','DELETED_AT');
	    $field_data=array("1","$user_name","SYSDATE"); 
	    $tablename="EX_INVOICE_DOKUMEN_INV";
	    if($fungsi->update($conn,$field_names,$field_data,$tablename,array('ID'),array($id))){
	    	$data=array('success'=>true);
	    }else{
	    	$data=array('errorMsg'=>'Data Gagal dihapus!!!');
	    }
		break;
	default:
		
		break;
}

echo json_encode($data);

?>