<?php

session_start();
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
    header("Pragma: public");
}

if (isset($_POST['ids'])) {
    // Pisahkan jadi array
    $ids = explode(',', $_POST['ids']);
    $data_size = count($ids);
} else {
    $data_size = 0;
}
    HeaderingExcel('template_upload_ekspedisi_dokumen.xls');

    // Creating a workbook
    $workbook = new Workbook("-");
    // Adding format
    $format_bold = $workbook->add_format();
    $format_bold->set_bold();
    $worksheet1 = $workbook->add_worksheet('Data Upload');

    $fields = array(
        'NO_BA', 'NO_EKSPEDISI'
    );

    $worksheet1->write(0, 0, 'NO_BA', $format_bold);
    $worksheet1->write(0, 1, 'NO_EKSPEDISI', $format_bold);

    // Data Sheet 1
    for($i=1; $i<=$data_size;$i++) {
        $worksheet1->write_string($i, 0, $ids[$i-1]);
        $worksheet1->write_string($i, 1, '');
    }

    for ($i = 0; $i <= 2; $i++) {
        $worksheet1->set_column($i, $i, 10);
    }

    // Header Sheet 2: Contoh Data
    $worksheet2 = &$workbook->add_worksheet('Contoh Data');
    for ($i = 0; $i < count($fields); $i++) {
        $worksheet2->write(0, $i, $fields[$i], $format_bold);
    }

    // Data Sheet 2
    $example_data = array(
        '9999000000', '00102826'
    );

    for ($i = 0; $i < count($fields); $i++) {
        $worksheet2->write_string(1, $i, $example_data[$i]);
    }

    $workbook->close();