<?php
include_once ('fungsi.php');
class or_fungsi extends fungsi 
{

  	var $or_username = "dev";
	var $or_password = "semeru2";
	var $or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
//////
//////
//////	var $bo_username = "CMSTES";
//////	var $bo_password = "cms1910tes";
//////	var $bo_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';
//
    //    var $bo_username = "cms";
	//    var $bo_password = "cms1910p4dAn9";
	//    var $bo_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = devsgg)(SERVER = DEDICATED)))'; //**********
	
	var $bo_username = "cmstes";
    var $bo_password = "semeru2";
    var $bo_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = devsgg)(SERVER = DEDICATED)))';
        
       
	public function bo_koneksi()
	{
                  $conn = oci_connect($this->bo_username, $this->bo_password, $this->bo_db, 'AL32UTF8' );
		  if (!$conn)
		  	return false;
		  else
		   return $conn;
	}

	public function or_koneksi()
	{
                $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_030.php');//dev
                // $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_210.php');//prod
		$conn = oci_connect($oraConfig['username_conn'], $oraConfig['password_conn'], $oraConfig['db'], 'AL32UTF8');
		if (!$conn)
			return false;
		else
		 return $conn;
	}
        //GPS Database
        public function gpsConnection(){
  //               $userName   = 'marketplace';
  //               $password   = 's3m3ngres1k';
  //               $config       = '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521))) (CONNECT_DATA = (SID = SGG)(SERVER = DEDICATED)))';
                
		// $conn = oci_connect($userName, $password, $config, 'AL32UTF8');
		// if (!$conn)
		// 	return false;
		// else
		//  return $conn;

		return false;
	}
        
	function or_status_shipment($ting){
		$k=array(1 => '10', '20', '30', '40', '50', '70');
		$nama=array(1 => '10-Antri', '20-Entri SPPS', '30-Matching Kota','40-Matching Alamat','50-Timbang Masuk','70-Complete');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function or_order_type($ting){
		$k=array(1 => 'ZOR', 'ZFD', 'ZFC', 'ZEX','ZPR','ZRE', 'X','ZPR1');
		$nama=array(1 => 'Standart Sales', 'Change Sales Item', 'FOC Sales','Export Sales','Project Sales','Return Sales','Confirmation Sales','Direct Selling');
		for($x=1;$x<=count($k);$x++)
			{
				echo("<option value='$k[$x]' title='$nama[$x]'");
				if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
        function or_order_type2($ting){
                $k=array(1 => 'ZOR','ZPR', 'ZEX','ZFC');
		$nama=array(1 => 'Standart Sales','Project Sales','Export Sales','Sales FOC');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	} 
	function or_order_type3($ting){
		$k=array(1 => 'ZOR', 'ZFD', 'ZFC', 'ZEX','ZPR','ZRE', 'X');
		$nama=array(1 => 'Standard Sales', 'Change Sales Item', 'FOC Sales','Export Sales','Project Sales','Return Sales', 'Confirmation Sales');
		for($x=1;$x<=count($k);$x++)
			{
				if ($k[$x] == 'ZOR' || $k[$x] == 'ZPR' || $k[$x] == 'ZFC') { //standart dan project sale saya yg ditampilkan
					echo("<option value='$k[$x]' title='$nama[$x]'");
					if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
				}
			}
	}

	function or_jns_plant($ting){
		$k=array(1 => 'PABRIK', 'GUDANG','OTHER');
		$nama=array(1 => 'PABRIK', 'GUDANG','OTHER');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
        
        function or_jns_plant2($ting){
		$k=array(1 => '6401', '6402','6601');
		$nama=array(1 => 'Quang Ninh', 'Ho Chi Minh','ICD');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'" );
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function or_jenis_kirim($ting){
		$k=array(1 => 'CIF','CNF','FOB','FOT','FRC','FOR','FAS');
		$nama=array(1 => 'Cost, Insurance & Freight', 'Cost and Freight','Free on Board','Free on Truck','Franco','Free On Rail','Free Alongside Ship');
		
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x] - $nama[$x]</option>");
			}
	}
        function or_jenis_kirim1($ting){
		$k=array(1 => 'CIF','CNF','FOB','FOT','FOR','FAS');
		$nama=array(1 => 'Cost, Insurance & Freight', 'Cost and Freight','Free on Board','Free on Truck','Free On Rail','Free Alongside Ship');
		
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x] - $nama[$x]</option>");
			}
	}
        function or_jenis_kirim2($ting){
		$k=array(1 => 'FOB','FOT','FAS');
		$nama=array(1 => 'Water Way','Truck','Free Alongside Ship');
		
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x] - $nama[$x]</option>");
			}
	}
	function or_status($ting){
		$k=array(1 => 'OPEN','PROCESS','APPROVE','REJECTED');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}

	function or_bayar($ting){
		$k=array(1 => 'CREDIT','CASH');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}

	function tgl_terima($tgl,$asal,$tujuan){
			$sql_jarak= "SELECT WAKTU FROM OR_LAMA_KIRIM WHERE ASAL='$asal' AND TUJUAN = '$tujuan' AND START_DATE <= To_date('$tgl','DD-MM-YYYY') AND (END_DATE >= To_date('$tgl','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";			
				$conn = $this->or_koneksi();
				$query_jarak=oci_parse($conn,$sql_jarak);
				oci_execute($query_jarak);
				$row_jarak=oci_fetch_assoc($query_jarak);
				$jarak_up=$row_jarak[WAKTU]; 
				
				if ($jarak_up==0 or $jarak_up ==""){
					list($hari,$bln,$thn)=split("-",$tgl);
					$ubah_hari=$hari+7;
					$tgl_terima = date("d-m-Y", @mktime (0,0,0,$bln,$ubah_hari,$thn)); 
				}else{
					
					list($hari,$bln,$thn)=split("-",$tgl);
					$ubah_hari=$hari+ceil($jarak_up/24);
					$tgl_terima = date("d-m-Y", @mktime (0,0,0,$bln,$ubah_hari,$thn)); 
				}
		return $tgl_terima;
	}

        function or_creditlimit($ting,$org){
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
               $sap->PrintStatus();
               exit;
            }

		$fce = $sap->NewFunction ("Z_CREDIT_EXPOSURE");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		// entri parameter
                $connfc=$this->or_koneksi();
                $orgmm=$this->findOneByOne($connfc,"OR_COM_COAREA","COM",$org,"CO_AREA"); 
                if($orgmm!=''){
                $fce->X_KKBER = $orgmm;
                $fce->X_KUNNR = $ting;//$distributor;
                }else{
                $fce->X_KKBER = $org;
                $fce->X_KUNNR = $ting;//$distributor;
                }
		$fce->X_DATE_CREDIT_EXPOSURE='31.12.9999';
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
				$delta=$fce->Z_DELTA_TO_LIMIT*100;
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $delta;			
	}

		function or_creditlimitminus($ting,$org){
			$sap = new SAPConnection();
			$sap->Connect("../include/sapclasses/logon_data.conf");
			if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			if ($sap->GetStatus() != SAPRFC_OK ) {
			$sap->PrintStatus();
			exit;
			}

		$fce = $sap->NewFunction ("Z_CREDIT_EXPOSURE");
		if ($fce == false ) {
		$sap->PrintStatus();
		exit;
		}
		// entri parameter
				$connfc=$this->or_koneksi();
				$orgmm=$this->findOneByOne($connfc,"OR_COM_COAREA","COM",$org,"CO_AREA"); 
				if($orgmm!=''){
				$fce->X_KKBER = $orgmm;
				$fce->X_KUNNR = $ting;//$distributor;
				}else{
				$fce->X_KKBER = $org;
				$fce->X_KUNNR = $ting;//$distributor;
				}
		$fce->X_DATE_CREDIT_EXPOSURE='31.12.9999';
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
				$delta=$fce->Z_DELTA_TO_LIMIT2*100;
		}else
				$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $delta;			
	}

        function or_creditlimit2($ting,$org){
            //cek jika pernah melakukan pengambilah hari ini
            $delta = 0;
            $conn = $this->or_koneksi();
        	if ($org=='2000' || $org=='7000'){
				$query = array(
					'X_KKBER'=>'2000',
					'X_KUNNR'=>$ting
				);
                }else{
				$query = array(
					'X_KKBER'=>$org,
					'X_KUNNR'=>$ting
				);
            }
	    $cek_stg_or_sap = $this->sap_or_stagging($conn,'Z_CREDIT_EXPOSURE','data',$query);

			if ($cek_stg_or_sap) {
                            // var_dump($cek_stg_or_sap);
                            $credit=$cek_stg_or_sap->Z_CREDITLIMIT*100; //credit limit
                            $delivery=$cek_stg_or_sap->Z_OPEN_DELIVERY*100; // do yg blom di billing
                            $minsum=$cek_stg_or_sap->Z_SUM_FLAG;
                            $usecredit0=$cek_stg_or_sap->Z_SUM_OPENS*100;
                            $usecredit=$minsum.$usecredit0;
                            $minsp=$cek_stg_or_sap->Z_OPEN_SP_FLAG;
                            $special0=$cek_stg_or_sap->Z_OPEN_SPECIALS*100;
                            $special=$minsp.$special0;
                            $sisa=$credit-$usecredit; //sisa kredit
                            $receivablesn=$cek_stg_or_sap->Z_OPEN_ITEMS*100;
                            $delta = $cek_stg_or_sap->SISA;
			}else{
			    $sap = new SAPConnection();
			    $sap->Connect("../include/sapclasses/logon_data.conf");
			    if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			    if ($sap->GetStatus() != SAPRFC_OK ) {
			       $sap->PrintStatus();
			       exit;
			    }

				$fce = $sap->NewFunction ("Z_CREDIT_EXPOSURE");
				if ($fce == false ) {
				   $sap->PrintStatus();
				   exit;
				}
				// entri parameter
				$fce->X_KKBER = $query['X_KKBER'];
				$fce->X_KUNNR = $query['X_KUNNR'];//$distributor;
				$fce->X_DATE_CREDIT_EXPOSURE='31.12.9999';
				$fce->Call();
				$arrdata = array();
				if ($fce->GetStatus() == SAPRFC_OK ) {		
						$credit=$fce->Z_CREDITLIMIT*100; //credit limit
						$delivery=$fce->Z_OPEN_DELIVERY*100; // do yg blom di billing
						$minsum=$fce->Z_SUM_FLAG;
			                            $usecredit0=$fce->Z_SUM_OPENS*100;
			                            $usecredit=$minsum.$usecredit0;
						$minsp=$fce->Z_OPEN_SP_FLAG;
			                            $special0=$fce->Z_OPEN_SPECIALS*100;
			                            $special=$minsp.$special0;
						$sisa=$credit-$usecredit; //sisa kredit
			                            $receivablesn=$fce->Z_OPEN_ITEMS*100;
						//if ($usecredit<0) { $sisa=0; } else { $sisa=$sisa; }
						$arrdata = array(
							'Z_CREDITLIMIT'=>$fce->Z_CREDITLIMIT,
							'Z_OPEN_DELIVERY'=>$fce->Z_OPEN_DELIVERY,
							'Z_SUM_FLAG'=>$fce->Z_SUM_FLAG,
							'Z_SUM_OPENS'=>$fce->Z_SUM_OPENS,
							'Z_OPEN_SP_FLAG'=>$fce->Z_OPEN_SP_FLAG,
							'Z_OPEN_SPECIALS'=>$fce->Z_OPEN_SPECIALS,
							'Z_OPEN_ITEMS'=>$fce->Z_OPEN_ITEMS,
							'SISA'=>number_format($sisa,2,'.',',')
						);
			    		$delta = number_format($sisa,2,'.',',');

				}else
			    		$fce->PrintStatus();

				$fce->Close();	
				$sap->Close();	
				$this->create_stagging_trans($conn,'Z_CREDIT_EXPOSURE','','','data',$arrdata,$query);
			}
			return $delta;			
	}
        
        function or_infobank($ting,$org){
            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
               $sap->PrintStatus();
               exit;
            }

		$fce = $sap->NewFunction ("Z_ZAPPSD_GET_BANKA_CUST");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		// entri parameter
                $fce->I_KUNNR = $ting;//$distributor;
		$fce->Call();
                unset($datainfbank);
		if ($fce->GetStatus() == SAPRFC_OK ) {		
                        $datainfbank['BANKL'] = $fce->RETURNDATA["BANKL"];			
                        $datainfbank['BANKA'] = $fce->RETURNDATA["BANKA"];
                        $datainfbank['BANKN'] = $fce->RETURNDATA["BANKN"];
		}else
        		$fce->PrintStatus();
                
		$fce->Close();	
		$sap->Close();	               
               
		return $datainfbank;			
	}

	function or_branch_plant($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPP_SELECT_SYSPLAN");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
		$fce->XPARAM = '2000';
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
			$plantcode[]= $fce->RETURN_DATA->row["WERKS"];
			$plantdesc[]= $fce->RETURN_DATA->row["NAME1"];
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($plantcode);$x++)
		{
		echo("<option value='$plantcode[$x]' title='$plantdesc[$x]' ");
		if($plantdesc[$x] == $ting){echo("selected");}
		echo(">$plantcode[$x]"." - "."$plantdesc[$x]</option>");
		}
	
	}
        
        function or_routelist($ting){
            $orgkey=trim($_SESSION['user_org']);
            $sql_route= "select ROUTE from TB_ROUTE
                    ";			
            $conn = $this->or_koneksi();
            $query_route=oci_parse($conn,$sql_route);
            oci_execute($query_route);unset($dataroute);
            while ($route=oci_fetch_assoc($query_route)){
            //    $dataroute[$route['ROUTE']] = $route['ROUTE'];

            echo("<option value='$route[$x]' title='$route[$x]' ");
            if($route[ROUTE] == $ting){echo("selected");}
            echo("> $route[ROUTE]</option>");


            }

            //	for($x=0;$x<count($route);$x++)
            //		{
            //	echo("<option value='$dataroute[$x]' title='$dataroute[$x]' ");
            //		if($route[$x] == $ting){echo("selected");}
            //		echo("> $route[$x]"." No "."$route[$x]</option>");
            //		}


        }
	function or_pricelist($ting){
                 $orgkey=trim($_SESSION['user_org']);
                $sql_price= "select KEY from ZREPORT_M_PRICE 
                            where DELETE_MARK=0 and ORG='$orgkey' and TIPE=2
                            group by KEY
                            ";			
                $conn = $this->or_koneksi();
                $query_price=oci_parse($conn,$sql_price);
                oci_execute($query_price);unset($dataprice);
                while ($price=oci_fetch_assoc($query_price)){
                    $dataprice[$price['KEY']] = $price['KEY'];
                }
                 
//                echo "<pre>";
//                print_r($dataprice);
//                echo "</pre>";
		$sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_MST_PRICE");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
                        if(count($dataprice)>0){
                            if($dataprice[$fce->RETURN_DATA->row["PLTYP"]]!=''){    
                                $price[]= $fce->RETURN_DATA->row["PLTYP"];
                                $pricedesc[]= $fce->RETURN_DATA->row["PTEXT"];
                            }
                        }else{
                            $price[]= $fce->RETURN_DATA->row["PLTYP"];
                            $pricedesc[]= $fce->RETURN_DATA->row["PTEXT"];
                        }    
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($price);$x++)
		{
		echo("<option value='$price[$x]' title='$pricedesc[$x]' ");
		if($price[$x] == $ting){echo("selected");}
		echo(">$price[$x]"." - "."$pricedesc[$x]</option>");
		}
	
	}
        function or_pricelist2($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_MST_PRICE");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
                        if(substr($fce->RETURN_DATA->row["PLTYP"],0,1)=='6'){    
			$price[]= $fce->RETURN_DATA->row["PLTYP"];
			$pricedesc[]= $fce->RETURN_DATA->row["PTEXT"];
                        }
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($price);$x++)
		{
		echo("<option value='$price[$x]' title='$pricedesc[$x]' ");
		if($price[$x] == $ting){echo("selected");}
		echo(">$price[$x]"." - "."$pricedesc[$x]</option>");
		}
	
	}


	function or_pricelist3($ting){
                 $orgkey=trim($_SESSION['user_org']);
                $sql_price= "select KEY from ZREPORT_M_PRICE 
                            where DELETE_MARK=0 and ORG='$orgkey' and TIPE=2
                            group by KEY
                            ";			
                $conn = $this->or_koneksi();
                $query_price=oci_parse($conn,$sql_price);
                oci_execute($query_price);unset($dataprice);
                while ($price=oci_fetch_assoc($query_price)){
                    $dataprice[$price['KEY']] = $price['KEY'];
                }
                 
		/*$sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_MST_PRICE");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
                        if(count($dataprice)>0){
                            if($dataprice[$fce->RETURN_DATA->row["PLTYP"]]!=''){    
                                $price[]= $fce->RETURN_DATA->row["PLTYP"];
                                $pricedesc[]= $fce->RETURN_DATA->row["PTEXT"];
                            }
                        }else{
                            $price[]= $fce->RETURN_DATA->row["PLTYP"];
                            $pricedesc[]= $fce->RETURN_DATA->row["PTEXT"];
                        }    
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	*/
		$sql = "SELECT * FROM RFC_Z_ZAPPSD_MST_PRICE";
                               
                $query= oci_parse($conn, $sql);
                oci_execute($query);
                while($datafunc=oci_fetch_assoc($query)){
                    if(count($dataprice)>0){
                        if($dataprice[$datafunc["PLTYP"]]!=''){    
                            $price[]= $datafunc["PLTYP"];
                            $pricedesc[]= $datafunc["PTEXT"];
                        }
                    }else{
                        $price[]= $datafunc["PLTYP"];
                        $pricedesc[]= $datafunc["PTEXT"];
                    }  
                }
			
		for($x=0;$x<count($price);$x++)
		{
		echo("<option value='$price[$x]' title='$pricedesc[$x]' ");
		if($price[$x] == $ting){echo("selected");}
		echo(">$price[$x]"." - "."$pricedesc[$x]</option>");
		}
	
	}
	

	function or_order_reason($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_ORDER_REASON");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		$fce->XAUGRU = $ting;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
			$order_reason[]= $fce->RETURN_DATA->row["AUGRU"];
			$desc[]= $fce->RETURN_DATA->row["BEZEI"];
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($order_reason);$x++)
		{
		echo("<option value='$order_reason[$x]' title='$desc[$x]' ");
		if($order_reason[$x] == $ting){echo("selected");}
		echo(">$order_reason[$x]"." - "."$desc[$x]</option>");
		}
	}

	function or_order_reason_soa($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_ORDER_REASON");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		$fce->XAUGRU = $ting;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
			$order_reason[]= $fce->RETURN_DATA->row["AUGRU"];
			$desc[]= $fce->RETURN_DATA->row["BEZEI"];
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($order_reason);$x++)
		{
		echo("<option value='$order_reason[$x]' title='$desc[$x]' ");
		if($order_reason[$x] == $ting){echo("selected");}
		echo(">$order_reason[$x]"." - "."$desc[$x]</option>");
		}
	}

	function or_reason_reject($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_LISTREASON");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		$fce->ABGRU = $ting;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->T_RESULT->Reset();
			while ( $fce->T_RESULT->Next() ){
			$reason[]= $fce->T_RESULT->row["ABGRU"];
			$desc[]= $fce->T_RESULT->row["BEZEI"];
			$desc1[]= substr($fce->T_RESULT->row["BEZEI"],0,23);
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($reason);$x++)
		{
		echo("<option value='$reason[$x]' title='$desc[$x]' ");
		if($reason[$x] == $ting){echo("selected");}
		echo(">$reason[$x]"." - "."$desc1[$x]</option>");
		}
	}

	function or_delivery_block($ting){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_LISTDLV_BLOCK");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		$fce->I_LIFSP = $ting;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->T_RESULT->Reset();
			while ( $fce->T_RESULT->Next() ){
			$block[]= $fce->T_RESULT->row["LIFSP"];
			$desc[]= $fce->T_RESULT->row["VTEXT"];
			$desc1[]= substr($fce->T_RESULT->row["VTEXT"],0,23);
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($block);$x++)
		{
		echo("<option value='$block[$x]' title='$desc[$x]' ");
		if($block[$x] == $ting){echo("selected");}
		echo(">$block[$x]"." - "."$desc1[$x]</option>");
		}
	}


	function or_cara_bayar($ting){
                $orgkey=trim($_SESSION['user_org']);
                $sql_price= "select KEY from ZREPORT_M_PRICE 
                            where DELETE_MARK=0 and ORG='$orgkey' and TIPE=1
                            group by KEY
                            ";			
                $conn = $this->or_koneksi();
                $query_price=oci_parse($conn,$sql_price);
                oci_execute($query_price);unset($dataprice);
                while ($price=oci_fetch_assoc($query_price)){
                    $dataprice[$price['KEY']] = $price['KEY'];
                }
                 
//                echo "<pre>";
//                print_r($dataprice);
//                echo "</pre>";
                
		$sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZCSD_SEL_TOP");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){			
                        if(count($dataprice)>0){
                            if($dataprice[$fce->RETURN_DATA->row["ZTERM"]]!=''){
                                $topcode[]= $fce->RETURN_DATA->row["ZTERM"];
                                $topdesc[]= $fce->RETURN_DATA->row["TEXT1"];
                            }
                        }else{    
                            $topcode[]= $fce->RETURN_DATA->row["ZTERM"];
                            $topdesc[]= $fce->RETURN_DATA->row["TEXT1"];
                        }
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($topcode);$x++)
		{
		echo("<option value='$topcode[$x]' title='$topdesc[$x]' ");
		if($topcode[$x] == $ting){echo("selected");}
		echo(">$topcode[$x]"." - "."$topdesc[$x]</option>");
		}
	
	}
        
        
	function combo_ship($ting){ 
                $mysql= "select * from ZMD_SHIPPING_CONDITION 
                            where DEL = 0
                            ";			
                $conn = $this->or_koneksi();
                $mysql_set=oci_parse($conn,$mysql);
                oci_execute($mysql_set);
                while ($val=oci_fetch_assoc($mysql_set)){ 
                    echo("<option value='{$val['SHIPPING_CONDITION']}' title='{$val['DESCRIPTION']}' ");
                    if($val['SHIPPING_CONDITION'] == $ting){echo("selected");}
                    echo(">{$val['SHIPPING_CONDITION']}"." - "."{$val['DESCRIPTION']}</option>");
                }
                  
			 
	
	}
        
	public function or_clearsessi_all()
	{
		$this->or_clearsessi_waktu_kirim();
	}
	public function or_clearsessi_waktu_kirim()
	{
		unset($_SESSION['or_asal_waktu']);
		unset($_SESSION['or_nama_asal_waktu']);
		unset($_SESSION['or_tujuan_waktu']);
		unset($_SESSION['or_nama_tujuan_waktu']);
		unset($_SESSION['or_incoterm_waktu']);
		unset($_SESSION['or_nama_incoterm_waktu']);
		unset($_SESSION['or_vehicle_type_waktu']);
		unset($_SESSION['or_waktu_waktu']);
		unset($_SESSION['or_keterangan_waktu']);
		unset($_SESSION['or_tgl_mulai_waktu']);
		unset($_SESSION['or_tgl_selesai_waktu']);
	}
	function or_new_pp_number($conn)
	{
		$sql="SELECT OR_PP_NUMBER_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='000000000'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		return $new_number_ok;

	}
        
        function or_new_pp_gelondong_number($conn)
	{
		$sql="SELECT OR_TRANS_REPLENISHMENT_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='000000000'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		return $new_number_ok;

	}
        
	function sapcode($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$sapcode='000000000'.$kode;
		if($panjang==2)$sapcode='00000000'.$kode;
		if($panjang==3)$sapcode='0000000'.$kode;
		if($panjang==4)$sapcode='000000'.$kode;
		if($panjang==5)$sapcode='00000'.$kode;
		if($panjang==6)$sapcode='0000'.$kode;
		if($panjang==7)$sapcode='000'.$kode;
		if($panjang==8)$sapcode='00'.$kode;
		if($panjang==9)$sapcode='0'.$kode;
		if($panjang==10)$sapcode=$kode;
		return $sapcode;
	}
	function linenum($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$linenum='00000'.$kode;
		if($panjang==2)$linenum='0000'.$kode;
		if($panjang==3)$linenum='000'.$kode;
		if($panjang==4)$linenum='00'.$kode;
		if($panjang==5)$linenum='0'.$kode;
		if($panjang==6)$linenum=$kode;
		return $linenum;
	}
	function qtyso($qty)
	{
		$panjang=strlen(strval($qty));
		if($panjang==1)$qtyso='000000000000'.$qty;
		if($panjang==2)$qtyso='00000000000'.$qty;
		if($panjang==3)$qtyso='0000000000'.$qty;
		if($panjang==4)$qtyso='000000000'.$qty;
		if($panjang==5)$qtyso='00000000'.$qty;
		if($panjang==6)$qtyso='0000000'.$qty;
		if($panjang==7)$qtyso='000000'.$qty;
		if($panjang==8)$qtyso='00000'.$qty;
		if($panjang==9)$qtyso='0000'.$qty;
		if($panjang==10)$qtyso='000'.$qty;
		if($panjang==11)$qtyso='00'.$qty;
		if($panjang==12)$qtyso='0'.$qty;
		return $qtyso;
	}
	
	function truk_tipebongkar($ting){
		$k=array(1 => '001','002');
		$nama=array(1 => 'SAMPING','BELAKANG');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function pilih_warnaplat($ting){
		$k=array(1 => 'HITAM', 'KUNING');
		$nama=array(1 => 'HITAM', 'KUNING');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function getLFART($SOType) {
        $lfart = array(array('so' => 'ZEX', 'do' => 'ZLFE'),
            array('so' => 'ZFC', 'do' => 'ZLC'),
            array('so' => 'ZOR', 'do' => 'ZLF'),
            array('so' => 'ZPR', 'do' => 'ZLFP'));
        $ada = false;
        for ($zxz = 0; $zxz < count($lfart); $zxz++) {
            if ($lfart[$zxz]['so'] == $SOType) {
                $DOType = $lfart[$zxz]['do'];
                $ada = true;
                return $DOType;
            }
        }
        if ($ada
            )return $DOType;
        else
            return false;
    }


	function token_admin_sig_api() {
		return "eyJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************.JDJ5JDEwJDd5V3lDZkouNExodHVNUWNNaVZ4OHVHWXgzTlF2NmFpaFY1UnhrMlJVWVltQ1lFSWowVWh1";
	}

	function domain_integrasi() {
		return "https://dev-integrasi-api.sig.id";
	}

	function login_sig_api() {
		$data = array("user"=>"thamrin", "pass"=>"IndonesiaMerdeka!1#");
		return $data;
	}

	function basic_auth_integrasi() {
		$data = "cmZjX2NzbXM6bjNXUzFnQ3Rnclkh";
		return $data;
	}

}
?>
