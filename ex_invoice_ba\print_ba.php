<?php
ini_set('memory_limit','128M'); // tambahan
require_once 'dompdf/autoload.inc.php';

$data = file_get_contents('php://input');
$data = json_decode($data);

use Dompdf\Dompdf;

$dompdf = new Dompdf();
$dompdf->set_option('isRemoteEnabled', true);
$dompdf->loadHtml($data->content);

// (Optional) Setup the paper size and orientation
$dompdf->setPaper('A4', 'potrait');

// Render the HTML as PDF
$dompdf->render();

// Output the generated PDF to Browser
return $dompdf->stream('print-ba.pdf', array('Attachment' => 0));
