<?
//by @liyantanto 2014

class autorisasi 
{

//koneksi oracle
function koneksi(){
    require_once ("classmain.php");
    $fungsi=new classmain();  
    $conn = $fungsi->koneksi();
    return $conn;
}

//koneksi oracledev
function koneksidev(){
    require_once ("classmain.php");
    $fungsi=new classmain();  
    $conn = $fungsi->koneksidev();
    return $conn;
}

//koneksi oracle devsd
function koneksidevsd(){
    require_once ("classmain.php");
    $fungsi=new classmain();  
    $conn = $fungsi->koneksidvsd();
    return $conn;
}

//koneksi oracle devsd
function con_devsd(){
    require_once ("classmain.php");
    $fungsi=new classmain();  
    $conn = $fungsi->con_devsd();
    return $conn;
}

function con_sp(){
    require_once ("classmain.php");
    $fungsi=new classmain();  
    $conn = $fungsi->con_sp();
    return $conn;
}

function login($token_int) { 
    unset($responAutori);
    $dateEk=date("H");
    $time_eks="19";
//    if ($dateEk==$time_eks || $time_eks=='') {   
    if ($token_int!='') {   
    $conn = $this->koneksi();    
    if ($conn) {
            $username=strip_tags(trim(strtoupper($username)));
            $passx = md5(@trim($password));
            $awo = "SELECT TB_USER_BOOKING.*, OR_MAP_USERCOM.COMPANY_CODE AS ORG FROM TB_USER_BOOKING
        LEFT JOIN OR_MAP_USERCOM ON TB_USER_BOOKING. ID = OR_MAP_USERCOM.USER_ID
        WHERE
        TB_USER_BOOKING.TOKEN = '$token_int'
        AND TB_USER_BOOKING.TOKEN IS NOT NULL
        AND TB_USER_BOOKING.DELETE_MARK = 0";
            $query= oci_parse($conn,$awo);
            oci_execute($query);
            $jumxx=0;unset($dataUser);
            while ($row=oci_fetch_assoc($query)){
                $dataUser['USER_ID'] = trim($row["ID"]);
                $dataUser['USER_NAME'] = trim($row["NAMA"]);
                $dataUser['USER_TYPE'] = trim($row["USER_TYPE"]);
                $org = trim($row["ORG"]);
                if($org=='2000'){
                    $org='7000';
                }
                $dataUser['ORG']=$org;
                $dataUser['NAMA_LENGKAP']=trim($row['NAMA_LENGKAP']);
                $dataUser['USER_TYPE']= trim($row['USER_TYPE']);
                $dataUser['DISTRIBUTOR_ID']= trim($row['DISTRIBUTOR_ID']);
                $dataUser['NAMA_DISTRIBUTOR']= trim($row['NAMA_DISTRIBUTOR']);
                $dataUser['VENDOR']= trim($row['VENDOR']);
                $dataUser['VENDOR_NAME']= trim($row['VENDOR_NAME']);                
                $jumxx++;
            }
            
        if($jumxx>0){
            $responAutori = array (
                'status' => true,
                'keterangan' => 'Data Found',
                'dataUserAuto' => $dataUser
            );
        }else{
            $responAutori = array (
                'status' => false,
                'keterangan' => 'Data User Not Found'
            );
        }
            
    } else {
        $responAutori = array (
            'status' => false,
            'keterangan' => 'Koneksi Gagal hena 1'
        );
    }
    
    }else{
        $responAutori = array (
            'status' => false,
            'keterangan' => 'Silahkan Cek Parameter Inputan Autorisasi boss ' .$token_int
        );
    }
    
//    }else{
//        $responAutori = array (
//            'status' => false,
//            'keterangan' => 'Waktu hanya diizinkan pada jam '.$time_eks
//        );
//    }
    
    return $responAutori;
}

function cekInDist($Dist) { 
    
        $conn = $this->koneksi();
    
        $query_cekInDist = "SELECT *FROM SV_IN_DISTRIBUTOR WHERE KODE_DISTRIBUTOR = '$Dist' and DEL = '0'";
        $query= oci_parse($conn,$query_cekInDist);
        oci_execute($query);unset($dataListCekinDist);
        $i = 0;
        while($list_dist=oci_fetch_array($query)){
             
        $dataListCekinDist[$i]['KODE_DISTRIBUTOR']     =$list_dist['KODE_DISTRIBUTOR'];
	$dataListCekinDist[$i]['DISTRIBUTOR_IN']       =$list_dist['DISTRIBUTOR_IN'];
	
	
            $i++;
        }
    
        $dataarray = $dataListCekinDist;
        
        return $dataarray;
}

function aksesservice($token_int) { 
    unset($responAutori);
    if ($token_int!='') {   
    $conn = $this->koneksi();    
    if ($conn) {

            $viewLimit="
                select * from M_SERVICE_LIMIT where DELETE_MARK=0 AND (token='$token_int' or token is null)
                order by token desc
            ";
            $query= oci_parse($conn,$viewLimit);
            oci_execute($query);$standrtLimitAksesolf=0;
            while ($row2=oci_fetch_assoc($query)){
                $standrtLimitAksesolf=$row2['LIMIT_SERVICE'];//menit
            }
            $standrtLimitAkses=$standrtLimitAksesolf;
            //$sqlakses="
            //    select tb1.*,(
            //    TO_DATE(TO_CHAR (SYSDATE,'DD-MM-YYYY HH24:MI:SS'), 'DD-MM-YYYY HH24:MI:SS')
            //    - TO_DATE(TO_CHAR (tb1.LOG_DATE,'DD-MM-YYYY HH24:MI:SS'), 'DD-MM-YYYY HH24:MI:SS')
            //    ) * (24 * 60)
            //    AS SELEISIH_INMENIT
            //    from ZREPORT_LOG_SERVICE tb1 where delete_mark=0 and token='$token_int' 
            //    and LOG_DATE=(select max(LOG_DATE) as datelast from ZREPORT_LOG_SERVICE where delete_mark=0 and token='$token_int')
            //   ";
            //$query= oci_parse($conn,$sqlakses);
            //oci_execute($query);
            //$row=oci_fetch_assoc($query);
            //$selisihex=$row['SELEISIH_INMENIT'];
            $pesanIzin='Akses dibatasi oleh Admin,diizinkan melakukan service setelah '.$standrtLimitAkses.' menit';
            if($standrtLimitAkses>0){
                if($selisihex<$standrtLimitAkses){
                    $responAutori = array (
                        'status' => false,
                        'keterangan' => $pesanIzin
                    );
                }else{
                    $responAutori = array (
                        'status' => true,
                        'keterangan' => 'Akses Diizinkan'
                    );
                }
            }else{
                $responAutori = array (
                    'status' => true,
                    'keterangan' => 'Akses Diizinkan'
                );
            }
            
    } else {
        $responAutori = array (
            'status' => false,
            'keterangan' => 'Koneksi Gagal hena 2'
        );
    }
    
    }else{
        $responAutori = array (
            'status' => false,
            'keterangan' => 'Silahkan Cek Parameter Inputan Autorisasi'
        );
    }
    
    return $responAutori;
}

function arrayTostr($arraykonv){
    unset($arraystr);
    if(count($arraykonv)>0){
        foreach ($arraykonv as $key => $value) {
            $arraystr .= $key."=>".$value.",";
        }
        
    }
    $arraystr = rtrim($arraystr,",");
    return $arraystr;
}

function log_service($data_request,$data_respon,$Log,$tokeninreq){
    $data_request = substr($this->arrayTostr($data_request),0,200);
    $data_respon =substr($data_respon['ResponseCode']." ".$data_respon['ResponseMessage'],0,200);       
    $groupLog='1';
    $tablename="ZREPORT_LOG_SERVICE";
    $conn = $this->koneksi();    
    if ($conn) {
            $awo = "INSERT INTO $tablename (
            GROUP_LOG, 
            REQUEST, RESPON, 
            BY_LOG, LOG_DATE,TOKEN) 
            VALUES ( '$groupLog',
            '$data_request','$data_respon',
            '$Log', SYSDATE,'$tokeninreq')";
            $query= oci_parse($conn,$awo);
            oci_execute($query);
    }
}
//penambahan fungi log_service_match_tms untuk memanjangkan karakter request dan respond
function log_service_match_tms($data_request,$data_respon,$Log,$tokeninreq){
    $data_request = $this->arrayTostr($data_request);
    $data_respon =$data_respon['ResponseCode']." ".$data_respon['ResponseMessage'];       
    $groupLog='1';
    $tablename="ZREPORT_LOG_SERVICE";
    $conn = $this->koneksi();    
    if ($conn) {
            $awo = "INSERT INTO $tablename (
            GROUP_LOG, 
            REQUEST, RESPON, 
            BY_LOG, LOG_DATE,TOKEN) 
            VALUES ( '$groupLog',
            '$data_request','$data_respon',
            '$Log', SYSDATE,'$tokeninreq')";
            $query= oci_parse($conn,$awo);
            oci_execute($query);
    }
}

function log_service_mki($data_request,$data_respon,$user_id,$pesan,$pesan_detail){
    // return early due to problem in db connection
    // return true;

    $data_request = substr($this->arrayTostr($data_request),0,200);
    $data_respon =substr($data_respon['ResponseCode']." ".$data_respon['ResponseMessage'],0,200);

    $tablename="ZMD_LOG_MKI";
    $conn = $this->koneksi();    
    if ($conn) {
        $awo = "INSERT INTO $tablename (
                SEND_PARAM, 
                RETURN_PARAM, 
                USER_SAVE,
                PESAN,
                PESAN_DETAIL,
                TGL) 
            VALUES ('$data_request','$data_respon','$user_id','$pesan','$pesan_detail',SYSDATE)";
            // var_dump($awo);die;
            $query= oci_parse($conn,$awo);
            oci_execute($query);
    }
}

function crm_new_so_number($conn)
{
        $sql="SELECT CRM_SO_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
        $result= oci_parse($conn, $sql);
        oci_execute($result);
        $data = oci_fetch_assoc ($result);
        $new_number = $data['NEXTVAL'];

        $panjang=strlen(strval($new_number));
        if($panjang==1)$new_number_ok='000000000'.$new_number;
        if($panjang==2)$new_number_ok='00000000'.$new_number;
        if($panjang==3)$new_number_ok='0000000'.$new_number;
        if($panjang==4)$new_number_ok='000000'.$new_number;
        if($panjang==5)$new_number_ok='00000'.$new_number;
        if($panjang==6)$new_number_ok='0000'.$new_number;
        if($panjang==7)$new_number_ok='000'.$new_number;
        if($panjang==8)$new_number_ok='00'.$new_number;
        if($panjang==9)$new_number_ok='0'.$new_number;
        return $new_number_ok;

}

function crm_new_spj($conn)
{
        $sql="SELECT CRM_SPJ_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
        $result= oci_parse($conn, $sql);
        oci_execute($result);
        $data = oci_fetch_assoc ($result);
        $new_number = $data['NEXTVAL'];

        $panjang=strlen(strval($new_number));
        if($panjang==1)$new_number_ok='00000'.$new_number;
        if($panjang==2)$new_number_ok='0000'.$new_number;
        if($panjang==3)$new_number_ok='000'.$new_number;
        if($panjang==4)$new_number_ok='00'.$new_number;
        if($panjang==5)$new_number_ok='0'.$new_number;
        if($panjang==6)$new_number_ok=$new_number;
        return $new_number_ok;
}
function crm_new_spj_new($conn, $field)
    {
            $kd_dist = substr($field, 7,10);

            $sql="SELECT CRM_SPJ_DTL_SEQ.NEXTVAL FROM SYS.DUAL";
            $result= oci_parse($conn, $sql);
            oci_execute($result);
            $data = oci_fetch_assoc ($result);
            $new_number = $data['NEXTVAL'];

            $panjang=strlen(strval($new_number));
            if($panjang==1)$new_number_ok="$kd_dist".'000000'.$new_number;
            if($panjang==2)$new_number_ok="$kd_dist".'00000'.$new_number;
            if($panjang==3)$new_number_ok="$kd_dist".'0000'.$new_number;
            if($panjang==4)$new_number_ok="$kd_dist".'000'.$new_number;
            if($panjang==5)$new_number_ok="$kd_dist".'00'.$new_number;
            if($panjang==6)$new_number_ok="$kd_dist".'0'.$new_number;
            if($panjang==7)$new_number_ok="$kd_dist".$new_number;
//                if($panjang==8)$new_number_ok='00'.$new_number;
//                if($panjang==9)$new_number_ok='0'.$new_number;
//		if($panjang==10)$new_number_ok=$new_number;
            return $new_number_ok;
    }

//untuk insert ke database
//$conn= koneksi ke database
//$field_names= nama-nama field database (array)
//$field_data= isi dari field database yang akan diisi (array)
//$tablename= nama table database
function insert($conn,$field_names,$field_data,$tablename)
{

        $query = "INSERT INTO $tablename ($field_names[0]";
        for($k=1;$k< count($field_names);$k++)
        {
                $query.=', '."$field_names[$k]";
        }
        $query.=") VALUES ('$field_data[0]'";
        for($k=1;$k< count($field_data);$k++)
        {
                list($tang,$gal)=split("_",$field_data[$k]);
                if($tang=="instgl")
                        $query.=', '."TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";	
                else if($field_data[$k]=='SYSDATE')	
                        $query.=', '."$field_data[$k]";
                else if ($tang == "TO" && substr($gal, 0, 4) == "Date")
                $query.=', ' . "$field_data[$k]";
                else	
                $query.=', '."'$field_data[$k]'";
        }
                $query.=')';
//			echo"<br>". $query;

        $sqlquery= oci_parse($conn, $query);
        oci_execute($sqlquery);
}

//untuk update ke database
//$conn = koneksi ke database
//$field_names = nama-nama field database (array)
//$field_data = isi dari field database yang akan diisi (array)
//$tablename = nama table database
//$field_id = nama field database yang dijadikan filter (array)
//$value_id= nilai/data dari field database yang dijadikan filter (array)
function update($conn,$field_names,$field_data,$tablename,$field_id,$value_id)
{
        $query="UPDATE $tablename SET $field_names[0]='$field_data[0]'";
        for($k=1;$k< count($field_names);$k++)
        {
                list($tang,$gal)=split("_",$field_data[$k]);
                if($tang=="updtgl")
                        $query.=', '."$field_names[$k]=TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
                else if($field_data[$k]=='SYSDATE')	
                        $query.=', '."$field_names[$k]=$field_data[$k]";
                else	
                        $query.=', '."$field_names[$k]='$field_data[$k]'";
        }
                $query.=" WHERE $field_id[0]='$value_id[0]'";
        for($j=1;$j< count($field_id);$j++)
        {
                $query.=' AND '."$field_id[$j]='$value_id[$j]'";
        }
//		echo "<br><br>".$query;
        $sqlquery= oci_parse($conn,$query);
        oci_execute($sqlquery);
}

//untuk insert ke database from database
//$conn= koneksi ke database
//$field_names= nama-nama field database (array)
//$field_data= isi dari field database yang akan diisi (array)
//$tablename= nama table database
function insertinto($conn,$field_names,$field_data,$tablenameto,$tablenamefrom,$field_id,$value_id)
{
        $query = "INSERT INTO $tablenameto ($field_names[0]";
        for($k=1;$k< count($field_names);$k++)
        {
                $query.=', '."$field_names[$k]";
        }
        $query.=") (SELECT $field_data[0]";
        for($k=1;$k< count($field_data);$k++)
        {
                list($tang,$gal)=split("_",$field_data[$k]);
                if($tang=="instgl")
                        $query.=', '."TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";	
                else if($field_data[$k]=='SYSDATE')	
                        $query.=', '."$field_data[$k]";
                else	
                $query.=', '."$field_data[$k]";
        }
                $query.=" FROM $tablenamefrom ";
                $query.=" WHERE $field_id[0]='$value_id[0]')";

                //echo $query;

        $sqlquery= oci_parse($conn, $query);
        oci_execute($sqlquery);
}


//untuk delete ke database
//$conn = koneksi ke database
//$tablename = nama table database
//$field_id = nama field database yang dijadikan filter (array)
//$value_id= nilai/data dari field database yang dijadikan filter (array)
function delete($conn,$tablename,$field_id,$value_id)
{
        $query="DELETE FROM $tablename WHERE $field_id[0]='$value_id[0]'";
        for($j=1;$j< count($field_id);$j++)
        {
                $query.=' AND '."$field_id[$j]='$value_id[$j]'";
        }

        $sqlquery= oci_parse($conn, $query);
        oci_execute($sqlquery);
}
function close($conn)
{
        oci_close($conn);
}


function getPRODUK($orgin,$codegudmaterial,$qtymaterial) {
        unset($data);
        require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
           echo $sap->PrintStatus();
           exit;
        }
        $fce = &$sap->NewFunction("Z_ZAPPSD_LIST_MATERIAL_SALES");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }
        //Param Export
        $fce->I_VKORG = $orgin;
        $fce->I_MATNR = $codegudmaterial;
        $fce->Call();
//            echo "<pre>";
//            print_r($fce);
//            echo "</pre>";
//            exit;
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->IT_OUT->Reset();
            //Display Tables
            $i = 0;
            while ($fce->IT_OUT->Next()) {                   
//                    echo "<pre>";
//                    print_r($fce->IT_OUT->row);
//                    echo "</pre>";
                $data[$i]=$fce->IT_OUT->row;
                if(strtoupper($fce->IT_OUT->row["GEWEI"])=='KG'){
                     $qtymto=@(number_format($fce->IT_OUT->row["NTGEW"])/1000);
                }else{
                     $qtymto=number_format($fce->IT_OUT->row["NTGEW"]);
                }
                $qtymto=@($qtymaterial*$qtymto);
                $data[$i]['QTY_TO']=$qtymto;
                $i++;

            }
            return $data;
        }else
            return $data;
        $fce->Close();
        $sap->Close();
}

function toDate($tgl, $jam) {
        $disply_tgl = substr($tgl, 6, 2) . '-' . substr($tgl, 4, 2) . '-' . substr($tgl, 0, 4);
        $disply_jam = substr($jam, 0, 2) . ':' . substr($jam, 2, 2) . ':' . substr($jam, 4, 2);
        if ($tgl != '')
                $display = $disply_tgl;
        if ($jam != '')
                $display .= ' ' . $disply_jam;
        return $display;
}

function loadPosisi($conn){
	unset($data);
	$sql="select KD_GDG,LATITUDE,LONGITUDE from CRM_GUDANG where delete_mark=0
		  and (LATITUDE is not null and LONGITUDE is not null)";
	$query= oci_parse($conn, $sql);
	$sukses_sel = oci_execute($query);
	while($row = @oci_fetch_array($query)){
		$data[$row['KD_GDG']]=$row;
	}
	return $data;

}

function loadStdWaktu($conn,$com,$plant,$kota){
	unset($data);
	$sql="SELECT * from ZAPPSD_TRANS_STD_KPI_AREA WHERE com in ('$com') AND plant in ('$plant') 
		and kota is not null AND tahun='2014' and pros_antri is null
		AND bulan='7' AND kota='$kota' ORDER BY id DESC
		";
	$query= oci_parse($conn, $sql);
	$sukses_sel = oci_execute($query);
	while($row = @oci_fetch_array($query)){
		$data[$row['PLANT'].$row['KOTA']]=$row;
	}
	return $data;

}

function keamananser($halaman,$user_id){    
    $conn = $this->koneksi(); 
    $posisi = strpos($halaman, "sdonline/");
    $newdir = substr($halaman, $posisi);
    $lokasi = str_replace("sdonline/", "", $newdir);
    $array  = array();
    $query  = "SELECT ID FROM TB_MASTER_HALAMAN WHERE LOKASI_FILE='$lokasi' ";
    $query_action1= oci_parse($conn, $query);
    oci_execute($query_action1);
    $result=oci_fetch_assoc($query_action1);
    $halaman_id=$result['ID'];    
    $action_page=0;
    if($user_id!="" and $user_id != NULL){
    $sql_action= "SELECT COUNT(*) AS ACTION FROM USER_HALAMAN_V WHERE DELETE_MARK <> '1' AND USER_ID = '$user_id' AND HALAMAN_ID = '$halaman_id' ORDER BY ACTION ASC";
    $query_action= oci_parse($conn, $sql_action);
    oci_execute($query_action);
    $row_action=oci_fetch_assoc($query_action);
    $action_page=$row_action[ACTION];
    }
    return $action_page;
}	

function getPropinsi($conn){
	unset($data);
	$sql="select KD_PROV,NM_PROV from ZREPORT_M_PROVINSI";
	$query= oci_parse($conn, $sql);
	$sukses_sel = oci_execute($query);
	while($row = @oci_fetch_array($query)){
		$data[$row['KD_PROV']]=$row;
	}
	return $data;

}

//name organisasi
function getNameCom($conn){
    unset($arraData);
    $sql = "
            select * from ZREPORT_M_ORG where delete_mark='0' order by URUTAN asc
            ";
    $query2= oci_parse($conn, $sql);
    oci_execute($query2);
//    echo $sql;
    while($dataRec1=oci_fetch_array($query2)){ 
               $arraData[$dataRec1['ORG']] = $dataRec1['NAMA_ORG'];
    } 

    return $arraData;

}

//mapping com sales
function getComin($conn,$orginj){
    unset($arraData);
    $sql = "select ORGIN from OR_MAP_COM where orgm='$orginj' and delete_mark=0
            group by ORGIN";
    $query2= oci_parse($conn, $sql);
    oci_execute($query2);
//    echo $sql;
    while($dataRec1=oci_fetch_array($query2)){ 
               $arraData[$dataRec1['ORGIN']] = $dataRec1['ORGIN'];
    } 

    return $arraData;

}
}
?>
