<?php

class ApiSmbr{
    // dev actual
    // private $url_get_token = 'http://sapdev.semenbaturaja.co.id:8011/zabap_einv/x_token/fetch?sap-client=120';
    // private $url_get_shipment_cost = 'http://sapdev.semenbaturaja.co.id:8011/zabap_einv/shp/recal?sap-client=120';

    // dev
    private $url_get_token = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/get_token.php';
    private $url_get_shipment_cost = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/get_shipment_cost.php';
    private $url_create_invoice = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/create_invoice.php';
    private $get_rek = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/get_rek.php';
    private $url_posting_invoice = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/posting_invoice.php';
    private $url_cancel_invoice = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/cancel_invoice.php';
    private $url_create_shipment = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/create_shipment.php';
    private $url_expedition_invoice = 'https://dev-app.sig.id/dev/sd/sdonline/ex_ba_sp/api/expedition_invoice.php';

    // local
    // private $url_get_token = 'http://localhost/csms_dev/ex_ba_sp/api/get_token.php';
    // private $url_get_shipment_cost = 'http://localhost/csms_dev/ex_ba_sp/api/get_shipment_cost.php';
    // private $url_create_invoice = 'http://localhost/csms_dev/ex_ba_sp/api/create_invoice.php';
    // private $get_rek = 'http://localhost/csms_dev/ex_ba_sp/api/get_rek.php';
    // private $url_posting_invoice = 'http://localhost/csms_dev/ex_ba_sp/api/posting_invoice.php';
    // private $url_cancel_invoice = 'http://localhost/csms_dev/ex_ba_sp/api/cancel_invoice.php';
    // private $url_create_shipment = 'http://localhost/csms_dev/ex_ba_sp/api/create_shipment.php';
    // private $url_expedition_invoice = 'http://localhost/csms_dev/ex_ba_sp/api/expedition_invoice.php';

    private $auth_username = 'smbr-jaya';
    private $auth_password = 'i-love-smbr';
    private $token = '';
    private $cookie = '';
    private $error_msg = '';

    public function __construct() {
        $result = $this->get_token($this->cookie);
        if(!$result['success']){
            $this->error_msg = 'failed get token: '. $result['msg'];
        }
        $this->token = $result['token'];
    }

    public function get_token(&$cookieFile)
    {
        $response = array(
            'success' => false,
            'msg' => '',
            'data' => array()
        );

        $cookieFile = tempnam(sys_get_temp_dir(), 'sap_cookie');
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_get_token);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile); // simpan cookie
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "x-csrf-token: Fetch",
            "Authorization: Basic " . $auth
        ));

        $result = curl_exec($ch);
        $error = curl_error($ch);

        if ($error) {
            curl_close($ch);
            $response['msg'] = 'Error: ' . $error;
            return $response;
        }

        // Pisahkan header dan body
        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $header = substr($result, 0, $header_size);
        $body = substr($result, $header_size);

        // Ambil token dari header
        $csrfToken = null;
        foreach (explode("\r\n", $header) as $line) {
            if (stripos($line, 'x-csrf-token:') !== false) {
                $csrfToken = trim(str_ireplace('x-csrf-token:', '', $line));
                break;
            }
        }

        curl_close($ch);

        $response['success'] = true;
        $response['msg'] = 'Success Get Token';
        $response['token'] = $csrfToken;

        return $response;
    }


    public function get_shipment_cost($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_get_shipment_cost);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function get_rek($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->get_rek);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function create_invoice($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_create_invoice);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function posting_invoice($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_posting_invoice);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function cancel_invoice($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_cancel_invoice);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function expedition_invoice($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          $response['msg'] = 'empty parameter!';
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_expedition_invoice);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] != '200'){
          $response['msg'] = $data['message'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }

    public function create_shipment($param)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if($this->error_msg){
            $response['msg'] = $this->error_msg;
            return $response;
        }

        if(!$param){
          return $response;
        };

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url_create_shipment);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($param));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $this->cookie);

        $auth = base64_encode($this->auth_username . ':' . $this->auth_password);

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/x-www-form-urlencoded',
            'x-csrf-token: '.$this->token,
            'Authorization: Basic ' . $auth
        ));
    
        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if(!$data['success']){
          $response['msg'] = $data['msg'];
          return $response;
        }

        $response['data'] = $data['data'];
        $response['success'] = true;
        $response['msg'] = $data['msg'];

        return $response;
    }
}

?>