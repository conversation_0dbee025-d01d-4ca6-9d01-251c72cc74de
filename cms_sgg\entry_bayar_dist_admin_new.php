<?php
session_start();
include ('cms_fungsi.php');
include ('../include/validasi.php');
$fungsi = new cms_fungsi();
$conn = $fungsi->cms_koneksi();

function cek() {
    alert('Data should not be empty');
}

// variable
$halaman_id = 1756;
//$halaman_id = 3293;
$currentPage = "entry_bayar_dist_admin_new.php";
$user_id = $_SESSION['user_id'];
$org = $_SESSION['user_org'];
$distr_id = $_POST['dist'];
$distr_nm = $fungsi->findOneByOne($conn, "TB_USER_BOOKING", "DISTRIBUTOR_ID", $distr_id, "NAMA_LENGKAP");

//$soldto = $fungsi->sapcode($distr_id);
$orgx = $org;
$disidx = $distr_id;
$soldto = $distr_id;
$msg_type = "";

//$read		= 'readonly';
//$tglxx= '20100928';


if ($org == '2000') {
    $org1 = 'SEMEN INDONESIA';
} elseif ($org == '3000') {
    $org1 = 'SEMEN PADANG';
} elseif ($org == '4000') {
    $org1 = 'SEMEN TONASA';
} elseif ($org == '5000') {
    $org1 = 'OPCO SEMEN';
} elseif ($org == '6000') {
    $org1 = 'TANGLONG CEMENT';
} elseif ($org == '7000') {
    $org1 = 'Virtual OPCO';
} elseif ($org == '7900') {
    $org1 = 'MD Semen Indonesia';
} else {
    $org = '';
    $org1 = '';
}


if ($fungsi->keamanan($halaman_id, $user_id) == 0) {
    ?>
    <SCRIPT LANGUAGE="JavaScript">
        <!--
        alert("You are not authorized to access this page.... \n Login First...");
        //-->
    </SCRIPT>
    <a href="../index.php">Login....</a>
    <?php
    exit();
}

// Untuk button Find
if ((isset($_POST['Find'])) && ($_POST['tanggal_selesai'] != '') && ($_POST['curr'] != '')) {
    //$tglm 		= $_POST['tanggal_mulai'];
    $tgls = $_POST['tanggal_selesai'];
    $tgl_rn = $_POST['create_rn_date'];
    $curr = $_POST['curr'];

    $tgl = explode("-", $tgls);
    $tglx = $tgl[2] . "" . $tgl[1] . "" . $tgl[0];
    $kmke = $_POST['kmk'];

    //rfc menampilkan data invoice
    $arrout = $fungsi->cms_select_invoice($orgx, $soldto, $tglx, $kmke, '', $curr);
//	echo "array=".$arrout[0][1];
    $total = count($arrout[0]);
    $date = date("Ymd");
//    $aa = 0;
//    $bb = 0;
//    for ($q = 1; $q <= $total; $q++) {
//        if (substr($arrout[5][$q], 0, 3) == '092') {
//            if ($date >= $arrout[20][$q]) {
//                $aa++;
//            } else if ($date <= $arrout[20][$q]) {
//                $bb++;
//            }
//        }
//    }
    //RFC Untuk Menampilkan Jurnal Potongan

    if ($orgx == '2000' || $orgx == '7000' || $orgx == '7900') {
        $jurnal_out = $fungsi->cms_jurnal_potongan($orgx, $soldto);
        $tot = count($jurnal_out[0]);
        for ($i = 0; $i < $total; $i++) {
            if ($arrout[13][$i] < 0 && $arrout[13][$i] < 0) { //Credit Note
                $jurnal_out[0][$tot] = $arrout[0][$i];
                $jurnal_out[1][$tot] = $arrout[1][$i];
                $jurnal_out[2][$tot] = $arrout[5][$i];
                $jurnal_out[3][$tot] = $arrout[2][$i];
                $jurnal_out[4][$tot] = "";
                $jurnal_out[5][$tot] = $arrout[7][$i];
                $jurnal_out[6][$tot] = "";
                $jurnal_out[7][$tot] = number_format($arrout[12][$i], 0, ',', '.');
                $jurnal_out[8][$tot] = number_format($arrout[13][$i], 0, ',', '.');
                $jurnal_out[9][$tot] = $arrout[9][$i];
                $jurnal_out[10][$tot] = "";
                $jurnal_out[11][$tot] = "";

                $lv_amount = $arrout[12][$i] / 100;
                $lv_amountlc = $arrout[13][$i] / 100;
                $lv_amount = str_replace('-', '', $lv_amount);
                $lv_amountlc = str_replace('-', '', $lv_amountlc);
                $lv_amount = $lv_amount . '.00-';
                $lv_amountlc = $lv_amountlc . '.00-';
                $jurnal_out[12][$tot] = $lv_amount;
                $jurnal_out[13][$tot] = $lv_amountlc;

                $jurnal_out[14][$tot] = $arrout[4][$i];
                $jurnal_out[15][$tot] = $arrout[6][$i];
				$jurnal_out[16][$tot] = $arrout[31][$i];
                $tot++;
            }
        }
        $arrout = $fungsi->cms_select_invoice($orgx, $soldto, $tglx, $kmke, "X", $curr);
        $total = count($arrout[0]);
    }

    $aa = 0;
    $bb = 0;
    for ($q = 1; $q <= $total; $q++) {
        if (substr($arrout[5][$q], 0, 3) == '092') {
            if ($date >= $arrout[20][$q]) {
                $aa++;
            } else if ($date <= $arrout[20][$q]) {
                $bb++;
            }
        }
    }
    if ($total < 1) {
        $pesan = "No Data Found...";
    }
//	echo "<br>".$total;
}

// untuk proses button OK
if (isset($_POST['oke'])) {


if ($_POST['comp']=='2000' || $_POST['comp']=='7000' || $_POST['comp']=='5000' || $_POST['comp']=='7900')
{
?>
    <SCRIPT LANGUAGE="JavaScript">
        alert("Sorry, Company 2000, 7000, and 5000 is under Maintenance");
        document.location = "entry_bayar_dist_admin_new.php";
    </SCRIPT>
<?php
    exit();
}

    // $tgls = $_POST['tglxx'];
    $tgls = isset($_POST['tgl_rn']) && $_POST['tgl_rn'] != "" ? $_POST['tgl_rn'] : $_POST['tglxx'];
    $tgl = explode("-", $tgls);
    $tglx = $tgl[2] . "" . $tgl[1] . "" . $tgl[0];
    // $tglx = date('Ymd');
    $curr = $_POST['curr'];

    $inv = $_POST['inv'];
    $cpcd = $_POST['cpcd'];
    //echo $cpcd[0];
    $kddist = $_POST['kddist'];
    //echo $kddist[0].'<br>';
    $year = $_POST['year'];
    //echo $year[0].'<br>';
    $noinva = $_POST['noinva'];
    //echo $noinva[0].'<br>';
    $buz = $_POST['buz'];
    //echo $buz[0].'<br>';
    $noinv = $_POST['noinv'];
    //echo $noinv[0].'<br>';
    $bl = $_POST['bl'];
    //echo $bl[0].'<br>';
    $invdt = $_POST['invdt'];
    //echo $invdt[0].'<br>';
    $duedt = $_POST['duedt'];
    //echo $duedt[0].'<br>';
    $duedt2 = $_POST['duedt2'];
    #echo $duedt2[0].'<br>';
    $curren = $_POST['curren'];
    //echo $curren[0].'<br>';
    $grossamtd = $_POST['grossamtd'];
    //echo $grossamtd[0].'<br>';
    $grossamtl = $_POST['grossamtl'];
    //echo $grossamtl[0].'<br>';
    $openamtd = $_POST['openamtd'];
    //echo $openamtd[0].'<br>';
    $openamtl = $_POST['openamtl'];
    //echo $openamtl[0].'<br>';
    $payamtd = str_replace(',', '.', str_replace('.', '', $_POST['payamtl']));
    //echo $payamtd[0].'<br>';
    $payamtl = str_replace(',', '.', str_replace('.', '', $_POST['payamtl']));
    //echo $payamtl[0].'<br>';
    $saldamtd = $_POST['saldamtd'];
    //echo $saldamtd[0].'<br>';
    $saldamtl = $_POST['saldamtl'];
    //echo $saldamtl[0].'<br>';
    $zterm = $_POST['zterm'];
    $kmk = $_POST['kmkX'];

    //$jumlah		= count($inv);
    $total = count($inv);
    $totpay = 0;
    $totpen = 0;
    //$arrout=$fungsi->cms_select_invoice($orgx,$disidx,$tglx);
//	echo "jml=".$jumlah."<br>";
    for ($i = 0; $i < $total; $i++) {
        $id = $inv[$i];
        $cpcdx = $cpcd[$id];
        $noinvax = $noinva[$id];
        $j = $inv[$i];

        //$data=
        //echo "data=".$inv[$i]." noinv= ".$noinv[$i]." ".$arrout[0][$j]."<br>";
        $totpay = $totpay + $payamtl[$j];
        $totopen = $totopen + $openamtl[$j];
        if ($curren[$j] == 'IDR') {
            $a++;
        } elseif ($curren[$j] == 'USD') {
            $b++;
        }
    }
    if ($a != 0 and $b != 0) {
        ?>
        <SCRIPT LANGUAGE="JavaScript">
        <!--
            alert("You must select the invoice with the same currency");
        //-->
        </SCRIPT>
        <?php
    } elseif ($totpay > $totopen) {
        ?>
        <SCRIPT LANGUAGE="JavaScript">
        <!--
            alert("You pay the debt exceeds the carrying");
        //-->
        </SCRIPT>
        <?php
    } else {
        if ($orgx == '3000') {
            $sap = new SAPConnection();
            $sap->Connect("sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                echo $sap->PrintStatus();
                exit;
            }

            $fce = $sap->NewFunction("Z_ZCFI_CMS_CHECK_RN");
            if ($fce == false) {
                $sap->PrintStatus();
                exit;
            }

            //parameter inputan
            $fce->XBUKRS = $cpcdx;
            $fce->XKUNNR = $disidx;
            $fce->Call();

            if ($fce->GetStatus() == SAPRFC_OK) {
                if ($fce->PE_RETURN["TYPE"] == "E") {
                    $pesan = "<font color='#FF0000'><br>There are still unpaid rn ..</font>";
                } else {
                    //rfc generate rn dan mengupdate amount yang dibayarkan per invoice
                    $datpars = array($cpcd, $kddist, $year, $noinva, $buz, $noinv, $bl, $invdt, $duedt, $curren,
                        $grossamtd, $grossamtl, $openamtd, $openamtl, $payamtd, $payamtl, $saldamtd, $saldamtl, $zterm, $duedt2);
                    //rfc nampilin data invoice yang dah select
                    $arrout = $fungsi->cms_create_rn($total, $datpars, $cpcdx, $soldto, $tglx, $curr, $inv, $kmk);
                    $pesan = $arrout[20];
                    $msg_type = $arrout[21];
                    $rnstat = $arrout[18];
                    //echo "Invoice=".$arrout[0][0]."".$arrout[0][1];
                    $byr = 0;
                    for ($i = 0; $i < $total; $i++) {
                        $j = $inv[$i];
                        $byr = $byr + $payamtl[$j];
                    }
                    $totbyr = $byr;
                }
            }
        } else if ($orgx == '2000' or $orgx == '7000' or $orgx == '7900') {
            $lv_check = "";
            $total_item = 0;
            $count_pot = 0;
            $datpars = array($cpcd, $kddist, $year, $noinva, $buz, $noinv, $bl, $invdt, $duedt, $curren,
                $grossamtd, $grossamtl, $openamtd, $openamtl, $payamtd, $payamtl, $saldamtd, $saldamtl, $zterm, $duedt2);

            $byr = 0;

            for ($i = 0; $i < $total; $i++) {
                $j = $inv[$i];
                $byr = $byr + $payamtl[$j];
            }
            $totbyr = $byr;
            if (isset($_POST[potcek])) {
                $count_pot = count($_POST[potcek]);
                $totalpot = 0;
                for ($i = 0; $i < $count_pot; $i++) {
                    $idx_pot = $_POST[potcek][$i];
                    $datpot[0][$i] = $_POST[potcom][$idx_pot];
                    $datpot[1][$i] = $_POST[potcust][$idx_pot];
                    $datpot[2][$i] = $_POST[potyear][$idx_pot];
                    $datpot[3][$i] = $_POST[potdoc][$idx_pot];
                    $datpot[4][$i] = $_POST[potbuzei][$idx_pot];
                    $datpot[5][$i] = $_POST[potdoc][$idx_pot];
                    $datpot[6][$i] = $_POST[potblart][$idx_pot];
                    $datpot[7][$i] = '';
                    $datpot[8][$i] = '';
                    $datpot[9][$i] = $_POST[potcurr][$idx_pot];

                    $lv_amount = str_replace('.', '', $_POST[potamount][$idx_pot]);
                    $lv_amountlc = str_replace('.', '', $_POST[potamountlc][$idx_pot]);
                    $datpot[10][$i] = $lv_amount;
                    $datpot[11][$i] = $lv_amountlc;
                    $datpot[12][$i] = $lv_amount;
                    $datpot[13][$i] = $lv_amountlc;
                    $datpot[14][$i] = $lv_amount;
                    $datpot[15][$i] = $lv_amountlc;
                    $datpot[16][$i] = $lv_amount;
                    $datpot[17][$i] = $lv_amountlc;
                    $datpot[18][$i] = '';
                    $datpot[19][$i] = '';

                    $totalpot += $lv_amountlc;
                    $jurnal_out[0][$i] = $_POST[potcom][$idx_pot];
                    $jurnal_out[1][$i] = $_POST[potcust][$idx_pot];
                    $jurnal_out[2][$i] = $_POST[potdoc][$idx_pot];
                    $jurnal_out[3][$i] = $_POST[potyear][$idx_pot];
                    $jurnal_out[4][$i] = $_POST[potposdate][$idx_pot];
                    $jurnal_out[5][$i] = $_POST[potdocdate][$idx_pot];
                    $jurnal_out[6][$i] = $_POST[potreff][$idx_pot];
                    $jurnal_out[7][$i] = $_POST[potamount][$idx_pot];
                    $jurnal_out[8][$i] = $_POST[potamountlc][$idx_pot];
                    $jurnal_out[9][$i] = $_POST[potcurr][$idx_pot];
                    $jurnal_out[10][$i] = $_POST[potgl][$idx_pot];
                    $jurnal_out[11][$i] = $_POST[potflag][$idx_pot];
                    $jurnal_out[12][$i] = $_POST[potwrbtr][$idx_pot];
                    $jurnal_out[13][$i] = $_POST[potdmbtr][$idx_pot];
                    $jurnal_out[14][$i] = $_POST[potbuzei][$idx_pot];
                    $jurnal_out[15][$i] = $_POST[potblart][$idx_pot];
                    $jurnal_out[16][$i] = $_POST[potreff][$idx_pot];
                }

                $totbyr = $totbyr + $totalpot;
                if ($total == 0) {
                    $lv_check = "X";
                    $msg_type = "E";
                    $pesan = "Gagal Create RN ! Pilih Minimal 1 Invoice";
                } elseif ($totbyr <= 0) {
                    $lv_check = "X";
                    $msg_type = "E";
                    $pesan = "Gagal Create RN ! Total Potongan Lebih Besar Atau Sama Dengan Total Invoice";
                } elseif ($count_pot > 0) {
                    $total_item = $total + $count_pot;
                    if ($total_item > 13) {
                        $lv_check = "X";
                        $msg_type = "E";
                        $pesan = "Gagal Create RN ! Jika Mengunakan Potongan Pembayaran, Total Item Tidak Boleh Lebih Dari 13";
                    }
                }
                if ($lv_check != "X") {
                    $arrout = $fungsi->cms_create_rn_potongan($total, $datpars, $cpcdx, $soldto, $tglx, $curr, $inv, $kmk, $datpot);
                    $pesan = $arrout[20];
                    $msg_type = $arrout[21];
                }
            } else {
                if ($totbyr <= 0) {
                    $lv_check = "X";
                    $msg_type = "E";
                    $pesan = "Gagal Create RN ! Total Potongan Lebih Besar Atau Sama Dengan Total Invoice";
                } else {
                    $arrout = $fungsi->cms_create_rn($total, $datpars, $cpcdx, $soldto, $tglx, $curr, $inv, $kmk);
                    $pesan = $arrout[20];
                    $msg_type = $arrout[21];
                }
            }
            $rnstat = $arrout[18];
        } else {
            //rfc generate rn dan mengupdate amount yang dibayarkan per invoice

            $datpars = array($cpcd, $kddist, $year, $noinva, $buz, $noinv, $bl, $invdt, $duedt, $curren,
                $grossamtd, $grossamtl, $openamtd, $openamtl, $payamtd, $payamtl, $saldamtd, $saldamtl, $zterm, $duedt2);

            //rfc nampilin data invoice yang dah select
            $arrout = $fungsi->cms_create_rn($total, $datpars, $cpcdx, $soldto, $tglx, $curr, $inv, $kmk);
            $rnstat = $arrout[18];
            //echo "Invoice=".$arrout[0][0]."".$arrout[0][1];
            $byr = 0;

            for ($i = 0; $i < $total; $i++) {
                $j = $inv[$i];
                $byr = $byr + $payamtl[$j];
            }
            $totbyr = $byr;
        }
    }
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
    <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
    <!-- import the calendar script -->
    <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
    <!-- import the language module -->
    <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
    <script type="text/javascript" src="js/jquery_1.7.1.js"></script>
    <script type="text/javascript" src="js/ui_1.8.16/jquery.ui.widget.js"></script>
    <script type="text/javascript" src="js/ui_1.8.16/jquery.ui.dialog.js"></script>
    <script type="text/javascript" src="js/ui_1.8.16/jquery.ui.draggable.js"></script>
    <script type="text/javascript" src="js/ui_1.8.16/jquery.ui.resizable.js"></script>
    <script type="text/javascript" src="js/ui_1.8.16/jquery.ui.position.js"></script>
    <script type="text/javascript" src="js/ui_1.8.16/jquery.ui.core.js"></script>
    <script type="text/javascript" src="js/jquery.validate.js"></script>
    <link rel="stylesheet" href="//code.jquery.com/ui/1.11.2/themes/smoothness/jquery-ui.css">
        <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
            <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
            <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
            <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
            <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
            <!--<link href="css/cms.css" rel="stylesheet" type="text/css" />-->
            <style type="text/css">
                <!--
                #Layer1 {
                    position:absolute;
                    width:795px;
                    height:115px;
                    z-index:0;
                    left: 159px;
                    top: 296px;
                }
                .style5 {color: #791800}
                .stylern { font-size:22px; font-weight:bold; height:auto }

            </style>
            <head>
                <script language=javascript>
    < !-- Edit the message as your wish -- >
            var message = "You dont have permission to right click";

    function clickIE()

    {
        if (document.all)
        {
            (message);
            return false;
        }
    }

    function clickNS(e) {
        if
                (document.layers || (document.getElementById && !document.all))
        {
            if (e.which == 2 || e.which == 3) {
                (message);
                return false;
            }
        }
    }
    if (document.layers)
    {
        document.captureEvents(Event.MOUSEDOWN);
        document.onmousedown = clickNS;
    }
    else
    {
        document.onmouseup = clickNS;
        document.oncontextmenu = clickIE;
    }

    document.oncontextmenu = new Function("return false")

    function getXMLHTTP() {
        var xmlhttp = false;
        try {
            xmlhttp = new XMLHttpRequest();
        }
        catch (e) {
            try {
                xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
            }
            catch (e) {
                try {
                    xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                }
                catch (e1) {
                    xmlhttp = false;
                }
            }
        }

        return xmlhttp;
    }


                </script>

                <script language=javascript>

                    function checkAll(n, fldName) {
                        if (!fldName) {
                            fldName = 'cb';
                        }
                        var f = document.formInv;
                        var c = f.toggle.checked;

                        var n2 = 0;
                        for (i = 0; i < n; i++)
                        {
                            cb = eval('f.' + fldName + '' + i);
                            if (cb) {
                                cb.checked = c;
                                n2++;
                            }
                        }
                        if (c) {
                            document.formInv.boxchecked.value = n2;
                        } else {
                            document.formInv.boxchecked.value = 0;
                        }
                    }
                </script>
                <style type="text/css">
                    body	{background:#fff;}
                    table	{border:0;border-collapse:collapse;}
                    td		{padding:4px;}
                    tr.odd1	{background:#F9F9F9;}
                    tr.odd0	{background:#FFFFFF;}
                    tr.highlight	{background:#BDA9A2;}
                    tr.checked	{background:orange;color:#fff;}
                </style>

                <script type="text/javascript">

                    function addLoadEvent(func) {
                        var oldonload = window.onload;
                        if (typeof window.onload != 'function') {
                            window.onload = func;
                        } else {
                            window.onload = function() {
                                oldonload();
                                func();
                            }
                        }
                    }

                    function addClass(element, value) {
                        if (!element.className) {
                            element.className = value;
                        } else {
                            newClassName = element.className;
                            newClassName += " ";
                            newClassName += value;
                            element.className = newClassName;
                        }
                    }

                    function removeClassName(oElm, strClassName) {
                        var oClassToRemove = new RegExp((strClassName + "\s?"), "i");
                        oElm.className = oElm.className.replace(oClassToRemove, "").replace(/^\s?|\s?$/g, "");
                    }


                    function stripeTables() {
                        var tables = document.getElementsByTagName("table");
                        for (var m = 0; m < tables.length; m++) {
                            if (tables[m].className == "s") {
                                var tbodies = tables[m].getElementsByTagName("tbody");
                                for (var i = 0; i < tbodies.length; i++) {
                                    var odd = true;
                                    var rows = tbodies[i].getElementsByTagName("tr");
                                    for (var j = 0; j < rows.length; j++) {
                                        if (odd == false) {
                                            odd = true;
                                        } else {
                                            addClass(rows[j], "odd");
                                            odd = false;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    function highlightRows() {
                        if (!document.getElementsByTagName)
                            return false;
                        var tables = document.getElementsByTagName("table");
                        for (var m = 0; m < tables.length; m++) {
                            if (tables[m].className == "pickme") {
                                var tbodies = tables[m].getElementsByTagName("tbody");
                                for (var j = 0; j < tbodies.length; j++) {
                                    var rows = tbodies[j].getElementsByTagName("tr");
                                    for (var i = 0; i < rows.length; i++) {
                                        rows[i].oldClassName = rows[i].className
                                        rows[i].onmouseover = function() {
                                            if (this.className.indexOf("checked") == -1)
                                                addClass(this, "highlight");
                                        }
                                        rows[i].onmouseout = function() {
                                            if (this.className.indexOf("checked") == -1)
                                                this.className = this.oldClassName
                                        }
                                    }
                                }
                            }
                        }
                    }

                    function highlight(box, obj)
                    {
                        var color1 = 'red';
                        var color2 = '';

                        document.getElementById(obj).style.background = (box.checked ? color1 : color2);
                    }

                    function selectRowRadio(row) {
                        var radio = row.getElementsByTagName("input")[0];
                        radio.checked = true;
                        checkForother(radio);
                    }

                    function removeSelectedStateFromOtherRows() {
                        var tables = document.getElementsByTagName("table");
                        for (var m = 0; m < tables.length; m++) {
                            if (tables[m].className == "pickme") {
                                var tbodies = tables[m].getElementsByTagName("tbody");
                                for (var j = 0; j < tbodies.length; j++) {
                                    var rows = tbodies[j].getElementsByTagName("tr");
                                    for (var i = 0; i < rows.length; i++) {
                                        if (rows[i].className.indexOf("checked") != -1) {
                                            removeClassName(rows[i], "checked");
                                            removeClassName(rows[i], "highlight");
                                        }
                                    }
                                }
                            }
                        }
                    }

                    function lockRow() {
                        var tables = document.getElementsByTagName("table");
                        for (var m = 0; m < tables.length; m++) {
                            if (tables[m].className == "pickme") {
                                var tbodies = tables[m].getElementsByTagName("tbody");
                                for (var j = 0; j < tbodies.length; j++) {
                                    var rows = tbodies[j].getElementsByTagName("tr");
                                    for (var i = 0; i < rows.length; i++) {
                                        rows[i].oldClassName = rows[i].className;
                                        rows[i].onclick = function() {
                                            if (this.className.indexOf("checked") != -1) {
                                                this.className = this.oldClassName;
                                            } else {
                                                removeSelectedStateFromOtherRows();
                                                addClass(this, "checked");
                                            }
                                            selectRowRadio(this);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    addLoadEvent(stripeTables);
                    addLoadEvent(highlightRows);
                    addLoadEvent(lockRow);

                    function lockRowUsingRadio() {
                        var tables = document.getElementsByTagName("table");
                        for (var m = 0; m < tables.length; m++) {
                            if (tables[m].className == "pickme") {
                                var tbodies = tables[m].getElementsByTagName("tbody");
                                for (var j = 0; j < tbodies.length; j++) {
                                    var radios = tbodies[j].getElementsByTagName("input");
                                    for (var i = 0; i < radios.length; i++) {
                                        radios[i].onclick = function(evt) {
                                            if (this.parentNode.parentNode.className.indexOf("checked") != -1) {
                                                this.parentNode.parentNode.className = this.parentNode.parentNode.oldClassName;
                                            } else {
                                                removeSelectedStateFromOtherRows();
                                                addClass(this.parentNode.parentNode, "checked");
                                            }
                                            if (window.event && !window.event.cancelBubble) {
                                                window.event.cancelBubble = "true";
                                            } else {
                                                evt.stopPropagation();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    addLoadEvent(lockRowUsingRadio);
                </script>

                <script>
                    function cek(id, tot, a, b, stat, ax, bx, inv) {
                        if (inv == 092) {
                            //alert(ax);
                            if (document.getElementById("cb" + id).checked == true) {
                                if (stat == 1) {
                                    var no = parseInt(document.getElementById('nilai').value) + 1;
                                    var no2 = document.getElementById('nilai2').value;
                                } else if (stat == 2) {
                                    var no = document.getElementById('nilai').value;
                                    var no2 = parseInt(document.getElementById('nilai2').value) + 1;
                                }
                            } else if (document.getElementById("cb" + id).checked == false) {
                                if (stat == 1) {
                                    //for (q = id; q < tot; q++){
                                    var no = parseInt(document.getElementById('nilai').value) - 1;
                                    var no2 = document.getElementById('nilai2').value;
                                    //}
                                } else if (stat == 2) {
                                    var no = document.getElementById('nilai').value;
                                    var no2 = parseInt(document.getElementById('nilai2').value) - 1;
                                }
                            }
                            document.getElementById("nilai").value = no;
                            document.getElementById("nilai2").value = no2;
                            if (a > 0) {
                                if (stat == 1) {
                                    //alert('Oke');
                                    //if (document.getElementById("cb" + id).checked == true) {
                                    if (document.getElementById("cb" + id).checked == false) {
                                        if (bx < 1) {
                                            document.getElementById("cb" + id).checked = false;
                                        } else {
                                            alert('Silakan uncheck yang belum melebihi due date');
                                            document.getElementById("cb" + id).checked = true;
                                            var no = parseInt(document.getElementById('nilai').value) + 1;
                                            document.getElementById("nilai").value = no;
                                        }
                                    }
                                    //}
                                } else if (stat == 2) {
                                    if (ax == a) {

                                    } else {
                                        alert('Silakan pilih dulu tagihan yang melebihi due date');
                                        document.getElementById("cb" + id).checked = false;
                                        var no2 = parseInt(document.getElementById('nilai2').value) - 1;
                                        document.getElementById("nilai2").value = no2;
                                    }
                                }
                            }
                        }
                    }
                    function cekNilai() {
                        var pay = document.getElementById('payamt1[]').value;
                        if (pay == 0 || pay == '') {
                            alert("Maaf, Nilai tidak boleh kosong");
                            //document.formInv.payamt1.focus();
                            return false;
                        } else {
                            document.forms[0].submit();
                            return true;
                        }
                    }

                </script>
                <script>
                    function popup_confirm(frm) {
                        var inv = document.getElementsByName("inv[]");
                        var potcek = document.getElementsByName("potcek[]");
                        var amt_inv = document.getElementsByName("payamtl[]");
                        var amt_pot = document.getElementsByName("potamountlc[]");
                        var lv_amount = 0;
                        var total_inv = 0;
                        var total_pot = 0;
                        var jml = 0;
                        var html = "";
                        var regex = "";
                        for (var i = 0; i < inv.length; i++) {
                            if (inv[i].checked) {
                                lv_amount = amt_inv[i].value;
                                console.log(lv_amount);
                                var loop = 1;
                                while (loop <= 5) {
                                    lv_amount = lv_amount.replace(".", "");
                                    loop++;
                                }
                                total_inv += parseInt(lv_amount);
                                console.log(total_inv);
                            }
                        }
                        for (var i = 0; i < potcek.length; i++) {
                            if (potcek[i].checked) {
                                lv_amount = amt_pot[i].value;
                                var loop = 1;
                                while (loop <= 5) {
                                    lv_amount = lv_amount.replace(".", "");
                                    loop++;
                                }
                                total_pot += parseInt(lv_amount);
                                console.log(total_pot);
                            }
                        }

                        jml = total_inv + total_pot;
                        html += "<table border=1><tr><td align=left width=250>Total Invoice</td><td align=right>" + new Intl.NumberFormat().format(total_inv) + "</td></tr>";
                        html += "<tr><td align=left width=250>Total Potongan</td><td align=right>" + new Intl.NumberFormat().format(total_pot) + "</td></tr>";
                        html += "<tr><td align=left width=250><strong>Jumlah Yang Harus Dibayar</strong></td><td align=right><strong>" + new Intl.NumberFormat().format(jml) + "</strong></td></strong></tr></table>";
                        document.getElementById("dialog_confirm").innerHTML = html;

                        $("#formInv").validate({
                            submitHandler: function(form) {
                                $("#dialog_confirm").dialog({
                                    buttons: {
                                        'Ok': function() {
                                            document.getElementById("formInv").submit();
                                            $(this).dialog("close");

                                        },
                                        'Cancel': function() {
                                            $(this).dialog("close");
                                        }
                                    },
                                    modal: true,
                                    width: 450,
                                    height: 200,
                                });
                            }
                        });
                    }

                    function finddistr(org) {
                        var strURL = "cari_distr.php?org=<?= $orgx; ?>";
                        popUp(strURL);
                    }
                </script>
                <style>
                    #dialog_confirm{
                        display:none;
                    }
                </style>
                <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
                <title>Entry Transfer Settlement</title>
            </head>

            <body>
                <div id="dialog_confirm" align="center"></div>
                <div align="center">
                    <table width="600" align="center" class="adminheading" border="0">
                        <tr>
                            <th class="kb2">Entry Transfer Settlement</th>
                        </tr></table></div>
                <?php // echo $orgx.'-'.$disidx.'-'.$tglxx;   ?>
                <div align="center">
                    <table width="600" align="center" class="adminlist">
                        <tr>
                            <th align="left" colspan="4"> &nbsp;Search Open Item</th>
                        </tr>
                    </table>
                </div>

                <form  id="form1" name="form1" method="post" action="<?= $currentPage; ?>">
                    <table width="600" border="0" align="center" class="adminform">
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td width="384">&nbsp;</td>
                        </tr>
                        <tr>
                            <td width="175"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Distributor </strong></td>
                            <td width="12"><strong>:</strong></td>
                            <td colspan="2">
                                <div id="distrdiv">
                                    <input name="dist" id="distr" type="text" class="" value="<?php echo $soldto; ?>" size="15"/>&nbsp;&nbsp;
                                    <input name="distrnm" id="nama_distr" type="text" class="" value="<?php echo $distr_nm ?>" size="40" readonly/>
                                    <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td width="175"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Company </strong></td>
                            <td width="12"><strong>:</strong></td>
                            <td colspan="2"><input name="comp" type="text" class="" value="<?php echo $org; ?>" size="15" readonly/>&nbsp;&nbsp;
                                <input name="compnm" type="text" class="" value="<?php echo $org1; ?>" size="40" readonly/>
                            </td>
                        </tr>
                        <?php
                        if (isset($_POST['oke'])) {
                            ?>
                            <tr>
                                <td width="189" class="stylern"><strong>&nbsp;&nbsp;Receipt Number </strong></td>
                                <td width="11" class="stylern"><strong>:</strong></td>
                                <td colspan="2" class="stylern">
                                    <input name="rna" type="text" class="stylern" value="<?php echo $rnstat; ?>" size="30" readonly/>
                                </td>
                            </tr>
                            <tr>
                                <td width="189" class="stylern"><strong>&nbsp;&nbsp;Total Payment </strong></td>
                                <td width="11" class="stylern"><strong>:</strong></td>
                                <td colspan="2" class="stylern">
                                    <?php $totbyr1 = number_format($totbyr, 2, ',', '.'); ?>
                                    <input name="curr" type="text" class="stylern" value="<?php echo $curr; ?>" size="3" readonly/>
                                    <input name="byr" type="text" class="stylern" value="<?php echo $totbyr1; ?>" size="22" readonly/>
                                </td>
                            </tr>
                        <?php } else { ?>
                            <tr>
                                <td><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;date of transfer until </strong></td>
                                <td><strong>:</strong></td>
                                <td><input name="tanggal_selesai" type="text" class="inputlabel" id="tanggal_selesai" value="<?= $tgls ? $tgls : date("d-m-Y"); ?>" onClick="return showCalendar('tanggal_selesai');"/>
                        <!--<input name="btn_selesai" type="button" class="button" onclick="return showCalendar('Tanggal Selesai');" value="..." />--></td>
                            </tr>
                            <!-- <tr>
                                <td><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create RN Date </strong></td>
                                <td><strong>:</strong></td>
                                <td><input name="create_rn_date" type="text" class="inputlabel" id="create_rn_date" size=12 value="<?= $tgl_rn ?>" onClick="return showCalendar('create_rn_date');"/></td>
                            </tr> -->
                            <tr>
                                <td width="189"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Payment Currency </strong></td>
                                <td width="11"><strong>:</strong></td>
                                <td colspan="2">
                                    <select name="curr" id="curr" >
                                        <?php
                                        if ($_SESSION['user_org'] == '3000') {
                                            echo"<option value='IDR'>IDR</option>";
                                        } else {
                                            ?>
                                            <option value="">---Pilih---</option>
                                            <?php
                                            $fungsi->cms_currency($curr);
                                        }
                                        ?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <?php
                                if ($_POST['Find'] == 'Find') {
                                    if ($_POST['kmk'] == '1') {
                                        $ceked = 'checked';
                                    }
                                    $dis = 'disabled';
                                }
                                ?>
                                <td width="189">&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="kmk" value="1" <?= $ceked; ?> <?= $dis; ?> /> <strong> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Via KMK</strong></td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td rowspan="2">
                                    <input name="Find" type="submit" class="button" value="Find" />
                                </td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        <?php } ?>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </table>
                </form>
                <?php
                if ($total > 0) {
//echo "jml=".$jumlah."<br>";
                    if (isset($jumlah)) {
                        $tot = $jumlah;
                    } else {
                        $tot = $total;
                    }
                    ?>
                    <p></p>
                    <div align="center">
                        <?php
                        if (isset($_POST['Find'])) {
                            if ($orgx != "2000" && $orgx != "7000") {
                                echo"<table border='0' align='center' width='900'>
                                    <tr>
                                        <td>Nb : For while KN can not be selected because the system can not accommodate</td>
                                    </tr>
                            </table>";
                            }
                        }
                        if ($_SESSION['user_org'] == '3000') {
                            if (isset($_POST['oke'])) {
                                echo"<table border='0' align='center' width='900'>
							<tr>
								<td align='right'><a href='cetakRN.php?rn=" . $rnstat . "&kdDist=" . $soldto . "&nmDist=" . $distr_nm . "&amount=" . $totbyr1 . "&curr=" . $curr . "' target='_blank'><img src='../images/fileprint.gif' border='0'></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
							</tr>
						</table>";
                            }
                        }
                        ?>
                        <form  name="formInv" id='formInv' method="post" action="<?= $currentPage; ?>">
                            <table width="1100" align="center" class="adminlist">
                                <tr>
                                    <th align="left" colspan="4"> <span class="style5">&nbsp;Sold To Debt List</span> </th>
                                </tr>
                            </table>
                    </div>
                    <div align="center">
                        <table id="test1" width="1100" align="center">
                            <thead>
                                <tr class="quote">
                                    <?php if (!isset($_POST['oke'])) { ?>
                                        <td><div align="center"><?php if ($_SESSION['user_org'] != '3000') { ?>
                                                    <input type="checkbox" name="toggle" value="PHP" onclick="checkAll(<?= $total ?>);"><?php } ?> <strong>Cek</strong></div></td>
                                    <?php } ?>
                                    <td align="center"><strong>Company Code</strong></td>
                                    <td align="center"><strong>No Invoice</strong></td>
									<td align="center"><strong>Reference Invoice</strong></td>
                                    <?php
                                    if ($_SESSION['user_org'] == '3000') {
                                        echo "<td align='center'><strong>Invoice Type</strong></td>";
                                    }
                                    ?>
                                    <td align="center"><strong>Invoice Date</strong></td>
                                    <td align="center"><strong>Due Date</strong></td>
                                    <td align="center"><strong>Gross Amount</strong></td>
                                    <td align="center"><strong>Open Amount</strong></td>
                                    <?php
                                    if ($_SESSION['user_org'] != '3000') {
                                        echo"<td align='center'><strong>Pay Amount</strong></td>
					<td align='center'><strong>Saldo Out Standing</strong></td>";
                                    }
                                    ?>
                                    <td align="center"><strong>Curr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></td>
                                    <?php
                                    if ($_SESSION['user_org'] == '3000') {
                                        echo"<td align='center'><strong>SO</strong></td>
					<td align='center'><strong>Tanggal SO</strong></td>
					<td align='center'><strong>Action</strong></td>";
                                    }
                                    ?>
                                </tr>
                            </thead>
                            <?php if (isset($_POST['oke'])) { ?>
                                <tbody>
                                <?php } else { ?>
                                    <tbody>
                                        <input type='hidden' name='nilai' id='nilai' value='0'>
                                            <input type='hidden' name='nilai2' id='nilai2' value='0'>
                                                <?php
                                            }
                                            for ($i = 0; $i < $tot; $i++) {
                                                //$cek="";
                                                //echo "Jumlah=".$inv[$i];
                                                if (isset($jumlah)) {
                                                    $j = $inv[$i];
                                                } else {
                                                    $j = $i;
                                                }


                                                if (($i % 2) == 0) {
                                                    echo "<tr class='odd0'>";
                                                } else {
                                                    echo "<tr class='odd1'>";
                                                }
                                                /* for($x=0;$x<$jumlah;$x++)
                                                  {

                                                  if ($inv[$x]==$i){$cek="checked";}
                                                  else $cek="";
                                                  echo "Check=".$cek;
                                                  } */
                                                //$b=$i+1;
                                                //$noinv=rray(a$fungsi->cms_select_invoice('3000','400','20100928'));
                                                ?>
                                                <tr>
                                                    <?php
                                                    if (!isset($_POST['oke'])) {
                                                        $tglNow = date("Ymd");
                                                        if ($tglNow >= $arrout[20][$i]) {
                                                            $stat = 1;
                                                        } else if ($tglNow <= $arrout[20][$i]) {
                                                            $stat = 2;
                                                        }
                                                        ?>

                                                        <td align="center">
                                                            <?php
                                                            //echo $arrout[19][$j];

                                                            if ($arrout[13][$j] < "0" || $arrout[19][$j] == '0001') {

                                                                $distabledcoy = "disabled";
                                                                #$distabledcoy = "";
                                                            } else {
                                                                $distabledcoy = "";
                                                            }
                                                            ?>
                                                            <input type="checkbox" id="cb<?= $i ?>" name="inv[]" value="<?= $i; ?>" <?=
                                                            $cek;
                                                            if ($_SESSION['user_org'] == '3000') {
                                                                ?> onclick='cek(<?= $i; ?>,<?= $tot; ?>,<?= $aa; ?>,<?= $bb; ?>,<?= $stat; ?>, document.getElementById("nilai").value, document.getElementById("nilai2").value,<?= substr($arrout[5][$j], 0, 3); ?>);' <?php } echo $distabledcoy; ?>/>
                                                        </td>
                                                    <?php } ?>
                                                    <td align="center"><?php echo $arrout[0][$j]; ?></td>
                                                    <td align="center"><?php echo $arrout[5][$j]; ?>
                                                        <input name="cpcd[]" type="hidden" value="<?= $arrout[0][$j] ?>" />
                                                        <input name="cpcdx" type="hidden" value="<?= $arrout[0][$j] ?>" />
                                                        <input name="dist" type="hidden" value="<?= $soldto ?>" />
                                                        <input name="kddist[]" type="hidden" value="<?= $arrout[1][$j] ?>" />
                                                        <input name="year[]" type="hidden" value="<?= $arrout[2][$j] ?>" />
                                                        <input name="noinva[]" type="hidden" value="<?= $arrout[3][$j] ?>" />
                                                    </td>

													<td align="center"><?php echo $arrout[32][$j]; ?></td>
                                                    <?php
                                                    if ($_SESSION['user_org'] == '3000') {
                                                        if (substr($arrout[5][$j], 0, 3) == '092') {
                                                            $jnsinv = "Semen";
                                                        } else {
                                                            $jnsinv = "Non Semen";
                                                        }
                                                        echo "<td align='center'>" . $jnsinv . "</td>";
                                                    }
                                                    ?>
                                                    <td align="center"><?php
                                                        $yinv = substr($arrout[7][$j], 0, 4);
                                                        $minv = substr($arrout[7][$j], 4, 2);
                                                        $dinv = substr($arrout[7][$j], 6, 2);
                                                        echo $dinv . '-' . $minv . '-' . $yinv;
                                                        ?>
                                                        <input name="buz[]" type="hidden" value="<?= $arrout[4][$j] ?>" />
                                                        <input name="noinv[]" type="hidden" value="<?= $arrout[5][$j] ?>" />
                                                        <input name="bl[]" type="hidden" value="<?= $arrout[6][$j] ?>" />

                                                    </td>
                                                    <td align="center"><?php
                                                        if ($_SESSION['user_org'] == '3000') {
                                                            if (isset($_POST['oke'])) {
                                                                $ydue = substr($arrout[19][$j], 0, 4);
                                                                $mdue = substr($arrout[19][$j], 4, 2);
                                                                $ddue = substr($arrout[19][$j], 6, 2);
                                                            } else {
                                                                $ydue = substr($arrout[20][$j], 0, 4);
                                                                $mdue = substr($arrout[20][$j], 4, 2);
                                                                $ddue = substr($arrout[20][$j], 6, 2);
                                                            }
                                                        } else {
                                                            $ydue = substr($arrout[8][$j], 0, 4);
                                                            $mdue = substr($arrout[8][$j], 4, 2);
                                                            $ddue = substr($arrout[8][$j], 6, 2);
                                                        }

                                                        echo $ddue . '-' . $mdue . '-' . $ydue;
                                                        ?>
                                                        <input name="invdt[]" type="hidden" value="<?= $arrout[7][$j] ?>" />
                                                        <input name="duedt[]" type="hidden" value="<?= $arrout[8][$j] ?>" />
                                                        <input name="duedt2[]" type="hidden" value="<?= $arrout[20][$j] ?>" />
                                                        <input name="curren[]" type="hidden" value="<?= $arrout[9][$j] ?>" /></td>
                                                    </td>
                                                    <td align="right"><?php
                                                        if (isset($_POST['oke'])) {
                                                            $grsamnt = number_format($arrout[11][$j] * 100, 2, ',', '.');
                                                            echo $grsamnt;
                                                        } else {
                                                            $grsamnt = number_format($arrout[11][$j], 2, ',', '.');
                                                            echo $grsamnt;
                                                        }
                                                        ?>
                                                        <input name="grossamtd[]" type="hidden" value="<?= $arrout[10][$j] ?>" />
                                                        <input name="grossamtl[]" type="hidden" value="<?= $arrout[11][$j] ?>" />
                                                        <input name="openamtd[]" type="hidden" value="<?= $arrout[12][$j] ?>" />

                                                    </td>
                                                    <td align="right"><?php
                                                        if (isset($_POST['oke'])) {
                                                            $openamt = number_format($arrout[13][$j] * 100, 2, ',', '.');
                                                            echo $openamt;
                                                        } else {
                                                            $openamt = number_format($arrout[13][$j], 2, ',', '.');
                                                            echo $openamt;
                                                        }
                                                        ?>
                                                        <input name="openamtl[]" type="hidden" value="<?= $arrout[13][$j] ?>" />
                                                        <input name="payamtd[]" type="hidden" value="<?= $arrout[14][$j] ?>" />
                                                    </td>
                                                    <?php
                                                    if ($_SESSION['user_org'] != '3000') {
                                                        ?>
                                                        <td align="right">
                                                            <?php
                                                            if (isset($_POST['oke'])) {
                                                                $payamt = number_format($arrout[15][$j] * 100, 2, ',', '.');
                                                                echo $payamt;
                                                            } elseif ($_POST['kmk'] == '1') {
                                                                $payamt = number_format($arrout[13][$j], 2, ',', '.');
                                                                echo $payamt;
                                                                ?>
                                                                <input name="payamtl[]" type="hidden" value="<?= $arrout[13][$j]; ?>" />
                                                            <?php } else { ?>
                                                                <input name="payamtl[]" id="payamt1[]" type="text" value="<?= number_format($arrout[13][$j], 2, ',', '.'); ?>" size="15" style="text-align:right " /> <?php } ?></td>
                                                        <td align="right"><?php
                                                            if (isset($_POST['oke'])) {
                                                                echo number_format($arrout[17][$j] * 100, 2, ',', '.');
                                                            } else {
                                                                echo number_format($arrout[17][$j], 2, ',', '.');
                                                            }
                                                            ?>
                                                            <input name="saldamtd[]" type="hidden" value="<?= $arrout[16][$j] ?>" />
                                                            <input name="saldamtl[]" type="hidden" value="<?= $arrout[17][$j] ?>" />
                                                            <input name="zterm[]" type="hidden" value="<?= $arrout[19][$j] ?>" />
                                                        </td>
                                                    <?php } ?>
                                                    <td align="center"><?php echo $arrout[9][$j]; ?>&nbsp;&nbsp;&nbsp&nbsp;&nbsp;&nbsp;
                                                        <?php
                                                        if ($_SESSION['user_org'] == '3000') {
                                                            if ($_POST['kmk'] == '1') {
                                                                #$payamt=number_format($arrout[13][$j], 2, ',', '.');
                                                                #echo $payamt;
                                                                echo "<input name='payamtl[]' type='hidden' value='" . $arrout[13][$j] . "'/>";
                                                            } else {
                                                                echo"<input name='payamtl[]' type='hidden' value='" . number_format($arrout[13][$j], 2, ',', '.') . "' size='15'/>";
                                                            }
                                                        }
                                                        ?>
                                                    </td>
                                                    <?php
                                                    if ($_SESSION['user_org'] == '3000') {
                                                        echo "<td align='center'>" . $arrout[21][$j] . "</td>
						<td align='center'>" . substr($arrout[22][$j], 6, 2) . "-" . substr($arrout[22][$j], 4, 2) . "-" . substr($arrout[22][$j], 0, 4) . "</td>
						<td align='center'><a href=\"javascript:popUp('detailDO.php?bukrs=" . $org . "&kunnr=" . $soldto . "&tgl=" . date("d-m-Y") . "&belnr=" . $arrout[5][$j] . "')\">Detail DO</a></td>";
                                                    }
                                                    ?>
                                                </tr>
                                                <?php
                                            }
                                            #echo $aa."<=>".$bb;
                                            ?>
                                            </tbody>
                                            </table>
                                            </div>
                                            <?php if ($org == '2000' || $org == '7000' || $org == '7900') { ?>
                                                <div align ='center'>
                                                    <table width="1100" align="center" class="adminlist">
                                                        <tr>
                                                            <th align="left" colspan="4"> <span class="style5">&nbsp;Discount & Credit Nota</span> </th>
                                                        </tr>
                                                    </table>
                                                    <table width="1100" align="center" class="adminlist" id="tab_pot">
                                                        <thead>
                                                            <tr class="quote">
                                                                <?php
                                                                $total = count($jurnal_out[0]);
                                                                ?>
                                                                <td align='center'><strong>Check</strong></td>
                                                                <td align='center'><strong>Company Code</strong></td>
                                                                <td align='center'><strong>Document Number</strong></td>
																<td align='center'><strong>Refrence Doc Number</strong></td>
                                                                <td align='center'><strong>Year</strong></td>
                                                                <td align='center'><strong>Posting Date</strong></td>
                                                                <td align='center'><strong>Document Date</strong></td>
                                                                <td align='center'><strong>Referrence RN</strong></td>
                                                                <td align='center'><strong>Referrence Number</strong></td>
                                                                <td align='center'><strong>Amount</strong></td>
                                                                <td align='center'><strong>Amount in LC</strong></td>
                                                                <td align='center'><strong>Currency</strong></td>
                                                            </tr>
                                                            <?php
                                                            $total = count($jurnal_out[0]);
                                                            for ($i = 0; $i < $total; $i++) {
                                                                echo "<tr>";
                                                                echo "<td align = 'center'>";
                                                                ?>
                                                                <input type="checkbox"
                                                                       name="potcek[]"
                                                                       value='<?= $i ?>'/>
                                                                       <?php
                                                                       echo "</td>";
                                                                       echo "<td align = 'center'>" . $jurnal_out[0][$i];
                                                                       ?>
                                                                <input type="hidden" name="potcom[]" value="<?= $jurnal_out[0][$i] ?>"/>

                                                                <input type="hidden" name="potcust[]" value="<?= $jurnal_out[1][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'center'>" . $jurnal_out[2][$i];
                                                                ?>
                                                                <input type="hidden" name="potdoc[]" value="<?= $jurnal_out[2][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
																echo "<td align = 'center'>" . $jurnal_out[16][$i];
																echo "</td>";
                                                                echo "<td align = 'center'>" . $jurnal_out[3][$i];
                                                                ?>
                                                                <input type="hidden" name="potyear[]" value="<?= $jurnal_out[3][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'center'>" . substr($jurnal_out[4][$i], 6, 2) . '-' . substr($jurnal_out[4][$i], 4, 2) . '-' . substr($jurnal_out[4][$i], 0, 4);
                                                                ?>
                                                                <input type="hidden" name="potposdate[]" value="<?= $jurnal_out[4][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'center'>" . substr($jurnal_out[5][$i], 6, 2) . '-' . substr($jurnal_out[5][$i], 4, 2) . '-' . substr($jurnal_out[5][$i], 0, 4);
                                                                ?>
                                                                <input type="hidden" name="potdocdate[]" value="<?= $jurnal_out[5][$i] ?>"/>
                                                                  <?php
                                                                echo "</td>";
                                                                echo "<td align = 'right'>" .substr($jurnal_out[17][$i],-10,10);
                                                                ?>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'center'>" . $jurnal_out[16][$i];
                                                                ?>
                                                                <input type="hidden" name="potreff[]" value="<?= $jurnal_out[16][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'right'>" . $jurnal_out[7][$i];
                                                                ?>
                                                                <input type="hidden" name="potamount[]" value="<?= $jurnal_out[7][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'right'>" . $jurnal_out[8][$i];
                                                                ?>
                                                                <input type="hidden" name="potamountlc[]" value="<?= $jurnal_out[8][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "<td align = 'center'>" . $jurnal_out[9][$i];
                                                                ?>
                                                                <input type="hidden" name="potcurr[]" value="<?= $jurnal_out[9][$i] ?>"/>
                                                                <input type="hidden" name="potgl[]" value="<?= $jurnal_out[10][$i] ?>"/>
                                                                <input type="hidden" name="potflag[]" value="<?= $jurnal_out[11][$i] ?>"/>
                                                                <input type="hidden" name="potwrbtr[]" value="<?= $jurnal_out[12][$i] ?>"/>
                                                                <input type="hidden" name="potdmbtr[]" value="<?= $jurnal_out[13][$i] ?>"/>
                                                                <input type="hidden" name="potbuzei[]" value="<?= $jurnal_out[14][$i] ?>"/>
                                                                <input type="hidden" name="potblart[]" value="<?= $jurnal_out[15][$i] ?>"/>
                                                                <?php
                                                                echo "</td>";
                                                                echo "</tr>";
                                                            }
                                                            ?>
                                                        </thead>
                                                    </table>
                                                </div>
                                            <?php }
                                            ?>
                                            <div align="center">
                                                <br /><br />

                                                <?php if (isset($_POST['oke'])) { ?>
                                                    <input type="button" name="Submit2" value="Previous" onClick="javascript
                                                                    :history.go( - 1);" class="button" />
                                                       <?php } else { ?>
                                                    <input type="hidden" value="Create RN" name="oke" class="button">



													 <?php //if ($org == '2000' || $org == '7000' || $org == '7KSO') { ?>
                                                        <input type="submit" value="Create RN" name="oke" class="button" onclick="popup_confirm(this.form)">
														<!-- <th align="left" colspan="4"> <span class="style5">&nbsp;Sorry, Create RN for Company 7000 & 2000 under maintenance</span> </th>-->
														<?php //} else { ?>
														<!-- <input type="submit" value="Create RN" name="oke" class="button" onclick="popup_confirm(this.form)">-->
														 <?php// } ?>



                                                        <?php } ?>
                                                        <input id="total" name="total" type="hidden" value="<?= $total ?>" />
                                                        <input id="tglxx" name="tglxx" type="hidden" value="<?= $tgls ?>" />
                                                        <input id="curr" name="curr" type="hidden" value="<?= $curr ?>" />
                                                        <input id="kmkX" name="kmkX" type="hidden" value="<?= $_POST['kmk'] ?>" />
                                                        </div>
                                                        </form>
                                                        </div>


                                                        <?php
                                                    }
//else echo " Maaf.. <br> Tidak Ada Data Yang Di Temukan..";


                                                    /* elseif((isset($_POST['Find'])) && (($_POST['tanggal_mulai']!='') or ($_POST['tanggal_selesai']!='') or ($_POST['curr']!='')))
                                                      {
                                                      $pesan="Maaf.. Data yang anda isikan tidak lengkap";
                                                      } */
                                                    ?>

                                                    <div align="center">
                                                        <?php
                                                        if ($msg_type == "S") {
                                                            echo "<span style='font-size:14px; color:green'>";
                                                        } elseif ($msg_type == "E") {
                                                            echo "<span style='font-size:14px; color:red'>";
                                                        } else {
                                                            echo "<span style='font-size:14px; color:blue'>";
                                                        }
                                                        echo $pesan;
                                                        echo "</span>";
                                                        ?>
                                                    </div>
                                                    <p>&nbsp;</p>
                                                    </p>
                                                    <?php include ('../include/ekor.php'); ?>

                                                    </body>
                                                    </html>
