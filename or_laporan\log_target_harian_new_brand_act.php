<?php

session_start();
//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';



if(isset ($aksi)){
//    if($aksi == 'show'){
//        displayData($conn,$sort,$order);
//        
//    }
    switch($aksi) { 
        case 'show' :
    {         
       // $where = '';
        if($DIST==''){
            $dst  = "";
        }else{
           $dist =  $DIST;
		$panjang=strlen(strval($DIST));
		if($panjang==1) $dst='000000000'.$dist;
		if($panjang==2) $dst='00000000'.$dist;
		if($panjang==3) $dst='0000000'.$dist;
		if($panjang==4) $dst='000000'.$dist;
		if($panjang==5) $dst='00000'.$dist;
		if($panjang==6) $dst='0000'.$dist;
		if($panjang==7) $dst='000'.$dist;
		if($panjang==8) $dst='00'.$dist;
		if($panjang==9) $dst='0'.$dist;
		if($panjang==10)$dst=$dist;
          //  $where  .= " AND tn.DISTRIBUTOR = '{$dst}'";  
        }
  
       if($BLN == '' ){
          $BLN='';
		// $where  .= ""; 
	   }else{
           $tahun=date("Y"); 
		   $periode=$tahun.$BLN;
		 // $where  .= " AND tn.DISTRIK = '{$distrik}'" ;
	   }
	    //$where  .= " AND tn.ORG = '{$ORG}' AND tn.TIPE = '{$TIPE}'" ;
	//	$tanggal = date('Ym');

        if($BLN == '' OR $dst == '' ){
			
            echo json_encode('tidak ada data yang dicari');
			
        }else{
//             $sql= "
//                SELECT * FROM ZSD_TARGET_HARIAN_NEW 
// WHERE AKTIF_MARK ='0' AND TO_CHAR(TANGGAL_TARGET,'YYYYMM')= '{$tanggal}' {$where} 
//             ";   
//             $sql= "
//              SELECT tn.*,
// TO_CHAR(tn.LASTUPDATE_DATE, 'DD-MON-YYYY HH24:MI:SS') AS FORMATTED_DATE,
//  (SELECT NAMA FROM TB_USER_BOOKING tb WHERE tb.ID = tn.CREATE_BY) as NAMA_PEMBUAT,(SELECT NAMA FROM TB_USER_BOOKING tb WHERE tb.ID = tn.LASTUPDATE_BY) as NAMA_UPDATE FROM ZSD_TARGET_HARIAN_NEW tn WHERE tn.AKTIF_MARK ='0' AND TO_CHAR(tn.TANGGAL_TARGET,'YYYYMM')= '202402' AND tn.DISTRIBUTOR = '0000000138' AND tn.ORG = '7900' AND tn.TIPE = '121-301' 
//             ";   
            $sql= "
              SELECT 
    Z.*, 
    TO_CHAR(Z.TGL, 'YYYY-MM-DD HH24:MI:SS') AS TANGGAL_KIRIM 
  FROM ZMD_LOG_MKI Z 
  WHERE SEND_PARAM = '{$dst}' 
    AND USER_SAVE = '{$periode}'
            ";   
                     //  echo $sql;
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            while($row=oci_fetch_array($query)){            
                array_push($result, $row);
            }   
           $periodeSekarang = date('Ym'); // Misal input
            $tahun = (int) substr($periodeSekarang, 0, 4);
            $bulan = (int) substr($periodeSekarang, 4, 2); 
            $periode6Bulan = array();

                for ($i = 6; $i >= 1; $i--) {
                  
                    $baseDate = mktime(0, 0, 0, $bulan - $i, 1, $tahun);
                    $periode6Bulan[] = date('Ym', $baseDate);
                }

              
                $periodeTerpilih = array_slice($periode6Bulan, 0, 3);

                $periodeString = "'" . implode("','", $periodeTerpilih) . "'";

                $sqldelete = "DELETE FROM ZMD_LOG_MKI WHERE USER_SAVE IN ($periodeString)";
                $querydelete = oci_parse($conn, $sqldelete);
               // echo $sqldelete;
                oci_execute($querydelete);
                  echo json_encode($result);
           // var_dump($result);
             //var_dump($sql);
            }
     }
     break;      
    }
    
}

?>
