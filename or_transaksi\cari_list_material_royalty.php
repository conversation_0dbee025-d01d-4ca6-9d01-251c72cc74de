<?
session_start();
 $url = basename($_SERVER['SCRIPT_NAME']);

if ($url != 'login.php') {
    // Check if the user is logged in
    if (empty($_SESSION['user_id'])) {
		// var_dump ($_SESSION);
		// die;
        echo '<script type="text/javascript">';
        echo 'window.location.href = "https://dev-app.sig.id/dev/sd/sdonline/login.php";';
        echo '</script>';
        exit;
    }
}

include ('../include/or_fungsi.php');
require_once ('../pgr_sanitizer.php');
require_once ('../auth_validation.php');
validation_(basename($_SERVER['SCRIPT_NAME']), $_SESSION['user_id']);
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);


$kode = '';
$nama = '';


$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$data_set = array();
$currentPage="cari_produksi.php";
$nourut = isset($_GET['nourut']) ? $_GET['nourut'] : '';
$plant = isset($_GET['plant']) ? $_GET['plant'] : '';
$unit = isset($_GET['unit']) ? $_GET['unit'] : '';
$distrik = isset($_GET['distrik']) ? $_GET['distrik'] : '';
$org = isset($_SESSION['user_org']) ? $_SESSION['user_org'] : '';
$brand = isset($_GET['brand']) ? $_GET['brand'] : '';
$incoterm = isset($_GET['incoterm']) ? $_GET['incoterm'] : '';
$so_type = isset($_GET['so_type']) ? $_GET['so_type'] : '';
if ($org=="2000" and $plant=="") {
	$plant1="2*";
} elseif ($org=="4000" and $plant=="") {
	$plant1="4*";
} elseif ($org=="3000" and $plant=="") {
	$plant1="3*";
} elseif ($org=="5000" and $plant=="") {
	$plant1="5*";
} elseif ($org=="6000" and $plant=="") {
	$plant1="6*";
} elseif ($org=="7000" and $plant=="") {
	$plant1="7*";
}elseif ($org=="7900" and $plant=="") {
	$plant1="79*";
}else { $plant1=isset($_GET['plant']) ? $_GET['plant'] : ''; }

$mp_coics=$fungsi->getComin($conn,$org);

// var_dump($mp_coics);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $org;
}

if(substr($plant,0,2)=='79'){
$orgcek = substr($plant,0,2)."00";
}else{
$orgcek = substr($plant,0,1)."000";
}

$sqlcek = "SELECT KODE_MATERIAL FROM OR_MAP_DISTRIK_MAT WHERE ORG = :orgcek AND DISTRIK = :distrik AND PLANT = :plant AND DELETE_MARK = '0'";

$querycek = oci_parse($conn, $sqlcek);

oci_bind_by_name($querycek, ":orgcek", $orgcek);
oci_bind_by_name($querycek, ":distrik", $distrik);
oci_bind_by_name($querycek, ":plant", $plant);


oci_execute($querycek);

$kode_mat_blacklist = array();
while($datacek=oci_fetch_assoc($querycek)){
    array_push($kode_mat_blacklist, $datacek[KODE_MATERIAL]);
}

if(isset($_REQUEST['kode'])){
	$kode = isset($_REQUEST['kode']) ? $_REQUEST['kode'] : '';
	$nama = isset($_REQUEST['nama']) ? $_REQUEST['nama'] : '';

	$params = array();
	$sql = "SELECT scm.BRAND, scm.MATERIAL AS MATNR, sal.MAKTX, upper(sal.MEINS) AS MEINS
        FROM ZSD_TARGET_HEADER_SCM scm
        LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = scm.MATERIAL
        WHERE MATNR LIKE '121-301%' 
          AND scm.BRAND = :brand 
          AND scm.FLAG_DEL != 'Y' 
          AND scm.PERIODE = :periode";
	
	
	$params[':brand'] = $brand;
	$params[':periode'] = date('m-Y');


	if ($kode) {
		$sql .= " AND sal.MATNR LIKE :kode";
		$params[':kode'] = "%$kode%";
	}
	if ($nama) {
		$sql .= " AND UPPER(sal.MAKTX) LIKE :nama";
		$params[':nama'] = "%" . strtoupper($nama) . "%";
	}
	if ($distrik) {
		$sql .= " AND scm.DISTRIK = :distrik";
		$params[':distrik'] = $distrik;
	}
	if ($incoterm) {
		$sql .= " AND scm.incoterm = :incoterm";
		$params[':incoterm'] = $incoterm;
	}
	if ($so_type) {
		$sql .= $so_type == 'ZPR' ? " AND scm.tipe_order = '1'" : " AND coalesce(scm.tipe_order, '0') != '1'";
	}
	if ($unit) {
		if ($unit == 'ZAK') {
			$sql .= " AND sal.MEINS IN ('$unit', 'BAG')";
		} else {
			$sql .= " AND sal.MEINS = $unit";
		}
	}
	$sql .= " GROUP BY scm.BRAND, scm.MATERIAL, sal.MAKTX, upper(sal.MEINS)";

	$query= oci_parse($conn, $sql);
	foreach ($params as $key => $val) {
		oci_bind_by_name($query, $key, $params[$key]);
	}

	oci_execute($query);
	while($datafunc=oci_fetch_assoc($query)){
            if(in_array($datafunc["MATNR"], $kode_mat_blacklist)){
                continue;
            } else{
		$matnr[]	= $datafunc["MATNR"];
		$nama_matnr[]	= $datafunc["MAKTX"];
		$uom[]		= $datafunc["MEINS"];
                                
        if(strtoupper($datafunc["GEWEI"])=='KG'){
             $qtymto=@(number_format($datafunc["NTGEW"]/1000,2));
        }else{
             $qtymto=number_format($datafunc["NTGEW"]);
        }
        $quantumTO[]    = $qtymto;
	}
        }


}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
<!-- Style css jquery table sorter -->
<link href="../Templates/css-sorter/jquery-sorter-style.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../Templates/css-sorter/jquery-latest.js"></script>
<script type="text/javascript" src="../Templates/css-sorter/jquery.js"></script>
<script type="text/javascript">
	$(document).ready(function() 
    { 
        $("#myTable").tablesorter({widthFixed: true, widgets: ['zebra']});
			//.tablesorter( {sortList: [[0,0], [1,0]]} ); 
    } 
	); 
</script>
<!-- end Style css jquery table sorter -->
<script>
<!--
function setForm() {
	var btn = document.getElementById("cekdata"); 
	var urut=<?=$nourut?>;
	if(btn.value != 0){
	var kenya=btn.value;
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var komponen_acc_id=document.getElementById(acc_id); 
	var acc_um='acc_um'+kenya;
	var komponen_acc_um=document.getElementById(acc_um); 
        var acc_kg='acc_to'+kenya;
	var komponen_acc_kg=document.getElementById(acc_kg); 
	opener.document.getElementById("produk"+urut).value = komponen_acc_id.value;
	opener.document.getElementById("nama_produk"+urut).value = komponen_acc_no.value;
	opener.document.getElementById("uom"+urut).value = komponen_acc_um.value;
        opener.document.getElementById("qto"+urut).value = komponen_acc_kg.value;
		opener.getPlant();
        self.close();
	}else
		{
			alert('Choose Product Data, please')
			return false;
		}
}
//-->
</script>
<script> 
function checkForother(obj) {  
	if (!document.layers) { 
	var kenya=obj.value;
	var btn = document.getElementById("cekdata"); 
	btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
} 

function checkForother_db(obj) {  
	var btn = document.getElementById("cekdata"); 
	var urut=<?=$nourut?>;
	setForm();

} 
</script> 

<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>

<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>List of Product</title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">List of Cement Product</th>
</tr></table></div>

<form  id="form1" name="form1" method="post" action="<? $currentPage;?>">
		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		  <td class="puso">Product Code</td>
		  <td class="puso">:</td>
		  <td><input name="kode" type="text" class="" value="<? echo $kode; ?>" size="40"/></td>
		  </tr>
		<tr>
		<td width="173" class="puso">Product Name</td>
		<td width="26" class="puso">:</td>
		<td width="585"><input name="nama" type="text" class="" value="<? echo $nama; ?>" size="40"/>
		&nbsp;&nbsp;</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
		<input name="Submit" type="submit" class="button" value="Show" />		</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<?
if(isset($_POST['kode'])){
$total=count($matnr);
	if($total>0){
?>
		<p></p>
		<div align="center">
			<table width="600" align="center" class="adminlist">
			<tr>
			<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel of Product Data </span> </th>
			</tr>
			</table>
		</div> 
		<div align="center">
			<form  name="formKaryawan">
				<span id="zebrax">
					<table id="myTable" width="600" align="center" class="pickme">
					<thead >
						<tr class="quote">
							<th width="40"><div align="center"><strong>&nbsp;&nbsp; Ceck.&nbsp;&nbsp;&nbsp;</strong></div></th>
							<th align="center"><strong>Product Code</strong></th>
							<th align="center"><strong>Product Name </strong></th>
							<th align="center"><strong>Unit</strong></th>
						</tr>
					</thead>
					<tbody >
				<?  for($i=0; $i<$total;$i++) {
						$b=$i+1;
						$acc_id="acc_id".$b;
						$acc_no="acc_no".$b;
						$acc_um="acc_um".$b;
                                                $acc_kg="acc_to".$b;
						?>
						<tr>
						<td align="center"><input name="radiokaryawan" type="radio" value="<?=$b?>" onChange="checkForother(this)" id="<?=$b?>" onDblClick="checkForother_db(this)"/>
			<input id="<?=$acc_id;?>" name="<?=$acc_id;?>" type="hidden" value="<?=$matnr[$i]?>" />
			<input id="<?=$acc_no;?>" name="<?=$acc_no;?>" type="hidden" value="<?=$nama_matnr[$i]?>" />
			<input id="<?=$acc_um;?>" name="<?=$acc_um;?>" type="hidden" value="<?=$uom[$i]?>" /></td>
                        <input id="<?=$acc_kg;?>" name="<?=$acc_kg;?>" type="hidden" value="<?=$quantumTO[$i]?>" /></td>                            
						<td align="center"><? echo $matnr[$i]; ?></td>
						<td align="left"><? echo $nama_matnr[$i]; ?></td>
						<td align="left"><? echo $uom[$i]; ?></td>
						</tr>
				<? } ?>
					</tbody>
					</table>
				</span>
			<input type="button" value="Oke" name="kartu" class="button" onClick="setForm()">
			<input id="cekdata" name="cekdata" type="hidden" value="0" />
			</form>
		</div>

	<?
	}else $komen = " Sorry.. <br> No Data Found..";	
	?>

<div align="center">
	<p>&nbsp;</p>
	<br />
	<?
	echo $komen;
	}
	?>
</div>
<p>&nbsp;</p>
</p>


</body>
</html>
