<?php
/* @var $this dView */

$this->controller->layout = '@/dee/layout-easyui';
$this->title = 'Entry Bayar VA';
?>

<div align="center">
    <table width="600" align="center" class="adminheading" border="0">
        <tr>
            <th class="kb2"><?= $this->title ?></th>
        </tr>
    </table>
</div>
<div class="easyui-panel" style="width:100%;" title="Search">
    <form id="form-find">
        <table>
            <tr>
                <th width="180">ORG</th>
                <td width="200">
                    <input id="org" class="easyui-combobox" style="width:100%;">
                </td>
            </tr>
            <tr>
                <th width="180">Vendor</th>
                <td width="200">
                    <input id="dist" class="easyui-textbox" style="width:100%;" required>
                </td>
            </tr>
            <tr>
                <td></td>
                <td><a id="find" class="easyui-linkbutton" iconCls="icon-search">Find</a></td>
            </tr>
        </table>
    </form>
</div>
<p></p>
<table id="dg" class="easyui-datagrid" title="List Invoice" style="width:100%;height:350px">
</table>

<?php $this->addJsBlock() ?>
<script>
    const NumFormater = new Intl.NumberFormat().format;
    const lokal = {};
    $('#org').combobox({
        data: <?= json_encode($orgs) ?>,
        valueField: 'id',
        textField: 'text',
        label: 'Org:',
        labelPosition: 'top'
    });
    $('#dg').datagrid({
        url: '@baseUrl/dee.php/cms_sgg/cEntryBayarVa/listInvoice',
        method: 'get',
        fitColumns: true,
        columns: [[
                {field: 'ck', title: '#', width: 60, checkbox: true},
                {field: 'BUKRS', title: 'Company', width: 100, sortable: false},
                {field: 'KUNNR', title: 'Vendor', width: 100, sortable: false},
                {field: 'NAME1', title: 'Vendor Name', width: 180},
                {field: 'RNNUM', title: 'No RN', width: 120, sortable: false},
                {field: 'PAYMD', title: 'Amount', width: 100, sortable: false,align:'right',
                    formatter(val) {
                        let res = val ? val.replaceAll('.', '').replaceAll('-', '') : '';
                        return NumFormater(res);
                    }
                },
                {field: 'PYCUR', title: 'Curr', width: 100, sortable: false},
                {field: 'CRDAT', title: 'Tanggal', width: 100, sortable: false,
                    formatter(val) {
                        if (val) {
                            let y = val.substring(0, 4);
                            let m = val.substring(4, 6);
                            let d = val.substring(6, 8);
                            return `${y}-${m}-${d}`;
                        }
                    }
                },
            ]],
        toolbar: [
            {
                text: 'Create VA',
                iconCls: 'icon-ok',
                handler: function () {
                    let total_inv = 0;
                    const post = {
                        invoices: [],
                    };
                    const rows = $('#dg').datagrid('getSelections');
                    if (rows.length) {
                        rows.forEach(function (row) {
                            let rawAmount = String(row.PAYMD || '').replace(/\./g, '').replace(/-/g, '');
                            let amount = parseFloat(rawAmount);
                            if (!isNaN(amount)) {
                                total_inv += amount;
                                row.PAYMD = rawAmount;
                                post.invoices.push(row);
                            }

                        });
                        let msg = `Jumlah yang harus dibayar ${NumFormater(total_inv)}.
                        Buat VA?`;
                        $.messager.confirm('Confirm', msg, function (r) {
                            if (r) {
                                let url = '<?= dee::createUrl('dee.php/cms_sgg/cEntryBayarVa/createVa') ?>';
                                $.post(url, post, function (res) {
                                    let va = res.success.map(v => v.virtual_account).join(', ')
                                    $.messager.show({
                                        title: 'Info',
                                        msg: `Sukses create VA dengan no ${va}`,
                                        timeout:0,
                                    });
                                    $('#dg').datagrid('reload');
                                }).fail(function (res) {
                                    let msg = `Gagal create VA:\n ${res.responseText}`;
                                    $.messager.alert('Error', msg);
                                });
                            }
                        });
                    } else {
                        $.messager.alert('Warning', 'Harus ada invoice yang dipilih.');
                    }
                }
            }
        ],
    });

    $('#find').linkbutton({
        onClick() {
            if ($('#form-find').form('validate')) {
                $('#dg').datagrid('load', {
                    org: $('#org').combobox('getValue'),
                    dist: $('#dist').textbox('getValue'),
                });
            }
        }
    });
</script>
<?php $this->endJsBlock() ?>