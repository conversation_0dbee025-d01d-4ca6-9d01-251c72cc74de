<?php
session_start();
require_once 'api/smbr.php';
require_once 'Excel/reader.php';
require_once ('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$org=$_SESSION['user_org'];
$username = $_SESSION['user_name'];

if($org != '1000'){
    echo 'Maaf, menu ini bukan untuk company yang saat ini anda pilih';
    exit;
};

function toNumber($value) {
    if (is_numeric($value)) {
        return $value + 0; // trik untuk cast ke number (bisa int/float)
    }

    if (empty($value)) {
        return 0;
    }

    return 0;
}

function showMessage($message)
{
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Result!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
        </div>
    </div>
<?php
}

if (isset($_POST['upload']) && $_FILES['excel_file']['error'] == 0) {
    $file_name = $_FILES['excel_file']['name'];
    $file_tmp = $_FILES['excel_file']['tmp_name'];

    $check = validate_excel($file_name, $file_tmp);
    if(!$check['status']){
        echo "<script>alert('" . $check['message'] ."');</script>";
        exit;
    }

    $data = new Spreadsheet_Excel_Reader();
    $data->setOutputEncoding('CP1251');
    $data->read($file_tmp);

    $sheet = $data->sheets[0];

    // Hitung jumlah baris
    $rows = count($sheet['cells']);
    if($rows <= 1){
        echo "Data upload kosong";
    }
    
    $msg = "";

    for ($i = 2; $i <= $rows; $i++) {
        $no_shipment = isset($sheet['cells'][$i][1]) ? $sheet['cells'][$i][1] : ''; // NO_SHIPMENT
        $qty_ktg_rusak = isset($sheet['cells'][$i][2]) ? $sheet['cells'][$i][2] : ''; // QTY_KTG_RUSAK
        $qty_semen_rusak = isset($sheet['cells'][$i][3]) ? $sheet['cells'][$i][3] : ''; // QTY_SEMEN_RUSAK
        $tanggal_datang = isset($sheet['cells'][$i][4]) ? $sheet['cells'][$i][4] : ''; // TANGGAL_DATANG
        $tanggal_bongkar = isset($sheet['cells'][$i][5]) ? $sheet['cells'][$i][5] : ''; // TANGGAL_BONGKAR
        $total_ktg_rusak = isset($sheet['cells'][$i][6]) ? $sheet['cells'][$i][6] : ''; // TOTAL_KTG_RUSAK
        $total_ktg_rezak = isset($sheet['cells'][$i][7]) ? $sheet['cells'][$i][7] : ''; // TOTAL_KTG_REZAK
        $total_klaim_ktg = isset($sheet['cells'][$i][8]) ? $sheet['cells'][$i][8] : ''; // TOTAL_KLAIM_KTG
        $total_semen_rusak = isset($sheet['cells'][$i][9]) ? $sheet['cells'][$i][9] : ''; // TOTAL_SEMEN_RUSAK
        $pdpks = isset($sheet['cells'][$i][10]) ? $sheet['cells'][$i][10] : ''; // PDPKS
        $keterangan_pod = isset($sheet['cells'][$i][11]) ? $sheet['cells'][$i][11] : ''; // KETERANGAN_POD
        $geofence_pod = isset($sheet['cells'][$i][12]) ? $sheet['cells'][$i][12] : ''; // GEOFENCE_POD

        $check_geofence = validate_geofence_pod($geofence_pod);
        if(!$check_geofence && !$error_msg){
            $error_msg .= "(X) => Gagal POD ($no_shipment): Invalid Geofence POD <br>";
            continue;
        }

        $tanggal_bongkar = date_create($tanggal_bongkar);
        if($tanggal_bongkar instanceof DateTime){
            $tanggal_bongkar = date_format($tanggal_bongkar, 'd-m-Y');
        }else{
            $error_msg .= "(X) => Gagal POD ($no_shipment): Invalid Tanggal Bongkar <br>";
            continue;
        }

        $tanggal_datang = date_create($tanggal_datang);
        if($tanggal_datang instanceof DateTime){
            $tanggal_datang = date_format($tanggal_datang, 'd-m-Y');
        }else{
            $error_msg .= "(X) => Gagal POD ($no_shipment): Invalid Tanggal Bongkar <br>";
            continue;
        }

        $sql = "SELECT COUNT(*) AS JML FROM EX_TRANS_HDR WHERE (NO_SHP_TRN = :no_shp OR NO_SHP_EX = :no_shp) AND STATUS2='OPEN'";
        $stmt = oci_parse($conn, $sql);
        oci_bind_by_name($stmt, ":no_shp", $no_shipment);
        oci_execute($stmt);
        $row = oci_fetch_assoc($stmt);
        if($row['JML'] == 0){
            $msg .= "(X) => Gagal POD, pastikan Shipment ($no_shipment) tersedia dan sudah ter-recalculate <br>";
            continue;
        }

        $total_klaim_semen = toNumber($total_semen_rusak) + toNumber($pdpks);
        $total_klaim_all = toNumber($total_klaim_semen) + toNumber($total_klaim_ktg);

        $field_names=array(
            'STATUS','LAST_UPDATE_DATE','LAST_UPDATED_BY','QTY_KTG_RUSAK',
            'QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG',
            'TOTAL_SEMEN_RUSAK','HARGA_TEBUS','PDPKS','TOTAL_KLAIM_SEMEN',
            'TOTAL_KLAIM_ALL','KETERANGAN_POD','EVIDENCE_POD1','EVIDENCE_POD2',
            'GEOFENCE_POD','TANGGAL_DATANG',
            'TANGGAL_BONGKAR','FLAG_POD');
        $field_data=array(
            "OPEN","SYSDATE","$username","$qty_ktg_rusak",
            "$qty_semen_rusak","$total_ktg_rusak","$total_ktg_rezak","$total_klaim_ktg",
            "$total_semen_rusak","$harga_tebus","$pdpks","$total_klaim_semen",
            "$total_klaim_all","$keterangan_pod","$saved_file_name_pod1","$saved_file_name_pod2",
            "$geofence_pod","updtgl_".$tanggal_datang,
            "updtgl_".$tanggal_bongkar,"POD-FIOS"); 		

        $tablename="EX_TRANS_HDR";
        $field_id=array('NO_SHP_TRN');
        $value_id=array("$no_shipment");
        $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);

        $tablename="EX_TRANS_HDR";
        $field_id=array('NO_SHP_EX');
        $value_id=array("$no_shipment");
        $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);

        $msg .= "(S) => Berhasil POD Shipment ($no_shipment) <br>";
    }

    showMessage($msg);
    exit;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Data SPJ</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Upload POD</th>
</tr></table>
</div>

<form method="post" name="upload" id="import" enctype="multipart/form-data">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
            <td class="puso">:</td>
            <td> <input name="excel_file" type="file"  class="button" accept=".xls, application/vnd.ms-excel" required></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="upload" type="submit"  class="button" value="Upload"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>

          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>

<form method="post" name="download" id="import" enctype="multipart/form-data" action="pod_upload_template_xls.php">
    <table width="800" align="center" class="adminform">
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>

        <tr>
            <td colspan="3" style="text-align:center;">
                <input type="submit" name="download_template" value="Download Template" />
            </td>
        </tr>

        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
