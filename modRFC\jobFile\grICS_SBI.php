<?php
    class grICS_SBI {

        private $fungsi;
        private $conn;
        private $logfile;
        public $msg;
        public $dataFuncRFC;
        private $status;

        function __construct() {
            $this->status = 'SUKSES';
            $this->fungsi = new or_fungsi();
            $this->conn = $this->fungsi->or_koneksi();
            // $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
        }

        function saveLog() {
            $this->msg = substr($this->msg, 0, 900);
            $sqllog = "INSERT INTO RFC_LOG VALUES ('GRICSSBI',SYSDATE,'" . $this->msg . "')";
            $querylog = oci_parse($this->conn, $sqllog);
            if ($querylog) {
                $execlog = oci_execute($querylog);
            }
            //set running down
            $sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0,RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
            $query_run = oci_parse($this->conn, $sqlset_run);
            oci_execute($query_run);
            // //end set
        }

        function run($group_plant='') {
            // fwrite($this->logfile, "Start ".get_class($this)."pada tanggal jam ".date('d-m-Y H:i:s')."\n");
            $this->msg = "Start " . get_class($this) . " pada tanggal jam " . date('d-m-Y H:i:s') . "\n";
            $messageResp = "";

            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                $sap->PrintStatus();
                $this->msg .= $sap->PrintStatus();
                $this->status = 'GAGAL';
                $this->saveLog();
                exit;
            }

            $fce = $sap->NewFunction("ZCSD_GET_DO_AUTOGR");
            if ($fce == false) {
                $sap->PrintStatus();
                $this->msg .= $sap->PrintStatus();
                $this->status = 'GAGAL';
                $this->saveLog();
                exit;
            }

            $date1 = date('Ymd', strtotime(' -7 days'));//date("Ym01");
            $date2 = date("Ymd");
            // $date2 = date("Ymd",strtotime("-1 day"));

            // $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => $date);
            $tmp = array("SIGN" => 'I', "OPTION" => 'BT', "LOW" => "$date1", "HIGH" => "$date2");
            $fce->LR_CREATE_DATE->Append($tmp);

            $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "PTSC");
            $fce->LR_VKORG->Append($tmp);
            
            $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "I300");
            $fce-> LR_WERKS->Append($tmp);

            $doNumExist = array();

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->T_DATA_AUTOGR->Reset();

                while ($fce->T_DATA_AUTOGR->Next()) {
                    array_push($doNumExist, $fce->T_DATA_AUTOGR->row["NO_DO"]);
                }
            }

            $fce->Close();
            $sap->Close();

            $strmap = "SELECT * FROM MAPPING_PO_ICS WHERE COMPANY_CODE = 'PTSC' AND SHIPPING_POINT = 'I300' AND  FLAG_DEL='X'";
    
            $querymap = @oci_parse($this->conn, $strmap);
            @oci_execute($querymap);
            $rowmap = oci_fetch_array($querymap, OCI_ASSOC);    
            $plant_tujuan = $rowmap["SOLD_TO_PARTY"];
            $plant_source = $rowmap["PLANT"];

            $strSoldto = "SELECT * FROM ZMD_MAPPING_CUSTOMER_ROYALTY WHERE SOLD_TO_MD = '".sprintf("%010s", $plant_tujuan)."' AND  DEL='0' ";
            
            $querySoldto = @oci_parse($this->conn, $strSoldto);
            @oci_execute($querySoldto);
            $rowSoldto = oci_fetch_array($querySoldto, OCI_ASSOC);    
            $soldtoOpco = $rowSoldto["SOLD_TO_OPCO"];

            $lookUpGi = array();

            ///// VERSI BY SOLD TO//////////
            $url = 'https://integrasi-api.sig.id/apimd/lookupgoodsissuebyrange/dev';//$results['URL'];
            $data = array(
                "Token" =>"aSsMx7GV0HFGzlufM4DH",// $results['TOKEN'],
                "Plant" =>$plant_source,
                "DistributionChannel" => null,
                "SoldTo" => $soldtoOpco,
                "VehicleNumber" => null,
                "StartGoodsIssueDate" => date('Y-m-d'),
                "EndGoodsIssueDate" => date('Y-m-d'),
                'SystemID' => 'QASSO'
            );

            $options = array(
                'http' => array(
                    'header' => "Content-type: application/json\r\n",
                    'method' => 'POST',
                    'content' => json_encode($data),
                )
            );

            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context); 
            
            $response = json_decode($result);   
            
            if (isset($response->Data) && count($response->Data) != 0) {
                $responseLGi = json_decode(json_encode($response->Data), true);
                foreach ($responseLGi as $kk => $vv) {
                    if (!in_array($vv['DONo'], $doNumExist)) {
                        array_push($lookUpGi, $vv);
                    }
                }

                if (count($lookUpGi) > 0) {
                    foreach ($lookUpGi as $k => $v) {
                        $resultApproval = $this->gr_process($lookUpGi[$k]);
                        $messageResp = $resultApproval."<br><hr><br>";
                        echo $messageResp;
                    }
                } else {
                    $messageResp = "Tidak ada data";
                    echo $messageResp;
                }
            } else {
                $messageResp = "Tidak ada data";
                echo $messageResp;
            }


            $this->msg .= $messageResp;
            $this->saveLog();
        }

        function gr_process($_datas){ 
            // var_dump($arData['JUMLAH'].'--'.$qty.'--'.$mapData['QTY_MINIMUM']);
            // die;

            $show_ket = "";
            $show_ket .= "<b>---- PROSES GR ----</b><br>";

            $sap = new SAPConnection();
            $sap->Connect("../include/sapclasses/logon_data.conf");
            if ($sap->GetStatus() == SAPRFC_OK)
                $sap->Open();
            if ($sap->GetStatus() != SAPRFC_OK) {
                $sap->PrintStatus();
                $this->msg .= $sap->PrintStatus();
                $this->status = 'GAGAL';
                $this->saveLog();
                exit;
            }
            
            $fce = $sap->NewFunction("BAPI_PO_GETDETAIL1");
            if ($fce == false) {
                $sap->PrintStatus();
                $this->msg .= $sap->PrintStatus();
                $this->status = 'GAGAL';
                $this->saveLog();
                exit;
            }

            $fce->PURCHASEORDER = $_datas['MDPurchaseOrderNumber'];

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->POITEM->Reset();

                while ($fce->POITEM->Next()) {
                        
                    $_datas['MATERIAL_CODE'] = $fce->POITEM->row["MATERIAL"];
                    $_datas['MATERIAL_DESC'] = $fce->POITEM->row["SHORT_TEXT"];
                    $_datas['PLANT_TARGET'] = $fce->POITEM->row["PLANT"];
                }
            }

            $fce->Close();
            
            if (isset($_datas['MATERIAL_CODE']) && $_datas['MATERIAL_CODE'] != '') {
                
                $fce = $sap->NewFunction("BAPI_GOODSMVT_CREATE");
                if ($fce == false) {
                    $sap->PrintStatus();
                    $this->msg .= $sap->PrintStatus();
                    $this->status = 'GAGAL';
                    $this->saveLog();
                    exit;
                }
    
                $fce->GOODSMVT_HEADER["PSTNG_DATE"] = date('Ymd');
                $fce->GOODSMVT_HEADER["DOC_DATE"] = date('Ymd');
                $fce->GOODSMVT_HEADER["REF_DOC_NO"] = "REFERENCE";
                $fce->GOODSMVT_HEADER["HEADER_TXT"] = "HEADER TEXT";
                
                $fce->GOODSMVT_CODE["GM_CODE"] = "04";
                
                $fce->GOODSMVT_ITEM->row["MATERIAL"] = $_datas['MATERIAL_CODE'];
                $fce->GOODSMVT_ITEM->row["PLANT"] = $_datas['PLANT_TARGET'];
                $fce->GOODSMVT_ITEM->row["STGE_LOC"] = "IC01";
                $fce->GOODSMVT_ITEM->row["MOVE_TYPE"] = "311";
                $fce->GOODSMVT_ITEM->row["ENTRY_QNT"] = $_datas['DOQty'];
                $fce->GOODSMVT_ITEM->row["ENTRY_UOM"] = "TO";
                $fce->GOODSMVT_ITEM->row["ITEM_TEXT"] = "ITEM TEXT";
                $fce->GOODSMVT_ITEM->Append($fce->GOODSMVT_ITEM->row);
    
                $nomorgr = '';
                $tahungr = '';
    
                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK) {
                    $nomorgr = $fce->MATERIALDOCUMENT;
                    $tahungr = $fce->MATDOCUMENTYEAR;
                    $fce->RETURN->Reset();
                    
                    //Commit Transaction
                    $fcecom = $sap->NewFunction("BAPI_TRANSACTION_COMMIT");
                    $fcecom->Call();
                    $fcecom->Close();
                }
    
                $fce->Close();

                if ($nomorgr != '' && $tahungr != '') {
                    $fce = $sap->NewFunction("ZCSD_PROCESS_AUTOGRBILL2");
                    if ($fce == false) {
                        $sap->PrintStatus();
                        $this->msg .= $sap->PrintStatus();
                        $this->status = 'GAGAL';
                        $this->saveLog();
                        exit;
                    }
    
                    $fce->T_INSERT->row["NMORG"] = "PTSC";
                    $fce->T_INSERT->row["NMPLAN"] = $_datas['Plant'];
                    $fce->T_INSERT->row["NO_DO"] = $_datas['DONo'];
                    $fce->T_INSERT->row["LINE_DO"] = $this->fungsi->linenum(1 * 10);
                    $fce->T_INSERT->row["NO_SO"] = $_datas['SONo'];
                    $fce->T_INSERT->row["LINE_SO"] = $this->fungsi->linenum(1 * 10);
                    $fce->T_INSERT->row["NO_PO"] = $_datas['MDPurchaseOrderNumber'];
                    $fce->T_INSERT->row["LINE_PO"] = "00010";
                    $fce->T_INSERT->row["NO_PO_REFICS"] = $_datas['MDPurchaseOrderNumber'];
                    $fce->T_INSERT->row["LINE_PO_REFICS"] = $this->fungsi->linenum(1 * 10);
                    $fce->T_INSERT->row["NMORG_REFICS"] = "7000";
                    $fce->T_INSERT->row["TYPE_TRANS"] = "1";
                    $fce->T_INSERT->row["STATUS_GR"] = "1";
                    $fce->T_INSERT->row["STATUS_INVOICE"] = "1";
                    $fce->T_INSERT->row["PROSES_GR"] = "3";
                    $fce->T_INSERT->row["NO_GR"] = $nomorgr;
                    $fce->T_INSERT->row["THN_GR"] = $tahungr;
                    $fce->T_INSERT->row["ACT_DATE"] = date('Ymd');
                    $fce->T_INSERT->row["ICS"] = "X";
                    $fce->T_INSERT->Append($fce->T_INSERT->row);
    
                    $tipe = '';
    
                    $fce->Call();
                    if ($fce->GetStatus() == SAPRFC_OK) {
                        $fce->T_RETURN->Reset();
                        while ($fce->T_RETURN->Next()) {
                            $tipe = $fce->T_RETURN->row["TYPE"];
                            $msg = $fce->T_RETURN->row["MESSAGE"];
                        }

                        if ($tipe == 'E') {
                            $show_ket .= $msg;
                            $show_ket .= '<br>';
                        }else {
                            $this->logIcsSbi($_datas);
                            
                            $show_ket .= "Nomor DO : ".$_datas['DONo']." Berhasil di Proses";
                            $show_ket .= '<br>';
                        }
                    }
                } else {
                    $show_ket .= "Nomor DO : ".$_datas['DONo']." gagal diproses, gagal pada generate GR Number";
                }
            } else {
                $show_ket .= "Nomor DO : ".$_datas['DONo']." gagal diproses, Nomor PO tidak ditemukan";
            }

            return $show_ket;
        }

        function logIcsSbi($data) {
            $sqlInsert = "INSERT INTO LOG_ICS_SBI 
                            (GI_NUMBER,
                            GI_YEAR,
                            ORG,
                            PLANT,
                            PLANT_SOURCE,
                            MATERIAL,
                            MATERIAL_DESC,
                            BUDAT,
                            BLDATE,
                            DO_NUMBER,
                            SO_NUMBER,
                            DO_QTY,
                            MEINS,
                            NOPOL,
                            NAMA_KAPAL,
                            BUKRS,
                            PO_NUMBER,
                            LOG_DATE) 
                        VALUES ('',
                                '',
                                'PTSC',
                                :PLANT,
                                :PLANT_SOURCE,
                                :MATERIAL,
                                :MATERIAL_DESC,
                                :BUDAT,
                                :BLDATE,
                                :DO_NUMBER,
                                :SO_NUMBER,
                                :DO_QTY,
                                'TO',
                                :NOPOL,
                                '',
                                :BUKRS,
                                :PO_NUMBER,
                                SYSDATE)";
            
            $stmtInsert = oci_parse($this->conn, $sqlInsert);
            oci_bind_by_name($stmtInsert, ':PLANT', $data['PLANT_TARGET']);
            oci_bind_by_name($stmtInsert, ':PLANT_SOURCE', $data['Plant']);
            oci_bind_by_name($stmtInsert, ':MATERIAL', $data['MATERIAL_CODE']);
            oci_bind_by_name($stmtInsert, ':MATERIAL_DESC', $data['MATERIAL_DESC']);
            oci_bind_by_name($stmtInsert, ':BUDAT', date('Y-m-d'));
            oci_bind_by_name($stmtInsert, ':BLDATE', date('Y-m-d'));
            oci_bind_by_name($stmtInsert, ':DO_NUMBER', $data['DONo']);
            oci_bind_by_name($stmtInsert, ':SO_NUMBER', $data['SONo']);
            oci_bind_by_name($stmtInsert, ':DO_QTY', $data['DOQty']);
            oci_bind_by_name($stmtInsert, ':NOPOL', $data['VehicleNumber']);
            oci_bind_by_name($stmtInsert, ':BUKRS', $data['PLANT_TARGET']);
            oci_bind_by_name($stmtInsert, ':PO_NUMBER', $data['MDPurchaseOrderNumber']);
            oci_execute($stmtInsert);
        }
    }


