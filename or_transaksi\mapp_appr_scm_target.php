<?
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$titlepage='Maintenance Mapping Approval Target SCM';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];


$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
if (false) {
?>
<SCRIPT LANGUAGE="JavaScript">
alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
</SCRIPT>
<a href="../login.php">Login....</a>
<?

exit();
}

?>

<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title><?=$titlepage;?></title>
    <!-- import easyui -->
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
    <script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
    <script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>

<body>

    <div align="center">
        <table id="dg" title="Maintenance Mapping Approval Target SCM" class="easyui-datagrid" style="width:100%;height:350px">
            <!-- idField="itemid" rownumbers="true" pagination="true"> -->
            <thead>
                <tr>
                    <th field="ID" hidden="true" align="center">ID</th>
                    <th field="ck" checkbox="true"></th>
                    <th field="APPROVAL_NAME" align="center" width="18%">APPROVAL NAME</th>
                    <th field="USERNAME" align="center" width="10%">USERNAME</th>
                    <th field="EMAIL" align="center" width="13%">EMAIL</th>
                    <th field="APPROVAL_LEVEL" align="center" width="5%">LEVEL</th>
                    <th field="STATUS" align="center" width="5%">STATUS</th>
                    <th field="CREATED_AT" align="center" width="15%">CREATED AT</th>
                    <th field="CREATED_BY" align="center" width="15%">CREATED BY</th>
                    <th field="UPDATED_AT" align="center" width="15%">UPDATED AT</th>
                    <th field="UPDATED_BY" align="center" width="15%">UPDATED BY</th>

                </tr>
            </thead>
        </table>
        <div id="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
                onclick="newAct()">New</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
                onclick="editAct()">Edit</a>
            <!-- <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
                onclick="deleteAct()">Delete</a> -->
            <a class="easyui-linkbutton" plain="true" iconCls="icon-excel"
                href="template_xls/template_maintenance_mapp_appr_target_scm.xls">Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
                onclick="uploadAct()">Upload Excel</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
                onclick="exportToExcel()">Export Excel</a>
        </div>

        <!-- AWAL TAMBAH DATA -->
        <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
            buttons="#dlg-buttons">
            <div class="ftitle">Create data</div>
            <form id="fm" method="post" novalidate>
                <div class="fitem">
                    <label>Approval Name</label>
                    <input name="approval_name" id="approval_name" class="easyui-textbox" required="true">
                </div>
                <div class="fitem">
                    <label>Username</label>
                    <input name="username" id="username" class="easyui-textbox">
                </div>
                <div class="fitem">
                    <label>Email</label>
                    <input name="email" id="email" class="easyui-textbox" required="true">
                </div>
                <div class="fitem">
                    <label>Level</label>
                    <select class="easyui-combobox" id="level" name="level" style="width:200px;" required="true">
                        <? for ($i=1; $i <= 10 ; $i++) {?>
                            <option value="<?= $i ?>"><?= $i ?></option>
                        <? } ?>
                    </select>
                </div>
            </form>
        </div>
        <div id="dlg-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()"
                style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>
    <!-- AKHIR TAMBAH DATA -->

    <!-- AWAL EDIT DAN DELETE DATA -->
    <div id="dlgEdit" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
        buttons="#dlg-buttons-edit">
        <div class="ftitle" id="titleEdit">Detail data</div>
        <form id="fmEdit" method="post" novalidate>
            <!-- AWAL ID -->
            <div class="fitem" style="visibility:hidden;position:fixed">
                <label>ID</label>
                <input name="id" id="idEdit" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Approval Name</label>
                <input name="approval_name" id="approval_nameEdit" class="easyui-textbox" required="true">
            </div>
            <div class="fitem">
                <label>Username</label>
                <input name="username" id="usernameEdit" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Email</label>
                <input name="email" id="emailEdit" class="easyui-textbox" required="true">
            </div>
            <div class="fitem">
                <label>Level</label>
                <select class="easyui-combobox" id="levelEdit" name="level" style="width:200px;" required="true">
                        <? for ($i=1; $i <= 10 ; $i++) {?>
                            <option value="<?= $i ?>"><?= $i ?></option>
                        <? } ?>
                    </select>
            </div>
            <div class="fitem">
                <label>Level</label>
                <select class="easyui-combobox" id="activeEdit" name="active" style="width:200px;" required="true">
                        <option value="0">Active</option>
                        <option value="1">Inactive</option>
                </select>
            </div>
        </form>
    </div>
    <div id="dlg-buttons-edit">
        <a href="javascript:void(0)" id="modal-submit" class="easyui-linkbutton c6" iconCls="icon-ok"
            onclick="saveEditAct()" style="width:90px" id="savedata">Oke</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
            onclick="javascript:$('#dlgEdit').dialog('close')" style="width:90px">Cancel</a>
    </div>
    </div>
    <!-- AKHIR EDIT DAN DELETE DATA -->

    <!-- AWAL UPLOAD FILE XLS -->
    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true"
        buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload"
                    name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px"
                id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>
    <!-- AKHIR UPLOAD FILE XLS -->

    <script type="text/javascript">
    $(function() {
        $("#dg").datagrid({
            url: 'mapp_appr_scm_target_act.php?act=show',
            // singleSelect: true,
            pagination: false,
            pageList: [5, 10, 20, 30, 40, 50, 100, 200, 300],
            pageSize: 10,
            rownumbers: true,
            loadMsg: 'Processing,please wait',
            height: 'auto',
            toolbar: '#toolbar'

        });
        $('#dg').datagrid('enableFilter');
    });

    $('#dlg').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });
    $('#dlgEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });

    $('#dlgEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });

    var url;

    function newAct() {
        $('#dlg').dialog('open').dialog('setTitle', 'New Data');
        $('#fm').form('clear');
        url = 'mapp_appr_scm_target_act.php?act=add';
    }

    function saveAct() {
        $('#fm').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                }
            }
        });
    }

    function editAct() {
        var row = $('#dg').datagrid('getSelected'); // Ambil data baris yang dipilih
        if (row) {
            document.getElementById("titleEdit").textContent = "Edit Data";
            $('#modal-submit').attr('onclick', 'saveEditAct()');
            $('#dlgEdit').dialog('open').dialog('setTitle', 'Edit Data');

            // enable semua input
            $('#approval_nameEdit').textbox('enable');
            $('#usernameEdit').textbox('enable');
            $('#emailEdit').textbox('enable');
            $('#levelEdit').combobox('enable');
            $('#activeEdit').combobox('enable');

            // Set nilai lainnya ke form
            $('#approval_nameEdit').textbox('setValue', row.APPROVAL_NAME);
            $('#usernameEdit').textbox('setValue', row.USERNAME);
            $('#emailEdit').textbox('setValue', row.EMAIL);
            $('#levelEdit').combobox('setValue', row.APPROVAL_LEVEL);
            $('#activeEdit').combobox('setValue', row.DEL_MARK);
            $('#idEdit').textbox('setValue', row.ID);

            url = 'mapp_appr_scm_target_act.php?act=edit';
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function saveEditAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function deleteAct() {
        var row = $('#dg').datagrid('getSelected');
        var rows = $('#dg').datagrid('getSelections');
        
        if (rows) {
            if (rows.length > 1) {
                $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
                    if (r){
                        $.post('mapp_appr_scm_target_act.php?act=multipleDel&',{data:rows},function(result){
                        // $.post('mapp_appr_scm_target_act.php?act=multipleDel&',{data:rows.ID},function(result){
                            if (result.success){
                                $.messager.show({
                                    title: 'Success',
                                    msg: result.success,
                                    width: 400, // Atur lebar (dalam piksel)
                                    height: 100, // Atur tinggi (dalam piksel)
                                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                    showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                });
                                $('#dg').datagrid('reload'); // reload the user data
                            } else {
                                $.messager.show({ // show error message
                                    title: 'Error',
                                    msg: result.errorMsg,
                                    width: 400, // Atur lebar (dalam piksel)
                                    height: 100, // Atur tinggi (dalam piksel)
                                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                    showType: 'show' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                });
                            }
                        },'json');
                    }
                });
            } else {
                document.getElementById("titleEdit").textContent = "Delete Master Data";
                $('#modal-submit').attr('onclick', 'saveDeleteAct()');
                $('#dlgEdit').dialog('open').dialog('setTitle', 'Delete Data');
    
                // disable semua input
                $('#approval_nameEdit').textbox('disable');
                $('#usernameEdit').textbox('disable');
                $('#levelEdit').combobox('disable');
                $('#emailEdit').textbox('disable');
    
                // Set nilai lainnya ke form
                $('#approval_nameEdit').textbox('setValue', row.APPROVAL_NAME);
                $('#usernameEdit').textbox('setValue', row.USERNAME);
                $('#emailEdit').textbox('setValue', row.EMAIL);
                $('#levelEdit').combobox('setValue', row.APPROVAL_LEVEL);
                $('#idEdit').textbox('setValue', row.ID);
    
                // URL untuk request delete
                url = 'mapp_appr_scm_target_act.php?act=delete';
            }
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function uploadAct() {
        $('#dlg_upload').dialog('open').dialog('setTitle', 'Upload Excel Data');
        $('#uploadForm').form('clear');
    }

    function saveUploadAct() {
        $('#uploadForm').form('submit', {
            url: 'mapp_appr_scm_target_act.php?act=upload_file',
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.data,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function saveDeleteAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function exportToExcel() {
        // var rows = data.rows;
        var rows = $('#dg').datagrid('getRows');

        // Filter the rows to include only the displayed columns and adjust FLAGING values
        var filteredRows = rows.map(function(row) {
            return {
                APPROVAL_NAME: row.APPROVAL_NAME,
                USERNAME: row.USERNAME,
                EMAIL: row.EMAIL,
                LEVEL: row.APPROVAL_LEVEL,
                STATUS: row.STATUS,
                CREATED_AT: row.CREATED_AT,
                CREATED_BY: row.CREATED_BY,
                UPDATED_AT: row.UPDATED_AT,
                UPDATED_BY: row.UPDATED_BY
            };
        });

        // Convert the filtered data to a worksheet
        var worksheet = XLSX.utils.json_to_sheet(filteredRows);
        var workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // Save the workbook as an Excel file
        XLSX.writeFile(workbook, "mapping_approval_target_scm.xls");
    }
    </script>

    <style type="text/css">
    .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .icon-upload {
        background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
        background: transparent url("icon/excel.png") no-repeat scroll center center;
    }


    .btn:not([disabled]) {
        color: white;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }

    thead th {
        text-align: left;
        padding: 7px;
    }

    tbody td {
        border-top: 1px solid #e3e3e3;
        padding: 7px;
    }

    #fm {
        margin: 0;
        padding: 10px;
    }

    .ftitle {
        font-size: 14px;
        font-weight: bold;
        padding: 5px 0;
        margin-bottom: 10px;
        border-bottom: 1px solid #ccc;
    }

    .fitem {
        margin-bottom: 5px;
    }

    .fitem label {
        display: inline-block;
        width: 101px;
        margin-bottom: 2px;
    }

    .fitem input {
        width: 190px;
        margin-bottom: 5px;
    }

    .fitem select {
        width: 195px;
        margin-bottom: 5px;
    }

    #dlg,
    #dlgEdit {
        padding: 10px 0 10px 10px;
    }

    .switch-button {
        position: relative;
        width: 50px;
        height: 25px;
        background-color: #ccc;
        border-radius: 15px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 0 auto;             /* Memastikan switch button terpusat secara horizontal */
    }

    .switch-button[data-checked="checked"] {
        background-color: #4caf50;
    }

    .switch-button-handle {
        position: absolute;
        width: 23px;
        height: 23px;
        background-color: white;
        border-radius: 50%;
        transition: left 0.3s ease;
        top: 1px;                   /* Menjaga posisi vertikal handle di tengah */
        left: 1px;                  /* Posisi handle di kiri ketika tidak aktif */
    }

    .switch-button .switch-button-handle.active {
        left: 26px;                 /* Memindahkan handle ke kanan ketika aktif */
    }

    .datagrid-cell {
        display: flex;
        align-items: center;        /* Vertikal tengah */
        justify-content: center;    /* Horizontal tengah */
        padding: 0;                 /* Menghilangkan padding jika ada */
    }
    </style>
    </div>
    <? 
include ('../include/ekor.php'); 
?>
</body>

</html>
