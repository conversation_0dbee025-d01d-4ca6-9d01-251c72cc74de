<?php
require_once '../ex_ba_sp/Excel/reader.php';
require_once ('../include/ex_fungsi.php');
require_once '../security_helper.php';
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

function showMessage($message)
{
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Result!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
        </div>
    </div>
<?php
}

$no_inv = isset($_GET['no_inv']) ? $_GET['no_inv'] : (isset($_POST['no_inv']) ? $_POST['no_inv'] : '');
if (isset($_POST['upload']) && $_FILES['excel_file']['error'] == 0) {
    $file_name = $_FILES['excel_file']['name'];
    $file_tmp = $_FILES['excel_file']['tmp_name'];

    $check = validate_excel($file_name, $file_tmp);
    if(!$check['status']){
        echo "<script>alert('" . $check['message'] ."');</script>";
        exit;
    }

    $data = new Spreadsheet_Excel_Reader();
    $data->setOutputEncoding('CP1251');
    $data->read($file_tmp);

    $sheet = $data->sheets[0];

    // Hitung jumlah baris
    $rows = count($sheet['cells']);
    if($rows <= 1){
        echo "Data upload kosong";
    }
    $msg = "";

    for ($i = 2; $i <= $rows; $i++) {
        $col1 = isset($sheet['cells'][$i][1]) ? $sheet['cells'][$i][1] : ''; // NO_INVOICE
        $col2 = isset($sheet['cells'][$i][2]) ? $sheet['cells'][$i][2] : ''; // X_INVOICEDOCNUMBER
        $col3 = isset($sheet['cells'][$i][3]) ? $sheet['cells'][$i][3] : ''; // X_ACCNUMBER

        $sql = "DELETE FROM EX_INVOICE_SMBR_RUN_PPL WHERE NO_INV = :no_invoice";
        $stmt = oci_parse($conn, $sql);
        oci_bind_by_name($stmt, ':no_invoice', $col1);
        oci_execute($stmt);

        $field_names = array(
            'NO_INV', 'X_INVOICEDOCNUMBER', 'X_ACCNUMBER'
        );

        $field_data = array(
            $col1, $col2, $col3
        );

        $tablename = "EX_INVOICE_SMBR_RUN_PPL";

        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
        $msg .= "$col1 => success<br>";
    }

    showMessage($msg);
    exit;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload RUN PPL</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
    border-style:ridge;
    border-width:1;
    border-collapse:collapse;
    font-family:sans-serif;
    font-size:12px;
}
table.excel thead th, table.excel tbody th {
    background:#CCCCCC;
    border-style:ridge;
    border-width:1;
    text-align: center;
    vertical-align:bottom;
}
table.excel tbody th {
    text-align:center;
    width:20px;
}
table.excel tbody td {
    vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
    border: 1px solid #EEEEEE;
}
</style>

<body>
<div align="center">
    <table width="800" align="center" class="adminheading" border="0">
        <tr>
            <th class="da2">Upload RUN PPL</th>
        </tr>
    </table>
</div>

<form method="post" name="upload" id="import" enctype="multipart/form-data">
    <table width="800" align="center" class="adminform">
        <tr height="30">
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
            <td class="puso">:</td>
            <td><input name="excel_file" type="file" class="button" accept=".xls, application/vnd.ms-excel" required></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="upload" type="submit" class="button" value="Upload"></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
        </tr>
    </table>
</form>

<form method="post" name="download" id="import" enctype="multipart/form-data" action="run_ppl_upload_template_xls.php?no_inv=<?= $no_inv; ?>">
    <table width="800" align="center" class="adminform">
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
        <tr>
            <td colspan="3" style="text-align:center;">
                <input type="submit" name="download_template" value="Download Template" />
            </td>
        </tr>
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
    </table>
</form>
<br><br>

<div align="center"></div>
<p>&nbsp;</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
