<?php
/* @var $this dView */

$this->controller->layout = '@/dee/layout-easyui';
$this->title = 'List VA BNI';
?>

<div align="center">
    <table width="600" align="center" class="adminheading" border="0">
        <tr>
            <th class="kb2"><?= $this->title ?></th>
        </tr>
    </table>
</div>
<div class="easyui-panel" style="width:100%;" title="Search">
    <form id="form-find">
        <table>
            <tr>
                <th width="180">ORG</th>
                <td width="200">
                    <input id="org" class="easyui-combobox" style="width:100%;">
                </td>
            </tr>
            <tr>
                <th width="180">VA Number</th>
                <td width="200">
                    <input id="vanum" class="easyui-textbox" style="width:100%;" required>
                </td>
            </tr>
            <tr>
                <td></td>
                <td><a id="find" class="easyui-linkbutton" iconCls="icon-search">Find</a></td>
                <td><button id="resetBtn" class="easyui-linkbutton" iconCls="icon-reload">Reset</button></td>
            </tr>
        </table>
    </form>
</div>
<p></p>
<table id="dg" class="easyui-datagrid" title="List Invoice" style="width:100%;height:350px">
</table>

<?php $this->addJsBlock() ?>
<script>
    const NumFormater = new Intl.NumberFormat().format;
    const lokal = {};
    $('#org').combobox({
        data: <?= json_encode($orgs) ?>,
        valueField: 'id',
        textField: 'text',
        label: 'Org:',
        labelPosition: 'top'
    });
    $('#dg').datagrid({
        url: '@baseUrl/dee.php/cms_sgg/cListVaBni/listInvoice',
        method: 'get',
        fitColumns: true,
        columns: [[
                {field: 'VANUM', title: 'Va Number', width: 100, sortable: false},
                {field: 'BUKRS', title: 'Company', width: 100, sortable: false},
                {field: 'KUNNR', title: 'Customer Code', width: 180},
                {field: 'VADAT', title: 'Tanggal VA', width: 120, sortable: false,
                    formatter(val) {
                        if (val) {
                            let y = val.substring(0, 4);
                            let m = val.substring(4, 6);
                            let d = val.substring(6, 8);
                            return `${y}-${m}-${d}`;
                        }
                    }
                },
                {field: 'VASTAT', title: 'Status VA', width: 180},
                {field: 'PAYMD', title: 'Amount', width: 100, sortable: false,align:'right',
                    formatter(val) {
                        let res = val ? val.replaceAll('.', '').replaceAll('-', '') : '';
                        return NumFormater(res);
                    }
                },
                {field: 'CRDAT', title: 'Created Date', width: 100, sortable: false,
                    formatter(val) {
                        if (val) {
                            let y = val.substring(0, 4);
                            let m = val.substring(4, 6);
                            let d = val.substring(6, 8);
                            return `${y}-${m}-${d}`;
                        }
                    }
                },
                {field: 'EXPIRED', title: 'Expired Date', width: 100, sortable: false,
                    formatter(val) {
                        if (val) {
                            let y = val.substring(0, 4);
                            let m = val.substring(4, 6);
                            let d = val.substring(6, 8);
                            return `${y}-${m}-${d}`;
                        }
                    }
                },
            ]],
    });

    $('#find').linkbutton({
        onClick() {
            if ($('#form-find').form('validate')) {
                let org   = $('#org').val();
                let vanum = $('#vanum').val();

                $('#dg').datagrid('load', {
                    org: org,
                    vanum: vanum
                });
            }
        }
    });

    $('#resetBtn').on('click', function() {
        $('#org').val('');
        $('#vanum').val('');
        $('#dg').datagrid('load', {}); 
    });
</script>
<?php $this->endJsBlock() ?>