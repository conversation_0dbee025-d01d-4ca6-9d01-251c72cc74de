<?
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$titlepage='Maintenance Mapping Target SNOP AP';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];


$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
if (false) {
?>
<SCRIPT LANGUAGE="JavaScript">
alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
</SCRIPT>
<a href="../login.php">Login....</a>
<?

exit();
}

?>

<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title><?=$titlepage;?></title>
    <!-- import easyui -->
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
    <script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
    <script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>

<body>

    <div align="center">
        <table id="dg" title="Maintenance Mapping Target SNOP AP" class="easyui-datagrid" style="width:100%;height:350px">
            <!-- idField="itemid" rownumbers="true" pagination="true"> -->
            <thead>
                <tr>
                    <th field="ID" hidden="true" align="center">ID</th>
                    <th field="ck" checkbox="true"></th>
                    <th field="PERIODE" align="center" width="8%">PERIODE</th>
                    <th field="DISTRIK_RET" align="center" width="18%">MDB / DISTRIK RET</th>
                    <th field="SEGMEN" align="center" width="5%">SEGMEN</th>
                    <th field="TARGET_F" align="center" width="14%">SNOP TARGET AWAL (TON)</th>
                    <th field="TARGET_ALLOC_F" align="center" width="10%">ADD SNOP (TON)</th>
                    <th field="TARGET_PLAFON_F" align="center" width="10%">SNOP AKHIR (TON)</th>
                    <th field="TARGET_SPC_F" align="center" width="10%">TARGET SPC (TON)</th>
                    <th field="SISA_PLAFON_F" align="center" width="10%">SISA SNOP (TON)</th>
                    <th field="CREATED_AT" align="center" width="15%">CREATED AT</th>
                    <th field="CREATED_BY" align="center" width="15%">CREATED BY</th>
                    <th field="UPDATED_AT" align="center" width="15%">UPDATED AT</th>
                    <th field="UPDATED_BY" align="center" width="15%">UPDATED BY</th>

                </tr>
            </thead>
        </table>
        <div id="toolbar">
            <label>Periode : </label>
            <input id="filter_periode" class="easyui-datetimespinner" value="<?= date('Y-m') ?>" data-options="label:'Periode',labelPosition:'left',formatter:formatter2,parser:parser2,selections:[[5,7],[5,7]],onChange:filt_periode" style="width:150px;">

            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
                onclick="newAct()">New</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
                onclick="editAct()">Edit</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
                onclick="deleteAct()">Delete</a>
            <a class="easyui-linkbutton" plain="true" iconCls="icon-excel"
                href="template_xls/template_maintenance_mapping_snop_ap.xls">Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
                onclick="uploadAct()">Upload Excel</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
                onclick="exportToExcel()">Export Excel</a>
        </div>

        <!-- AWAL TAMBAH DATA -->
        <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
            buttons="#dlg-buttons">
            <div class="ftitle">Create data</div>
            <form id="fm" method="post" novalidate>
                <div class="fitem">
                    <label>Periode</label>
                    <input name="periode" id="periode" class="easyui-textbox" value="<?= date('Y-m') ?>" required="true">
                </div>
                <div class="fitem">
                    <label>MDB / Distrik Ret.</label>
                    <input name="distrik_ret" id="distrik_ret" class="easyui-textbox">
                </div>
                <div class="fitem">
                    <label>SNOP Target Awal</label>
                    <input name="target" id="target" class="easyui-textbox" type="number">
                </div>
                <div class="fitem">
                    <label>Add SNOP</label>
                    <input name="target_alloc" id="target_alloc" class="easyui-textbox" type="number">
                </div>
            </form>
        </div>
        <div id="dlg-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()"
                style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>
    <!-- AKHIR TAMBAH DATA -->

    <!-- AWAL EDIT DAN DELETE DATA -->
    <div id="dlgEdit" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
        buttons="#dlg-buttons-edit">
        <div class="ftitle" id="titleEdit">Detail data</div>
        <form id="fmEdit" method="post" novalidate>
            <!-- AWAL ID -->
            <div class="fitem" style="visibility:hidden;position:fixed">
                <label>ID</label>
                <input name="id" id="idEdit" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Perode</label>
                <input name="periode" id="periodeEdit" class="easyui-textbox" required="true">
            </div>
            <div class="fitem">
                <label>Distrik Ret.</label>
                <input name="distrik_ret" id="distrik_retEdit" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>SNOP Target Awal</label>
                <input name="target" id="targetEdit" class="easyui-textbox" type="number">
            </div>
            <div class="fitem">
                <label>Add SNOP</label>
                <input name="target_alloc" id="target_allocEdit" class="easyui-textbox" type="number">
            </div>
        </form>
    </div>
    <div id="dlg-buttons-edit">
        <a href="javascript:void(0)" id="modal-submit" class="easyui-linkbutton c6" iconCls="icon-ok"
            onclick="saveEditAct()" style="width:90px" id="savedata">Oke</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
            onclick="javascript:$('#dlgEdit').dialog('close')" style="width:90px">Cancel</a>
    </div>
    </div>
    <!-- AKHIR EDIT DAN DELETE DATA -->

    <!-- AWAL UPLOAD FILE XLS -->
    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true"
        buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload"
                    name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px"
                id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>
    <!-- AKHIR UPLOAD FILE XLS -->

    <script type="text/javascript">
    function formatter2(date){
            console.log(date);
            
            if (!date){return '';}
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            return y + '-' + (m<10?('0'+m):m);
    }

    function parser2(s){
        console.log(s);
        if (!s){return null;}
        var ss = s.split('-');
        var y = parseInt(ss[0],10);
        var m = parseInt(ss[1],10);
        if (!isNaN(y) && !isNaN(m)){
            return new Date(y,m-1,1);
        } else {
            return new Date();
        }
    }

    $(function() {
        var filterPeriode = $('#filter_periode').val();
        
        $("#dg").datagrid({
            url: 'maintenance_mapping_plafon_ap_act.php?act=show&filter_periode='+filterPeriode,
            // singleSelect: true,
            pagination: false,
            pageList: [5, 10, 20, 30, 40, 50, 100, 200, 300],
            pageSize: 10,
            rownumbers: true,
            loadMsg: 'Processing,please wait',
            height: 'auto',
            toolbar: '#toolbar'

        });
        $('#dg').datagrid('enableFilter');
    });

    function filt_periode(e) {
        $('#dg').datagrid('options').url = 'maintenance_mapping_plafon_ap_act.php?act=show&filter_periode='+e;
        $('#dg').datagrid('reload');
    }

    $('#dlg').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });
    $('#dlgEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });

    $('#dlgEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });

    var url;

    function newAct() {
        $('#dlg').dialog('open').dialog('setTitle', 'New Data');
        $('#fm').form('clear');
        url = 'maintenance_mapping_plafon_ap_act.php?act=add';
    }

    function saveAct() {
        $('#fm').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                }
            }
        });
    }

    function editAct() {
        var row = $('#dg').datagrid('getSelected'); // Ambil data baris yang dipilih
        if (row) {
            document.getElementById("titleEdit").textContent = "Edit Master Data";
            $('#modal-submit').attr('onclick', 'saveEditAct()');
            $('#dlgEdit').dialog('open').dialog('setTitle', 'Edit Data');

            // enable semua input
            $('#periodeEdit').textbox('enable');
            $('#distrik_retEdit').textbox('enable');
            $('#targetEdit').textbox('enable');
            $('#target_allocEdit').textbox('enable');

            // Set nilai lainnya ke form
            $('#periodeEdit').textbox('setValue', row.PERIODE);
            $('#distrik_retEdit').textbox('setValue', row.DISTRIK_RET);
            $('#targetEdit').textbox('setValue', row.TARGET);
            $('#target_allocEdit').textbox('setValue', row.TARGET_ALLOC);
            $('#idEdit').textbox('setValue', row.ID);

            url = 'maintenance_mapping_plafon_ap_act.php?act=edit';
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function saveEditAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        // timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        // timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function deleteAct() {
        var row = $('#dg').datagrid('getSelected');
        var rows = $('#dg').datagrid('getSelections');
        
        if (rows) {
            if (rows.length > 1) {
                $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
                    if (r){
                        $.post('maintenance_mapping_plafon_ap_act.php?act=multipleDel&',{data:rows},function(result){
                        // $.post('maintenance_mapping_plafon_ap_act.php?act=multipleDel&',{data:rows.ID},function(result){
                            if (result.success){
                                $.messager.show({
                                    title: 'Success',
                                    msg: result.success,
                                    width: 400, // Atur lebar (dalam piksel)
                                    height: 100, // Atur tinggi (dalam piksel)
                                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                    showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                });
                                $('#dg').datagrid('reload'); // reload the user data
                            } else {
                                $.messager.show({ // show error message
                                    title: 'Error',
                                    msg: result.errorMsg,
                                    width: 400, // Atur lebar (dalam piksel)
                                    height: 100, // Atur tinggi (dalam piksel)
                                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                    showType: 'show' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                });
                            }
                        },'json');
                    }
                });
            } else {
                document.getElementById("titleEdit").textContent = "Delete Master Data";
                $('#modal-submit').attr('onclick', 'saveDeleteAct()');
                $('#dlgEdit').dialog('open').dialog('setTitle', 'Delete Data');
    
                // disable semua input
                $('#periodeEdit').textbox('disable');
                $('#distrik_retEdit').textbox('disable');
                $('#targetEdit').textbox('disable');
                $('#target_allocEdit').textbox('disable');
    
                // Set nilai lainnya ke form
                $('#periodeEdit').textbox('setValue', row.PERIODE);
                $('#distrik_retEdit').textbox('setValue', row.DISTRIK_RET);
                $('#targetEdit').textbox('setValue', row.TARGET);
                $('#target_allocEdit').textbox('setValue', row.TARGET_ALLOC);
                $('#idEdit').textbox('setValue', row.ID);
    
                // URL untuk request delete
                url = 'maintenance_mapping_plafon_ap_act.php?act=delete';
            }
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function uploadAct() {
        $('#dlg_upload').dialog('open').dialog('setTitle', 'Upload Excel Data');
        $('#uploadForm').form('clear');
    }

    function saveUploadAct() {
        $('#uploadForm').form('submit', {
            url: 'maintenance_mapping_plafon_ap_act.php?act=upload_file',
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                console.log(result);
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 0, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.alert({
                        title: 'Success',
                        msg: result.data,
                        buttons:[{
                            text: 'Export Log',
                            width: '100',
                            onClick: function(){
                                console.log(result.log);
                                
                                exportLogToExcel(result.log);
                            }
                        }],
                        width: 400, // Atur lebar (dalam piksel)
                        height: 200, // Atur tinggi (dalam piksel)
                        timeout: 0, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function saveDeleteAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function exportToExcel() {
        // var rows = data.rows;
        var rows = $('#dg').datagrid('getRows');

        // Filter the rows to include only the displayed columns and adjust FLAGING values
        var filteredRows = rows.map(function(row) {
            return {
                PEIRODE: row.PERIODE,
                DISTRIK_RET: row.DISTRIK_RET,
                SNOP_TARGET_AWAL: row.TARGET,
                ADD_SNOP: row.TARGET_ALLOC,
                SEGMEN: row.SEGMEN,
                SNOP_AKHIR: row.TARGET_PLAFON,
                TARGET_SPC: row.TARGET_SPC,
                SISA_SNOP: row.SISA_PLAFON,
                CREATED_AT: row.CREATED_AT,
                CREATED_BY: row.CREATED_BY,
                UPDATED_AT: row.UPDATED_AT,
                UPDATED_BY: row.UPDATED_BY
            };
        });

        // Convert the filtered data to a worksheet
        var worksheet = XLSX.utils.json_to_sheet(filteredRows);
        var workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // Save the workbook as an Excel file
        XLSX.writeFile(workbook, "master_mapping_snop_ap.xls");
    }
    
    function exportLogToExcel(rows) {
        // Filter the rows to include only the displayed columns and adjust FLAGING values
        var filteredRows = rows.map(function(row) {
            return {
                BARIS: row.baris,
                MESSAGE: row.message
            };
        });

        // Convert the filtered data to a worksheet
        var worksheet = XLSX.utils.json_to_sheet(filteredRows);
        var workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // Save the workbook as an Excel file
        XLSX.writeFile(workbook, "log_master_mapping_snop.xls");
    }
    </script>

    <style type="text/css">
    .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .icon-upload {
        background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
        background: transparent url("icon/excel.png") no-repeat scroll center center;
    }


    .btn:not([disabled]) {
        color: white;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }

    thead th {
        text-align: left;
        padding: 7px;
    }

    tbody td {
        border-top: 1px solid #e3e3e3;
        padding: 7px;
    }

    #fm {
        margin: 0;
        padding: 10px;
    }

    .ftitle {
        font-size: 14px;
        font-weight: bold;
        padding: 5px 0;
        margin-bottom: 10px;
        border-bottom: 1px solid #ccc;
    }

    .fitem {
        margin-bottom: 5px;
    }

    .fitem label {
        display: inline-block;
        width: 101px;
        margin-bottom: 2px;
    }

    .fitem input {
        width: 190px;
        margin-bottom: 5px;
    }

    .fitem select {
        width: 195px;
        margin-bottom: 5px;
    }

    #dlg,
    #dlgEdit {
        padding: 10px 0 10px 10px;
    }

    .switch-button {
        position: relative;
        width: 50px;
        height: 25px;
        background-color: #ccc;
        border-radius: 15px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 0 auto;             /* Memastikan switch button terpusat secara horizontal */
    }

    .switch-button[data-checked="checked"] {
        background-color: #4caf50;
    }

    .switch-button-handle {
        position: absolute;
        width: 23px;
        height: 23px;
        background-color: white;
        border-radius: 50%;
        transition: left 0.3s ease;
        top: 1px;                   /* Menjaga posisi vertikal handle di tengah */
        left: 1px;                  /* Posisi handle di kiri ketika tidak aktif */
    }

    .switch-button .switch-button-handle.active {
        left: 26px;                 /* Memindahkan handle ke kanan ketika aktif */
    }

    .datagrid-cell {
        display: flex;
        align-items: center;        /* Vertikal tengah */
        justify-content: center;    /* Horizontal tengah */
        padding: 0;                 /* Menghilangkan padding jika ada */
    }
    </style>
    </div>
    <? 
include ('../include/ekor.php'); 
?>
</body>

</html>
