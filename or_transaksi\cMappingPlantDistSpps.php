<?php

session_start();
include ('../include/or_fungsi.php');
require_once('../MainPHPExcel/MainPHPExcel.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$dist=sprintf("%010s",$_SESSION['distr_id']);

$org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$id = htmlspecialchars($_REQUEST['id']);
$aksi = htmlspecialchars($_REQUEST['act']);
$ID_REQ    = htmlspecialchars($_REQUEST['id']);

function importDataExcel($conn, $data_trgt){
    $insert=0;
    $update=0;
    $statusimport=0;
    $error_get_deskripsi=0;
    $data_expired = 0;
    $h = 0;
    $user_name=$_SESSION['user_name'];

    foreach ($data_trgt as $key => $value) { 
        $plant = trim($value[2]);
        $distributor = trim($value[3]);
        $distrik = trim($value[4]);
        $format_spps = trim($value[5]);
        unset($val);

        $val['PLANT'] = $plant;
        $val['DISTRIBUTOR'] = str_pad($distributor, 10, '0', STR_PAD_LEFT);
        $val['DISTRIK'] = $distrik;
        $val['FORMAT_SPPS'] = $format_spps;
        $val['CREATED_BY'] = $user_name;
        $val['CREATED_AT'] = date('Y-m-d H:i:s');
        $ins=false; $upd=false;
             
        $data_cek = CekSelectData($conn,$val);
        if ($data_cek['JUMLAH'] > 0){
            $upd= UpdateData($conn,$val);
            $update++;
            $statusimport=1;
        } else {
            $ins= InsertData($conn,$val);
            $insert++;
            $statusimport=1;
        }
    }
    $msg="";
    if ($insert>0) {
        $msg .= "- ".$insert." data baru berhasil di-insert <br> ";
    }
    if ($update>0) {
        $msg .= "- ".$update." data berhasil di-update <br>  ";
    }
    if ($error_get_deskripsi>0) {
        $msg .= "- ".$error_get_deskripsi." data tidak ditemukan di SAP <br>  ";
    }
    if ($data_expired > 0) {
        $msg .= "- ".$data_expired." data gagal di-process <br>  ";
    }
    $result['status'] = $statusimport;
    $result['message'] = $msg;
    return $result;
}

function CekSelectData($conn,$data){
    $sql_select =  "SELECT
                        COUNT(PLANT) AS JUMLAH
                    FROM
                       MAPPING_PLANT_DIST_SPPS
                    WHERE
                        PLANT = '".$data['PLANT']."' AND DISTRIBUTOR = '".$data['DISTRIBUTOR']."' AND DEL = '0' ";
    if ($data['DISTRIK'] != '' && $data['DISTRIK'] != null) {
        $sql_select .= " AND DISTRIK = '".$data['DISTRIK']."'";
    }

    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query))
    {   
        $arData['JUMLAH']= $row['JUMLAH'];
    }
    return $arData;
}

function CekSelectDataUpd($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       MAPPING_PLANT_DIST_SPPS
                    WHERE
                        PLANT = '".$data['PLANT']."' AND DISTRIBUTOR = '".$data['DISTRIBUTOR']."' AND DISTRIK = '".$data['DISTRIK']."' AND FORMAT_SPPS = '".$data['FORMAT_SPPS']."' AND DEL = '0' ";
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);
    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];
    return $arData;
}

function CekSelectDataIns($conn,$data){
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                       MAPPING_PLANT_DIST_SPPS
                    WHERE
                        PLANT = '".$data['PLANT']."' AND DISTRIBUTOR = '".$data['DISTRIBUTOR']."' AND DEL = '0' ";
    if ($data['DISTRIK'] != '' && $data['DISTRIK'] != null) {
        $sql_select .= " AND DISTRIK = '".$data['DISTRIK']."'";
    }
    
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);
    $row = oci_fetch_array($query);
    $arData['JUMLAH']= $row['JUMLAH'];
    return $arData;
}

function InsertData($conn,$data_insert) {
    $nama_user = $_SESSION['user_name'];
    unset($val); 

    $distributor = str_pad($data_insert['DISTRIBUTOR'], 10, '0', STR_PAD_LEFT);
    $val['DISTRIBUTOR'] = $distributor;
    $val['PLANT'] = $data_insert['PLANT'];
    $val['DISTRIK'] = $data_insert['DISTRIK'];
    $val['FORMAT_SPPS'] = $data_insert['FORMAT_SPPS'];

    $data_cek2 = CekSelectDataIns($conn,$val);
    if ($data_cek2['JUMLAH'] > 0) {
        // // UPDATE karena data sudah ada
        // $sql1 = "UPDATE MAPPING_PLANT_DIST_SPPS
        //         SET FORMAT_SPPS = '".$val['FORMAT_SPPS']."',
        //             UPDATED_AT  = SYSDATE,
        //             UPDATED_BY  = '".$_SESSION['user_name']."'
        //         WHERE PLANT = '".$val['PLANT']."'
        //         AND DISTRIBUTOR = '".$val['DISTRIBUTOR']."'
        //         AND DEL = '0'";
        // if (!empty($val['DISTRIK'])) {
        //     $sql1 .= " AND DISTRIK = '".$val['DISTRIK']."'";
        // } else {
        //     $sql1 .= " AND DISTRIK IS NULL";
        // }
        // echo $sql1;
        $query1 = oci_parse($conn, $sql1);
        $upd = oci_execute($query1, OCI_COMMIT_ON_SUCCESS);

        $show_ket = "Data Already up to date<br>";
        $ins = array('errorMsg'=>$show_ket);
        return false;
    }else{
        // $sql_seq = "SELECT NVL(MAX(LAST_SEQUENCE), 0) AS MAX_SEQ
        //     FROM MAPPING_PLANT_DIST_SPPS";
        // $stmt_seq = oci_parse($conn, $sql_seq);
        // oci_execute($stmt_seq);
        // $row_seq = oci_fetch_assoc($stmt_seq);
        $next_seq = 1;


        $sql2 = "INSERT INTO MAPPING_PLANT_DIST_SPPS (PLANT, DISTRIBUTOR,  DISTRIK, FORMAT_SPPS,  LAST_SEQUENCE, CREATED_AT, CREATED_BY,DEL) 
        VALUES ( '".$val['PLANT']."', '".$val['DISTRIBUTOR']."', '".$val['DISTRIK']."', '".$val['FORMAT_SPPS']."', '".$next_seq."', SYSDATE, '".$nama_user."','0')";
        
        $query2 = oci_parse($conn, $sql2);
        $ins = oci_execute($query2);
        return $ins;
    }
}

function UpdateData($conn, $data_update) {
    $nama_user = $_SESSION['user_name'];
    $date = date('Y-m-d H:i:s');    
    $plant = $data_update['PLANT'];
    $distributor = $data_update['DISTRIBUTOR'];
    $distrik = $data_update['DISTRIK'];
    $format_spps = $data_update['FORMAT_SPPS'];

    $sql1 = "UPDATE MAPPING_PLANT_DIST_SPPS
             SET PLANT = '$plant',
                 DISTRIBUTOR = '$distributor',
                 DISTRIK = '$distrik',
                 FORMAT_SPPS = '$format_spps',
                 UPDATED_AT = SYSDATE,
                 UPDATED_BY = '$nama_user'
             WHERE
                 PLANT = '$plant' AND
                 DISTRIBUTOR = '$distributor'"; 
        if (!empty($distrik)) {
            $sql1 .= " AND DISTRIK = '".$distrik."'";
        } else {
            $sql1 .= " AND DISTRIK IS NULL";
        }
    $query1 = oci_parse($conn, $sql1);
    $upd = oci_execute($query1);
      
    return $upd;
}

if(isset($aksi)){
switch($aksi) { 
    case 'getKota' :
        {   
            $page = !empty($_REQUEST['page']) ? htmlspecialchars($_REQUEST['page']) : 1;
            $rows = !empty($_REQUEST['row']) ? htmlspecialchars($_REQUEST['row']) : 10;
            $offset = ($page - 1) * $rows;
            $q = !empty($_REQUEST['q']) ? htmlspecialchars($_REQUEST['q']) : '';
            $sql = "SELECT DISTINCT
                        KD_KOTA AS KODE_DISTRIK,
                        NM_KOTA AS NAMA_DISTRIK
                    FROM
                        ZREPORT_M_KOTA
                    WHERE
                        (KD_KOTA LIKE '%$q%' OR REGEXP_LIKE(NM_KOTA, '$q', 'i'))
                        AND ROWNUM <= 25";
            $query = oci_parse($conn, $sql);
            oci_execute($query);
            $result = array();
            while ($row = oci_fetch_array($query, OCI_ASSOC+OCI_RETURN_NULLS)) {
                $result[] = $row;
            }
            echo json_encode($result);
        }
  break;
  case 'show' :
    {
        $sql = "SELECT
            ma.ID,
            ma.PLANT,
            ma.DISTRIBUTOR,
            ma.DISTRIK,
            ma.FORMAT_SPPS,
            ma.LAST_SEQUENCE,
            ma.CREATED_AT,
            ma.CREATED_BY,
            ma.UPDATED_AT,
            ma.UPDATED_BY,
            MAX(ZREPORT_M_KOTA.NM_KOTA) as CITY_NAME,
            ma.FORMAT_SPPS || '/' ||
            TO_CHAR(ma.CREATED_AT, 'YYMM') || '/' ||
            LPAD(ma.LAST_SEQUENCE, 4, '0') AS FORMAT_SPPS_FULL
        FROM
            MAPPING_PLANT_DIST_SPPS ma
        LEFT JOIN ZREPORT_M_KOTA ON
            ma.DISTRIK = ZREPORT_M_KOTA.KD_KOTA
        WHERE
            DEL != '1' $wheres
        GROUP BY
            ma.ID,
            ma.PLANT,
            ma.DISTRIBUTOR,
            ma.DISTRIK,
            ma.FORMAT_SPPS,
            ma.LAST_SEQUENCE,
            ma.CREATED_AT,
            ma.CREATED_BY,
            ma.UPDATED_AT,
            ma.UPDATED_BY
        ORDER BY
            ma.CREATED_AT DESC
        ";
        
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $i=0;
        while($row=oci_fetch_array($query)){
            array_push($result, $row);
        }
        echo json_encode($result);
     }
     break;
     case 'add' :
     {
        if ($user_org != '') {
            $plant = htmlspecialchars($_REQUEST['plant']);
            $distributor = htmlspecialchars($_REQUEST['distributor']);
            $distributor = str_pad($distributor, 10, '0', STR_PAD_LEFT);
            $distrik = htmlspecialchars($_REQUEST['distrik']);
            $format_spps = htmlspecialchars($_REQUEST['format_spps']);

            unset($val); 
            $val['PLANT'] = $plant;
            $val['DISTRIBUTOR'] = $distributor;
            $val['DISTRIK'] = $distrik;
            $val['FORMAT_SPPS'] = $format_spps;
            $val['CREATED_BY'] = $user_name;
            $val['CREATED_AT'] = date('Y-m-d H:i:s');
           
            $ins=false; $upd=false;  
                $data_cek = CekSelectData($conn,$val);
                // echo "JUMLAH".$data_cek['JUMLAH'];exit;
                if ($data_cek['JUMLAH'] > 0){
                    $upd= UpdateData($conn,$val);
                    $update= true;
                } else {
                    $ins= InsertData($conn,$val);
                    $insert = true;
                }

                if($insert){
                    echo json_encode(array('success'=>true));
                }elseif($update) {
                    echo json_encode(array('success'=>true));
                }else{
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }
        }else{
            echo json_encode(array('errorMsg'=>'Org tidak boleh kosong!!!'));
        }
     }
     break;
     case 'del' :
     {
        error_reporting(0);
        $value = ($_POST['data']);
        $date = date('Y-m-d H:i:s');
        $list = array();
        $gagal = 0;
        $sukses= 0;
        $i=0; 
        while($i < count($value)){
            $idDlt = $value[$i]['ID'];       
            $sql = "UPDATE MAPPING_PLANT_DIST_SPPS SET DEL='1' WHERE ID = '$idDlt' ";
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);

            if($result){ 
                $sukses=$sukses+1; 
            }else{ 
                $gagal=$gagal+1; 
            }

            array_push($list, $ID);
          $i++;
         }  

        if ($result){
            $keterangan = array('success'=>"Data Berhasil Di Delete = ".$sukses.", gagal = ".$gagal." ! ");
        } else {
            $keterangan = array('errorMsg'=>"Data Gagal Di Delete = gagal = ".$gagal." ! ");
        }
        echo json_encode($keterangan);

     }
     break;
     case 'updateApp' :
        {
            $plant = htmlspecialchars($_REQUEST['plant']);
            $distributor = htmlspecialchars($_REQUEST['distributor']);
            $distrk = htmlspecialchars($_REQUEST['distrik']);
            $format_spps = htmlspecialchars($_REQUEST['format_spps']);

            $ID   = htmlspecialchars($_REQUEST['ID']);
            unset($val); 
            $distributor = str_pad($distributor, 10, '0', STR_PAD_LEFT);
            $val['PLANT'] = $plant;
            $val['DISTRIBUTOR'] = $distributor;
            $val['DISTRIK'] = $distrik;
            $val['FORMAT_SPPS'] = $format_spps;
            $val['ID'] = $ID;
                
            $data_cek2 = CekSelectDataUpd($conn,$val);
            if ($data_cek2['JUMLAH'] > 0) {
                $show_ket = "Data Already up to date<br>";
                $keterangan = array('errorMsg'=>$show_ket);
                echo json_encode($keterangan);
            }else{
                $user = $_SESSION['user_name'];            
                $sql = "UPDATE MAPPING_PLANT_DIST_SPPS SET PLANT = '$plant', DISTRIBUTOR = '$distributor', DISTRIK = '$distrik', FORMAT_SPPS = '$format_spps', UPDATED_AT = SYSDATE , UPDATED_BY = '$user' WHERE ID = '$ID'";
                // var_dump($sql);exit;
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
            if ($result){
                $show_ket = "Data berhasil di update <br>";
                $keterangan = array('success'=>$show_ket);
            } else {
                $show_ket = "Data Gagal di update <br>";
                $keterangan = array('errorMsg'=>$show_ket);
            }
            echo json_encode($keterangan);
            }          
        }
        break;
     case 'upload_file' :
     {
        error_reporting(E_ALL ^ E_NOTICE);
        require_once '../ex_report/excel_reader2.php';

        $allowedExts = "xls";
        $extension = end(explode(".", $_FILES["file_upload"]["name"]));
        if ($extension==$allowedExts) {
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file_upload']['tmp_name']);
            $jumlah_row = $cell->rowcount($sheet_index=0);
            $jumlah_col = $cell->colcount($sheet_index=0);
            $kode_file  = $cell->val( 1,2 );
            for ($i = 6; $i <= $jumlah_row; $i++) {
                for ($j = 1; $j <= 5; $j++) {                    
                    $ke = $i-6;
                    $data[$ke][$j]= $cell->val( $i,$j );
                }
            }
            $messData = importDataExcel($conn, $data);

            if (isset($messData['status'])){
                if ($messData['status'] == 1 ) {
                    $show_ket = "Data Berhasil di Simpan <br>".$messData['message'];
                    $keterangan = array('success'=>$show_ket);
                } else {
                    $show_ket = "Data Gagal di Simpan <br>".$messData['message'];
                    $keterangan = array('errorMsg'=>$show_ket);
                }
            } else {
                $show_ket = "Data Gagal di Simpan <br>".$messData['message'];
                $keterangan = array('errorMsg'=>$show_ket);
            }
        } else {
            $show_ket = "Invalid file...!! <br>";
            $keterangan = array('errorMsg'=>$show_ket);
        }
        echo json_encode($keterangan);
     }
     break;
        case 'exportMappingPlantDistSpps':
            $sql = "SELECT 
                        ma.*,
                        kota.NM_KOTA AS CITY_NAME,
                        ma.FORMAT_SPPS || '/' ||
                        TO_CHAR(ma.CREATED_AT, 'MMYYYY') || '/' ||
                        LPAD(ma.LAST_SEQUENCE, 4, '0') AS FORMAT_SPPS_FULL
                    FROM MAPPING_PLANT_DIST_SPPS ma
                    LEFT JOIN ZREPORT_M_KOTA kota
                        ON ma.DISTRIK = kota.KD_KOTA
                    WHERE ma.DEL = 0 ORDER BY ma.ID ASC";
            $parse = oci_parse($conn, $sql);
            oci_execute($parse);
            $datain = array();
            while ($row = oci_fetch_array($parse, OCI_ASSOC + OCI_RETURN_NULLS)) {
                $datain[] = $row;
            }

            // $datain = $_POST['data'];
            $total = count($datain);
            $namafile = "mapping_plant_dist_spps.xls";
            send($namafile);
            $WritePHPExcel = new PHPExcel();
            $WritePHPExcel->setActiveSheetIndex(0);
            $colomSt = 'A';
            $WritePHPExcel->getActiveSheet()->setTitle('MAPPING PLANT DIST SPPS'); //title sheet
            $Worksheet1 = $WritePHPExcel->getActiveSheet();

            //head excel
            $Worksheet1->setCellValueByColumnAndRow(0, 1, 'No.');
            $Worksheet1->setCellValueByColumnAndRow(1, 1, 'PLANT');
            $Worksheet1->setCellValueByColumnAndRow(2, 1, 'DISTRIBUTOR');
            $Worksheet1->setCellValueByColumnAndRow(3, 1, 'DISTRIK');
            $Worksheet1->setCellValueByColumnAndRow(4, 1, 'NAMA DISTRIK');
            $Worksheet1->setCellValueByColumnAndRow(5, 1, 'FORMAT SPPS');
            $Worksheet1->setCellValueByColumnAndRow(6, 1, 'CREATED AT');
            $Worksheet1->setCellValueByColumnAndRow(7, 1, 'CREATED BY');
            $Worksheet1->setCellValueByColumnAndRow(8, 1, 'UPDATED AT');
            $Worksheet1->setCellValueByColumnAndRow(9, 1, 'UPDATED BY');

            $colomAk = $Worksheet1->getHighestColumn();
            if ($colomSt != '' && $colomAk != '') {
                $WritePHPExcel->getActiveSheet()->getStyle($colomSt . "1:" . $colomAk . "1")->getFont()->setBold(true); //bold header
            }
            $Worksheet1->getStyle($colomSt . '1:' . $colomAk . '1')->applyFromArray($styleHead); //style head         
            // Version 4 fixed
            for ($col = $colomSt; $col != $colomAk; $col++) {
                $Worksheet1->getColumnDimension($col)->setAutoSize(true); //auto size
            }
            $i = 0;
            $j = 2;
            foreach ($datain as $key => $valuef) {
                //setROW
                if ($colomSt != '' && $colomAk != '') {
                    $Worksheet1->getStyle($colomSt . $j . ':' . $colomAk . $j)->applyFromArray($styleBorder); //style border record
                }

                $Worksheet1->setCellValueByColumnAndRow(0, $j, $i + 1);
                $Worksheet1->setCellValueByColumnAndRow(1, $j, $valuef[PLANT]);
                $Worksheet1->setCellValueExplicitByColumnAndRow(2, $j, $valuef[DISTRIBUTOR], PHPExcel_Cell_DataType::TYPE_STRING);
                $Worksheet1->setCellValueByColumnAndRow(3, $j, $valuef[DISTRIK]);
                $Worksheet1->setCellValueByColumnAndRow(4, $j, $valuef[CITY_NAME]);
                $Worksheet1->setCellValueByColumnAndRow(5, $j, $valuef[FORMAT_SPPS_FULL]);
                $Worksheet1->setCellValueByColumnAndRow(6, $j, $valuef[CREATED_AT]);
                $Worksheet1->setCellValueByColumnAndRow(7, $j, $valuef[CREATED_BY]);
                $Worksheet1->setCellValueByColumnAndRow(8, $j, $valuef[UPDATED_AT]);
                $Worksheet1->setCellValueByColumnAndRow(9, $j, $valuef[UPDATED_BY]);
                $i++;
                $j = $j + 1;
            }
            $objWriter = new PHPExcel_Writer_Excel5($WritePHPExcel);
            $objWriter->save("php://output");
            break;
    }
}
?>
