<?

session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$targetVolume='cMappingPlantDistSpps.php';
$titlepage='Mapping Plant Distributor SPPS';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$distr_id=$_SESSION['distr_id'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);
// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
// ?>
// 				<SCRIPT LANGUAGE="JavaScript">				
// 					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
// 				</SCRIPT>
         <!--<a href="../index.php">Login....</a>-->
// <?

// exit();
// }


// ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<style type="text/css">
      #outtable{
        padding:1px;
        border:1px solid #e3e3e3;
        width:600px;
        border-radius: 5px;
      }
 
      .short{
        width: 50px;
      }
 
      .normal{
        width: 150px;
      }
      .tabel_1{
        border-collapse: collapse;
        font-family: arial;
        color:#5E5B5C;
      }

      .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .btn:not([disabled]) {
        color: white;
    }

    .icon-upload {
     background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
     background: transparent url("icon/excel.png") no-repeat scroll center center;
    }

    .icon-mail {
     background: transparent url("icon/send-mail.png") no-repeat scroll center center;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }
 
      thead th{
        text-align: left;
        padding: 7px;
      }
 
      tbody td{
        border-top: 1px solid #e3e3e3;
        padding: 7px;
      }

      
</style>
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>
<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:1500px;height:350px">
        <thead>
            <tr>
                <th field="ck" checkbox="true"></th>
                <th field="PLANT" width="100">PLANT</th>     
                <th field="DISTRIBUTOR" width="200">DISTRIBUTOR</th>
                <th field="DISTRIK" width="200">DISTRIK</th>
                <th field="CITY_NAME" width="200">DISTRIK NAME</th>
                <th field="FORMAT_SPPS_FULL" width="200">LAST FORMAT SPPS</th>
                <th field="LAST_SEQUENCE" width="200">LAST SQUENCE NUMBER</th>
                <th field="CREATED_AT" width="100">CREATED AT</th>
                <th field="CREATED_BY" width="150">CREATED BY</th>
                <th field="UPDATED_AT" width="100">UPDATED AT</th>
                <th field="UPDATED_BY" filterable width="150">UPDATED BY</th>

            </tr>
        </thead>
    </table>
    <div id="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" onclick="newAct()" plain="true">Add</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="updateAppAct()">Edit</a> 
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" plain="true" onclick="cancelAct()">delete</a>
            <a class="easyui-linkbutton" plain="true" iconCls="icon-upload" href="template_xls/template_mapping_plant_dist_spps.xls" >Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-excel" onclick="uploadAct()">Upload Excel</a>
            <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>
    </div>
    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true" buttons="#dlg-buttons">
        <div class="ftitle"><?=$titlepage;?></div>
            <form id="fm" method="post" novalidate>
                <input type="hidden"  id="ID" name="ID">
                <div class="fitem">
                    <label>Plant</label>
                    <input type="text" class="easyui-combogrid" id="plant" name="plant" style="width:200px;" required="true">
                    <i id="plant_error" style="color:red;"></i>
                </div>
                <div class="fitem">
                    <label>Distributor</label>
                    <input type="text" class="easyui-textbox" id="distributor" name="distributor" style="width:200px;" required="true">
                    <i id="dist_error" style="color:red;"></i>
                </div>
                <div class="fitem">
                    <label>Distrik</label>
                    <input type="text" class="easyui-textbox" id="distrik" name="distrik" style="width:200px;" >
                    <i id="distrik_error" style="color:red;"></i>
                </div>
                <div class="fitem">
                    <label>Format SPPS</label>
                    <input type="text" class="easyui-textbox" id="format_spps" name="format_spps" style="width:200px;" required="true" data-options="prompt:'Contoh Format : SPP/PLB-GSK'">
                    <i id="format_spps_error" style="color:red;"></i>
                </div>    
            </form>
        </div>

        <div id="dlg-buttons">  
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px" id="close">Cancel</a>
        </div>
    </div>

    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload" name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px" id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>

<script type="text/javascript">
    $('#filterPeriode').combobox({
        url:'cMappingPlantDistSpps.php?act=filterPeriode',
        editable: true,
        valueField: 'PERIODE',
        textField: 'PERIODE',
        panelHeight: 130,
    });
    
    $(function(){
        $("#dg").datagrid({
            url:'cMappingPlantDistSpps.php?act=show',
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar',
            onLoadSuccess: function(data) {
                if (!data.rows || data.rows.length === 0) {
                    $('#btnExport').hide();  // sembunyikan tombol jika data kosong
                } else {
                    $('#btnExport').show();  // tampilkan tombol jika ada data
                }
            }
        });

        $('#dg').datagrid('enableFilter');    
    });

    function aksiGet(value) {
        $('#dg').datagrid({
            url:'cMappingPlantDistSpps.php?act=show&filter_status='+value
        });
    }

    $('#plant').combogrid({
        panelWidth:200,
        url:'cSourcePlant.php?act=getPlant',
        idField:'WERKS',
        textField:'NAME1',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var kode = row.WERKS;
            getMat(kode) ;
        },
        columns:[[
        {field:'WERKS',title:'PLANT',align:'PLANT',width:50},
        {field:'NAME1',title:'NAMA PLANT',align:'NAMA PLANT',width:150},
        ]]
    });

    $('#distributor').combogrid({
        panelWidth:400,
        url:'cShiptoEx.php?act=getDistributor',
        idField:'KUNNR',
        textField:'NAME1',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var nama = row.NAME1;
        },
        columns:[[
        {field:'KUNNR',title:'DISTRIBUTOR CODE',align:'DISTRIBUTOR CODE',width:200},
        {field:'NAME1',title:'DISTRIBUTOR NAME',align:'DISTRIBUTOR NAME',width:200},
        ]]
    });

    $('#distrik').combogrid({
        panelWidth:500,
        url:'cMappingPlantDistSpps.php?act=getKota',
        idField:'KODE_DISTRIK',
        textField:'NAMA_DISTRIK',
        fitColumns:true,
        mode:'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index,row){
            var nama = row.NM_KOTA;
            $('#ktName').val(nama);
        },
        columns:[[
        {field:'KODE_DISTRIK',title:'KODE DISTRIK',align:'KODE DISTRIK',width:100},
        {field:'NAMA_DISTRIK',title:'NAMA DISTRIK',align:'NAMA DISTRIK',width:100}
        ]]
    });


    $("#btnExport").click(function() {        
        var myData = $('#dg').datagrid('getData');        
        var mapForm = document.createElement("form");
        mapForm.id = "formexport";
        mapForm.target = "dialogSave";
        mapForm.method = "POST";
        mapForm.action = "cMappingPlantDistSpps.php?act=exportMappingPlantDistSpps";        
        $.each(myData.rows, function(k,v){
            $.each(v, function(k2, v2){
                var hiddenField = document.createElement("input");              
                hiddenField.type = "hidden";
                hiddenField.name = "data[" + k + "][" + k2 + "]";
                hiddenField.value = v2;
                mapForm.appendChild(hiddenField);
            });
        });            
        document.body.appendChild(mapForm);
        mapForm.submit();
        document.body.removeChild(mapForm);
        
    });

var url;



function filterData() {
        $('#dialog_filter').dialog('open').dialog('setTitle', 'Filter');
        $('#form_search').form('clear');
    }

function filter_search() {        
        var periode = $('#prd').val();
        var brand = $('#filterBrand').val();
        var prioritas = $('#filterPrioritas').val();
        var plant =  $('#filterPlant').val();
        var distrik = $('#filterDistrik').val();
        var material = $('#filterMaterial').val();
        var incoterm = $('#filterIncoterm').val();
        
        if (periode == '') {
            alert('Kolom Peirode tidak boleh kosong');
            $('#dialog_filter').dialog('close');
        }

        $("#dg").datagrid("reload", {            
            filterPeriode: periode,
            filterBrand: brand,
            filterPrioritas: prioritas,
            filterPlant:plant,
            filterDistrik:distrik,
            filterMaterial:material,
            filterIncoterm:incoterm
        });
        
        $('#dialog_filter').dialog('close');
        // $('#<?=$id?>-lod').dialog('close');
    }

function newAct(value){
    $('#dlg').dialog('open').dialog('setTitle','Create');
    $("#hideshipto").show();
    $('#fm').form('clear');
    $("#soldto").textbox('setValue', value);

    // balikin field ke normal (editable + putih)
    $("#plant").textbox('readonly', false)
               .textbox('textbox').css('background-color','#ffffff');

    $("#distributor").textbox('readonly', false)
                     .textbox('textbox').css('background-color','#ffffff');

    $("#distrik").textbox('readonly', false)
                 .textbox('textbox').css('background-color','#ffffff');

    url = 'cMappingPlantDistSpps.php?act=add';
}

function editAct(){
    var row = $('#dg').datagrid('getSelected');
    if (row){
        $('#dlg').dialog('open').dialog('setTitle','Edit');
        var idnh = row.ID;
        $('#fm').form('load',row);
        $('#ORG').combo('readonly', true);
        $('#TIPE_SEMEN').combo('readonly', true);
        $('#DISTRIK').combo('readonly', true);
        $('#BULAN').combo('readonly', true);
        $('#TAHUN').combo('readonly', true);
        url = 'cMappingPlantDistSpps.php?act=edit&id='+row.ID;
    }
}

function styleQuary(val,row){
        if(val=='' || val == null){
            return '-';
        }else{
               return '<div style="background-color:lightblue; color:#1b1c1c; text-align: center;">'+val+'</div>'; 
        }
    } 

    function styleHarian(val,row){
        if(val=='' || val == null){
            return '-';
        }else{
            return '<div style="background-color:#eaebd1; color:#1b1c1c; text-align: center;">'+val+'</div>'; 
        }
    } 

    function styleBulanan(val,row){
        if(val=='' || val == null){
            return '-';
        }else{
            return '<div style="background-color:#eaebd1; color:#1b1c1c; text-align: center;">'+val+'</div>'; 
        }
    } 

    function styleBulananOvr(val,row){
        if(val=='' || val == null){
            return '-';
        }else{
            return '<div style="background-color:#f75964; color:#1b1c1c; text-align: center;">'+val+'</div>'; 
        }
    } 

function updateAct(){
    var id_target   = [''];
    var edit_target = [''];
    
    var id_target_input     = document.getElementsByName('id_target[]');
    var edit_target_input   = document.getElementsByName('edit_target[]');

    for (var i = 0; i < id_target_input.length; i++) {
        var id_target_2 = id_target_input[i];
        id_target[i]    = id_target_2.value;

        var edit_target_2   = edit_target_input[i];
        edit_target[i]      = edit_target_2.value;
    }

    $('#fm_detail').form('submit',{
        url: 'cMappingPlantDistSpps.php?act=editAdm&id_target_ku='+id_target+'&edit_target_ku='+edit_target,
        onSubmit: function() {
            if($(this).form('validate')){
                $.messager.progress({
                    title:'Please waiting',
                    msg:'Loading data...'
                });
            }
            return $(this).form('validate');
        },
        success: function(result){        
            var result = eval('('+result+')');
            if (result.errorMsg){
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg
                });
                $.messager.progress('close');
                $('#dlg_detail').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            } else {
                $.messager.show({
                    title: 'Success',
                    msg: result.success
                });

                $.messager.progress('close');
                $('#dlg_detail').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            }
        }
    });
}


function saveAct(){
    $('#fm').form('submit',{
        url: url,
        onSubmit: function(){ 
            return $(this).form('validate');
        },
        success: function(result){
            var result = eval('('+result+')');
            if (result.errorMsg){
                $.messager.show({
                    title: 'Error',
                    msg: result.errorMsg
                });
            } else {
                $.messager.show({
                    title: 'Success',
                    msg: 'Success Insert Data'
                });
                $('#dlg').dialog('close'); // close the dialog
                $('#dg').datagrid('reload'); // reload the user data
            }
        }
    });
}

function cek(val,row){
        if(val=='1'){
            // return "<span style='color:yellow;'>Waiting Approve</span>";
            return `<span><button style="width:120px" class="btn yellow">Waiting Approve</button></span>`;
                                
        }else if (val=='2') {
            return `<span><button style="width:120px" class="btn green">Approved</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        } else {
            // return "<span style='color:red;'>Rejected</span>";
            return `<span><button style="width:120px" class="btn red">Rejected</button></span>`;
        } 
            
    }

function cancelAct(){
    var row = $('#dg').datagrid('getSelections');
    if (row){
        $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
        if (r){
            $.post('cMappingPlantDistSpps.php?act=del&',{data:row},function(result){
            if (result.success){
                $('#dg').datagrid('reload'); // reload the user data
                $.messager.show({ // show success message
                title: 'Success',
                msg: result.success
                });
            } else {
                $.messager.show({ // show error message
                title: 'Error',
                msg: result.errorMsg
                });
            }
            },'json');
        }
        });
    } else {
        $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
    }
}

// function deleteAct(){
//     var row = $('#dgcc').datagrid('getSelected');
//     if (row){
//         $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
//         if (r){
//             $.post('cSourcePlant.php?act=delcc&',{id:row.ID},function(result){
//             if (result.success){
//                 $('#dgcc').datagrid('reload'); // reload the user data
//             } else {
//                 $.messager.show({ // show error message
//                 title: 'Error',
//                 msg: result.errorMsg
//                 });
//             }
//             },'json');
//         }
//         });
//     } else {
//         $.messager.alert('Confirm','Pilih data yang akan di Hapus !', 'info');
//     }
// }

function updateAppAct(){
    var row = $('#dg').datagrid('getSelected');
    var rows = $('#dg').datagrid('getSelections');

    if(rows.length > 1)
    {
        $.messager.alert('Error', 'Select only one data to be edited', 'error');
        return
    }
console.log(row);
    if(row) {
            $("#dlg").dialog('open').dialog('setTitle', 'Edit');
            $("#fm").form('clear');
            $("#fm").form("load", row);
            var row = $('#dg').datagrid('getSelected');
           $("#plant").textbox('setValue', row.PLANT)
                   .textbox('readonly', true)
                   .textbox('textbox').css('background-color','#f0f0f0');
        
            $("#distributor").textbox('setValue', row.DISTRIBUTOR)
                            .textbox('readonly', true)
                            .textbox('textbox').css('background-color','#f0f0f0');
            
            $("#distrik").textbox('setValue', row.DISTRIK)
                        .textbox('readonly', true)
                        .textbox('textbox').css('background-color','#f0f0f0');
            $("#format_spps").textbox('setValue', row.FORMAT_SPPS);
            url = 'cMappingPlantDistSpps.php?act=updateApp';
            
        }
        else {
            $.messager.alert('Error', 'Select one of the data to be edited', 'error');
        }
}

function uploadAct() {
    $('#dlg_upload').dialog('open').dialog('setTitle','Upload Mapping Soldto FIOS');
    $('#uploadForm').form('clear');
    // url = 'cSourcePlant.php?act=upload_file';
}

function saveUploadAct() {
        $('#uploadForm').form('submit',{
            url: 'cMappingPlantDistSpps.php?act=upload_file',
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.success
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
  
}

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
