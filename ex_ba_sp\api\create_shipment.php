<?php
include ('../../include/ex_fungsi.php');

foreach ($_POST as $k => $v) {
    if (strpos($k, 'amp;') === 0) {
        $clean_key = str_replace('amp;', '', $k);
        $_POST[$clean_key] = $v;
        unset($_POST[$k]);
    }
}

// --- Konfigurasi username & password ---
$valid_users = array(
    "smbr-jaya" => "i-love-smbr"
);

// --- <PERSON>bil credential dari header Authorization ---
if (!isset($_SERVER['PHP_AUTH_USER']) || !isset($_SERVER['PHP_AUTH_PW'])) {
    header('WWW-Authenticate: Basic realm="My API"');
    header('HTTP/1.0 401 Unauthorized');
    echo json_encode(array("status" => "401", "message" => "Authentication required"));
    exit;
}

$username = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

// --- Validasi login ---
if (!isset($valid_users[$username]) || $valid_users[$username] != $password) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("status" => "403", "message" => "Invalid credentials"));
    exit;
}

// --- Cek X-CSRF-Token di header ---
if (!isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("status" => "403", "message" => "Missing X-CSRF-Token header"));
    exit;
}

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

function generate_no_shp_trn($conn) {
    $sql = "SELECT MAX(TO_NUMBER(SUBSTR(NO_SHP_TRN, 2))) AS MAX_NO 
            FROM DEV.EX_TRANS_HDR 
            WHERE NO_SHP_TRN LIKE 'S%'";
    $stmt = oci_parse($conn, $sql);
    oci_execute($stmt);

    $max_no = 0;
    $row = oci_fetch_array($stmt, OCI_ASSOC);
    if ($row && isset($row['MAX_NO'])) {
        $max_no = (int)$row['MAX_NO'];
    }

    $next_no = $max_no + 1;
    $next_no_padded = str_pad($next_no, 9, "0", STR_PAD_LEFT);
    return 'S' . $next_no_padded;
}

function is_date_column($col) {
    return preg_match('/TANGGAL|DATE|TGL/i', $col);
}

function write_log($message, $filename = 'log.txt') {
    $log_file = dirname(__DIR__) . '/' . $filename;
    $date = date('Y-m-d H:i:s');
    $entry = "[$date] $message" . PHP_EOL;

    $fh = fopen($log_file, 'a');
    if ($fh) {
        fwrite($fh, $entry);
        fclose($fh);
    }
}

$columns = array(
    'NO_SHP_EX',
    'PLANT',
    'SOLD_TO',
    'SHIP_TO',
    'SAL_DISTRIK',
    'SAL_GROUP',
    'SAL_OFFICE',
    'VENDOR',
    'NAMA_VENDOR',
    'NAMA_SAL_OFF',
    'NAMA_SAL_GRP',
    'NAMA_SAL_DIS',
    'NO_POL',
    'VEHICLE_TYPE',
    'WARNA_PLAT',
    'KODE_PRODUK',
    'NAMA_PRODUK',
    'KODE_KANTONG',
    'NAMA_KANTONG',
    'KODE_SEMEN',
    'NAMA_SEMEN',
    'QTY_SHP',
    'TIPE_TRANSAKSI',
    'TANGGAL_KIRIM',
    'CREATED_BY',
    'NAMA_PLANT',
    'STATUS_APP',
    'NAMA_SOLD_TO',
    'NAMA_SHIP_TO',
    'ALAMAT_SHIP_TO',
    'SATUAN_SHP',
    'KELOMPOK_TRANSAKSI',
    'KAPASITAS_VEHICLE',
    'UOM_KAPASITAS',
    'PLANT_RCV',
    'ORG_RCV',
    'DOC_SHP',
    'TIPE_DO',
    'INCO',
    'NAMA_KAPAL',
    'SUPIR',
    'KODE_KECAMATAN',
    'NAMA_KECAMATAN',
    'MODE_TRANSPORT',
    'NO_SO',
    // 'QTY_KTG_RUSAK',
    // 'QTY_SEMEN_RUSAK',
    // 'TOTAL_KTG_RUSAK',
    // 'TOTAL_KTG_REZAK',
    // 'TOTAL_KLAIM_KTG',
    // 'TOTAL_SEMEN_RUSAK',
    // 'HARGA_TEBUS',
    // 'PDPKS',
    // 'TOTAL_KLAIM_SEMEN',
    // 'TOTAL_KLAIM_ALL',
    // 'KETERANGAN_POD',
    // 'EVIDENCE_POD1',
    // 'EVIDENCE_POD2',
    // 'GEOFENCE_POD',
    // 'TANGGAL_DATANG',
    // 'TANGGAL_BONGKAR'
);

// nomor transaksi smbr
$no_shp_ex = $_POST['NO_SHP_EX'];

// generate nomor transaksi csms
$columns[] = 'NO_SHP_TRN';
$no_shp_trn = generate_no_shp_trn($conn);
$_POST['NO_SHP_TRN'] = $no_shp_trn;

$columns[] = 'ORG';
$_POST['ORG'] = '1000';

$columns[] = 'NAMA_ORG';
$_POST['NAMA_ORG'] = 'PT SEMEN BATURAJA';

$columns[] = 'STATUS';
$_POST['STATUS'] = 'DRAFT';

$columns[] = 'STATUS2';
$_POST['STATUS2'] = 'DRAFT';

$columns[] = 'DELETE_MARK';
$_POST['DELETE_MARK'] = '0';

// $columns[] = 'FLAG_POD';
// $_POST['FLAG_POD'] = 'POD-FIOS';

$columns[] = 'CREATE_DATE';
$_POST['CREATE_DATE'] = date('Y-m-d H:i:s');

// Siapkan array
$insert_columns = array();
$insert_placeholders = array();
$bind_values = array();

// avoid duplicate shipment
$sql = "SELECT COUNT(*) AS JML FROM EX_TRANS_HDR WHERE NO_SHP_TRN = :no_shp";
$stmt = oci_parse($conn, $sql);
oci_bind_by_name($stmt, ":no_shp", $no_shp_ex);
oci_execute($stmt);
$row = oci_fetch_assoc($stmt);
if($row['JML'] > 0){
    header("HTTP/1.1 409 Conflict");
    echo json_encode(array(
        "status" => 409,
        "message" => "Gagal Save, Shipment($no_shp_ex) sudah ditambahkan sebelumnya"
    ));
    exit;
}

foreach ($columns as $col) {
    if (!isset($_POST[$col]) || trim($_POST[$col]) === '') {
        continue; // skip kolom yang tidak dikirim dari form
    }

    $value = $_POST[$col];

    // Format tanggal
    if (is_date_column($col)) {
        $timestamp = strtotime($value);
        if ($timestamp !== false) {
            $value = strtoupper(date('d-M-Y H:i:s', $timestamp));
            $insert_placeholders[] = "TO_DATE(:$col, 'DD-MON-YYYY HH24:MI:SS')";
        } else {
            $insert_placeholders[] = ":$col";
            $value=null;
        }
    } else {
        $insert_placeholders[] = ":$col";
    }

    $insert_columns[] = $col;
    $bind_values[$col] = $value;
}

if (count($insert_columns) === 0) {
    echo json_encode(array('success' => false, 'msg' => 'Tidak ada data yang dikirim.'));
    exit;
}

$sql = "INSERT INTO EX_TRANS_HDR (" . implode(',', $insert_columns) . ") VALUES (" . implode(',', $insert_placeholders) . ")";
$stmt = oci_parse($conn, $sql);

// cek bind value
// foreach ($bind_values as $key => $val) {
//     write_log("Bind $key => " . var_export($val, true));
// }

// Bind variabel
foreach ($bind_values as $key => &$val) {
    oci_bind_by_name($stmt, ":$key", $val);
}

// Eksekusi
$result = oci_execute($stmt);

if ($result) {
    echo json_encode(array('success' => true, 'msg' => "Berhasil Create Shipment($no_shp_ex) dengan NO_SHP_TRN: " . $no_shp_trn));
} else {
    $e = oci_error($stmt);
    if (!$e) {
        $e = oci_error();
    }
    $error_msg = isset($e['message']) ? $e['message'] : 'Unknown Error';

    echo json_encode(array('success' => false, 'msg' => "Gagal Create Shipment($no_shp_ex): " . $error_msg));
}
?>
