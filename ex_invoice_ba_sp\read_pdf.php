<?php
// PHP 5.2 Compatible
function invoiceSpDendaTanggalSama($no_ba) {
    $urlPrint = "http://************/dev/sd/sdonline/ex_invoice_ba_sp/print_invoice_sp_denda_tgl_sama.php?no_ba=$no_ba";
    $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

    // Ambil HTML dari $urlPrint
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlPrint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        die('CURL ERROR (HTML): ' . curl_errno($ch) . ' - ' . curl_error($ch));
    }
    curl_close($ch);

    // Siapkan data untuk PDF
    $headers = array(
        'Content-Type: application/json'
    );

    $dataRenderPdf = array(
        'content' => $response
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlConverter);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSLVERSION, 3);

    $pdfResponse = curl_exec($ch);
    if (curl_errno($ch)) {
        die('CURL ERROR (PDF): ' . curl_errno($ch) . ' - ' . curl_error($ch));
    }
    curl_close($ch);

    return $pdfResponse;
}

// Ambil parameter dari URL
if (!isset($_GET['no_ba'])) {
    die("Parameter 'no_ba' diperlukan.");
}

$no_ba = $_GET['no_ba'];

// Panggil fungsi
$pdfContent = invoiceSpDendaTanggalSama($no_ba);

// Tampilkan ke browser sebagai PDF
header('Content-Type: application/pdf');
header('Content-Disposition: inline; filename="invoice.pdf"');
header('Content-Length: ' . strlen($pdfContent));
echo $pdfContent;
exit;
?>
