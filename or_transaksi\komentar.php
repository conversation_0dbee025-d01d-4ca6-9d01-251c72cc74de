<?php 
session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);
$_SESSION['status_selesai'];
$link_redirect=$_SESSION['linkredirect'];
print_r($habis);
// mulai session untuk semua formula
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
// if($user_id==""){
if(false){
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
.style1 {font-size: 20px}
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<script language=javascript>



document.onkeydown = function(e) {
    if ((e.which || e.keyCode) == 116) { // 116 is the key code for F5
        e.preventDefault(); // Prevent default F5 behavior
        return false;
    }
};
window.addEventListener('beforeunload', function(event) {
    // Display a confirmation message to the user
    event.returnValue = false;
  
    // Or, perform custom logic here before the page unloads
});
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

function popUp(URL) 
{
	day = new Date();
	id = day.getTime();
	eval("page" + id + " = window.open(URL, '" + id + "', 'toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=1,width=900,height=600,left = 34,top = 102');");
}

</script>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Untitled Document</title>
</head>

<body>
<p>&nbsp;</p>
<p>&nbsp;</p>


<? 
$aksicetak='';
include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$bo_conn=$fungsi->bo_koneksi();
$action = isset($_POST['action']) ? $_POST['action'] : '';
$sudahsimpan=0;
//echo $_POST[''];

include ('formula.php');

?>
<div align="center" class="login">
<?
echo isset($show_ket) ? $show_ket : '';

?>
<br/>
<? if(isset($_REQUEST['no_pp']) && $user_org=='3000')
{
?>
<form action='list_approve_ppsp.php' method='post'>
                <input type='submit' name='cari' class='button' value='Back'>
<?
}
else
{
?>   
<a href="<? echo $habis ?>">&lt;&lt; &nbsp;Back&nbsp;&gt;&gt;</a>
<?
}
if($aksicetak!=''){
?>
<a href="javascript:popUp('<?=$aksicetak;?>')">&lt;&lt; &nbsp;Print&nbsp;&gt;&gt;</a>
<?
}
?>
</div>
</p>
<p>&nbsp;</p>
<p>&nbsp;</p>

<script language=javascript>
setTimeout(function () {
   window.location.href= "<?php echo $habis;?>"; // the redirect goes here

	},5000); // 59 seconds
</script>

<script type="text/javascript">
	


	var ketahuan = <?= $data_lempar ? $data_lempar : ""?>;
	if(ketahuan != ""){
		console.log(ketahuan);
	}
</script>

</body>
