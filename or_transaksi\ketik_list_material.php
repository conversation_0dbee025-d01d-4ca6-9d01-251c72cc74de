<? 
session_start();
include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$produk=$_GET['produk'];
$nourut=$_GET['nourut']; 
$plant1=$_GET['plant'];
$org=$_SESSION['user_org'];
$pr =$_GET['pr'];
$unit=$_GET['unit'];
if ($org!="7900"){
$orgnew=substr($plant1,0,1)."000";
} else {
$orgnew = $org;
}

if ($produk != ""){

    $kode = $_REQUEST['kode'];
    $nama = $_REQUEST['nama'];
    if ($org!="7900"){
    $orgcek = substr($plant,0,1)."000";
    } else {
    $orgcek = $org;
    }

    $mp_coics=$fungsi->getComin($conn,$org);

    // var_dump($mp_coics);
    if(count($mp_coics)>0){
        unset($inorg);$orgcounter=0;
        foreach ($mp_coics as $keyOrg => $valorgm){
            $inorg .="'".$keyOrg."',";
            $orgcounter++;
        }
        $inorg= rtrim($inorg, ',');        
    }else{
    $inorg= $org;
    }


    $sqlcek = "SELECT KODE_MATERIAL FROM OR_MAP_DISTRIK_MAT WHERE ORG = '$orgcek' AND DISTRIK = '$distrik' AND PLANT = '$plant1' AND DELETE_MARK = '0'";
    $querycek = oci_parse($conn, $sqlcek);
    oci_execute($querycek);
    $kode_mat_blacklist = array();
    while($datacek=oci_fetch_assoc($querycek)){
        array_push($kode_mat_blacklist, $datacek[KODE_MATERIAL]);
    }

    if ($brand == 'CURAH' || $brand == 'MORTAR ZAK' || $brand == 'MORTAR CURAH' ) {
        $sql = "SELECT DISTINCT
		scm.BRAND,
		scm.MATERIAL AS MATNR,
		sal.MAKTX AS MAKTX,
		UPPER( sal.MEINS ) AS MEINS 
        FROM
            ZSD_TARGET_HEADER_SCM scm 
            LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = scm.MATERIAL
        WHERE
        sal.VKORG IN ($inorg) AND scm.BRAND = '$brand' AND  scm.FLAG_DEL != 'Y' AND  ROWNUM <= 1";
        if ($produk) {
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= "sal.MATNR = '".$produk."'";
        }
        $sql .= "AND ROWNUM <= 1";
    }else{
        //fungsi oracle
        // $sql = "SELECT DISTINCT
        // mm.BRAND,
        // mm.KODE_MATERIAL AS MATNR,
        // sal.MAKTX as MAKTX,
        // UPPER(sal.MEINS) as MEINS
    
        // FROM
        // MAPPING_MATERIAL_BRAND mm
        // LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = mm.KODE_MATERIAL 
        // LEFT JOIN ZSD_TARGET_HEADER_SCM scm ON scm.MATERIAL = sal.MATNR
        // WHERE
        // sal.VKORG IN ($inorg) AND mm.BRAND = '$brand' AND  mm.FLAG_DEL != 'Y'";
        // if ($produk) {
        //     $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
        //     $sql .= "sal.MATNR = '".$produk."'";
        // }
        // if ($unit) {
        //     if($unit == 'ZAK'){
        //         $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
        //         $sql .= "sal.MEINS IN ('".$unit."','BAG')";
        //     } else {
        //         $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
        //         $sql .= "sal.MEINS = '".$unit."'";
        //     }
        // }
        // $sql .= "AND ROWNUM <= 1";

        //fungsi oracle
        $sql = "SELECT scm.BRAND, scm.MATERIAL AS MATNR, sal.MAKTX, upper(sal.MEINS) AS MEINS
            FROM ZSD_TARGET_HEADER_SCM scm
            LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = scm.MATERIAL
        WHERE
        --sal.VKORG IN ($inorg) AND 
        MATNR LIKE '$produk' AND scm.BRAND = '$brand' AND  scm.FLAG_DEL != 'Y' AND scm.PERIODE='".date('m-Y')."'
        ";
        if ($kode) {
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= "sal.MATNR LIKE '%".$produk."%'";
        }
        if ($nama) {
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= "UPPER(sal.MAKTX) LIKE '%".strtoupper($nama)."%'";
        }
        if($distrik){
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= "scm.DISTRIK = '".$distrik."'";
        }
        if($incoterm){
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= "scm.incoterm = '".$incoterm."'";
        }
        if($so_type){
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= $so_type == 'ZPR' ? "scm.tipe_order = '1'" : "coalesce(scm.tipe_order,'0') != '1'";
        }
        if ($unit) {
                if($unit == 'ZAK'){
            $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
            $sql .= "sal.MEINS IN ('".$unit."','BAG')";
                } else {
                $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                $sql .= "sal.MEINS = '".$unit."'";
            }

        }
	    $sql .= "GROUP BY scm.BRAND, scm.MATERIAL, sal.MAKTX, upper(sal.MEINS)";
        
    }

        
    // var_dump($sql);
    $query= oci_parse($conn, $sql);
    oci_execute($query);
    while($datafunc=oci_fetch_assoc($query)){
        if(in_array($datafunc["MATNR"], $kode_mat_blacklist)){
                continue;
        } else{
            $matnr    = $datafunc["MATNR"];
            $nama_matnr   = $datafunc["MAKTX"];
            $uom      = $datafunc["MEINS"];

            if(strtoupper($datafunc["GEWEI"])=='KG'){
                 $qtymto=@(number_format($datafunc["NTGEW"]/1000,2));
            }else{
                 $qtymto=number_format($datafunc["NTGEW"]);
            }
            $quantumTO[]    = $qtymto;
        }
    }

/*include('../include/sapclasses/sap.php');
$sap = new SAPConnection();
$sap->Connect("../include/sapclasses/logon_data.conf");
if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
if ($sap->GetStatus() != SAPRFC_OK ) {
   echo $sap->PrintStatus();
   exit;
}

        $fce = $sap->NewFunction ("Z_ZAPPSD_LIST_MATERIAL_SALES");
        if ($fce == false ) {
           $sap->PrintStatus();
           exit;
        }

        //header entri
        $fce->I_VKORG = $org;
        $fce->I_MATNR = $produk;
        $fce->I_WERKS = $plant1;
        $fce->I_MEINS = $unit; 
        
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {		
                $fce->IT_OUT->Reset();
                while ( $fce->IT_OUT->Next() ){
                        $matnr	= $fce->IT_OUT->row["MATNR"];
                        $nama_matnr	= $fce->IT_OUT->row["MAKTX"];
                        $uom		= $fce->IT_OUT->row["MEINS"];
                        if(strtoupper($fce->IT_OUT->row["GEWEI"])=='KG'){
                             $qtymto=@(number_format($fce->IT_OUT->row["NTGEW"])/1000);
                        }else{
                             $qtymto=number_format($fce->IT_OUT->row["NTGEW"]);
                        }
        }
	}else
			$fce->PrintStatus();

	$fce->Close();	
	$sap->Close();	*/
}

if ($error){
$uom = "";
$uomtlcc = "";
$produk = "";
$nama_produk = "";
$quantumTO ="";
$val_error_produk=1;
}
else {
$uom = $uom;
$produk = $matnr;
$nama_produk=$nama_matnr;
$quantumTO =$qtymto;
$val_error_produk=0;
}
echo '<input name="produk'.$nourut.'" type="text" class="inputlabel" id="produk'.$nourut.'" value="'.$produk.'" onchange="ketik_produk(this,'.$nourut.')" maxlength="20" size="12" '.(($pr) ? 'readonly' : '').' />';
echo '<input name="nama_produk'.$nourut.'" type="text" class="inputlabel" id="nama_produk'.$nourut.'" value="'.$nama_produk.'" readonly="true" size="20" />';
echo '<input name="uom'.$nourut.'" type="text" class="inputlabel" id="uom'.$nourut.'" value="'.$uom.'" readonly="true" size="4" />';
echo '<input name="qto'.$nourut.'" type="hidden" class="inputlabel" id="qto'.$nourut.'" value="'.$quantumTO.'" readonly="true"  size="4"/>';
echo '<input name="btn_produk'.$nourut.'" type="button" class="button" id="btn_produk'.$nourut.'" value="..." onClick="findproduk('.$nourut.')"  '.(($pr) ? 'disabled' : '').' />';
echo '<input name="val_error_produk'.$nourut.'" type="hidden" id="val_error_produk'.$nourut.'" value="'.$val_error_produk.'"/>';
echo '<div id="plant_warning"></div>';
