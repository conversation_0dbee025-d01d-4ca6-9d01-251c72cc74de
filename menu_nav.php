<?
session_start();
include ('include/fungsi.php');
$fungsi=new fungsi();
$conn=$fungsi->koneksi();

//Translate
require_once('include/class.translation.php');
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);


if(isset($_GET['setorg'])&& trim($_GET['status'])=='aktif'){    
    if(trim($_GET['setorg'])!=''){
        $_SESSION['user_org']=$_GET['setorg'];
        echo '<script>parent.frames["isi"].location.reload();parent.frames["isi"].location.href="welcome.php";</script>';
    }
}

if(isset($_GET['setbhs'])&& trim($_GET['status'])=='aktif'){    
    if(trim($_GET['setbhs'])!=''){
        $_SESSION['user_setbhs']=$_GET['setbhs'];
        echo '<script>parent.frames["isi"].location.reload();parent.frames["isi"].location.href="welcome.php";</script>';
        echo '<script>parent.frames["leftFrame"].location.reload();parent.frames["leftFrame"].location.href="menu_nav.php";</script>';
    }
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>Tree Menu</title>
<link rel='stylesheet' href='Templates/css/tree.css'>
<link href="Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="include/tree.js"></script>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>SGG Online :Main Menu ;p</title>
<style type="text/css">
<!--
.style1 {color: #FF0000}
.style2 {color: #0000FF}

.flag_vie{
	width:16px;
	height:11px;
	float:left;
	background:transparent url(images/vie.png) no-repeat top left;
	margin:3px;
}
.flag_english{
	width:16px;
	height:11px;
	float:left;
	background:transparent url(images/us.png) no-repeat top left;
	margin:3px;
}
.flag_default{
	width:16px;
	height:11px;
	float:left;
	background:transparent url(images/us.png) no-repeat top left;
	margin:3px;
}

-->
</style>
<script language=javascript>
function pilih()
{    
    var message = '';
    var valorg = document.getElementsByName("orggroup");    
    //For each radio button if it is checked get the value and break.
    for (var i = 0; i < valorg.length; i++){
      if (valorg[i].checked){
         message = valorg[i].value;
         break
      }
   }
   //alert(message);
   window.location.href="menu_nav.php?setorg="+message+"&status=aktif";
   //window.location.reload(true);
   //parent.frames["isi"].location.reload();
   
   return message;
}

function pilihbhs(val)
{      
   window.location.href="menu_nav.php?setbhs="+val+"&status=aktif";
   //window.location.reload(true);
   //parent.frames["isi"].location.reload();
}
</script>
</head>

<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
<table  border=0 cellpadding='1' cellspacing=1>
	<tr>
		<td  colspan="2" align="center" class="ctr style2">
		<?
		echo $_SESSION['nama_lengkap'];
		?>	  </td>
	</tr>
</table>
<?
//echo "<pre>";
//print_r($_SESSION);
//echo "</pre>";
$jmlcormgr='';unset($arraymok);
$sqliorgmgr= "
    select COMPANY_CODE from OR_MAP_USERCOM where USER_ID='".$_SESSION['user_id']."' and delete_mark='0' and COMPANY_CODE not in('2000')
    GROUP BY COMPANY_CODE
    union 
    select 
    case when ORG ='2000' then '7000'
    else ORG
    end as COMPANY_CODE from tb_user_booking where ID='".$_SESSION['user_id']."'
    and ID in (
    select USER_ID from OR_MAP_USERCOM where USER_ID='".$_SESSION['user_id']."' and delete_mark='0' and COMPANY_CODE not in('2000')
    GROUP BY USER_ID)
";
$queryirtto= @oci_parse($conn, $sqliorgmgr);
@oci_execute($queryirtto);
while ($datain = @oci_fetch_array($queryirtto)) {
    $arraymok[$datain['COMPANY_CODE']]=$datain['COMPANY_CODE'];
}
echo $_SESSION['user_org'];
if(count($arraymok)>0){?>
 <fieldset style="font-weight: bold;">
    <legend>Org Management:</legend>
        <?
        $rryorg=$fungsi->arrayorg();
        unset($chek);
        foreach($arraymok as $keyorg => $valorg){
        if($keyorg==$_SESSION['user_org']){$chek='checked';}else{$chek='';}  
        ?>
            <input type="radio" name="orggroup" value="<?=$keyorg;?>" <?=$chek;?> onChange="pilih();" ><?=@$rryorg[$valorg];?><br>
        <?}
        ?>
  </fieldset>      
<?}
if($_SESSION['user_org']=='6000'){?>    
 <fieldset style="font-weight: bold;">
    <legend><? $translatebhsxx->__1('Language'); ?>:</legend>

            <a class="flag_vie" title="vietnam" onclick="pilihbhs('vie')"></a>
            <a class="flag_default" title="default" onclick="pilihbhs('df')"></a>
            
       
  </fieldset>    
<?}?> 
<br />
<table border=0 cellpadding='1' cellspacing=1>
	<tr>
		<td colspan="2" align="center">
		<a href="login.php?act=logout" target="_parent" class="style1"> >> <?php $translatebhsxx->__1('Logout'); ?> >> </a>		</td>
	</tr>
</table>
<table border=0 cellpadding='10' cellspacing=0><tr><td>
	<table border=0 cellpadding='1' cellspacing=1><tr><td colspan="2" align="center"><a href="javascript:Collapse();"> -- <?php $translatebhsxx->__1('Menu Navigator'); ?> -- </a></td></tr></table>
<?
function pohon($parent,$respon_id,$translatebhsxx){
	global $not_ulang,$arr_not,$datake,$arr_datake,$child_nodes,$node_data,$last_child,$pertama,$wata;
	for($i=0; $i < sizeof($child_nodes[$parent]); $i++){
		$ancesor=$child_nodes[$parent][$i];
		$tipe=$node_data[$ancesor][type];
		$not_ulang+=1;
		$datake+=1;
		$last_indeks=$datake-1;
		$last_dataindeks=$arr_datake[$last_indeks];
		$wata=true;

		if($last_dataindeks!=$node_data[$ancesor][up] and $pertama!=0){
			echo " </div>";
			$wata=false;
		}
		if($last_dataindeks!=$node_data[$ancesor][up]){
			$datake-=1;
			$yangakan1=$datake;
			for($b=1;$b<$yangakan1;$b++){
				$last_indeks=$yangakan1-$b;
				
				$last_dataindeks=$arr_datake[$last_indeks];
				$divyes=true;
				if(sizeof($child_nodes[$last_dataindeks])>1){
					$b=$yangakan1+1;
					$divyes=false;
				}
				if($divyes){
					echo "</div>";
					$datake=$last_indeks;
				}	
			}
		}
		$arr_datake[$datake]=$ancesor;
		$arr_not[$not_ulang]=$ancesor;
		$deskripsi=$node_data[$ancesor][name].$node_data[$ancesor][id];
		$deskripsi1=$node_data[$ancesor][name];
		if($tipe == 2){
			$url=$node_data[$ancesor][url]."?keamanan_id=".$respon_id;
			$yahoo=true;
			?> <table border='0' cellpadding='1' cellspacing='1'><tr><td width='16'><img src='images/text.gif' width='16' height='16' hspace='0' vspace='0' border='0'></td><td><a href='<?=$url;?>' target='isi'><? $translatebhsxx->__1($deskripsi1);?></a></td></tr></table>
			<?
                        if ( sizeof($child_nodes[$ancesor]) == 0 ){
				$yang_akan=$datake;
				$setdata=$not_ulang;
				$wata=true;
				for($a=1; $a <= $yang_akan; $a++){
					$last_indeks=$yang_akan-$a;
					$last_dataindeks=$arr_datake[$last_indeks];
					$last_indeks_not=$setdata-$a;
					$last_data_not=$arr_not[$last_indeks_not];
					if(sizeof($child_nodes[$last_dataindeks])>1){
						$a=$yang_akan+1;
						$datake=$last_indeks;
						$wata=false;
					}else{
						if($a==1 and ($yang_akan>2 or $yang_akan==1)){
						}else{
						echo "</div>";
						}
						if(sizeof($child_nodes[$last_dataindeks])==1){
						$datake=$last_indeks;
						}
						$wata=false;
					}
				}
			}
		}else{
		?>
		<table border=0 cellpadding='1' cellspacing=1><tr><td width='16'><a id="x<?=$deskripsi;?>" href="javascript:Toggle('<?=$deskripsi;?>');"><img src='images/folder.gif' width='16' height='16' hspace='0' vspace='0' border='0'></a></td><td><a href="javascript:Toggle('<?=$deskripsi;?>');"><b><? $translatebhsxx->__1($deskripsi1);?></b></a></table>
				<div id="<?=$deskripsi;?>" style="display: none; margin-left: 2em;">
		<?	
			//echo $datake;
			if ( sizeof($child_nodes[$ancesor]) == 0 ){
				$yang_akan=$datake;
				$setdata=$not_ulang;
				$wata=true;
				for($a=1; $a <= $yang_akan; $a++){
					$last_indeks=$yang_akan-$a;
					$last_dataindeks=$arr_datake[$last_indeks];
					$last_indeks_not=$setdata-$a;
					$last_data_not=$arr_not[$last_indeks_not];
					if(sizeof($child_nodes[$last_dataindeks])>1){
						$a=$yang_akan+1;
						$datake=$last_indeks;
 						echo " </div>";
						$wata=false;
					}else{
						echo " </div>";
						if(sizeof($child_nodes[$last_dataindeks])==1){
						$datake=$last_indeks;
						}
						$wata=false;
					}
				}
			}
			$pertama=1;
			pohon($ancesor,$respon_id,$translatebhsxx);
		}
	}
}
$user_id_resp=$_SESSION['user_id'];
$sql_user= "SELECT * FROM TB_USER_RESPONSIBILITY WHERE USER_ID = '$user_id_resp' AND DELETE_MARK <> 1 ORDER BY ID ASC";
$query_user= oci_parse($conn, $sql_user);
oci_execute($query_user);
while ($data_user = oci_fetch_array($query_user)) {
$user_res_id=$data_user[RESPONSIBILITY_ID];
	$mialo = "SELECT ID,PARENT,TYPE,ICON,NAME,URL FROM TB_DIRECTORY WHERE RESPONSIBILITY_ID = '$user_res_id' AND DELETE_MARK <> 1 ORDER BY PARENT,TYPE,ID ASC";
	$query= oci_parse($conn, $mialo);
	oci_execute($query);
    $child_nodes = array();
    $node_data = array();
	$last_child = array();
	$ke=0;
    while (list($id, $parent, $type, $icon, $name,$url) = oci_fetch_array($query)) {
    	$child_nodes[(int)$parent][] = $id;
    	$node_data[$id] = array('id' => $id,
    				'up' => $parent,
    				'type' => $type,
    				'icon' => $icon,
    				'name' => $name,
					'url' => $url);
    	$last_child[(int)$parent] = $id;
		$ke+=1;
    }
	for ($i=0; $i < sizeof($child_nodes[0]); $i++){
	$parent=$child_nodes[0][$i];
	$deskripsi=$node_data[$parent][name].$node_data[$parent][id];
	$deskripsi1=$node_data[$parent][name];
	$datake=0;
	$not_ulang=0;
	$pertama=0;
	$arr_datake = array();
	$arr_not = array();
	?>
	<table border=0 cellpadding='1' cellspacing=1><tr><td width='16'><a id="x<?=$deskripsi;?>" href="javascript:Toggle('<?=$deskripsi;?>');"><img src='images/folder.gif' width='16' height='16' hspace='0' vspace='0' border='0'></a></td><td><a href="javascript:Toggle('<?=$deskripsi;?>');"><b><? $translatebhsxx->__1($deskripsi1);?></b></a></table>
			<div id="<?=$deskripsi;?>" style="display: none; margin-left: 2em;">
	<?	
	pohon($parent,$user_res_id,$translatebhsxx);
	echo "</div>";
	echo "</div>";
	}
}
?>
<p><a href="user/ownpass.php" target="isi"><?php $translatebhsxx->__1('Change Password'); ?></a>
<p><a href="javascript:Expand();"><?php $translatebhsxx->__1('Expand All'); ?> - </a><a href="javascript:Collapse();"><?php $translatebhsxx->__1('Collapse All'); ?></a>
</td></tr></table>
<?
if(($_SESSION['user_org']=='2000' || $_SESSION['user_org']=='5000' || $_SESSION['user_org']=='7000' )&&($_SESSION['vendor_id']!='')){

if($_SESSION['user_org']=='2000' || $_SESSION['user_org']=='7000'){
    if($_SESSION['user_org']=='2000'){
        $user_orgcd='7000';
    }else{
        $user_orgcd='2000';
    }
    $orgIn = "'".$_SESSION['user_org']."','".$user_orgcd."'";
}else{
    $orgIn = "'".$_SESSION['user_org']."'";
}    
    
?>    
 <fieldset style="font-weight: bold;">
    <legend>Info Invoice:</legend>           
        <marquee class="smooth_m" behavior="scroll" direction="up" onMouseOut="this.start()" onMouseOver="this.stop()" scrollamount="0.5" height="170px" width="162px">
            <?
            $sqlinfoinv= "
                select NO_VENDOR,NAMA_VENDOR,NO_INVOICE,ALASAN,TGL_DIKEMBALIKAN,NO_INVOICE_EX,to_char(TGL_DIKEMBALIKAN,'DD-MM-YYYY') as TGL_DIKEMBALIKANF
                from KPI_TERIMA_INV_VENDOR where DEL=1  AND ORG in ($orgIn)
                and FLAG_CETAK is null    
                and FLAG_CETAKTERM is not null  
                and NO_VENDOR='".$_SESSION['vendor_id']."'
            ";
            $queryinfvo= oci_parse($conn, $sqlinfoinv);
            oci_execute($queryinfvo);
            while ($datainfvoi = oci_fetch_array($queryinfvo)) {
            ?>
            <span align="justify">
                <span style="color:red;">Invoice: <?=$datainfvoi['NO_INVOICE'];?></span><br/>
                <span style="color:red;">Invoice Exp: <?=$datainfvoi['NO_INVOICE_EX'];?></span><br/>
                Tgl. Kembali: <?=$datainfvoi['TGL_DIKEMBALIKANF'];?><br/>
                <span style="color:blue;"><?=$datainfvoi['ALASAN'];?></span><br>
            </span>
            <hr style="border: 1px dotted silver">
            <?}?>
        </marquee>   
  </fieldset>    
<?}?>
</body>
</html>
