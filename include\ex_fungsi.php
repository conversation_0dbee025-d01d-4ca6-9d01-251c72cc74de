<?
include_once ('fungsi.php');

class ex_fungsi extends fungsi 
{

	var $bo_username = "C<PERSON>";
	var $bo_password = "cms1910p4dAn9";
	var $bo_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';
//        
//        var $bo_username = "CMSTES";
//	var $bo_password = "cms1910tes";
//	var $bo_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';
        
	public function bo_koneksi()
	{
		$conn = oci_connect($this->bo_username, $this->bo_password, $this->bo_db , 'AL32UTF8');
		if (!$conn)
			return false;
		else
		 return $conn;
	}
        
	public function ex_koneksi()
	{   
		$oraConfig = require ('C:\xampp\htdocs\csms\sgg\include\connect\ora_sd_030.php');//local
		// $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_030.php');//dev
		// $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_210.php');//prod
		$conn = oci_connect($oraConfig['username_conn'], $oraConfig['password_conn'], $oraConfig['db'] , 'AL32UTF8');

		if (!$conn)
			return false;
		else
		 return $conn;
	}
         //Testing
        /*public function ex_koneksi2()
	{
		//$oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_030.php');//dev
               $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_210.php');//prod
		$conn = oci_connect($oraConfig['username_conn'], $oraConfig['password_conn'], $oraConfig['db'] , 'AL32UTF8');

		if (!$conn)
			return false;
		else
		 return $conn;
	}*/
	function ex_status_shipment($ting){
		$k=array(1 => '10', '20', '30', '40', '50');
		$nama=array(1 => '10-Antri', '20-Entri SPPS', '30-Matching Kota','40-Matching Alamat','50-Timbang Masuk');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	public function ex_find_user($conn,$user){
		$sql="SELECT * FROM TB_USER_BOOKING WHERE DELETE_MARK <> '1' AND ID = '$user' ";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$row=oci_fetch_assoc($query);
		$user=array('tipe' => $row['USER_TYPE'],
					'vendor' => $row['VENDOR'],
					'nama_vendor' => $row['VENDOR_NAME'],
					'plant' => $row['PLANT'],
					'nama_plant' => $row['NAMA_PLANT'],
					'org' => $row['ORG'],
					'nama_org' => $row['NAMA_ORG']
					);
		return $user;
		//return "0000410019";
	}

	public function ex_find_vendor($conn,$user){
		$sql="SELECT USER_TYPE, VENDOR FROM TB_USER_BOOKING WHERE DELETE_MARK <> '1' AND ID = '$user' ";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$row=oci_fetch_assoc($query);
		if ($row['USER_TYPE'] == 'admin')
		return "";
		else 
		return $row['VENDOR'];
		//return "0000410019";
	}
	public function ex_hanya_baca($param){
		//$param = "admin"; // "0000410019";
		if ($param == "" or $param == "admin") $hanya_baca = "";
		else $hanya_baca = " readonly = 'readonly' ";
		return $hanya_baca;
	}
	public function ex_cek_display($param){
		//$param = "admin"; // "0000410019";
		if ($param == "" or $param == "admin") $cek = true;
		else $cek = false;
		return $cek;
	}
	public function ex_clearsessi_all()
	{
		$this->ex_clearsessi_semen_claim();
		$this->ex_clearsessi_kantong_claim();
	}
	public function ex_clearsessi_semen_claim()
	{
		unset($_SESSION['ex_produk_smn_cl']);
		unset($_SESSION['ex_nama_produk_smn_cl']);
		unset($_SESSION['ex_harga_tebus_smn_cl']);
		unset($_SESSION['ex_harga_tebus_45_smn_cl']);
		unset($_SESSION['ex_harga_tebus_45plus_smn_cl']);
		unset($_SESSION['ex_biaya_klaim_smn_cl']);
		unset($_SESSION['ex_tujuan_smn_cl']);
		unset($_SESSION['ex_nama_tujuan_smn_cl']);
		unset($_SESSION['ex_tanggal_mulai_smn_cl']);
		unset($_SESSION['ex_tanggal_selesai_smn_cl']);
		unset($_SESSION['ex_plant_smn_cl']);
		unset($_SESSION['ex_nama_plant_smn_cl']);
		unset($_SESSION['ex_org_smn_cl']);
		unset($_SESSION['ex_nama_org_smn_cl']);
		unset($_SESSION['ex_toleransi_smn_cl']);
	}
	function ex_bulan($ting){
		$k=array('januari', 'februari','maret','april','mei','juni','juli','agustus','september','oktober','november','desember');
		$l=array('01','02','03','04','05','06','07','08','09','10','11','12');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$l[$x]' title='$k[$x]' ");
			if($l[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function ex_organisasi($ting){
		$k=array('-Pilih-','PT Semen Padang','PT Semen Tonasa','5000 PT SEMEN GRESIK','7000 PT SEMEN INDONESIA','7900 MD SEMEN INDONESIA');
		$l=array('','3000','4000','5000','7000','7900');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$l[$x]' title='$k[$x]' ");
			if($l[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
        function ex_organisasi2($ting){
		$k=array('5000 PT SEMEN GRESIK','7000 PT SEMEN INDONESIA');
		$l=array('5000','7000');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$l[$x]' title='$k[$x]' ");
			if($l[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function ex_warna_plat($ting){
		$k=array('HITAM', 'KUNING');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'onClick=display_div('$k[$x]')");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
        
        function ex_umStatus($ting){
            $k=array('OPEN', 'CLOSE','REJECT','INVOICED');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'onClick=display_div('$k[$x]')");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
        }
        
        function ex_umStatusTrns($ting){
            $k=array('OPEN', 'PRINTED');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'onClick=display_div('$k[$x]')");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
        }
        
        function categoryPOEX($ting){
            $k=array('Recon', 'Dropshot','Kontrak','Lainya');
            $n=array('RCN', 'DRP','CNT','OTHR');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$n[$x]'onClick=display_div('$n[$x]')");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
        }
        
        function persenPotong($ting){
            $k=array('25', '40','50');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'onClick=display_div('$k[$x]')");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
        }
        
	function ex_spt_pajak($ting){
		$k=array('GABUNGAN', 'TRANSAKSI');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]' name='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function ex_status_pajak($ting){
		$k=array('OK', 'NOT OK');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function ex_tipe_trans($ting){
		$k=array('BAG', 'CURAH');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function ex_kelompok_trans($ting){
		$k=array('DARAT', 'LAUT');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}        
	function ex_status($ting){
		$k=array('BLOCK','DRAFT', 'OPEN','PROGRESS','INVOICED','PAYMENT');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	function ex_status2($ting){
		$k=array('DRAFT', 'OPEN');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	// STATUS2 => OPEN, INVOICED , INVOICED2 
        function ex_status3($ting){
		$k=array('ADA','BLANK');
		$l=array('NOT NULL','NULL');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$l[$x]' title='$k[$x]' ");
			if($l[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
        function ex_inco($ting){
		$k=array('FRC', 'FOT', 'FOB', 'CIF', 'CNF', 'FOR');
		for($x=0;$x<count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}
	public function ex_clearsessi_kantong_claim()
	{
		unset($_SESSION['ex_produk_ktg_cl']);
		unset($_SESSION['ex_nama_produk_ktg_cl']);
		unset($_SESSION['ex_biaya_rezak_ktg_cl']);
		unset($_SESSION['ex_biaya_ktg_ktg_cl']);
		unset($_SESSION['ex_tanggal_mulai_ktg_cl']);
		unset($_SESSION['ex_tanggal_selesai_ktg_cl']);
		unset($_SESSION['ex_org_ktg_cl']);
		unset($_SESSION['ex_nama_org_ktg_cl']);
	}
	public function ex_clearsessi_pajak_claim()
	{
		unset($_SESSION['ex_vendor_pjk_cl']);
		unset($_SESSION['ex_nama_vendor_pjk_cl']);
		unset($_SESSION['ex_no_pajak_pjk_cl']);
		unset($_SESSION['ex_status_pajak_pjk_cl']);
		unset($_SESSION['ex_spt_pajak_pjk_cl']);
		unset($_SESSION['ex_keterangan_pjk_cl']);
		unset($_SESSION['ex_tanggal_mulai_pjk_cl']);
		unset($_SESSION['ex_tanggal_selesai_pjk_cl']);
		unset($_SESSION['ex_org_pjk_cl']);
		unset($_SESSION['ex_nama_org_pjk_cl']);
	}
	public function ex_clearsessi_kom_biaya()
	{
		unset($_SESSION['ex_kode_kom']);
		unset($_SESSION['ex_nama_biaya_kom']);
		unset($_SESSION['ex_no_gl_kom']);
		unset($_SESSION['ex_nama_gl_kom']);
		unset($_SESSION['ex_org_kom']);
		unset($_SESSION['ex_nama_org_kom']);
		unset($_SESSION['ex_keterangan_kom']);
		unset($_SESSION['ex_tax_code_kom']);
		unset($_SESSION['ex_tax_name_kom']);
		unset($_SESSION['ex_rate_kom']);
		unset($_SESSION['ex_prctr_kom']);
		unset($_SESSION['ex_cost_center_kom']);
	}

	function new_invoice_number($conn)
	{
		$sql="SELECT EX_INVOICE_NUMBER_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='*********'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		if($panjang==10)$new_number_ok=$new_number;
		
		return $new_number_ok;

	}
	function new_invoice_ba($conn)
	{
		// $sql="SELECT SEQ_CREATE_BA.NEXTVAL FROM SYS.DUAL";
		$sql="SELECT SEQ_NO_DOKUMEN_BA.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='*********'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		if($panjang==10)$new_number_ok=$new_number;
		
		return $new_number_ok;

	}
        function newPotonganOANum($conn){
                $sql="SELECT POTONGANOA_INV.NEXTVAL FROM DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='00000'.$new_number;
		if($panjang==2)$new_number_ok='0000'.$new_number;
		if($panjang==3)$new_number_ok='000'.$new_number;
		if($panjang==4)$new_number_ok='00'.$new_number;
		if($panjang==5)$new_number_ok='0'.$new_number;
		return $new_number_ok;
        }
	function new_invoice_number_st($conn)
	{
		$sql="SELECT EX_INVOICE_NUMBER_ST_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='*********'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		if($panjang==10)$new_number_ok=$new_number;
		return $new_number_ok;

	}

	function ex_cari_vendor($vendor)
	{
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZCSD_VENDOR");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$org="2000";
		if ($org=="2000")$ktokk="4100";
                if ($org=="7000")$ktokk="4100";
                if ($org=="5000")$ktokk="4100";
		if ($org=="3000")$ktokk="4200";
		if ($org=="4000")$ktokk="4300";

                $panjang=strlen(strval($vendor));
		if($panjang==1)$vendor='*********'.$vendor;
		if($panjang==2)$vendor='00000000'.$vendor;
		if($panjang==3)$vendor='0000000'.$vendor;
		if($panjang==4)$vendor='000000'.$vendor;
		if($panjang==5)$vendor='00000'.$vendor;
		if($panjang==6)$vendor='0000'.$vendor;
		if($panjang==7)$vendor='000'.$vendor;
		if($panjang==8)$vendor='00'.$vendor;
		if($panjang==9)$vendor='0'.$vendor;

		$fce->XKTOKK = $ktokk;
		$fce->XDLGRP = "";
		$fce->XLIFNR = $vendor;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
				$lifnr= $fce->RETURN_DATA->row["LIFNR"];
				$nama= str_replace('"','',$fce->RETURN_DATA->row["NAME1"]);
				$kota=$fce->RETURN_DATA->row["ORT01"];
				$alamat=$fce->RETURN_DATA->row["STRAS"];

		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $kota;
	}
	function ex_produk($ting,$conf,$jenis,$satuan,$matkl){
		$sap = new SAPConnection();
		$sap->Connect($conf);
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZCSD_MATERIAL_MST");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
		$fce->ZMTART = $jenis;
		$fce->ZMEINS = $satuan;
		$fce->ZMATKL = $matkl;
		$fce->ZMATNR = $matnr;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
			echo " <br>".	$matnr[]= $fce->RETURN_DATA->row["MATNR"];
				$nama_matnr[]= $fce->RETURN_DATA->row["MAKTX"];
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
			
		for($x=0;$x<count($matnr);$x++)
		{
		echo("<option value='$nama_matnr[$x]' title='$matnr[$x]' ");
		if($nama_matnr[$x] == $ting){echo("selected");}
		echo(">$matnr[$x]</option>");
		}
	
	}
	function ex_pilih_truk($plant,$vendor,$tipe,$warna_plat,$nopol)
	{
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
        
        //$sap->Connect("../include/sapclasses/logon_data1.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_SELECT_TRUK2");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		//header entri

		$fce->XPARAM["NO_EXPEDITUR"] = $vendor;
		//$fce->XPARAM["NAMA_EXPEDITUR"] = $vendor;
		$fce->XPARAM["VEHICLE_TYPE"] = $tipe;
		$fce->XDATA_APP["NMORG"] = '7000';//$plant;
		$fce->XDATA_APP["NMPLAN"] = '7403';//$plant;
		$fce->XDATA_APP["WARNA_PLAT"] = $warna_plat;
		$fce->XDATA_APP["NOPOLISI"] = $nopol;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
			$return_data[]=array(
				'no_pol'=> $fce->RETURN_DATA->row["NOPOLISI"],
				'no_expeditur' => $fce->RETURN_DATA->row["NO_EXPEDITUR"],
				'nama_expeditur' => $fce->RETURN_DATA->row["NAMA_EXPEDITUR"],
				'plat' => $fce->RETURN_DATA->row["WARNA_PLAT"],
				'tipe' => $fce->RETURN_DATA->row["VEHICLE_TYPE"],
				'tipe_truk' => $fce->RETURN_DATA->row["MODE_OFTRANSPORT"],
				'plant' => $fce->RETURN_DATA->row["NMPLAN"],
                                'kapasitas' => $fce->RETURN_DATA->row["KAPASITAS"]
				);
		}
		}else
       		$fce->PrintStatus();
		$fce->Close();	
		$sap->Close();	
              //  print_r($return_data);
		return $return_data;
	}

// add 31 des 2011
	public function ex_user_plant($conn,$user,$plant){
		$sql="SELECT PLANT, NM_PLANT FROM TB_USER_VS_PLANT WHERE DELETE_MARK = '0' AND USER_ID = '$user' ";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		while($row=oci_fetch_assoc($query)){
		$kode = $row['PLANT'];
		$nama = $row['NM_PLANT'];
		
		echo("<option value='$kode' ");
		if($nama == $plant){echo("selected");}
		echo(">$nama</option>");
		}
	}

	function ex_query_truk_antri($nopol,$antri,$kode_exp,$user_org,$plant)
	{
                $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
                if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                if ($sap->GetStatus() != SAPRFC_OK ) {
                   echo $sap->PrintStatus();
                   exit;
                }
		$fce = $sap->NewFunction ("Z_ZAPPSD_GR_SEL_TRANS1");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
	
	
            if(strlen($nopol)>0){
                $fce->XPARAM["NO_POLISI"]=$nopol;
            }
            //if(strlen($this->rfid->Text)>0)$fce->XPARAM["ID_CARD"]=strtoupper($this->rfid->Text);
            if(strlen($antri)>0)$fce->XPARAM["NO_TRANSAKSI"]=$antri;
            
            $fce->XPARAM["STATUS_TRANS"]='01';
            $fce->XPARAM["COMPLETED"]='0';
            $fce->XPARAM["NO_EXPEDITUR"]=$kode_exp;

            $fce->XDATA_APP["NMORG"]=$user_org;
            $fce->XDATA_APP["NMPLAN"]=$plant;

            $fce->Call();
            if ($fce->GetStatus() == SAPRFC_OK) {
                $fce->RETURN_DATA->Reset();
                //Display Tables
                while ($fce->RETURN_DATA->Next()){
					$tgl2="".substr($fce->RETURN_DATA->row["TGL_DFT_TRUK"],6,2)."-".
						substr($fce->RETURN_DATA->row["TGL_DFT_TRUK"],4,2)."-".
						substr($fce->RETURN_DATA->row["TGL_DFT_TRUK"],0,4);

					$return_data[]=array(
						'no_antri_v'=>$fce->RETURN_DATA->row["NO_TRANSAKSI"],
						'no_polisi_v'=>$fce->RETURN_DATA->row["NO_POLISI"],
						'tipe_truk_v'=>$fce->RETURN_DATA->row["TIPE_TRUK"],
						'id_card_v'=>$fce->RETURN_DATA->row["ID_CARD"],
						'no_expeditur_v'=>$fce->RETURN_DATA->row["NO_EXPEDITUR"],
						'nama_expeditur_v'=>$fce->RETURN_DATA->row["NAMA_EXPEDITUR"],
						'tgl_antri_v'=>$tgl2,
						'jam_antri_v'=>$fce->RETURN_DATA->row["JAM_DFT_TRUK"],
						'kapasitas_v'=>$fce->RETURN_DATA->row["KAPASITAS"],
						'kapasitas_master_v'=>$fce->RETURN_DATA->row["KAPASITAS_MASTER"]
					);
                }
			}else
				$fce->PrintStatus();
			
            $fce->Close();
			$sap->Close();

		return $return_data;
	}

	function ex_update_truk_antri($no_antri,$kapasitas,$user_org,$plant,$user_nama)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_UPD_SPPS_PDG_N1");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}

		$fce->XDATA_APP["NMORG"]=$user_org;
		$fce->XDATA_APP["NMPLAN"]=$plant;
		$fce->XDATA_KEY=$no_antri;
		$fce->XDATA_UPD["KAPASITAS"]=$kapasitas;

		$fce->XDATA_UPD["LAST_UPDATED_BY"]=$user_nama;
		$fce->XDATA_UPD["LAST_UPDATE_DATE"]=date('Ymd');
		$fce->XDATA_UPD["LAST_UPDATE_TIME"]=date('His');

		$fce->Call();

		if ($fce->GetStatus() == SAPRFC_OK) {
			$status['tipe'] = $fce->RETURN["TYPE"];
			$status['pesan'] = $fce->RETURN["MESSAGE"];
		}
		$fce->Close();
		$sap->Close();

		return $status;
	}


	function ex_delete_truk_antri($no_antri,$user_org,$plant,$user_nama)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}
	
		$fce = $sap->NewFunction ("Z_ZAPPSD_UPD_SPPS_PDG_N1");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}

		$fce->XDATA_APP["NMORG"]=$user_org;
		$fce->XDATA_APP["NMPLAN"]=$plant;
		$fce->XDATA_KEY=$no_antri;
		$fce->XDATA_UPD["STATUS_TRANS"]='-1';
		$fce->XDATA_UPD["DEL"]='1';
		$fce->XDATA_UPD["LAST_UPDATED_BY"]=$user_nama;
		$fce->XDATA_UPD["LAST_UPDATE_DATE"]=date('Ymd');
		$fce->XDATA_UPD["LAST_UPDATE_TIME"]=date('His');

		$fce->Call();

		if ($fce->GetStatus() == SAPRFC_OK) {
			$status['tipe'] =$fce->RETURN["TYPE"];
			$status['pesan'] = $fce->RETURN["MESSAGE"];
		}
		$fce->Close();
		$sap->Close();
		return $status;
	}
        
        function ex_getClearing($no_invoice_sap,$tahun)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}	
		$fce = $sap->NewFunction ("Z_ZAPPSD_GET_STAT_INV");
		if ($fce == false ) { $sap->PrintStatus(); exit;}

		$fce->I_BELNR = $no_invoice_sap; 
		$fce->I_GJAHR = $tahun; 
		$fce->Call();
                $returndata=array();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$kode_status_invoice_sap = $fce->E_ZLSPR;
			$status_invoice_sap =  $fce->E_PAYSTAT;
                        $tgl_clearing = $fce->E_AUGDT;
                        $no_clearing = $fce->E_AUGBL;
                        $returndata = array("KODE_STATUS"=>$kode_status_invoice_sap,
                                            "KET_STATUS"=>$status_invoice_sap,
                                            "TGL_CLEARING"=>$tgl_clearing,
                                            "NO_CLEARING"=>$no_clearing);
		}
		$fce->Close();
		$sap->Close();
		return $returndata;
	}

	public function ex_getTglClearing($conn,$com,$no_accounting,$vendor){
		echo $sql="SELECT TGL_CLEARING FROM EX_TRANS_HDR WHERE org='2000' and DELETE_MARK = '0' and ACCOUNTING_DOC ='**********' and tgl_clearing is not null and rownum=1";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
                $tgl_clearing='';
		while($row=oci_fetch_assoc($query)){
		$tgl_clearing = $row['TGL_CLEARING'];
                }
                return $tgl_clearing;
       }



}

//tambah pilih kapal
function nama_kapal($conn, $vendor)
	{
		$sql = "SELECT NAMA_KAPAL FROM EX_TRANS_HDR TH WHERE VENDOR = '$vendor' AND DELETE_MARK = 0 AND STATUS IN ('DRAFT', 'OPEN')
                AND ORG IN (SELECT ORGIN FROM OR_MAP_COM WHERE ORGIN = TH.ORG) AND NAMA_KAPAL IS NOT NULL GROUP BY NAMA_KAPAL";
				echo $sql;
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$namakapal[$a] = $row['NAMA_KAPAL'];
		}
		$namakapal[0] = "Pilih Nama Kapal";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$namakapal[$x]'");
			echo (">$namakapal[$x]</option>");
		}
	}

?>
