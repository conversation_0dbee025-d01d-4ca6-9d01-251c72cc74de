<?php
/*
 * upload master supir expeditur
 * @yopi satria
 */

session_start();
include('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

//Format Nilai
function showNilai2($nilai)
{
    if ($nilai > 0) return number_format($nilai, 2);
    else return '0';
}

//$hakakses=array("admin");
// $halaman_id = 3095;
$halaman_id=4878;
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];
//$conns=$fungsi->ex_koneksi();
//$dirr = $_SERVER['PHP_SELF']; 


$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();
$dirr = $_SERVER['PHP_SELF'];
// $halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

//echo "<pre>";
//print_r($_SESSION);
//echo "</pre>";
$no_ba = $_GET['no_ba'];
$importtargetVolume = 'upload_ba.php';
$waktu = date("d-m-Y");

function formatTGL($tgl)
{
    $a = explode("/", $tgl);
    $tgal = $a[3] . $a[2] . $a[1];
    return $tgal;
}


if (isset($_POST['Import'])) {

    if (isset($_FILES['pdf_file']['name'])) {
        $user_name = $_SESSION['user_name'];
        $file_name = $_FILES['pdf_file']['name'];
        $file_tmp = $_FILES['pdf_file']['tmp_name'];
        $get_nama_file = $_POST['nama_file'];
        
        $check = validate_pdf($file_name, $file_tmp);

        $ext = strtolower(substr(strrchr($file_name, '.'), 1));
        $saved_file_name = $get_nama_file . "." . $ext;
        if ($check['status']) {
            echo $saved_file_name;
            move_uploaded_file($file_tmp, "./lampiran/" . $saved_file_name);
            // $field_names = array('FILENAME', 'STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
            // $field_data = array("$saved_file_name", "10", "SYSDATE", "$user_name");
            // $tablename = "EX_BA";
            // $field_id = array('ID');
            // $value_id = array("$no_ba");
            // $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            echo "<script>alert('Berhasil replace PDF');</script>";
            // echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =lihat_ba_hdr_ex.php'>";
            // echo "<script>setTimeout(function() { win.close();}, 3000)</script>";
        } else {
            echo "<script>alert('Invalid file...!!, ." . $check['message'] ."');</script>";
        }
    } else {
        echo "<script>alert('Invalid file...!!');</script>";
    }
}

// $query = "SELECT SIGN_LLX_1, SIGN_LLY_1, SIGN_URX_1, SIGN_URY_1, SIGN_PAGE_1, SIGN_LLX_2, SIGN_LLY_2, SIGN_URX_2, SIGN_URY_2, SIGN_PAGE_2 FROM EX_BA WHERE ID = '$no_ba'";
// $sql = oci_parse($conn, $query);
// oci_execute($sql);

// $data = oci_fetch_array($sql);

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>Upload Data BA</title>
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link href="../css/tombol.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
    table.excel {
        border-style: ridge;
        border-width: 1;
        border-collapse: collapse;
        font-family: sans-serif;
        font-size: 12px;
    }

    table.excel thead th,
    table.excel tbody th {
        background: #CCCCCC;
        border-style: ridge;
        border-width: 1;
        text-align: center;
        vertical-align: bottom;
    }

    table.excel tbody th {
        text-align: center;
        width: 20px;
    }

    table.excel tbody td {
        vertical-align: bottom;
    }

    table.excel tbody td {
        padding: 0 3px;
        border: 1px solid #EEEEEE;
    }
</style>

<body>
    <div align="center">
        <table width="800" align="center" class="adminheading" border="0">
            <tr>
                <th class="da2">Upload BA</th>
            </tr>
        </table>
    </div>

    <form method="post" name="import" id="import" enctype="multipart/form-data" action="">
        <table width="800" align="center" class="adminform">
            <tr height="30">
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Nama File</td>
                <td class="puso">:</td>
                <td> <input name="nama_file" type="text" class="button" value="<?= $get_nama_file ?>" required></td>
            </tr>
            <tr>
                <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
                <td class="puso">:</td>
                <td> <input name="pdf_file" type="file" class="button" accept=".pdf" required></td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td><input name="Import" type="submit" class="button" value="Import"></td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
            </tr>
            <tr>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>
                <td class="puso">&nbsp;</td>

            </tr>
        </table>
    </form>
    <br><br>



    <div align="center">
    </div>
    <p>&nbsp;</p>
    </p>
    <? include('../include/ekor.php'); ?>

</body>

</html>