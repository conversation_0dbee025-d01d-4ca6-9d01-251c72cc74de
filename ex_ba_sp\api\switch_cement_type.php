<?php
include ('../../include/ex_fungsi.php');
require_once ('../../security_helper.php');
require_once 'helper.php';
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

// --- Konfigurasi username & password ---
$valid_users = array(
    "invoice-sp" => "sp-24072025"
);

// --- <PERSON>bil credential dari header Authorization ---
if (!isset($_SERVER['PHP_AUTH_USER']) || !isset($_SERVER['PHP_AUTH_PW'])) {
    header('WWW-Authenticate: Basic realm="My API"');
    header('HTTP/1.0 401 Unauthorized');
    echo json_encode(array("status" => "401", "message" => "Authentication required"));
    exit;
}

$username = $_SERVER['PHP_AUTH_USER'];
$password = $_SERVER['PHP_AUTH_PW'];

// --- Val<PERSON>i login ---
if (!isset($valid_users[$username]) || $valid_users[$username] != $password) {
    header('HTTP/1.0 403 Forbidden');
    echo json_encode(array("status" => "403", "message" => "Invalid credentials"));
    exit;
}

$start_date = '2025-07-28'; // tanggal go-live invoice sp
$end_date = '2025-08-01'; // tanggal naiknya perbaikan timbkel
$org = '3000';

$sql = "UPDATE
	EX_TRANS_HDR
SET
	TIPE_TRANSAKSI = 'BAGX'
WHERE
	ORG = '$org'
	AND (CREATE_DATE BETWEEN TO_DATE('$start_date', 'YYYY-MM-DD') AND TO_DATE('$end_date', 'YYYY-MM-DD'))
	AND TIPE_TRANSAKSI = 'BAG'";

$stmt = oci_parse($conn, $sql);
oci_execute($stmt, OCI_DEFAULT);

$sql = "UPDATE
	EX_TRANS_HDR
SET
	TIPE_TRANSAKSI = 'BAG'
WHERE
	ORG = '$org'
	AND (CREATE_DATE BETWEEN TO_DATE('$start_date', 'YYYY-MM-DD') AND TO_DATE('$end_date', 'YYYY-MM-DD'))
	AND TIPE_TRANSAKSI = 'CURAH'";

$stmt = oci_parse($conn, $sql);
oci_execute($stmt, OCI_DEFAULT);

$sql = "UPDATE
	EX_TRANS_HDR
SET
	TIPE_TRANSAKSI = 'CURAH'
WHERE
	ORG = '$org'
	AND (CREATE_DATE BETWEEN TO_DATE('$start_date', 'YYYY-MM-DD') AND TO_DATE('$end_date', 'YYYY-MM-DD'))
	AND TIPE_TRANSAKSI = 'BAGX'";

$stmt = oci_parse($conn, $sql);
oci_execute($stmt, OCI_DEFAULT);

oci_commit($conn);
$response['status'] = "200";
$response['message'] = "Success";

// Set response header
header('Content-Type: application/json');
echo json_encode($response);
?>
