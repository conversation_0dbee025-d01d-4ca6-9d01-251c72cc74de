<? 
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=1310;//dev
//$halaman_id=2414;//prod
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id'];
$sold_to=$fungsi->sapcode($distr_id);


require_once('../include/class.translation.php');
$bhsset=trim($_SESSION['user_setbhs']);
$translatebhsxx = new Translator($bhsset);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
    <SCRIPT LANGUAGE="JavaScript">
        <!--
        alert("You are not authorized to access this page.... \n Login First...");
        //-->
    </SCRIPT>

    <a href="../index.php">Login....</a>
<?

exit();
}
 $tgl1 = date('d-m-Y', strtotime('-5 days'));
  $tgl2 = date('d-m-Y');
//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="so_distr2.php";
$distrik = $_POST['kode_distrik'];
$no_so = trim($_POST['no_so']);
$no_so = $fungsi->sapcode($no_so);
$so_type = $_POST['so_type'];
$incoterm = $_POST['incoterm'];
$status = $_POST['status'];
$no_orpp=trim($_POST['no_orpp']);

		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;
                //Edatu
                $tglEdatu1 = $_POST['tglEdatu1'];
		list($day,$month,$year)=split("-",$tglEdatu1);
		$tglEdatu1=$year.$month.$day;
                $tglEdatu2 = $_POST['tglEdatu2'];
		list($day1,$month1,$year1)=split("-",$tglEdatu2);
		$tglEdatu2=$year1.$month1.$day1;

$currentPage="so_distr2.php";
$komen="";
if(isset($_POST['cari'])){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}
		
		$fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN9");
		
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
		$fce->XVKORG = $user_org; //org
		$fce->XKUNNR = $sold_to; // sold to
		$fce->XBZIRK = $distrik; 
		$fce->XFLAG = $status;
		$fce->XAUART = $so_type;
		$fce->XINCO1 = $incoterm;
		$fce->XFLAG = $status;
                $fce->XBSTKD = $no_orpp;
                 
		if ($no_so!=""){
                    $fce->LR_VBELN1->row["SIGN"] = 'I';
                    $fce->LR_VBELN1->row["OPTION"] = 'EQ';
                    $fce->LR_VBELN1->row["LOW"] = $no_so;
                    $fce->LR_VBELN1->row["HIGH"] = '';
                    $fce->LR_VBELN1->Append($fce->LR_VBELN1->row);
		}
                
		if ($tglm!="" and $tgls!=""){
                    $fce->LR_AUDAT->row["SIGN"] = 'I';
                    $fce->LR_AUDAT->row["OPTION"] = 'BT';
                    $fce->LR_AUDAT->row["LOW"] = $tglm;
                    $fce->LR_AUDAT->row["HIGH"] = $tgls;
                    $fce->LR_AUDAT->Append($fce->LR_AUDAT->row);
		}
                if ($tglEdatu1!="" and $tglEdatu2!=""){
                    $fce->LR_EDATU->row["SIGN"] = 'I';
                    $fce->LR_EDATU->row["OPTION"] = 'BT';
                    $fce->LR_EDATU->row["LOW"] = $tglEdatu1;
                    $fce->LR_EDATU->row["HIGH"] = $tglEdatu2;
                    $fce->LR_EDATU->Append($fce->LR_EDATU->row);
		}	
		// filter reason for rejection
//		$fce->LR_ABGRU->row["SIGN"] = 'I';
//		$fce->LR_ABGRU->row["OPTION"] = 'EQ';
//		$fce->LR_ABGRU->row["LOW"] = '';
//		$fce->LR_ABGRU->Append($fce->LR_ABGRU->row);

		$fce->LR_AUGRU->row["SIGN"] = 'I';
		$fce->LR_AUGRU->row["OPTION"] = 'NE';
		$fce->LR_AUGRU->row["LOW"] = 'Z02';
		$fce->LR_AUGRU->Append($fce->LR_AUGRU->row);
	
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {
			$fce->RETURN_DATA->Reset();
                        
			$s=0;
			while ( $fce->RETURN_DATA->Next() ){                           
			$kddistr[$s]= $fce->RETURN_DATA->row["KUNNR"];
			$distr[$s]= $fce->RETURN_DATA->row["NAME1"];
			$pp_num[$s]= $fce->RETURN_DATA->row["BSTKD"];
			$co_num[$s]= $fce->RETURN_DATA->row["VGBEL"];
			$so_num[$s]= $fce->RETURN_DATA->row["VBELN"];
			$tglso[$s]= $fce->RETURN_DATA->row["AUDAT"];
			$incoterm2[$s]= $fce->RETURN_DATA->row["INCO1"];
			$price[$s]= $fce->RETURN_DATA->row["PLTYP"];
			$kddistrik[$s]= $fce->RETURN_DATA->row["BZIRK"];
			$nmdistrik[$s]= $fce->RETURN_DATA->row["BZTXT"];
			$kdproduk[$s]= $fce->RETURN_DATA->row["MATNR"];
			$produk[$s]= $fce->RETURN_DATA->row["MAKTX"];
			$plant[$s]= $fce->RETURN_DATA->row["WERKS"];
			$sotype[$s]= $fce->RETURN_DATA->row["AUART"];
			$kdshipto[$s]= $fce->RETURN_DATA->row["KUNNR2"];
			$shipto[$s]= $fce->RETURN_DATA->row["NAME2"];
			$alamat[$s]= $fce->RETURN_DATA->row["STRAS2"];
			$qty_so[$s]= $fce->RETURN_DATA->row["KWMENG"];
			$qty_do[$s]= $fce->RETURN_DATA->row["RFMNG"];
			$uom[$s]= $fce->RETURN_DATA->row["MEINS"];
			$kapal[$s]= $fce->RETURN_DATA->row["BNAME"];
			$tglkirim[$s]= $fce->RETURN_DATA->row["EDATU"];
			$harga[$s]= $fce->RETURN_DATA->row["NETWR"];
			$posnr[$s]= $fce->RETURN_DATA->row["POSNR"];
                        $nopol[$s]= $fce->RETURN_DATA->row["ZTEXT1"];
                        $kantongc[$s]= $fce->RETURN_DATA->row["ZTEXT4"];
                        $DRIVER_NAME[$s]= $fce->RETURN_DATA->row["ZTEXT6"];
                        $DRIVER_LICENSE[$s]= $fce->RETURN_DATA->row["ZTEXT7"];
                        $SHIPTO_NAME[$s]= $fce->RETURN_DATA->row["SHIPTO_NAME"];
                        $SHIPTO_ADDR[$s]= $fce->RETURN_DATA->row["SHIPTO_ADDR"];
                        $SOLD_TO_NAME[$s]= $fce->RETURN_DATA->row["SOLD_TO_NAME"];
                        $SOLD_TO_ADDR[$s]= $fce->RETURN_DATA->row["SOLD_TO_ADDR"];

			$s++;
			}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($so_num);	

}

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function finddistr(org) {
		var com_org = document.getElementById('org');		
		var strURL="cari_distr.php?org="+com_org.value;
		popUp(strURL);
		} 
		  
function ketik_distr(obj) {
	var com_org = document.getElementById('org');		
	var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() { 
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {	
					document.getElementById("distrdiv").innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP:\n');?>" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shiptoadm.php?nourut="+j+"&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shiptoadm.php?shipto="+obj.value+"&nourut="+j+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv"+j).innerHTML=req.responseText;						
				} else {
					alert("<?php $translatebhsxx->__1('There was a problem while using XMLHTTP:\n');?> "+ req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function finddistrik() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_distrik.php?sold_to="+com_sold.value;
		popUp(strURL);
}
function cekTgl(){
    var a = document.getElementById("tglEdatu1").value;
    var b = document.getElementById("tglEdatu2").value;
    var explod = a.split('-');
    var explod2 = b.split('-');
    var tgl = new Date();
    var tgl_a = tgl.setFullYear(explod[2],explod[1],explod[0]);
    var tgl_b = tgl.setFullYear(explod2[2],explod2[1],explod2[0]);
    var milisecond = 60*60*24*1000;
        var c = ((tgl_b-tgl_a)/ milisecond)+1;
    if(c>31){
        alert('<?php $translatebhsxx->__1('Sorry, Date of maximum 31 days :)');?>');
        return false;
        }
} 
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?php $translatebhsxx->__1('Aplikasi SGG Online: List of Sales Order :)');?></title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2"><?php $translatebhsxx->__1('List of Sales Order');?> </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;<?php $translatebhsxx->__1('Form Search Sales Order');?> </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('','R','tglEdatu1','','R','tglEdatu2','','R');return document.hasil">
<!-- <form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('so_type','','R','tglEdatu1','','R','tglEdatu2','','R');return document.hasil"> -->
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
     <tr>
       <td  class="puso"><?php $translatebhsxx->__1('SO Number');?> </td>
       <td  class="puso">:</td>
       <td ><input name="no_so" type="text" class="" value="<? echo $no_so; ?>" size="40" maxlength="10"/></td>
     </tr>
     <tr>
       <td  class="puso"><?php $translatebhsxx->__1('OR Number');?> </td>
       <td  class="puso">:</td>
       <td ><input name="no_orpp" type="text" class="" value="<? echo $no_orpp; ?>" size="40" maxlength="10"/></td>
     </tr> 
     <tr>
      <td  class="puso"><?php $translatebhsxx->__1('District');?></td>
      <td  class="puso">:</td>
      <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
	  <input name="sold_to" id="sold_to" class="inputlabel" type="hidden" size="10" maxlength="10" value="<?=$sold_to;?>"/>
	  	<div id="shiptodiv">
	  <input type="text" value="" class="inputlabel" id="kode_distrik" name="kode_distrik" size="10">
	  <input type="text" value="" class="inputlabel" id="nama_distrik" name="nama_distrik"  size="20"  readonly="true" >
      <input name="btn_distrik" type="button" class="button" id="btn_distrik" value="..." onClick="finddistrik()"/>
      <input name="val_error_distrik" type="hidden" id="val_error_distrik" value="0" />
    </div></td></tr>
     <tr>
      <td  class="puso"><?php $translatebhsxx->__1('SO Type');?> </td>
      <td  class="puso">:</td>
      <td ><select name="so_type" id="so_type">
		<!--<option value="">---Select SO Type---</option>-->
		<option value="">---<?php $translatebhsxx->__1('Select Order Type');?>---</option>
		<? $fungsi->or_order_type2($so_type); ?>     
		</select>
</td></tr>
     <tr>
      <td  class="puso"><?php $translatebhsxx->__1('Incoterm');?></td>
      <td  class="puso">:</td>
      <td ><select name="incoterm" id="incoterm" >
		<option value="">---<?php $translatebhsxx->__1('Select Incoterm');?>---</option>
		<? $fungsi->or_jenis_kirim2($incoterm); ?>     
		</select>	
</td></tr> 
     <tr>
      <td class="puso"><?php $translatebhsxx->__1('SO Status');?></td>
      <td class="puso">:</td>
      <td ><select name="status" id="status" >
		<option value="O">Open</option>
		<option value="C">Complete</option>
		<option value="A">All</option>
		</select>	
</td></tr>
<!--    <tr>
      <td  class="puso">Tanggal SO</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="" onClick="return showCalendar('tgl2');"/></td>
    </tr> -->
    <tr>
      <td  class="puso"><?php $translatebhsxx->__1('Delivery Date');?></td>
      <td  class="puso">:</td>
      <td ><input name="tglEdatu1" type="text" id="tglEdatu1" size=12 value="<?=$tgl1?>" onClick="return showCalendar('tglEdatu1');"/>&nbsp; to &nbsp;
	<input name="tglEdatu2" type="text" id="tglEdatu2" size=12 value="<?=$tgl2?>" onClick="return showCalendar('tglEdatu2');"/></td>
    </tr>   
	<tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="<?php $translatebhsxx->__1('Find');?>" onclick="return cekTgl();"/>    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1800" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;<?php $translatebhsxx->__1('List of Sales Order');?> <?=$nama_sold_to?></span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1800" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;<?php $translatebhsxx->__1('No.');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('No. OR');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('Contrac No.');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('Sales Order');?></strong></td>
                <td align="center"><strong><?php $translatebhsxx->__1('Line');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('SO Date');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('Delivery Date');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('SO Type');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('Incoterm');?></strong></td>
		<td align="center"><strong ><?php $translatebhsxx->__1('Price group');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Sold To Code');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Sold To');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('District Code');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('District Name');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('ShipTo Code');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Ship To');?> </strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Adress');?> </strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Plant');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Material Code');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Material');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('SO Qty');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('UOM');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Real');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('SO Open');?></strong></td>
		 <td align="center"><strong><?php $translatebhsxx->__1('Price');?></strong></td>
                 <td align="center"><strong><?php $translatebhsxx->__1('Licence Plate');?></strong></td>
                 <td align="center"><strong><?php $translatebhsxx->__1('Driver Name');?></strong></td>
                 <td align="center"><strong><?php $translatebhsxx->__1('Driver Licence');?></strong></td>
                 <td align="center"><strong><?php $translatebhsxx->__1('Bag Code');?></strong></td>
<!--		 <td align="center"><strong>Ship Name</strong></td>-->
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $pp_num[$i]; ?></td>
		<td align="center"><? echo $co_num[$i]; ?></td>
		<td align="center"><? echo $so_num[$i]; ?></td>
                <td align="center"><? echo $posnr[$i]; ?></td>
		<td align="center"><? $thn=substr($tglso[$i],0,4);
							  $bln=substr($tglso[$i],4,2);
							  $hr=substr($tglso[$i],6,2);
							  $tgl=$hr.'-'.$bln.'-'.$thn;
								echo $tgl; ?></td>
		<td align="center"><? $thn1=substr($tglkirim[$i],0,4);
							  $bln1=substr($tglkirim[$i],4,2);
							  $hr1=substr($tglkirim[$i],6,2);
							  $tglkrm=$hr1.'-'.$bln1.'-'.$thn1;
								echo $tglkrm; ?></td>
		<td align="center"><? echo $sotype[$i]; ?></td>
		<td align="center"><? echo $incoterm2[$i]; ?></td>
		<td align="center"><? echo $price[$i]; ?></td>
		<td align="left"><? echo $kddistr[$i]; ?></td>
		<td align="left"><? echo $SOLD_TO_NAME[$i]; ?></td>
		<td align="center"><? echo $kddistrik[$i]; ?></td>
		<td align="left"><? echo $nmdistrik[$i]; ?></td>
		<td align="left"><? echo $kdshipto[$i]; ?></td>
		<td align="left"><? echo $shipto[$i]; ?></td>
		<td align="left"><? echo $alamat[$i]; ?></td>
		<td align="center"><? echo $plant[$i]; ?></td>
		<td align="left"><? echo $kdproduk[$i]; ?></td>
		<td align="left"><? echo $produk[$i]; ?></td>
		<td align="right"><? echo number_format($qty_so[$i],3,",","."); ?></td>
		<td align="center"><? echo $uom[$i]; ?></td>
		<td align="right"><? echo number_format($qty_do[$i],3,",","."); ?></td>
		<td align="right"><? $sisa=$qty_so[$i]-$qty_do[$i]; echo number_format($sisa,3,",","."); ?></td>
		<td align="left"><? $hargasatuan = ($harga[$i]*100) / $qty_so[$i];
							echo number_format($hargasatuan,2,",","."); ?></td>
<!--		<td align="left"><? echo $kapal[$i]; ?></td>-->
                <td align="center"><? echo $nopol[$i]; ?></td> 
                <td align="center"><? echo $DRIVER_NAME[$i]; ?></td> 
                <td align="center"><? echo $DRIVER_LICENSE[$i]; ?></td> 
                <td align="center"><? echo $kantongc[$i]; ?></td> 
		
		</tr>
	  <? } ?>
	</table>
<p>&nbsp;</p>
<div class="nonPrint" >
<form name="export"  method="post" action="so_distr_xls2.php">
<input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
<input name="so_type" type="hidden" id="so_type" value="<?=$so_type?>"/>
<input name="status" type="hidden" id="status" size=12 value="<?=$status?>"/>
<input name="incoterm" type="hidden" id="incoterm" size=12 value="<?=$_POST['incoterm']?>"/>
<input type="hidden" value="<?=$kode_distrik;?>" class="inputlabel" id="hidden" name="kode_distrik" size="10">
<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tglEdatu1?>"/>
<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tglEdatu2?>"/>
<input name="Print" type="button" id="Print" value="<?php $translatebhsxx->__1('Print');?>"  onclick="javascript:window.print();" class="button" /> 	
&nbsp;&nbsp;
<input name="excel" type="Submit" id="excel" value="<?php $translatebhsxx->__1('Export');?>" class="button"/> 	
&nbsp;&nbsp;
<a href="so_distr2.php" target="isi" class="button"><?php $translatebhsxx->__1('Back');?></a>
</form>
</div>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
