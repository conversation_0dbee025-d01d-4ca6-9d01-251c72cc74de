-- Views EX_PAJAK_HDR_V4 source

CREATE OR REPLACE
FORCE VIEW "EX_PAJAK_HDR_V4" ("ID",
"NO_SHP_TRN",
"NO_SO",
"TANG<PERSON>L_KIRIM",
"SAL_DISTRIK",
"NAMA_SAL_DIS",
"VENDOR",
"NAMA_VENDOR",
"SPT_PAJAK",
"STATUS_PAJAK",
"KODE_PRODUK",
"NAMA_PRODUK",
"QTY_SHP",
"STATUS",
"STATUS2",
"REJECT_STATUS",
"SHP_COST",
"TARIF_COST",
"SOLD_TO",
"NAMA_SOLD_TO",
"SHIP_TO",
"<PERSON><PERSON><PERSON>POK_TRANSAKSI",
"TIPE_TRANSAKSI",
"WARNA_PLAT",
"NO_POL",
"PLANT",
"QTY_KTG_RUSAK",
"QTY_SEMEN_RUSAK",
"TOTAL_KTG_RUSAK",
"TOTAL_KTG_REZAK",
"TOTAL_SEMEN_RUSAK",
"TOTAL_KLAIM_KTG",
"TOTAL_KLAIM_SEMEN",
"PDPKS",
"DELETE_MARK",
"NO_PAJAK_EX",
"NAMA_KAPAL",
"HARGA_TEBUS",
"ORG",
"INCO",
"ORGPAJAK",
"VEHICLE_TYPE",
"TIPE_DO",
"TANGGAL_DATANG",
"TANGGAL_BONGKAR",
"FLAG_POD",
"EVIDENCE_POD1",
"EVIDENCE_POD2",
"GEOFENCE_POD",
"READY_TO_INV",
"KETERANGAN_POD",
"NO_INVOICE",
"NO_INV_SAP",
"NO_INV_VENDOR",
"NO_SHP_TRN2",
"NOTE") AS 
  b."ID",
b."NO_SHP_TRN",
b.NO_SO,
b."TANGGAL_KIRIM",
b."SAL_DISTRIK",
b."NAMA_SAL_DIS",
b."VENDOR",
b."NAMA_VENDOR",
b."SPT_PAJAK",
b."STATUS_PAJAK",
b."KODE_PRODUK",
b."NAMA_PRODUK",
b."QTY_SHP",
b."STATUS",
b."STATUS2",
b."REJECT_STATUS",
b."SHP_COST",
b."TARIF_COST",
b."SOLD_TO",
b."NAMA_SOLD_TO",
b."SHIP_TO",
b."KELOMPOK_TRANSAKSI",
b."TIPE_TRANSAKSI",
b."WARNA_PLAT",
b."NO_POL",
b."PLANT",
b."QTY_KTG_RUSAK",
b."QTY_SEMEN_RUSAK",
b."TOTAL_KTG_RUSAK",
b."TOTAL_KTG_REZAK",
b."TOTAL_SEMEN_RUSAK",
b."TOTAL_KLAIM_KTG",
b."TOTAL_KLAIM_SEMEN",
b."PDPKS",
b."DELETE_MARK",
b."NO_PAJAK_EX",
b."NAMA_KAPAL",
b."HARGA_TEBUS",
b."ORG",
b."INCO",
b."ORGPAJAK",
b."VEHICLE_TYPE",
b."TIPE_DO",
b.TANGGAL_DATANG,
b.TANGGAL_BONGKAR,
b.FLAG_POD,
b.EVIDENCE_POD1,
b.EVIDENCE_POD2,
b.GEOFENCE_POD,
b.READY_TO_INV,
b.KETERANGAN_POD,
b.no_invoice,
b.no_inv_sap,
b.no_inv_vendor,
b.NO_SHP_TRN2,
b.NOTE
FROM
(
SELECT
	DISTINCT h.id,
	h.no_shp_trn,
	h.tanggal_kirim,
	h.sal_distrik,
	h.nama_sal_dis,
	h.vendor,
	h.no_so,
	h.nama_vendor,
	P.spt_pajak,
	p.status AS status_pajak,
	h.kode_produk,
	h.nama_produk,
	NVL(h.qty_shp, 0) qty_shp,
	h.status,
	h.status2,
	h.reject_status,
	h.shp_cost,
	h.tarif_cost,
	h.sold_to,
	h.nama_sold_to,
	h.ship_to,
	h.kelompok_transaksi,
	h.tipe_transaksi,
	h.warna_plat,
	h.no_pol,
	h.plant,
	NVL(h.qty_ktg_rusak, 0) AS qty_ktg_rusak,
	NVL(h.qty_semen_rusak, 0) AS qty_semen_rusak,
	NVL(h.TOTAL_KTG_RUSAK, 0) AS TOTAL_KTG_RUSAK,
	NVL(h.TOTAL_KTG_REZAK, 0) AS TOTAL_KTG_REZAK,
	NVL(h.TOTAL_SEMEN_RUSAK, 0) AS TOTAL_SEMEN_RUSAK,
	NVL(h.TOTAL_KLAIM_KTG, 0) AS TOTAL_KLAIM_KTG,
	NVL(h.TOTAL_KLAIM_SEMEN, 0) AS TOTAL_KLAIM_SEMEN,
	NVL(h.PDPKS, 0) AS PDPKS,
	h.delete_mark,
	h.no_pajak_ex,
	h.nama_kapal,
	h.harga_tebus,
	h.org,
	h.inco,
	p.org AS orgpajak,
	h.vehicle_type,
	h.tipe_do,
	NVL( TO_CHAR( TANGGAL_DATANG, 'DD-MM-YYYY HH24:MI:SS' ), '00:00:00' ) AS TANGGAL_DATANG,
	NVL( TO_CHAR( TANGGAL_BONGKAR, 'DD-MM-YYYY HH24:MI:SS' ), '00:00:00' ) AS TANGGAL_BONGKAR,
	h.FLAG_POD,
	h.EVIDENCE_POD1,
	h.EVIDENCE_POD2,
	h.GEOFENCE_POD,
	h.READY_TO_INV,
	h.KETERANGAN_POD,
	h.no_invoice,
	h.no_inv_sap,
	h.no_inv_vendor,
	h.NO_SHP_TRN2,
	h.NOTE
FROM
	ex_trans_hdr h
JOIN ex_pajak_vendor p ON
	h.VENDOR = p.KODE_VENDOR
WHERE
	h.DELETE_MARK = '0'
	AND p.DELETE_MARK = '0'
	AND h.TANGGAL_KIRIM >= p.START_DATE
	AND h.tanggal_kirim <= p.end_date
	AND h.ORG = p.ORG) b
LEFT JOIN EX_INPUTCLAIM_SEMEN a ON
( a.NO_SPJ = b.NO_SHP_TRN
	AND a.DELETE_MARK = '0');

-- Table: EX_MASTER_CONFIG
CREATE TABLE EX_MASTER_CONFIG (
    ID                NUMBER PRIMARY KEY,
    NAMA              VARCHAR2(100) NOT NULL,
    VALUE             VARCHAR2(500),
    CREATED_BY        VARCHAR2(50),
    CREATED_DATE      DATE DEFAULT SYSDATE,
    LAST_UPDATE_DATE  DATE,
    LAST_UPDATE_BY    VARCHAR2(50)
);

-- Sequence untuk ID
CREATE SEQUENCE SEQ_EX_MASTER_CONFIG START WITH 1 INCREMENT BY 1;

-- Trigger untuk auto-insert ID
CREATE OR REPLACE TRIGGER TRG_EX_MASTER_CONFIG_BI
BEFORE INSERT ON EX_MASTER_CONFIG
FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_EX_MASTER_CONFIG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;

-- Create Trigger for auto update LAST_UPDATED_DATE
CREATE OR REPLACE TRIGGER TRG_EX_MASTER_CONFIG_UPDATE
BEFORE UPDATE ON EX_MASTER_CONFIG
FOR EACH ROW
BEGIN
  :NEW.LAST_UPDATED_DATE := SYSDATE;
END;

-- Table: EX_USER_MASTER_CONFIG
CREATE TABLE EX_USER_MASTER_CONFIG (
    ID           NUMBER PRIMARY KEY,
    USER_ID      VARCHAR2(50) NOT NULL,
    CONFIG_ID    NUMBER NOT NULL,
    CREATED_BY   VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE
);

CREATE SEQUENCE SEQ_USER_MASTER_CONFIG
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_USER_MASTER_CONFIG_BI
BEFORE INSERT ON EX_USER_MASTER_CONFIG
FOR EACH ROW
BEGIN
  IF :NEW.ID IS NULL THEN
    SELECT SEQ_USER_MASTER_CONFIG.NEXTVAL INTO :NEW.ID FROM DUAL;
  END IF;
END;

CREATE TABLE EX_USER_APPROVER (
    ID        NUMBER(10)     PRIMARY KEY,
    USER_ID   VARCHAR2(50)   NOT NULL,
    ORG       VARCHAR2(100)
);

-- Sequence untuk ID
CREATE SEQUENCE SEQ_EX_USER_APPROVER
START WITH 1
INCREMENT BY 1
NOCACHE;

-- Trigger untuk auto-insert ID
CREATE OR REPLACE TRIGGER TRG_EX_USER_APPROVER_ID
BEFORE INSERT ON EX_USER_APPROVER
FOR EACH ROW
BEGIN
  IF :NEW.ID IS NULL THEN
    SELECT SEQ_EX_USER_APPROVER.NEXTVAL INTO :NEW.ID FROM DUAL;
  END IF;
END;

-- Master Data 
INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Batas Waktu Approval(Jam) - Approval Pejabat Transportasi 2', '48', 'admin', TIMESTAMP '2025-05-09 03:26:41.000000', TIMESTAMP '2025-05-09 03:26:41.000000', 'ADMIN_PORTAL_SP', '3000', 'single_parameter');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Batas Waktu Approval(Jam) - Approval Pejabat Transportasi 1', '48', 'admin', TIMESTAMP '2025-05-09 03:26:41.000000', TIMESTAMP '2025-05-09 03:26:41.000000', 'ADMIN_PORTAL_SP', '3000', 'single_parameter');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Batas Waktu Approval(Jam) - Approval Admin Transportasi', '48', 'admin', TIMESTAMP '2025-05-09 03:26:41.000000', TIMESTAMP '2025-05-09 03:26:41.000000', 'ADMIN_PORTAL_SP', '3000', 'single_parameter');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Batas Waktu Approval(Jam) - Approval Ekspeditur', '48', 'admin', TIMESTAMP '2025-05-09 03:26:41.000000', TIMESTAMP '2025-05-09 03:26:41.000000', 'ADMIN_PORTAL_SP', '3000', 'single_parameter');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Batas Jarak Geofence(Meter)', '6500', 'admin', TIMESTAMP '2025-05-09 03:27:02.000000', TIMESTAMP '2025-05-09 03:27:02.000000', 'ADMIN_PORTAL_SP', '3000', 'single_parameter');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Approver Ekspeditur - Auto BASTP', '1469', 'admin', TIMESTAMP '2025-05-09 03:27:02.000000', TIMESTAMP '2025-05-09 03:27:02.000000', 'ADMIN_PORTAL_SP', '3000', 'multiple_user');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Approver Ekspeditur - Manual BASTP', '1469', 'admin', TIMESTAMP '2025-05-09 03:27:02.000000', TIMESTAMP '2025-05-09 03:27:02.000000', 'ADMIN_PORTAL_SP', '3000', 'multiple_user');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Approver Admin Transportasi', '1470', 'admin', TIMESTAMP '2025-05-09 03:27:02.000000', TIMESTAMP '2025-05-09 03:27:02.000000', 'ADMIN_PORTAL_SP', '3000', 'multiple_user');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Approver Pejabat Transportasi 1', '1471', 'admin', TIMESTAMP '2025-05-09 03:27:02.000000', TIMESTAMP '2025-05-09 03:27:02.000000', 'ADMIN_PORTAL_SP', '3000', 'multiple_user');

INSERT INTO EX_MASTER_CONFIG
(NAMA, VALUE, CREATED_BY, CREATED_DATE, LAST_UPDATE_DATE, LAST_UPDATE_BY, ORG, VALUE_TYPE)
VALUES('Approver Pejabat Transportasi 2', '1472', 'admin', TIMESTAMP '2025-05-09 03:27:02.000000', TIMESTAMP '2025-05-09 03:27:02.000000', 'ADMIN_PORTAL_SP', '3000', 'multiple_user');

INSERT INTO EX_INVOICE_DIRUT_VENDOR
(KODE_VENDOR, NAMA_DIRUT, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY)
VALUES('BIROTRANS_SP', 'Suryadi Wizar', NULL, NULL, NULL, NULL);


-- INVOICE SMBR --

-- TABLE SHIPMENT COST --
CREATE TABLE EX_INVOICE_SMBR_SHIPMENT_COST
(
  ID        NUMBER(10)     PRIMARY KEY,
  NO_SHP_TRN VARCHAR2(100) DEFAULT '',
  TKNUM      VARCHAR2(100) DEFAULT '',
  EXTI1      VARCHAR2(100) DEFAULT '',
  FKNUM      VARCHAR2(100) DEFAULT '',
  FKPOS      VARCHAR2(100) DEFAULT '',
  NETWR      VARCHAR2(100) DEFAULT '',
  FKPTY      VARCHAR2(100) DEFAULT '',
  WERKS      VARCHAR2(100) DEFAULT '',
  EBELN      VARCHAR2(100) DEFAULT '',
  EBELP      VARCHAR2(100) DEFAULT '',
  LBLNI      VARCHAR2(100) DEFAULT '',
  STABR      VARCHAR2(100) DEFAULT '',
  KOSTL      VARCHAR2(100) DEFAULT '',
  PRCTR      VARCHAR2(100) DEFAULT '',
  BANKN      VARCHAR2(100) DEFAULT '',
  BANKA      VARCHAR2(100) DEFAULT '',
  BRNCH      VARCHAR2(100) DEFAULT '',
  SAKTO      VARCHAR2(100) DEFAULT '',
  NETWR_DO   VARCHAR2(100) DEFAULT '',
  NTGEW      VARCHAR2(100) DEFAULT '',
  LIFNR      VARCHAR2(100) DEFAULT '',
  NAME1      VARCHAR2(100) DEFAULT '',
  BVTYP      VARCHAR2(100) DEFAULT '',
  BRAN1      VARCHAR2(100) DEFAULT '',
  VTEXTX     VARCHAR2(100) DEFAULT ''
);

CREATE SEQUENCE SEQ_INVOICE_SMBR_SHIPMENT_COST
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_INVOICE_SMBR_SHIPMENT_COST
BEFORE INSERT ON EX_INVOICE_SMBR_SHIPMENT_COST
FOR EACH ROW
BEGIN
  IF :NEW.ID IS NULL THEN
    SELECT SEQ_INVOICE_SMBR_SHIPMENT_COST.NEXTVAL INTO :NEW.ID FROM DUAL;
  END IF;
END;

-- TABLE RUN PPL --
CREATE TABLE EX_INVOICE_SMBR_RUN_PPL
(
  ID        			 NUMBER(10)     PRIMARY KEY,
  NO_INV			     VARCHAR2(100) DEFAULT '',
  X_ACC_DOC_MIR7         VARCHAR2(100) DEFAULT '',
  X_ACC_DOC_INVOICE      VARCHAR2(100) DEFAULT '',
  X_INVOICEDOCNUMBER     VARCHAR2(100) DEFAULT '',
  X_ACCNUMBER            VARCHAR2(100) DEFAULT '',
  X_RETURN_TYPE          VARCHAR2(100) DEFAULT '',
  X_RETURN_MESSAGE       VARCHAR2(100) DEFAULT '',
  X_RETURN_ID            VARCHAR2(100) DEFAULT '',
  X_RETURN_NUMBER        VARCHAR2(100) DEFAULT ''
);

CREATE SEQUENCE SEQ_INVOICE_SMBR_RUN_PPL
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_INVOICE_SMBR_RUN_PPL
BEFORE INSERT ON EX_INVOICE_SMBR_RUN_PPL
FOR EACH ROW
BEGIN
  IF :NEW.ID IS NULL THEN
    SELECT SEQ_INVOICE_SMBR_RUN_PPL.NEXTVAL INTO :NEW.ID FROM DUAL;
  END IF;
END;

-- TABLE POSTING PPL
CREATE TABLE EX_INVOICE_SMBR_POSTING_PPL
(
  ID        			 NUMBER(10)     PRIMARY KEY,
  NO_INV			     VARCHAR2(100) DEFAULT '',
  PSTNG_DATE       VARCHAR2(100) DEFAULT '',
  DOC_DATE         VARCHAR2(100) DEFAULT '',
  COMP_CODE        VARCHAR2(100) DEFAULT '',
  DIFF_INV         VARCHAR2(100) DEFAULT '',
  FISC_YEAR        VARCHAR2(100) DEFAULT '',
  E_BELNR          VARCHAR2(100) DEFAULT '',
);

CREATE SEQUENCE SEQ_INVOICE_SMBR_POSTING_PPL
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_INVOICE_SMBR_POSTING_PPL
BEFORE INSERT ON EX_INVOICE_SMBR_POSTING_PPL
FOR EACH ROW
BEGIN
  IF :NEW.ID IS NULL THEN
    SELECT SEQ_INVOICE_SMBR_POSTING_PPL.NEXTVAL INTO :NEW.ID FROM DUAL;
  END IF;
END;
