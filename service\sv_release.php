<?php
//by @liyantanto 2014

include("../include/lib/nusoap.php");
$server = new soap_server();
$server->configureWSDL('wsdlQuery', 'urn:wsdlQuery');

require_once ("autorisasi.php");
$fautoris= new autorisasi();

$server->wsdl->addComplextype(
	'wsdlQueryRequest',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'token' => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'token', 'type' => 'xsd:string'),
		'distrik' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'distrik', 'type' => 'xsd:string'),            
		'soNumber' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'soNumber', 'type' => 'xsd:string'),
                'noSPJ' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'noSPJ', 'type' => 'xsd:string'),
		'spjStatus' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'soStatus', 'type' => 'xsd:string'),
		'tipeOrder' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'tipeOrder', 'type' => 'xsd:string'),
		'noEkspeditur' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'noEkspeditur', 'type' => 'xsd:string'),
                'plant' => array('minOccurs' => '0', 'maxOccurs' => '1', 'name' => 'plant', 'type' => 'xsd:string'),
		'dateFrom' => array('minOccurs' => '1', 'maxOccurs' => '1', 'name' => 'dateFrom', 'type' => 'xsd:string'),
		'dateTo' => array('minOccurs' => '1', 'maxOccurs' => '1','name' => 'dateTo', 'type' => 'xsd:string')
	)
);

$server->wsdl->addComplextype(
	'wsdlQueryResponse',
	'complexType',
	'struct',
	'sequence',
	'',
	array(
		'ResponseCode' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ResponseCode', 'type' => 'xsd:string'),
		'ResponseMessage' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ResponseMessage', 'type' => 'xsd:string'),
		'detailData' => array('minOccurs' => '0', 'maxOccurs' => 'unbounded','name' => 'detailData', 'type' => 'tns:DetailData')
	)
);

$server->wsdl->addComplexType(
	'DetailData',
	'complexType',
	'struct',
	'sequence',
	'',        
	array(
                'com' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'com', 'type' => 'xsd:string'),
                'com_name' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'com_name', 'type' => 'xsd:string'),            
		'noPP' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noPP', 'type' => 'xsd:string'),
                'tglPP' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'tglPP', 'type' => 'xsd:string'),
                'noKontrak' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noKontrak', 'type' => 'xsd:string'),
                'noSO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noSO', 'type' => 'xsd:string'),
                'tipeOrder' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'tipeOrder', 'type' => 'xsd:string'), 
                'tglSO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'tglSO', 'type' => 'xsd:string'),
                'incoterm' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'incoterm', 'type' => 'xsd:string'),
                'noDO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noDO', 'type' => 'xsd:string'),
                'tglDO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'tglDO', 'type' => 'xsd:string'),  
                'kodeproduk' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'produk', 'type' => 'xsd:string'),
                'produk' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'produk', 'type' => 'xsd:string'),
                'qtyDO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'qtyDO', 'type' => 'xsd:float'),            
                'uom' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'uom', 'type' => 'xsd:string'),
                'qtyTO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'qtyTO', 'type' => 'xsd:float'),   
                'klpHarga' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'klpHarga', 'type' => 'xsd:float'),
                'noSPJ' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noSPJ', 'type' => 'xsd:string'),
                'tglSPJ' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'tglSPJ', 'type' => 'xsd:string'),
                'jamSPJ' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'jamSPJ', 'type' => 'xsd:string'),
                'noSPPS' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noSPPS', 'type' => 'xsd:string'),
                'noPolisi' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noPolisi', 'type' => 'xsd:string'),
                'namaSupir' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'namaSupir', 'type' => 'xsd:string'),
                'kodeDistributor' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'kodeDistributor', 'type' => 'xsd:string'),
                'distributor' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'distributor', 'type' => 'xsd:string'),
                'kodeShipto' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'kodeShipto', 'type' => 'xsd:string'),
                'namaShipto' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'namaShipto', 'type' => 'xsd:string'),
                'alamatShipto' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'alamatShipto', 'type' => 'xsd:string'),
                'kodeDistrik' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'kodeDistrik', 'type' => 'xsd:string'),
                'distrik' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'distrik', 'type' => 'xsd:string'),
                'kodeEkspeditur' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'kodeEkspeditur', 'type' => 'xsd:string'),
                'ekspeditur' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'ekspeditur', 'type' => 'xsd:string'),
                'kodePlant' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'kodePlant', 'type' => 'xsd:string'),
                'plant' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'plant', 'type' => 'xsd:string'),
                'namaKapal' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'namaKapal', 'type' => 'xsd:string'),
                'status' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'status', 'type' => 'xsd:string'),
		'lineSO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'lineSO', 'type' => 'xsd:string'),
                'harga' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'harga', 'type' => 'xsd:string'),
                'nomerPO' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'nomerPO', 'type' => 'xsd:string'),
                'noTransaksi' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'noTransaksi', 'type' => 'xsd:string'),
                'shpTipe' => array('minOccurs' => '0', 'maxOccurs' => '1','name' => 'shpTipe', 'type' => 'xsd:string')   
            
            
	)
);

$server->register('getListRelease',					// method name
	array('wsdlQueryRequest' => 'tns:wsdlQueryRequest'),		// input parameters
	array('wsdlQueryResponse' => 'tns:wsdlQueryResponse'),	// output parameters
	'urn:wsdlQuery',						// namespace
	'urn:wsdlQuery#getListRelease',					// soapaction
	'rpc',									// style
	'literal',								// use
	'wsdlQuery Request...'		// documentation
);

// Define the method as a PHP function
function getListRelease($inqueryRequest){
    global $server;
    global $fautoris;
    unset($dataDetail);	
    
    //Autorisasi
    $token_in = trim($inqueryRequest['token']);            
    $role=$fautoris->login($token_in);
    $jmlData=count($role['dataUserAuto']);
    
    if($role['status']==true && $jmlData>0){
    $aksesser=$fautoris->aksesservice($token_in);//pembatasan akses ke service
    if($aksesser['status']==true){     
        $user_id = trim($role['dataUserAuto']['USER_ID']);
	$user_org = trim($role['dataUserAuto']['ORG']);
	$sold_to = trim($role['dataUserAuto']['DISTRIBUTOR_ID']);
	$distrik = trim($inqueryRequest['distrik']);
	$no_so = trim($inqueryRequest['soNumber']);
	$status = trim($inqueryRequest['spjStatus']);
	$tipeOrder = trim($inqueryRequest['tipeOrder']);
        $eks_in =trim($role['dataUserAuto']['VENDOR']);
        $noSPJin = trim($inqueryRequest['noSPJ']);
        $plantn = trim($inqueryRequest['plant']);
        if($status==''){$status='70';}
    //role 
    $dirr = $_SERVER['PHP_SELF'];    
    $rolenn=$fautoris->keamananser($dirr,$user_id);//pembatasan akses ke service
    if($rolenn==true){
        
        if($eks_in!=''){
            $noEkspeditur = $eks_in;
        }else{
            $noEkspeditur = trim($inqueryRequest['noEkspeditur']);
        }
	$tglm = trim($inqueryRequest['dateFrom']);
	$tgls = trim($inqueryRequest['dateTo']);
               
        if($user_org!='' && $tglm!='' && $tgls!='' && $status!=''){           
        if(date("Ymd",strtotime("$tgls -31 day"))<=$tglm){           
        
        require_once ("../include/sapclasses/sap.php");
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
        //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_030.php"; //dev sap
        //$sap->Connect($link_koneksi_sap);
	if ($sap->GetStatus() != 'SAPRFC_OK' ){ ##--- Ketika Gagal Koneksi SAP ---##
			
		$ResponseCode = '01';
		$ResponseMessage = 'Gagal koneksi ke SAP';
		$responseRequest = array (
			'ResponseCode' => $ResponseCode,
			'ResponseMessage' => $ResponseMessage,
			'detailData' => $dataDetail
		);
	}else{
		$sap->Open ();
		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
		if ($fce==false){ ##--- Ketika RFC Tidak ada ---##
			
			$ResponseCode = '02';
			$ResponseMessage = 'RFC Tidak Ditemukan';
			$responseRequest = array (
				'ResponseCode' => $ResponseCode,
				'ResponseMessage' => $ResponseMessage,
				'detailData' => $dataDetail
			);
		}else{
			$fce->X_VKORG = $user_org; //org
                        if($plantn!=''){
                            $fce->X_WERKS=$plantn;
                        }
                        if($status=='70'){
                            $fce->X_TGL1	= $tglm; 
                            $fce->X_TGL2	= $tgls;
                        }
                        $fce->X_STATUS	= $status;
                        $fce->X_NOSPJ	= $noSPJin;
                        
                        //cek jika in distributor
                        $data_indist        = $fautoris->cekInDist($sold_to);
                        $jml_data_distin    = count($data_indist);
                        
                        if($sold_to!=''){
                            $sold_to = sprintf('%010d',trim($sold_to));
                        }
                        
                         if($jml_data_distin > 0){
                            
                            for($i = 0;$i < $jml_data_distin ;$i++){
                                
                                $fce->LR_KUNNR->row["SIGN"]    = 'I';
				$fce->LR_KUNNR->row["OPTION"]  = 'EQ';
				$fce->LR_KUNNR->row["LOW"]     = sprintf('%010d',trim($data_indist[$i][DISTRIBUTOR_IN]));//
				$fce->LR_KUNNR->row["HIGH"]    = '';
				$fce->LR_KUNNR->Append($fce->LR_KUNNR->row);
                                
                            }
                        }else{
                               $fce->X_KUNNR = $sold_to; // sold to
                        }
			
			$fce->X_BZIRK = $distrik; // distrik
                        if($no_so!=''){
                            $no_so = sprintf('%010d',trim($no_so));
                        }
			$fce->X_VBELN =  $no_so;// nomer so
                        
                        if($noEkspeditur!=''){
                            $noEkspeditur = sprintf('%010d',trim($noEkspeditur));
                        }
			$fce->X_LIFNR =  $noEkspeditur; // ekspeditur
                        
                        if ($tipeOrder == 'X') {
                                $fce->X_KONFIRMASI = $tipeOrder;
                        } else {
                                $fce->X_AUART = $tipeOrder; // tipe so
                        }
                        
                        if ($user_org == '3000') {
                                $fce->X_WITH_LOOP = 'X';
                        }
                        
                        $conn = $fautoris->koneksi();
                        $mNameOrg=$fautoris->getNameCom($conn); 
                        $mp_coics=$fautoris->getComin($conn,$user_org);   
                        //incompany
                        if(count($mp_coics)>0){
                            foreach ($mp_coics as $keyOrg2 => $valorgm2){
                                if($keyOrg2=='2000'){
                                    continue;
                                }
                                $fce->LRI_VKORG->row['SIGN']='I';
                                $fce->LRI_VKORG->row['OPTION']='EQ';
                                $fce->LRI_VKORG->row['LOW']=$keyOrg2;
                                $fce->LRI_VKORG->row['HIGH']='';
                                $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
                            }
                        }
                        
                                $fce->LRI_VKORG->row['SIGN']='I';
                                $fce->LRI_VKORG->row['OPTION']='EQ';
                                $fce->LRI_VKORG->row['LOW']='7900';
                                $fce->LRI_VKORG->row['HIGH']='';
                                $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
		
			$fce->Call();
//                        echo "<pre>";
//                        print_r($fce);
//                        echo "</pre>";
                        if ($fce->GetStatus() == SAPRFC_OK ) {
			$fce->ZDATA->Reset();                        
			$s=0;unset($pesan);
                        $tipe = trim($fce->RETURN["TYPE"]);			
                        $msg = trim($fce->RETURN["MESSAGE"]);	
                        $pesan = $msg." (".$tipe.")";       
			while( $fce->ZDATA->Next() ){          
                            	
                            $dataDetail[$s]["com"] = trim($fce->ZDATA->row["NMORG"]);//company
                            $dataDetail[$s]["com_name"] = trim($mNameOrg[$fce->ZDATA->row["NMORG"]]);//company name                                
                            $dataDetail[$s]["noPP"] = trim($fce->ZDATA->row["NO_MINTA"]);//No PP
                            $dataDetail[$s]["tglPP"] = trim($fce->ZDATA->row["TGL_MINTA"]);//Tgl PP		
                            $dataDetail[$s]["noKontrak"] = trim($fce->ZDATA->row["VGBEL"]);//No Kontrak
                            $dataDetail[$s]["noSO"] = trim($fce->ZDATA->row["NO_SO"]);//No SO
                            $dataDetail[$s]["tipeOrder"] = trim($fce->ZDATA->row["AUART"]);//Tiper Order
                            $dataDetail[$s]["tglSO"] = trim($fce->ZDATA->row["AUDAT"]);//Tgl SO
                            $dataDetail[$s]["incoterm"] = trim($fce->ZDATA->row["INCOTERM"]);//Incoterm		
                            $dataDetail[$s]["noDO"] = trim($fce->ZDATA->row["NO_DO"]);//No DO
                            $dataDetail[$s]["tglDO"] = trim($fce->ZDATA->row["TGL_DO"]);//Tgl DO
                            $dataDetail[$s]["kodeproduk"] = trim($fce->ZDATA->row["ITEM_NO"]);//Kode Produk                            
                            $dataDetail[$s]["produk"] = trim($fce->ZDATA->row["PRODUK"]);//Produk
                            $dataDetail[$s]["qtyDO"] = trim($fce->ZDATA->row["KWANTUM"]*1);//Qty DO
                            $dataDetail[$s]["uom"] = trim($fce->ZDATA->row["UOM"]);//UOM
                            $dataDetail[$s]["qtyTO"] = trim($fce->ZDATA->row['KWANTUMX']*1);//Qty DO TO
                            $dataDetail[$s]["klpHarga"] = trim($fce->ZDATA->row["PLTYP"]);//Klp Harga                            
                            $dataDetail[$s]["noTransaksi"] = trim($fce->ZDATA->row["NO_TRANSAKSI"]);//No Transaksi
                            $dataDetail[$s]["noSPJ"] = trim($fce->ZDATA->row["NO_SPJ"]);//No SPJ
                            $dataDetail[$s]["tglSPJ"] = trim($fce->ZDATA->row["TGL_CMPLT"]);//Tgl SPJ
                            $dataDetail[$s]["jamSPJ"] = trim($fce->ZDATA->row["JAM_CMPLT"]);//Jam SPJ
                            $dataDetail[$s]["noSPPS"] = trim($fce->ZDATA->row["NO_SPPS"]);//No SPPS
                            $dataDetail[$s]["noPolisi"] = trim($fce->ZDATA->row["NO_POLISI"]);//No Polisi
                            $dataDetail[$s]["namaSupir"] = trim($fce->ZDATA->row["NAMA_SOPIR"]);//Nama Sopir
                            $dataDetail[$s]["kodeDistributor"] = trim($fce->ZDATA->row["SOLD_TO"]);//Kode Distributor
                            $dataDetail[$s]["distributor"] = trim($fce->ZDATA->row["NAMA_SOLD_TO"]);//Distributor
                            $dataDetail[$s]["kodeShipto"] = trim($fce->ZDATA->row["KODE_DA"]);//Kode Shipto
                            $dataDetail[$s]["namaShipto"] = trim($fce->ZDATA->row["NAMA_TOKO"]);//Nama Shipto
                            $dataDetail[$s]["alamatShipto"] = trim($fce->ZDATA->row["ALAMAT_DA"]);//Alamat Shipto
                            $dataDetail[$s]["kodeDistrik"] = trim($fce->ZDATA->row["AREA"]);//Kode Distrik
                            $dataDetail[$s]["distrik"] = trim($fce->ZDATA->row["NAMA_AREA"]);//Distrik
                            $dataDetail[$s]["kodeEkspeditur"] = trim($fce->ZDATA->row["NO_EXPEDITUR"]);//Kode Ekspeditur
                            $dataDetail[$s]["ekspeditur"] = trim($fce->ZDATA->row["NAMA_EXPEDITUR"]);//Ekspeditur
                            $dataDetail[$s]["kodePlant"] = trim($fce->ZDATA->row["PLANT"]);//Kd Plant
                            $dataDetail[$s]["plant"] = trim($fce->ZDATA->row["NAMA_PLANT"]);//Plant
                            $dataDetail[$s]["namaKapal"] = trim($fce->ZDATA->row["NAMA_KAPAL"]);//Nama Kapal 
                            $dataDetail[$s]["status"] = trim($fce->ZDATA->row["STATUS"]);//Status
                            $dataDetail[$s]["lineSO"] = trim($fce->ZDATA->row["LINE_SO"]);//Line SO
                            $dataDetail[$s]["nomerPO"] = trim($fce->ZDATA->row["NO_PO"]);//nomor po
                            if ($fce->ZDATA->row["KWMENG"] > 0){
                                $harga_satuan =  @($fce->ZDATA->row["HARGA"]/$fce->ZDATA->row["KWMENG"]);
                            }else{
                                $harga_satuan =  0;
                            }    
                            if($sold_to==''){$harga_satuan=0;}
                            $harga_shp= $harga_satuan * $fce->ZDATA->row["KWANTUM"];	
                            $dataDetail[$s]["harga"] = $harga_shp;//harga satuan   
                            $dataDetail[$s]["shpTipe"] = trim($fce->ZDATA->row["ORDER_TYPE"]);//order type
                            $s++;
			}
                        
                        }else{
                          $fce->PrintStatus();                            
                        }
                        
                        $fce->Close();	
                        $sap->Close();	
			
                        if(count($dataDetail)>0){
                            $ResponseCode = '00';
                            $ResponseMessage = 'Sukses';
                            $responseRequest = array (
                                    'ResponseCode' => $ResponseCode,
                                    'ResponseMessage' => $ResponseMessage.", ".$pesan,
                                    'detailData' => $dataDetail
                            );
                        }else{
                            $ResponseCode = '01';
                            $ResponseMessage = $pesan;
                            $responseRequest = array (
                                    'ResponseCode' => $ResponseCode,
                                    'ResponseMessage' => $ResponseMessage,
                                    'detailData' => $dataDetail
                            );
                        }
		}
	}
        
        }else{
            $ResponseCode = '01';
            $ResponseMessage = 'Tanggal tidak boleh melebihi 31 hari';
            $responseRequest = array (
                    'ResponseCode' => $ResponseCode,
                    'ResponseMessage' => $ResponseMessage,
                    'detailData' => $dataDetail
            );
        }
        
        }else{
            $ResponseCode = '01';
            $ResponseMessage = 'Silahkan Cek Parameter Inputan';
            $responseRequest = array (
                    'ResponseCode' => $ResponseCode,
                    'ResponseMessage' => $ResponseMessage,
                    'detailData' => $dataDetail
            );
        }
    }else{
        $ResponseCode = '01';
        $ResponseMessage = 'Tidak ada akses terhadap service ini';
        $responseRequest = array (
                'ResponseCode' => $ResponseCode,
                'ResponseMessage' => $ResponseMessage,
                'detailData' => $dataDetail
        );
    }  
    }else{
        $ResponseCode = '01';
        $ResponseMessage = $aksesser['keterangan'];
        $responseRequest = array (
                'ResponseCode' => $ResponseCode,
                'ResponseMessage' => $ResponseMessage,
                'detailData' => $dataDetail
        );
    }        
    }else{
        $ResponseCode = '01';
        $ResponseMessage = $role['keterangan'];
        $responseRequest = array (
                'ResponseCode' => $ResponseCode,
                'ResponseMessage' => $ResponseMessage,
                'detailData' => $dataDetail
        );
    }
    
    //log service
    $byLog='SV_RELEASE';
    $log_servie=$fautoris->log_service($inqueryRequest,$responseRequest,$byLog,$token_in);
    
    return $responseRequest;
}

if ( !isset( $HTTP_RAW_POST_DATA ) ) $HTTP_RAW_POST_DATA =file_get_contents( 'php://input' );
$server->service($HTTP_RAW_POST_DATA);
?>
