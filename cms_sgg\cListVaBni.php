<?php

/**
 * Description of cEntryBayarVa
 *
 * <AUTHOR> D Munir <<EMAIL>>
 * @since 1.0
 */
class cListVaBni extends dController
{

    protected function pageIds()
    {
        return array(
            'index' => true,
            'listInvoice' => true,
        );
    }

    public function index()
    {
        $orgs = array(
            array('id' => '', 'text' => '-')
        );
        foreach (dee::$app->fungsi->arrayorg() as $key => $value) {
            $orgs[] = array('id' => $key, 'text' => $key . '- ' . $value);
        }
        return $this->render('list_va_bni', array('orgs' => $orgs));
    }

    public function listInvoice()
    {
        $model = new mListVaBni();
        $org = $this->request->get('org');
        $vanum = $this->request->get('vanum');
        $invoices = $model->getInvoices($org,$vanum);
        return array(
            'total' => count($invoices), 
            'rows' => $invoices,
        );
    }
}
