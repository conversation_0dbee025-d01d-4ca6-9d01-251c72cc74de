<?php
session_start();
require_once 'api/smbr.php';
require_once 'Excel/reader.php';
require_once ('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$org=$_SESSION['user_org'];

if($org != '1000'){
    echo 'Maaf, menu ini bukan untuk company yang saat ini anda pilih';
    exit;
};

function showMessage($message)
{
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Result!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
        </div>
    </div>
<?php
}

if (isset($_POST['upload']) && $_FILES['excel_file']['error'] == 0) {
    $file_name = $_FILES['excel_file']['name'];
    $file_tmp = $_FILES['excel_file']['tmp_name'];

    $check = validate_excel($file_name, $file_tmp);
    if(!$check['status']){
        echo "<script>alert('" . $check['message'] ."');</script>";
        exit;
    }

    $data = new Spreadsheet_Excel_Reader();
    $data->setOutputEncoding('CP1251');
    $data->read($file_tmp);

    $sheet = $data->sheets[0];
    $rows = count($sheet['cells']);

    if($rows <= 1){
        echo "Data upload kosong";
    }

    $fields = array(
        'NO_SHP_EX',
        'PLANT', 'SOLD_TO', 'SHIP_TO', 'SAL_DISTRIK', 'SAL_GROUP', 'SAL_OFFICE', 'VENDOR',
        'NAMA_VENDOR', 'NAMA_SAL_OFF', 'NAMA_SAL_GRP', 'NAMA_SAL_DIS', 'NO_POL',
        'VEHICLE_TYPE', 'WARNA_PLAT', 'KODE_PRODUK', 'NAMA_PRODUK', 'KODE_KANTONG',
        'NAMA_KANTONG', 'QTY_SHP', 'TIPE_TRANSAKSI','TANGGAL_KIRIM',
        'NAMA_PLANT', 'STATUS_APP', 'NAMA_SOLD_TO', 'NAMA_SHIP_TO',
        'SATUAN_SHP', 'KELOMPOK_TRANSAKSI', 'KAPASITAS_VEHICLE',
        'DOC_SHP', 'TIPE_DO', 'INCO',
        'SUPIR', 'NO_SO'
        // 'QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR',
        // 'KETERANGAN_POD', 'EVIDENCE_POD1', 'EVIDENCE_POD2', 'GEOFENCE_POD'
    );

    for ($i = 2; $i <= $rows; $i++) {
        $param = array();
        for ($j = 0; $j < count($fields); $j++) {
            $value = isset($sheet['cells'][$i][$j + 1]) ? $sheet['cells'][$i][$j + 1] : '';
            $param[$fields[$j]] = $value;
        }
        $param['CREATED_BY'] = $_SESSION['user_name'];

        $api_smbr = new ApiSmbr();
        $response = $api_smbr->create_shipment($param);
        $response_msg = $response['msg'];

        if ($response['success']) {
            $msg .= "(S) => $response_msg <br>";
        } else {
            $msg .= "(X) => $response_msg <br>";
        }
    }

    showMessage($msg);
    exit;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Data SPJ</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Upload Shipment</th>
</tr></table>
</div>

<form method="post" name="upload" id="import" enctype="multipart/form-data">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
            <td class="puso">:</td>
            <td> <input name="excel_file" type="file"  class="button" accept=".xls, application/vnd.ms-excel" required></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="upload" type="submit"  class="button" value="Upload"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>

          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>

<form method="post" name="download" id="import" enctype="multipart/form-data" action="shipment_upload_template_xls.php">
    <table width="800" align="center" class="adminform">
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>

        <tr>
            <td colspan="3" style="text-align:center;">
                <input type="submit" name="download_template" value="Download Template" />
            </td>
        </tr>

        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
