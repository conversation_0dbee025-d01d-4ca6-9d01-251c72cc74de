<?php
/**
 * description : class untuk scheduller post data create SO SAP
 * author : PT.Artavel
 * tambah field NAMA_KAPAL DI Tabel OR_TRANS_HDR, field NO_KONTRAK_POSNR Di tabel OR_TRANS_DTL
 * edit file or_transaksi/formula.php edit case tambah new untuk insert variabel $nama_kapal ke field NAMA_KAPAL
 */
set_time_limit(0);
class getsonotifyemail {

    private $fungsi;
    private $conn;
    private $logfile;
    public $msg;
    public $dataFuncRFC;
    private $status;

    function __construct() {
        $this->status = 'SUKSES';
        $this->fungsi = new or_fungsi();
        require_once('phpmailer.php');
        require_once('class.smtp.php');
        $this->conn = $this->fungsi->or_koneksi();
        // $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
    }

    function saveLog() {
        $this->msg = substr($this->msg, 0, 900);
        $sqllog = "INSERT INTO RFC_LOG VALUES ('GET SO NOTIFY BY EMAIL',SYSDATE,'" . $this->msg . "')";
        $querylog = oci_parse($this->conn, $sqllog);
        if ($querylog) {
            $execlog = @oci_execute($querylog);
        }
        //set running down
        $sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0,RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
        $query_run = oci_parse($this->conn, $sqlset_run);
        oci_execute($query_run);
        //end set
    }

    function run() {
        // fwrite($this->logfile, "Start ".get_class($this)."pada tanggal jam ".date('d-m-Y H:i:s')."\n");
        $this->msg = "Start " . get_class($this) . "pada tanggal jam " . date('d-m-Y H:i:s') . "\n";
        $datapp = array();
        // proyek SOCC V.2
        $sqlhdr1 = "SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG= 'GET_SO_EMAIL_NOTIFY' and DELETE_MARK='0'";

        $queryhdr1 = oci_parse($this->conn, $sqlhdr1);
        oci_execute($queryhdr1);
        $row_settgl=oci_fetch_assoc($queryhdr1);
        $configtglmin=$row_settgl[CONFIG];

        ///////////////////
        $sqlccemail = "SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG= 'CC_EMAIL_NOTIF' and DELETE_MARK='0'";

        $querysqlccemail = oci_parse($this->conn, $sqlccemail);
        oci_execute($querysqlccemail);
        $row_querysqlccemail=oci_fetch_assoc($querysqlccemail);
        $almaat_cc_email=$row_querysqlccemail[CONFIG];
        //////////////////

        $sqlhdr2 = "SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG= 'UMUR_SO_SAP' and DELETE_MARK='0'";

        $queryhdr2 = oci_parse($this->conn, $sqlhdr2);
        oci_execute($queryhdr2);
        $row_settgl2=oci_fetch_assoc($queryhdr2);
        $configtgl30=$row_settgl2[CONFIG];

        $tglgetso = $configtgl30-$configtglmin;
        
        $hari_h = date('Ymd');
        $paramtgl = date('Ymd',strtotime($hari_h. ' -'.$tglgetso.' day'));
        // var_dump($hari_h+" hari_h");
        // var_dump($paramtgl);
        // var_dump($tglgetso+" tglgetso");
        // exit;
        
        


        $sqlhdr = "SELECT
	                or_trans_dtl.KODE_TUJUAN AS DISTRIK, 
                    or_trans_dtl.SHIP_TO AS ID_SHIPTO, 
                    or_trans_hdr.ORG AS COMPANY_CODE, 
                    or_trans_dtl.TGL_LEADTIME  AS LEADTIME_DATE, 
                    or_trans_dtl.APPROVE_DATE AS TGL_APPROVE, 
                    or_trans_dtl.NO_SO AS SO_NUMBER, 
                    or_trans_hdr.NO_PP AS PP_NUMBER, 
                    or_trans_hdr.SOLD_TO AS SOLDTO_ID, 
                    or_trans_dtl.SHIP_TO AS SHIPTO_ID
                    FROM
                        or_trans_dtl
                        LEFT JOIN or_trans_hdr
                        ON or_trans_dtl.NO_PP = or_trans_hdr.NO_PP 
                    WHERE
                        or_trans_dtl.DELETE_MARK='0'
                        AND or_trans_dtl.status_line = 'APPROVE'
                        AND or_trans_hdr.DELETE_MARK='0'
                        AND or_trans_hdr.STATUS = 'APPROVE'
                        AND to_char(or_trans_dtl.TGL_LEADTIME, 'YYYYMMDD')= '$paramtgl'
                        AND or_trans_hdr.ORG IN (7000,7900)
                        AND or_trans_dtl.NO_SO IS NOT NULL
                        ";


        $queryhdr = oci_parse($this->conn, $sqlhdr);
        oci_execute($queryhdr);
        $hdr2 = oci_fetch_assoc($queryhdr);


        $i = 0;

        //    $queryhdr = oci_parse($this->conn, $sqlhdr);
        //     oci_execute($queryhdr);
        //     $i = 0;
            while ($datafunc = oci_fetch_assoc($queryhdr)) {
                $datapp[$i] = $datafunc;
                $datapp[$i]['DATA_SAP_SO'] = array();
                /////////////////////////////////////////////////

                $sap = new SAPConnection();
                $sap->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
                if ($sap->GetStatus() == SAPRFC_OK)
                    $sap->Open();
                if ($sap->GetStatus() != SAPRFC_OK) {
                    echo $sap->PrintStatus();
                    $this->msg .= $sap->PrintStatus();
                    $this->status = 'GAGAL';
                    $this->saveLog();
                    return false;
                }
                
                $fce = $sap->NewFunction ("Z_ZAPPSD_SO_OPEN2");
                if ($fce == false ) {
                    $sap->PrintStatus();
                    exit;
                }           
                            $fce->XVKORG = $datafunc['COMPANY_CODE'];
                            // $fce->XVKORG = 7900;
                            
                            // $fce->XKUNNR2 = $datafunc['ID_SHIPTO'];
                            // $fce->XBZIRK = $datafunc['DISTRIK'];
                            $fce->XFLAG = 'O';
                            $fce->XVBELN = $datafunc['SO_NUMBER'];
        
                                // $hari = date('Ymd', strtotime($datafunc['TGL_APPROVE']));
                                $fce->LR_VDATU->row["SIGN"] = 'I';
                                $fce->LR_VDATU->row["OPTION"] = 'BT';
                                $fce->LR_VDATU->row["LOW"] = $paramtgl;
                                $fce->LR_VDATU->row["HIGH"] = $paramtgl;
                                $fce->LR_VDATU->Append($fce->LR_VDATU->row);        
                               
                                $fce->Call();
                                if ($fce->GetStatus() == SAPRFC_OK ) {
                                    $fce->RETURN_DATA->Reset();
                                    while ( $fce->RETURN_DATA->Next() ){
                                    // echo "<pre>";
                                    // print_r($fce->RETURN_DATA->row);
                                    // echo "</pre>";
                                    // exit;
                                    
                                    $plnReff  = $fce->RETURN_DATA->row["WERKS"];
                                    $strkdven = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$plnReff}' AND DEL=0";
                                    
                                    $query = @oci_parse($this->conn, $strkdven);
                                    @oci_execute($query);
                                    $rowkdven = oci_fetch_array($query, OCI_ASSOC);
                                    // $plant_opco = $rowkdven["PLANT_OPCO"];
                                    $org_opco   = $rowkdven["COM_OPCO"];
                                    // $plant_opco2 = $rowkdven["PLANT_OPCO_2"];
                                    // $org_opco2   = $rowkdven["COM_OPCO_2"];
                                    echo '<pre>';
                                    // echo  $org_opco;
                                    $so_num= $fce->RETURN_DATA->row["VBELN"];

                                    // $statusReject = count($fce->RETURN_DATA->row["ABGRU"]);
                                    // $statusDeliveryBlock = count($fce->RETURN_DATA->row["LIFSK"]);
                                    $orderQty =  ltrim($fce->RETURN_DATA->row["KWMENG"] , '0') * 1;    
                                    $deliverQty =  ltrim($fce->RETURN_DATA->row["RFMNG"] , '0') * 1 ;
                                    // var_dump($fce->RETURN_DATA->row["ABGRU"],$fce->RETURN_DATA->row["LIFSK"],$deliverQty);exit;

                                    if (($fce->RETURN_DATA->row["ABGRU"] == '' && $deliverQty == 0) && ($fce->RETURN_DATA->row["LIFSK"] == ''&& $deliverQty == 0)) {
                                    if ($org_opco != 'PTSC' ) { 
                                    
                                    $getNmPlant = "SELECT NAMA_PLANT FROM OR_TRANS_HDR WHERE PLANT_ASAL = '$plnReff' AND ROWNUM <= 1";
                                    // var_dump($getNmPlant);exit;
                                    $qyr = @oci_parse($this->conn, $getNmPlant);
                                    @oci_execute($qyr);
                                    $gtData  = oci_fetch_array($qyr, OCI_ASSOC);    
                                    $namaPlant = $gtData["NAMA_PLANT"];

                                    $org_so= $fce->RETURN_DATA->row["VKORG"];
                                    $kode_sold_to= $fce->RETURN_DATA->row["KUNNR"];
                                    
                                    $distrik = $fce->RETURN_DATA->row["BZIRK"];
                                    $namaDistrik = $fce->RETURN_DATA->row["BZTXT"];
                                    $soType = $fce->RETURN_DATA->row["AUART"];
                                    $kdShipto = $fce->RETURN_DATA->row["KUNNR2"];
                                    $mmShipto = $fce->RETURN_DATA->row["NAME2"];
                                    $material = $fce->RETURN_DATA->row["MATNR"];
                                    $nmMaterial = $fce->RETURN_DATA->row["MAKTX"];
                                    $inct = $fce->RETURN_DATA->row["INCO1"];
                                    //
                                    
                                    $sisaQty = $orderQty - $deliverQty;
                                    print_r("SO : ".$so_num);
                                

                                    /////////////////////////////////////////////
                                    $sap2 = new SAPConnection();
                                    $sap2->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
                                    if ($sap2->GetStatus() == SAPRFC_OK)
                                        $sap2->Open();
                                    if ($sap2->GetStatus() != SAPRFC_OK) {
                                        echo $sap2->PrintStatus();
                                        $this->msg2 .= $sap2->PrintStatus();
                                        $this->status2 = 'GAGAL';
                                        $this->saveLog();
                                        return false;
                                    }
                                    
                                    $fce2 = $sap2->NewFunction ("ZCSD_EMAIL_CUST");
                                    if ($fce2 == false ) {
                                        $sap2->PrintStatus();
                                        exit;
                                    }
                                                    $fce2->P_BUKRS = $org_so;
                                                    // $fce2->P_BUKRS = 7900;
                                                    $fce2->SO_KUNNR->row["SIGN"] = "I";  
                                                    $fce2->SO_KUNNR->row["OPTION"] = "EQ";
                                                    $fce2->SO_KUNNR->row["LOW"] = $kode_sold_to;
                                                    $fce2->SO_KUNNR->row["HIGH"] = '';
                                                    $fce2->SO_KUNNR->Append($fce2->SO_KUNNR->row);        
                                                    // echo "<pre>";
                                                    // print_r($fce2);
                                                    // echo "</pre>";
                        
                                                    $fce2->Call();
                                                    if ($fce2->GetStatus() == SAPRFC_OK ) {
                                                        $fce2->T_DATA->Reset();
                                                        while ( $fce2->T_DATA->Next() ){
                                                        $email_sold_to = $fce2->T_DATA->row["EMAIL"];
                                                        // var_dump($email_sold_to); /////
                                                        // print_r("SO : ".$email_sold_to);
                                                                        ////////////////////////////////////////////
                                                                                        $tgl_email = date("m-d-Y");
                                                                                        $tanggalKirim = base64_encode(date("m-d-Y")); //s
                                                                                        // $tgl_email = date("10-29-2023");
                                                                                        // $tanggalKirim = base64_encode(date("10-29-2023"));
                                                                                        $so_numEnc = base64_encode($so_num);
                                                                                        $tgl_email = base64_encode($tgl_email);
                                                                                        $part="http://10.4.194.150/dev/sd/sdonline/or_transaksi/vRequestRescheduleSo.php?so_num=$so_numEnc&tanggal=$tgl_email&tanggalKirim=$tanggalKirim";
                                                                                        $mail = new PHPMailer();
                                                                                        $body = "
                                                                                        Dengan Hormat, <br>
                                                                                        Umur SO anda atas Nomor ".$so_num." akan habis dalam 5 hari kedepan, jika ingin melakukan adjust(reschedule) atas sisa qty yang belum terkirim silahkan klik link dibawah ini.<br><br>
                                                                                        <a href='".$part."'>Link Disini</a>
                                                                                        <br><br>
                                                                                        
                                                                                        <table style=\"width:100%; border: 1px solid black; border-collapse: collapse;\">
                                                                                        <tr>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">SO Number</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">SO Type</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Shipto</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Name Ship to</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">KD Distrik</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Nama Distrik</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Material</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Nama Material</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Incoterm</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Reff Plant</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Order Qty(Ton)</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Deliver Qty(Ton)</th>
                                                                                            <th style=\"border: 1px solid black; border-collapse: collapse; background-color: #9eeeee;\">Sisa Qty(Ton)</th>
                                                                                            
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$so_num."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$soType."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$kdShipto."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$mmShipto."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$distrik."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$namaDistrik."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$material."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$nmMaterial."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$inct."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$plnReff." - ".$namaPlant." </td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$orderQty."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse;\">".$deliverQty."</td>
                                                                                            <td style=\"border: 1px solid black; border-collapse: collapse; background-color: #e6ff6c; \">".$sisaQty."</td>
                                                                                        </tr>
                                                                                        
                                                                                        </table>
                                                                                        " ; //isi dari email
                                                                                         $mail->IsSMTP();
                                                                                         $mail->SMTPDebug  = 1;                   
                                                                                         $mail->Host       = "relay.sig.id";     
                                                                                         $mail->Port       = 25;                
                                                                                         $mail->SetFrom('<EMAIL>', 'Notification RDD SO'); // masukkan alamat pengririm dan nama pengirim jika alamat email tidak sama, maka yang digunakan alamat email untuk username
                                                                                         $mail->Subject   = "Notification RDD SO ";//masukkan subject
                                                                                         $mail->MsgHTML($body);//masukkan isi dari email
                                                                                         //$mail->AddCC($emailcc);
                                                                                        
                                                                                        //  $explodeEmailTo = explode(', ',$email);
                                                                                        //  $j = count($explodeEmailTo);
                                                                                        //  for ($i=0; $i < $j ; $i++) { 
                                                                                        //      # code...
                                                                                        //      //var_dump($explodeEmailTo[$i]);
                                                                                             $mail->AddAddress($email_sold_to);//masukkan penerima
                                                                                            
                                                                                             //  }
                                                                                        
                                                                                         if(!$mail->Send()) {
                                                                                             echo "Mailer Error : " . $mail->ErrorInfo; // jika pesan tidak terkirim
                                                                                         }else {
                                                                                             echo "sukses mail ";
                                                                                         }
                                                                        ////////////////////////////////////////////
                                                    }
                                                } else {
                                                    $fce2->PrintStatus();
                                                }
                                                $fce2->Close();
                                                $sap2->Close(); 
                                            }
                                
                                                   
                                    /////////////////////////////////////////////
                                        }
                            }
                        } else {
                            $fce->PrintStatus();
                        }
        
                $fce->Close();
                $sap->Close();            
                ///////////////////////////////////////////////
                // $querydtl = oci_parse($this->conn, $sqldtl);
                // oci_execute($querydtl);
                // while ($datadtl = oci_fetch_assoc($querydtl)) {
                //     array_push($datapp[$i]['DATA_SAP_SO'], $datadtl);
                // }
                // $i++;
            }
            // if (count($datapp) > 0) {
            //     for ($i = 0; $i < count($datapp); $i++) {
            //         $this->postSoSAP($datapp[$i]);
            //     }
            // } else {
            //     echo 'Tidak ada data';
            // }
            // $this->saveLog();
    }
}

?>
