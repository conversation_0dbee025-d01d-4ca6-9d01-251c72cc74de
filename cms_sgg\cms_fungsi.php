<?
# Update 01-06-2011
include('../include/fungsi.php');
class cms_fungsi extends fungsi
{
	var $cms_username = "dev";
	var $cms_password = "semeru2";
	//var $cms_db = '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
	var $cms_db = '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = DEVSGG)(SERVER = DEDICATED)))';

	public function cms_koneksi()
	{
		$conn = oci_connect($this->cms_username, $this->cms_password, $this->cms_db);
		if (!$conn)
			return false;
		else
			return $conn;
	}

	function cms_currency($ting)
	{
		$k = array(1 => 'IDR', 'USD');
		for ($x = 1; $x <= count($k); $x++) {
			echo ("<option value='$k[$x]' title='$k[$x]'");
			if ($k[$x] == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}

	function cms_select_invoice($orgx, $disidx, $tglxx, $kmke, $doc_kn = "", $curr)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_AR_OI_VO");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan
		$fce->PI_BUKRS = $orgx;
		$fce->PI_KUNNR = $disidx;
		$fce->PI_STIDA = $tglxx;
		$fce->PI_KMK = $kmke;
		#if($_SESSION['user_org']=='3000'){
		$fce->DUE_DATE_SORT = 'X';
		#}


		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->PT_ITEMSX->Reset();
			while ($fce->PT_ITEMSX->Next()) {
				if ($doc_kn == "X" && $fce->PT_ITEMSX->row["OPENL"] < 0) {
				} else {
					if ($fce->PT_ITEMSX->row["WAERS"] == $curr) {
						$cpcd[] = $fce->PT_ITEMSX->row["BUKRS"];
						$kddist[] = $fce->PT_ITEMSX->row["KUNNR"];
						$year[] = $fce->PT_ITEMSX->row["GJAHR"];
						$noinva[] = $fce->PT_ITEMSX->row["BELNR"];
						$buz[] = $fce->PT_ITEMSX->row["BUZEI"];
						$noinv[] = $fce->PT_ITEMSX->row["INVOI"];
						$bl[] = $fce->PT_ITEMSX->row["BLART"];
						$invdt[] = $fce->PT_ITEMSX->row["ZFBDT"];
						$duedt[] = $fce->PT_ITEMSX->row["FAEDN"];
						$duedt2[] = $fce->PT_ITEMSX->row["FAEDT"];
						$curren[] = $fce->PT_ITEMSX->row["WAERS"];
						## Update 4942
						if ($fce->PT_ITEMSX->row["WAERS"] == 'IDR') {
							$grossamtd[] = $fce->PT_ITEMSX->row["GROSD"] * 100;
							$grossamtl[] = $fce->PT_ITEMSX->row["GROSL"] * 100;
							$openamtd[] = $fce->PT_ITEMSX->row["OPEND"] * 100;
							$openamtl[] = $fce->PT_ITEMSX->row["OPENL"] * 100;
							$payamtd[] = $fce->PT_ITEMSX->row["PAYMD"] * 100;
							$payamtl[] = $fce->PT_ITEMSX->row["PAYML"] * 100;
							$saldamtd[] = $fce->PT_ITEMSX->row["SALDD"] * 100;
							$saldamtl[] = $fce->PT_ITEMSX->row["SALDL"] * 100;
						} else {
							$grossamtd[] = $fce->PT_ITEMSX->row["GROSD"];
							$grossamtl[] = $fce->PT_ITEMSX->row["GROSL"] * 100;
							$openamtd[] = $fce->PT_ITEMSX->row["OPEND"];
							$openamtl[] = $fce->PT_ITEMSX->row["OPENL"] * 100;
							$payamtd[] = $fce->PT_ITEMSX->row["PAYMD"];
							$payamtl[] = $fce->PT_ITEMSX->row["PAYML"] * 100;
							$saldamtd[] = $fce->PT_ITEMSX->row["SALDD"];
							$saldamtl[] = $fce->PT_ITEMSX->row["SALDL"] * 100;
						}
						## Update 4942
						$sgtxt[] = $fce->PT_ITEMSX->row["SGTXT"];
						$zterm[] = $fce->PT_ITEMSX->row["ZTERM"];
						$aubel[] = $fce->PT_ITEMSX->row["AUBEL"];
						$erdat[] = $fce->PT_ITEMSX->row["ERDAT"];
						$zbd1t[] = $fce->PT_ITEMSX->row["ZBD1T"]; //Day 1
						$zbd2t[] = $fce->PT_ITEMSX->row["ZBD2T"]; //Day 2
						$zbd3t[] = $fce->PT_ITEMSX->row["ZBD3T"]; //Net Payment Terms Period
						$zbd1p[] = $fce->PT_ITEMSX->row["ZBD1P"]; //Discount 1
						$zbd2p[] = $fce->PT_ITEMSX->row["ZBD2P"]; //Discount 2
						$skfbt[] = $fce->PT_ITEMSX->row["SKFBT"]; //Amount Eligible for Cash Discount in Document Currency
						$selisih[] = $fce->PT_ITEMSX->row["SELISIH"];
						$fkart[] = $fce->PT_ITEMSX->row["FKART"];
						$ref[] = $fce->PT_ITEMSX->row["XBLNR"];
						$awkey[] = $fce->PT_ITEMSX->row["AWKEY"];
					}
				}
			}
			$arroutx = array(
				$cpcd,
				$kddist,
				$year,
				$noinva,
				$buz,
				$noinv,
				$bl,
				$invdt,
				$duedt,
				$curren,
				$grossamtd,
				$grossamtl,
				$openamtd,
				$openamtl,
				$payamtd,
				$payamtl,
				$saldamtd,
				$saldamtl,
				$sgtxt,
				$zterm,
				$duedt2,
				$aubel,
				$erdat,
				$zbd1t,
				$zbd2t,
				$zbd3t,
				$zbd1p,
				$zbd2p,
				$skfbt,
				$selisih,
				$fkart,
				$ref,
				$awkey
			);
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_create_rn($totdat, $dat, $cpcdx, $disidx, $tglxx, $cur, $indx, $kmk)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_RN_CREATE");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		// Paramete inputan input
		$fce->PI_KMK = $kmk;
		//parameter inputan struktur table header
		$fce->PI_RN_HD["BUKRS"] = $cpcdx;
		$fce->PI_RN_HD["KUNNR"] = $disidx;
		$fce->PI_RN_HD["STIDA"] = $tglxx;
		$fce->PI_RN_HD["RNDAT"] = $tglxx;
		$fce->PI_RN_HD["PYCUR"] = $cur;
		#echo "BUKRS=".$orgx."<br>KUNNR=".$disidx."<br>STIDA=".$tglxx."<br>RNDAT=".$tglxx."<br>PYCUR=".$cur;
		//parameter inputan table items
		for ($j = 0; $j < $totdat; $j++) {
			$i = $indx[$j];
			$fce->PT_ITEMS->row["BUKRS"] = $dat[0][$i];
			$fce->PT_ITEMS->row["KUNNR"] = $dat[1][$i];
			$fce->PT_ITEMS->row["GJAHR"] = $dat[2][$i];
			$fce->PT_ITEMS->row["BELNR"] = $dat[3][$i];
			$fce->PT_ITEMS->row["BUZEI"] = $dat[4][$i];
			$fce->PT_ITEMS->row["INVOI"] = $dat[5][$i];
			$fce->PT_ITEMS->row["BLART"] = $dat[6][$i];
			$fce->PT_ITEMS->row["ZFBDT"] = $dat[7][$i];
			$fce->PT_ITEMS->row["FAEDN"] = $dat[8][$i];
			$fce->PT_ITEMS->row["WAERS"] = $dat[9][$i];
			## Update 4942
			if ($dat[9][$i] == 'IDR') {
				$fce->PT_ITEMS->row["GROSD"] = $dat[10][$i] / 100;
				$fce->PT_ITEMS->row["GROSL"] = $dat[11][$i] / 100;
				$fce->PT_ITEMS->row["OPEND"] = $dat[12][$i] / 100;
				$fce->PT_ITEMS->row["OPENL"] = $dat[13][$i] / 100;
				$fce->PT_ITEMS->row["PAYMD"] = $dat[14][$i] / 100;
				$fce->PT_ITEMS->row["PAYML"] = $dat[15][$i] / 100;
				$fce->PT_ITEMS->row["SALDD"] = ($dat[12][$i] / 100) - ($dat[14][$i] / 100);
				$fce->PT_ITEMS->row["SALDL"] = ($dat[13][$i] / 100) - ($dat[15][$i] / 100);
			} else {
				$hargaidr = $dat[15][$i];
				$exchangerate = $hargaidr / $dat[12][$i];
				$jadinya = $dat[14][$i] * $exchangerate;
				echo $jadinya;

				$fce->PT_ITEMS->row["GROSD"] = $dat[10][$i];
				$fce->PT_ITEMS->row["GROSL"] = $dat[11][$i] / 100;
				$fce->PT_ITEMS->row["OPEND"] = $dat[12][$i];
				$fce->PT_ITEMS->row["OPENL"] = $dat[13][$i] / 100;
				$fce->PT_ITEMS->row["PAYMD"] = $dat[14][$i];
				$fce->PT_ITEMS->row["PAYML"] = $jadinya / 100;
				$fce->PT_ITEMS->row["SALDD"] = ($dat[12][$i]) - ($dat[14][$i]);
				$fce->PT_ITEMS->row["SALDL"] = ($dat[13][$i] / 100) - ($jadinya / 100);
			}
			## Update 4942
			$fce->PT_ITEMS->row["ZTERM"] = $dat[18][$i];
			$fce->PT_ITEMS->Append($fce->PT_ITEMS->row);
		}
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->PT_ITEMSX->Reset();
			while ($fce->PT_ITEMSX->Next()) {
				$cpcd[] = $fce->PT_ITEMSX->row["BUKRS"];
				$kddist[] = $fce->PT_ITEMSX->row["KUNNR"];
				$year[] = $fce->PT_ITEMSX->row["GJAHR"];
				$noinva[] = $fce->PT_ITEMSX->row["BELNR"];
				$buz[] = $fce->PT_ITEMSX->row["BUZEI"];
				$noinv[] = $fce->PT_ITEMSX->row["INVOI"];
				$bl[] = $fce->PT_ITEMSX->row["BLART"];
				$invdt[] = $fce->PT_ITEMSX->row["ZFBDT"];
				$duedt[] = $fce->PT_ITEMSX->row["FAEDN"];
				$duedt2[] = $fce->PT_ITEMSX->row["FAEDT"];
				$curren[] = $fce->PT_ITEMSX->row["WAERS"];
				$grossamtd[] = $fce->PT_ITEMSX->row["GROSD"];
				$grossamtl[] = $fce->PT_ITEMSX->row["GROSL"];
				$openamtd[] = $fce->PT_ITEMSX->row["OPEND"];
				$openamtl[] = $fce->PT_ITEMSX->row["OPENL"];
				$payamtd[] = $fce->PT_ITEMSX->row["PAYMD"];
				$payamtl[] = $fce->PT_ITEMSX->row["PAYML"];
				$saldamtd[] = $fce->PT_ITEMSX->row["SALDD"];
				$saldamtl[] = $fce->PT_ITEMSX->row["SALDL"];
			}
			$rn = $fce->PE_RNNUM;
			#echo "ERROR NO : ".$fce->PE_RETURN['NUMBER']."<br>";
			#echo "PESAN ERROR : ".$fce->PE_RETURN['MESSAGE'];
			$message = $fce->PE_RETURN['MESSAGE'];
			$message_type = $fce->PE_RETURN['TYPE'];
			$arroutx = array(
				$cpcd,
				$kddist,
				$year,
				$noinva,
				$buz,
				$noinv,
				$bl,
				$invdt,
				$duedt,
				$curren,
				$grossamtd,
				$grossamtl,
				$openamtd,
				$openamtl,
				$payamtd,
				$payamtl,
				$saldamtd,
				$saldamtl,
				$rn,
				$duedt2,
				$message,
				$message_type
			);
			//$total		= count($noinv);
			//return $total;
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_select_so($orgx, $disidx, $tgl1, $tgl2, $doc_kn = "")
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL_CBD");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan
		$fce->XVKORG = $orgx;
		$fce->XKUNNR = $disidx;
		$fce->XAUART = 'ZFC';
		//$fce->PI_KMK = $kmke;
		#if($_SESSION['user_org']=='3000'){
		//$fce->DUE_DATE_SORT = 'X';
		#}
		$fce->LR_EDATU->ROW['SIGN'] = "I";
		$fce->LR_EDATU->ROW['OPTION'] = "BT";
		$fce->LR_EDATU->ROW['LOW'] = $tgl1;
		$fce->LR_EDATU->ROW['HIGH'] = $tgl2;
		$fce->LR_EDATU->Append($fce->LR_EDATU->ROW);

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->RETURN_DATA->Reset();
			while ($fce->RETURN_DATA->Next()) {
				$VBELN[] = $fce->RETURN_DATA->row['VBELN'];
				$VKORG[] = $fce->RETURN_DATA->row['VKORG'];
				$VKBUR[] = $fce->RETURN_DATA->row['VKBUR'];
				$BEZEI_VKBUR[] = $fce->RETURN_DATA->row['BEZEI_VKBUR'];
				$BZIRK[] = $fce->RETURN_DATA->row['BZIRK'];
				$BZTXT[] = $fce->RETURN_DATA->row['BZTXT'];
				$KUNNR[] = $fce->RETURN_DATA->row['KUNNR'];
				$NAME1[] = $fce->RETURN_DATA->row['NAME1'];
				$INCO1[] = $fce->RETURN_DATA->row['INCO1'];
				$POSNR[] = $fce->RETURN_DATA->row['POSNR'];
				$MATNR[] = $fce->RETURN_DATA->row['MATNR'];
				$MAKTX[] = $fce->RETURN_DATA->row['MAKTX'];
				$MEINS[] = $fce->RETURN_DATA->row['MEINS'];
				$BNAME[] = $fce->RETURN_DATA->row['BNAME'];
				$RFMNG[] = $fce->RETURN_DATA->row['RFMNG'];
				$KWMENG[] = $fce->RETURN_DATA->row['KWMENG'];
				$LFSTK[] = $fce->RETURN_DATA->row['LFSTK'];
				$LFSTA[] = $fce->RETURN_DATA->row['LFSTA'];
				$KUNNR2[] = $fce->RETURN_DATA->row['KUNNR2'];
				$NAME2[] = $fce->RETURN_DATA->row['NAME2'];
				$NTGEW[] = $fce->RETURN_DATA->row['NTGEW'];
				$GEWEI[] = $fce->RETURN_DATA->row['GEWEI'];
				$WERKS[] = $fce->RETURN_DATA->row['WERKS'];
				$DESC_PLANT[] = $fce->RETURN_DATA->row['DESC_PLANT'];
				$EDATU[] = $fce->RETURN_DATA->row['EDATU'];
				$AUART[] = $fce->RETURN_DATA->row['AUART'];
				$SDABW[] = $fce->RETURN_DATA->row['SDABW'];
				$BEZEI[] = $fce->RETURN_DATA->row['BEZEI'];
				$LIFNR[] = $fce->RETURN_DATA->row['LIFNR'];
				$NAMEL[] = $fce->RETURN_DATA->row['NAMEL'];
				$KVGR1[] = $fce->RETURN_DATA->row['KVGR1'];
				$BEZE1[] = $fce->RETURN_DATA->row['BEZE1'];
				$AUDAT[] = $fce->RETURN_DATA->row['AUDAT'];
				$BSTKD[] = $fce->RETURN_DATA->row['BSTKD'];
				$BSTDK[] = $fce->RETURN_DATA->row['BSTDK'];
				$AUGRU[] = $fce->RETURN_DATA->row['AUGRU'];
				$KTEXT[] = $fce->RETURN_DATA->row['KTEXT'];
				$GUEBG[] = $fce->RETURN_DATA->row['GUEBG'];
				$GUEEN[] = $fce->RETURN_DATA->row['GUEEN'];
				$NETWR[] = $fce->RETURN_DATA->row['NETWR'];
				$MWSBP[] = $fce->RETURN_DATA->row['MWSBP'];
				$PLTYP[] = $fce->RETURN_DATA->row['PLTYP'];
				$STRAS2[] = $fce->RETURN_DATA->row['STRAS2'];
				$BEZEI2[] = $fce->RETURN_DATA->row['BEZEI2'];
				$VGBEL[] = $fce->RETURN_DATA->row['VGBEL'];
				$ROUTE[] = $fce->RETURN_DATA->row['ROUTE'];
				$BEZEI_ROUTE[] = $fce->RETURN_DATA->row['BEZEI_ROUTE'];
				$ACCESSNO[] = $fce->RETURN_DATA->row['ACCESSNO'];
				$ABGRU[] = $fce->RETURN_DATA->row['ABGRU'];
				$BEZEI_ABGRU[] = $fce->RETURN_DATA->row['BEZEI_ABGRU'];
				$BRAN1[] = $fce->RETURN_DATA->row['BRAN1'];
				$TIPE_KONTRAK[] = $fce->RETURN_DATA->row['TIPE_KONTRAK'];
				$ZTERM[] = $fce->RETURN_DATA->row['ZTERM'];
				$ZTERMT[] = $fce->RETURN_DATA->row['ZTERMT'];
				$INCO2[] = $fce->RETURN_DATA->row['INCO2'];
				$KDKG1[] = $fce->RETURN_DATA->row['KDKG1'];
				$STRAS[] = $fce->RETURN_DATA->row['STRAS'];
				$JARAK[] = $fce->RETURN_DATA->row['JARAK'];
				$PALLET[] = $fce->RETURN_DATA->row['PALLET'];
				$ZTEXT5[] = $fce->RETURN_DATA->row['ZTEXT5'];
				$CNC[] = $fce->RETURN_DATA->row['CNC'];
				$STUT[] = $fce->RETURN_DATA->row['STUT'];
			}
			$arroutx = array(
				$VBELN,
				$VKORG,
				$VKBUR,
				$BEZEI_VKBUR,
				$BZIRK,
				$BZTXT,
				$KUNNR,
				$NAME1,
				$INCO1,
				$POSNR,
				$MATNR,
				$MAKTX,
				$MEINS,
				$BNAME,
				$RFMNG,
				$KWMENG,
				$LFSTK,
				$LFSTA,
				$KUNNR2,
				$NAME2,
				$NTGEW,
				$GEWEI,
				$WERKS,
				$DESC_PLANT,
				$EDATU,
				$AUART,
				$SDABW,
				$BEZEI,
				$LIFNR,
				$NAMEL,
				$KVGR1,
				$BEZE1,
				$AUDAT,
				$BSTKD,
				$BSTDK,
				$AUGRU,
				$KTEXT,
				$GUEBG,
				$GUEEN,
				$NETWR,
				$MWSBP,
				$PLTYP,
				$STRAS2,
				$BEZEI2,
				$VGBEL,
				$ROUTE,
				$BEZEI_ROUTE,
				$ACCESSNO,
				$ABGRU,
				$BEZEI_ABGRU,
				$BRAN1,
				$TIPE_KONTRAK,
				$ZTERM,
				$ZTERMT,
				$INCO2,
				$KDKG1,
				$STRAS,
				$JARAK,
				$PALLET,
				$ZTEXT5,
				$CNC,
				$STUT,
			);
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_create_rn_so($totdat, $dat, $cpcdx, $disidx, $tglxx, $cur, $indx, $kmk)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_RN_CREATE_SO");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		// Paramete inputan input
		$fce->PI_KMK = $kmk;
		//parameter inputan struktur table header
		$fce->PI_RN_HD["BUKRS"] = $cpcdx;
		$fce->PI_RN_HD["KUNNR"] = $disidx;
		$fce->PI_RN_HD["STIDA"] = $tglxx;
		$fce->PI_RN_HD["RNDAT"] = $tglxx;
		$fce->PI_RN_HD["PYCUR"] = $cur;
		// echo "BUKRS=".$cpcdx."<br>KUNNR=".$disidx."<br>STIDA=".$tglxx."<br>RNDAT=".$tglxx."<br>PYCUR=".$cur;
		//parameter inputan table items
		for ($j = 0; $j < $totdat; $j++) {
			$i = $indx[$j];
			$fce->PT_ITEMS->row["BUKRS"] = $dat[0][$i];
			$fce->PT_ITEMS->row["KUNNR"] = $dat[1][$i];
			$fce->PT_ITEMS->row["GJAHR"] = $dat[2][$i];
			$fce->PT_ITEMS->row["BELNR"] = $dat[3][$i];
			$fce->PT_ITEMS->row["BUZEI"] = $dat[4][$i];
			$fce->PT_ITEMS->row["INVOI"] = $dat[5][$i];
			$fce->PT_ITEMS->row["BLART"] = $dat[6][$i];
			$fce->PT_ITEMS->row["ZFBDT"] = $dat[7][$i];
			$fce->PT_ITEMS->row["FAEDN"] = $dat[8][$i];
			$fce->PT_ITEMS->row["WAERS"] = $dat[9][$i];
			## Update 4942
			if ($dat[9][$i] == 'IDR') {
				$fce->PT_ITEMS->row["GROSD"] = $dat[10][$i] / 100;
				$fce->PT_ITEMS->row["GROSL"] = $dat[11][$i] / 100;
				$fce->PT_ITEMS->row["OPEND"] = $dat[12][$i] / 100;
				$fce->PT_ITEMS->row["OPENL"] = $dat[13][$i] / 100;
				$fce->PT_ITEMS->row["PAYMD"] = $dat[14][$i] / 100;
				$fce->PT_ITEMS->row["PAYML"] = $dat[15][$i] / 100;
				$fce->PT_ITEMS->row["SALDD"] = ($dat[12][$i] / 100) - ($dat[14][$i] / 100);
				$fce->PT_ITEMS->row["SALDL"] = ($dat[13][$i] / 100) - ($dat[15][$i] / 100);
			} else {
				$fce->PT_ITEMS->row["GROSD"] = $dat[10][$i];
				$fce->PT_ITEMS->row["GROSL"] = $dat[11][$i] / 100;
				$fce->PT_ITEMS->row["OPEND"] = $dat[12][$i];
				$fce->PT_ITEMS->row["OPENL"] = $dat[13][$i] / 100;
				$fce->PT_ITEMS->row["PAYMD"] = $dat[14][$i];
				$fce->PT_ITEMS->row["PAYML"] = $dat[15][$i] / 100;
				$fce->PT_ITEMS->row["SALDD"] = ($dat[12][$i]) - ($dat[14][$i]);
				$fce->PT_ITEMS->row["SALDL"] = ($dat[13][$i] / 100) - ($dat[15][$i] / 100);
			}
			## Update 4942
			$fce->PT_ITEMS->row["ZTERM"] = $dat[18][$i];
			$fce->PT_ITEMS->Append($fce->PT_ITEMS->row);
		}
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->PT_ITEMSX->Reset();
			while ($fce->PT_ITEMSX->Next()) {
				$cpcd[] = $fce->PT_ITEMSX->row["BUKRS"];
				$kddist[] = $fce->PT_ITEMSX->row["KUNNR"];
				$year[] = $fce->PT_ITEMSX->row["GJAHR"];
				$noinva[] = $fce->PT_ITEMSX->row["BELNR"];
				$buz[] = $fce->PT_ITEMSX->row["BUZEI"];
				$noinv[] = $fce->PT_ITEMSX->row["INVOI"];
				$bl[] = $fce->PT_ITEMSX->row["BLART"];
				$invdt[] = $fce->PT_ITEMSX->row["ZFBDT"];
				$duedt[] = $fce->PT_ITEMSX->row["FAEDN"];
				$duedt2[] = $fce->PT_ITEMSX->row["FAEDT"];
				$curren[] = $fce->PT_ITEMSX->row["WAERS"];
				$grossamtd[] = $fce->PT_ITEMSX->row["GROSD"];
				$grossamtl[] = $fce->PT_ITEMSX->row["GROSL"];
				$openamtd[] = $fce->PT_ITEMSX->row["OPEND"];
				$openamtl[] = $fce->PT_ITEMSX->row["OPENL"];
				$payamtd[] = $fce->PT_ITEMSX->row["PAYMD"];
				$payamtl[] = $fce->PT_ITEMSX->row["PAYML"];
				$saldamtd[] = $fce->PT_ITEMSX->row["SALDD"];
				$saldamtl[] = $fce->PT_ITEMSX->row["SALDL"];
			}
			$rn = $fce->PE_RNNUM;
			#echo "ERROR NO : ".$fce->PE_RETURN['NUMBER']."<br>";
			#echo "PESAN ERROR : ".$fce->PE_RETURN['MESSAGE'];
			$message = $fce->PE_RETURN['MESSAGE'];
			$message_type = $fce->PE_RETURN['TYPE'];
			$arroutx = array(
				$cpcd,
				$kddist,
				$year,
				$noinva,
				$buz,
				$noinv,
				$bl,
				$invdt,
				$duedt,
				$curren,
				$grossamtd,
				$grossamtl,
				$openamtd,
				$openamtl,
				$payamtd,
				$payamtl,
				$saldamtd,
				$saldamtl,
				$rn,
				$duedt2,
				$message,
				$message_type
			);
			//$total		= count($noinv);
			//return $total;
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_create_rn_potongan($totdat, $dat, $cpcdx, $disidx, $tglxx, $cur, $indx, $kmk, $pot)
	{
		$message = "";
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_RN_CREATE");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		// Paramete inputan input
		$fce->PI_KMK = $kmk;
		//parameter inputan struktur table header
		$fce->PI_RN_HD["BUKRS"] = $cpcdx;
		$fce->PI_RN_HD["KUNNR"] = $disidx;
		$fce->PI_RN_HD["STIDA"] = $tglxx;
		$fce->PI_RN_HD["RNDAT"] = $tglxx;
		$fce->PI_RN_HD["PYCUR"] = $cur;
		#echo "BUKRS=".$orgx."<br>KUNNR=".$disidx."<br>STIDA=".$tglxx."<br>RNDAT=".$tglxx."<br>PYCUR=".$cur;
		//parameter inputan table items
		for ($j = 0; $j < $totdat; $j++) {
			$i = $indx[$j];
			$fce->PT_ITEMS->row["BUKRS"] = $dat[0][$i];
			$fce->PT_ITEMS->row["KUNNR"] = $dat[1][$i];
			$fce->PT_ITEMS->row["GJAHR"] = $dat[2][$i];
			$fce->PT_ITEMS->row["BELNR"] = $dat[3][$i];
			$fce->PT_ITEMS->row["BUZEI"] = $dat[4][$i];
			$fce->PT_ITEMS->row["INVOI"] = $dat[5][$i];
			$fce->PT_ITEMS->row["BLART"] = $dat[6][$i];
			$fce->PT_ITEMS->row["ZFBDT"] = $dat[7][$i];
			$fce->PT_ITEMS->row["FAEDN"] = $dat[8][$i];
			$fce->PT_ITEMS->row["WAERS"] = $dat[9][$i];
			$fce->PT_ITEMS->row["GROSD"] = $dat[10][$i] / 100;
			$fce->PT_ITEMS->row["GROSL"] = $dat[11][$i] / 100;
			$fce->PT_ITEMS->row["OPEND"] = $dat[12][$i] / 100;
			$fce->PT_ITEMS->row["OPENL"] = $dat[13][$i] / 100;
			$fce->PT_ITEMS->row["PAYMD"] = $dat[14][$i] / 100;
			$fce->PT_ITEMS->row["PAYML"] = $dat[15][$i] / 100;
			$fce->PT_ITEMS->row["SALDD"] = ($dat[12][$i] / 100) - ($dat[14][$i] / 100);
			$fce->PT_ITEMS->row["SALDL"] = ($dat[13][$i] / 100) - ($dat[15][$i] / 100);
			$fce->PT_ITEMS->row["ZTERM"] = $dat[18][$i];
			$fce->PT_ITEMS->Append($fce->PT_ITEMS->row);
		}

		$tot_pot = count($pot[0]);
		for ($i = 0; $i < $tot_pot; $i++) {
			$fce->PT_DISCOUNT->row["BUKRS"] = $pot[0][$i];
			$fce->PT_DISCOUNT->row["KUNNR"] = $pot[1][$i];
			$fce->PT_DISCOUNT->row["GJAHR"] = $pot[2][$i];
			$fce->PT_DISCOUNT->row["BELNR"] = $pot[3][$i];
			$fce->PT_DISCOUNT->row["BUZEI"] = $pot[4][$i];
			$fce->PT_DISCOUNT->row["INVOI"] = $pot[5][$i];
			$fce->PT_DISCOUNT->row["BLART"] = $pot[6][$i];
			$fce->PT_DISCOUNT->row["ZFBDT"] = $pot[7][$i];
			$fce->PT_DISCOUNT->row["FAEDN"] = $pot[8][$i];
			$fce->PT_DISCOUNT->row["WAERS"] = $pot[9][$i];
			$fce->PT_DISCOUNT->row["GROSD"] = $pot[10][$i] / 100;
			$fce->PT_DISCOUNT->row["GROSL"] = $pot[11][$i] / 100;
			$fce->PT_DISCOUNT->row["OPEND"] = $pot[12][$i] / 100;
			$fce->PT_DISCOUNT->row["OPENL"] = $pot[13][$i] / 100;
			$fce->PT_DISCOUNT->row["PAYMD"] = $pot[14][$i] / 100;
			$fce->PT_DISCOUNT->row["PAYML"] = $pot[15][$i] / 100;
			$fce->PT_DISCOUNT->row["SALDD"] = ($pot[12][$i] / 100) - ($pot[14][$i] / 100);
			$fce->PT_DISCOUNT->row["SALDL"] = ($pot[13][$i] / 100) - ($pot[15][$i] / 100);
			$fce->PT_DISCOUNT->row["ZTERM"] = $pot[18][$i];
			$fce->PT_DISCOUNT->Append($fce->PT_DISCOUNT->row);
		}
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->PT_ITEMSX->Reset();
			while ($fce->PT_ITEMSX->Next()) {
				$cpcd[] = $fce->PT_ITEMSX->row["BUKRS"];
				$kddist[] = $fce->PT_ITEMSX->row["KUNNR"];
				$year[] = $fce->PT_ITEMSX->row["GJAHR"];
				$noinva[] = $fce->PT_ITEMSX->row["BELNR"];
				$buz[] = $fce->PT_ITEMSX->row["BUZEI"];
				$noinv[] = $fce->PT_ITEMSX->row["INVOI"];
				$bl[] = $fce->PT_ITEMSX->row["BLART"];
				$invdt[] = $fce->PT_ITEMSX->row["ZFBDT"];
				$duedt[] = $fce->PT_ITEMSX->row["FAEDN"];
				$duedt2[] = $fce->PT_ITEMSX->row["FAEDT"];
				$curren[] = $fce->PT_ITEMSX->row["WAERS"];
				$grossamtd[] = $fce->PT_ITEMSX->row["GROSD"];
				$grossamtl[] = $fce->PT_ITEMSX->row["GROSL"];
				$openamtd[] = $fce->PT_ITEMSX->row["OPEND"];
				$openamtl[] = $fce->PT_ITEMSX->row["OPENL"];
				$payamtd[] = $fce->PT_ITEMSX->row["PAYMD"];
				$payamtl[] = $fce->PT_ITEMSX->row["PAYML"];
				$saldamtd[] = $fce->PT_ITEMSX->row["SALDD"];
				$saldamtl[] = $fce->PT_ITEMSX->row["SALDL"];
			}
			$rn = $fce->PE_RNNUM;
			$message = $fce->PE_RETURN['MESSAGE'];
			$message_type = $fce->PE_RETURN['TYPE'];
			$arroutx = array(
				$cpcd,
				$kddist,
				$year,
				$noinva,
				$buz,
				$noinv,
				$bl,
				$invdt,
				$duedt,
				$curren,
				$grossamtd,
				$grossamtl,
				$openamtd,
				$openamtl,
				$payamtd,
				$payamtl,
				$saldamtd,
				$saldamtl,
				$rn,
				$duedt2,
				$message,
				$message_type
			);
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_add_to_rn($rn, $totdat, $dat, $orgx, $disidx, $tglxx, $cur, $indx)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_ADD_TO_RN");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan struktur table header
		$fce->XRNNUM = $rn;
		$fce->XBUKRS = $orgx;
		$fce->XKUNNR = $disidx;

		//parameter inputan table items
		for ($j = 0; $j < $totdat; $j++) {
			$i = $indx[$j];

			$fce->PT_ITEMS->row["BUKRS"] = $dat[0][$i];
			$fce->PT_ITEMS->row["KUNNR"] = $dat[1][$i];
			$fce->PT_ITEMS->row["GJAHR"] = $dat[2][$i];
			$fce->PT_ITEMS->row["BELNR"] = $dat[3][$i];
			$fce->PT_ITEMS->row["BUZEI"] = $dat[4][$i];
			$fce->PT_ITEMS->row["INVOI"] = $dat[5][$i];
			$fce->PT_ITEMS->row["BLART"] = $dat[6][$i];
			$fce->PT_ITEMS->row["ZFBDT"] = $dat[7][$i];
			$fce->PT_ITEMS->row["FAEDN"] = $dat[8][$i];
			$fce->PT_ITEMS->row["WAERS"] = $dat[9][$i];
			$fce->PT_ITEMS->row["GROSD"] = $dat[10][$i] / 100;
			$fce->PT_ITEMS->row["GROSL"] = $dat[11][$i] / 100;
			$fce->PT_ITEMS->row["OPEND"] = $dat[12][$i] / 100;
			$fce->PT_ITEMS->row["OPENL"] = $dat[13][$i] / 100;
			$fce->PT_ITEMS->row["PAYMD"] = $dat[14][$i] / 100;
			$fce->PT_ITEMS->row["PAYML"] = $dat[15][$i] / 100;
			$fce->PT_ITEMS->row["SALDD"] = ($dat[12][$i] / 100) - ($dat[14][$i] / 100);
			$fce->PT_ITEMS->row["SALDL"] = ($dat[13][$i] / 100) - ($dat[15][$i] / 100);
			$fce->PT_ITEMS->row["ZTERM"] = $dat[18][$i];
			$fce->PT_ITEMS->row["FAEDT"] = $dat[19][$i];
			$fce->PT_ITEMS->Append($fce->PT_ITEMS->row);
		}

		$fce->Call();
		#if ($fce->GetStatus() == SAPRFC_OK ) 
		#{		

		#}
		#else
		#$fce->PrintStatus();
		$fce->Close();
		$sap->Close();
	}

	function cms_display_rn($dattgl, $datrn, $orgx, $disidx)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_RN_DISPLAY_VO");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan comp.code
		$fce->PI_BUKRS = $orgx;
		$fce->PI_PYMNT = 'X';
		#echo "COMP=".$orgx."<br>";

		//paramaete inputan table tanggal
		if ($dattgl <> '') {
			$fce->PT_RNDAT->row["SIGN"] = $dattgl[0];
			$fce->PT_RNDAT->row["OPTION"] = $dattgl[1];
			$fce->PT_RNDAT->row["LOW"] = $dattgl[2];
			$fce->PT_RNDAT->row["HIGH"] = $dattgl[3];
			$fce->PT_RNDAT->Append($fce->PT_RNDAT->row);
		}

		if ($datrn <> '') {
			//parameter inputan table 
			$fce->PT_RNNUM->row["SIGN"] = $datrn[0];
			$fce->PT_RNNUM->row["OPTION"] = $datrn[1];
			$fce->PT_RNNUM->row["LOW"] = $datrn[2];
			$fce->PT_RNNUM->row["HIGH"] = $datrn[3];
			$fce->PT_RNNUM->Append($fce->PT_RNNUM->row);
		}

		//parameter distributor
		$fce->PT_KUNNR->row["SIGN"] = 'I';
		$fce->PT_KUNNR->row["OPTION"] = 'EQ';
		$fce->PT_KUNNR->row["LOW"] = $disidx;
		$fce->PT_KUNNR->row["HIGH"] = '';
		$fce->PT_KUNNR->Append($fce->PT_KUNNR->row);
		#echo "Distributor=".$disidx;
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->PT_RN_HDX->Reset();
			while ($fce->PT_RN_HDX->Next()) {
				$rn[] = $fce->PT_RN_HDX->row["RNNUM"];
				$cpcd[] = $fce->PT_RN_HDX->row["BUKRS"];
				$kddist[] = $fce->PT_RN_HDX->row["KUNNR"];
				$tglrn1[] = $fce->PT_RN_HDX->row["STIDA"];
				$tglrn2[] = $fce->PT_RN_HDX->row["RNDAT"];
				$curr[] = $fce->PT_RN_HDX->row["PYCUR"];
				$amt[] = $fce->PT_RN_HDX->row["PAYML"] * 100;
				$kmk[] = $fce->PT_RN_HDX->row["KMK"];
			}

			$fce->PT_RN_IT2->Reset();
			while ($fce->PT_RN_IT2->Next()) {
				$client[] = $fce->PT_RN_IT2->row["MANDT"];
				$rnd[] = $fce->PT_RN_IT2->row["RNNUM"];
				$year[] = $fce->PT_RN_IT2->row["GJAHR"];
				$noinva[] = $fce->PT_RN_IT2->row["BELNR"];
				$buz[] = $fce->PT_RN_IT2->row["BUZEI"];
				$noinv[] = $fce->PT_RN_IT2->row["INVOI"];
				$bl[] = $fce->PT_RN_IT2->row["BLART"];
				$invdt[] = $fce->PT_RN_IT2->row["ZFBDT"];
				$duedt[] = $fce->PT_RN_IT2->row["FAEDN"];
				$duedt2[] = $fce->PT_RN_IT2->row["FAEDT"];
				$curren[] = $fce->PT_RN_IT2->row["WAERS"];
				$grossamtd[] = $fce->PT_RN_IT2->row["GROSDX"] * 100;
				$grossamtl[] = $fce->PT_RN_IT2->row["GROSLX"] * 100;
				$openamtd[] = $fce->PT_RN_IT2->row["OPENDX"] * 100;
				$openamtl[] = $fce->PT_RN_IT2->row["OPENLX"] * 100;
				$payamtd[] = $fce->PT_RN_IT2->row["PAYMDX"] * 100;
				$payamtl[] = $fce->PT_RN_IT2->row["PAYMLX"] * 100;
				$saldamtd[] = $fce->PT_RN_IT2->row["SALDDX"] * 100;
				$saldamtl[] = $fce->PT_RN_IT2->row["SALDLX"] * 100;
			}
			$type = $fce->PE_RETURN["TYPE"];
			$id = $fce->PE_RETURN["ID"];
			$number = $fce->PE_RETURN["NUMBER"];
			$msg = $fce->PE_RETURN["MESSAGE"];

			#echo $msg;
			$arroutx = array($client, $rnd, $year, $noinva, $buz, $noinv, $bl, $invdt, $duedt, $curren, $grossamtd, $grossamtl, $openamtd, $openamtl, $payamtd, $payamtl, $saldamtd, $saldamtl, $msg, $rn, $cpcd, $kddist, $tglrn1, $tglrn2, $curr, $amt, $duedt2, $kmk);

			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_change_rn($tot, $rn, $cpcd, $dis, $tgl1, $tgl2, $curr, $datit)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_RN_CHANGE");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan comp.code
		$fce->PI_RNNUM = $rn[0];

		$fce->PI_RN_HD["BUKRS"] = $cpcd[0];
		$fce->PI_RN_HD["KUNNR"] = $dis[0];
		$fce->PI_RN_HD["STIDA"] = $tgl1[0];
		$fce->PI_RN_HD["RNDAT"] = $tgl2[0];
		$fce->PI_RN_HD["PYCUR"] = $curr[0];

		for ($i = 0; $i < $tot; $i++) {
			$fce->PT_RN_IT->row["MANDT"] = $datit[0][$i];
			$fce->PT_RN_IT->row["RNNUM"] = $datit[1][$i];
			$fce->PT_RN_IT->row["GJAHR"] = $datit[2][$i];
			$fce->PT_RN_IT->row["BELNR"] = $datit[3][$i];
			$fce->PT_RN_IT->row["BUZEI"] = $datit[4][$i];
			$fce->PT_RN_IT->row["INVOI"] = $datit[5][$i];
			$fce->PT_RN_IT->row["BLART"] = $datit[6][$i];
			$fce->PT_RN_IT->row["ZFBDT"] = $datit[7][$i];
			$fce->PT_RN_IT->row["FAEDN"] = $datit[8][$i];
			$fce->PT_RN_IT->row["WAERS"] = $datit[9][$i];
			## Update 4942
			if ($datit[9][$i] == 'IDR') {
				$fce->PT_ITEMS->row["GROSD"] = $datit[10][$i] / 100;
				$fce->PT_ITEMS->row["GROSL"] = $datit[11][$i] / 100;
				$fce->PT_ITEMS->row["OPEND"] = $datit[12][$i] / 100;
				$fce->PT_ITEMS->row["OPENL"] = $datit[13][$i] / 100;
				$fce->PT_ITEMS->row["PAYMD"] = $datit[14][$i] / 100;
				$fce->PT_ITEMS->row["PAYML"] = $datit[15][$i] / 100;
				$fce->PT_ITEMS->row["SALDD"] = ($datit[12][$i] / 100) - ($datit[14][$i] / 100);
				$fce->PT_ITEMS->row["SALDL"] = ($datit[13][$i] / 100) - ($datit[15][$i] / 100);
			} else {
				$fce->PT_ITEMS->row["GROSD"] = $datit[10][$i];
				$fce->PT_ITEMS->row["GROSL"] = $datit[11][$i] / 100;
				$fce->PT_ITEMS->row["OPEND"] = $datit[12][$i];
				$fce->PT_ITEMS->row["OPENL"] = $datit[13][$i] / 100;
				$fce->PT_ITEMS->row["PAYMD"] = $datit[14][$i];
				$fce->PT_ITEMS->row["PAYML"] = $datit[15][$i] / 100;
				$fce->PT_ITEMS->row["SALDD"] = ($datit[12][$i]) - ($datit[14][$i]);
				$fce->PT_ITEMS->row["SALDL"] = ($datit[13][$i] / 100) - ($datit[15][$i] / 100);
			}
			## Update 4942
			$fce->PT_RN_IT->Append($fce->PT_RN_IT->row);
		}

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {

			$fce->PT_RN_IT->Reset();
			while ($fce->PT_RN_IT->Next()) {
				$client[] = $fce->PT_RN_IT->row["MANDT"];
				$rnd[] = $fce->PT_RN_IT->row["RNNUM"];
				$year[] = $fce->PT_RN_IT->row["GJAHR"];
				$noinva[] = $fce->PT_RN_IT->row["BELNR"];
				$buz[] = $fce->PT_RN_IT->row["BUZEI"];
				$noinv[] = $fce->PT_RN_IT->row["INVOI"];
				$bl[] = $fce->PT_RN_IT->row["BLART"];
				$invdt[] = $fce->PT_RN_IT->row["ZFBDT"];
				$duedt[] = $fce->PT_RN_IT->row["FAEDN"];
				$curren[] = $fce->PT_RN_IT->row["WAERS"];
				$grossamtd[] = $fce->PT_RN_IT->row["GROSD"];
				$grossamtl[] = $fce->PT_RN_IT->row["GROSL"];
				$openamtd[] = $fce->PT_RN_IT->row["OPEND"];
				$openamtl[] = $fce->PT_RN_IT->row["OPENL"];
				$payamtd[] = $fce->PT_RN_IT->row["PAYMD"];
				$payamtl[] = $fce->PT_RN_IT->row["PAYML"];
				$saldamtd[] = $fce->PT_RN_IT->row["SALDD"];
				$saldamtl[] = $fce->PT_RN_IT->row["SALDL"];
			}

			$type = $fce->PE_RETURN["TYPE"];
			$id = $fce->PE_RETURN["ID"];
			$number = $fce->PE_RETURN["NUMBER"];
			$msg = $fce->PE_RETURN["MESSAGE"];

			$arroutx = array($client, $rnd, $year, $noinva, $buz, $noinv, $bl, $invdt, $duedt, $curren, $grossamtd, $grossamtl, $openamtd, $openamtl, $payamtd, $payamtl, $saldamtd, $saldamtl, $msg);

			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_delete_rn($tot, $rn, $cpcd, $dis, $tgl1, $tgl2, $curr, $amt, $datit)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_RN_CHANGE");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan comp.code
		$fce->PI_RNNUM = $rn[0];

		$fce->PI_RN_HD["BUKRS"] = $cpcd[0];
		$fce->PI_RN_HD["KUNNR"] = $dis[0];
		$fce->PI_RN_HD["STIDA"] = $tgl1[0];
		$fce->PI_RN_HD["RNDAT"] = $tgl2[0];
		$fce->PI_RN_HD["PYCUR"] = $curr[0];
		$fce->PI_RN_HD["PAYMD"] = $amt[0];
		$fce->PI_RN_HD["PAYML"] = $amt[0];

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$type = $fce->PE_RETURN["TYPE"];
			$id = $fce->PE_RETURN["ID"];
			$number = $fce->PE_RETURN["NUMBER"];
			$msg = $fce->PE_RETURN["MESSAGE"];

			$arroutx = array($msg);

			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function or_branch_plant($ting)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZAPP_SELECT_SYSPLAN");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//header entri

		$fce->XPARAM = '2000';

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->RETURN_DATA->Reset();
			while ($fce->RETURN_DATA->Next()) {
				$plantcode[] = $fce->RETURN_DATA->row["WERKS"];
				$plantdesc[] = $fce->RETURN_DATA->row["NAME1"];
			}
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();

		for ($x = 0; $x < count($plantcode); $x++) {
			echo ("<option value='$plantcode[$x]' title='$plantdesc[$x]' ");
			if ($plantdesc[$x] == $ting) {
				echo ("selected");
			}
			echo (">$plantcode[$x]" . " - " . "$plantdesc[$x]</option>");
		}
	}

	function sapcode($kode)
	{
		$panjang = strlen(strval($kode));
		if ($panjang == 1)
			$sapcode = '000000000' . $kode;
		if ($panjang == 2)
			$sapcode = '00000000' . $kode;
		if ($panjang == 3)
			$sapcode = '0000000' . $kode;
		if ($panjang == 4)
			$sapcode = '000000' . $kode;
		if ($panjang == 5)
			$sapcode = '00000' . $kode;
		if ($panjang == 6)
			$sapcode = '0000' . $kode;
		if ($panjang == 7)
			$sapcode = '000' . $kode;
		if ($panjang == 8)
			$sapcode = '00' . $kode;
		if ($panjang == 9)
			$sapcode = '0' . $kode;
		if ($panjang == 10)
			$sapcode = $kode;
		return $sapcode;
	}

	function cms_jurnal_potongan($orgx, $disidx)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_AR_POTONGAN");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan
		$fce->PI_BUKRS = $orgx;
		$fce->PI_KUNNR = $disidx;

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->T_DATA->Reset();
			while ($fce->T_DATA->Next()) {
				$BUKRS[] = $fce->T_DATA->row["BUKRS"];
				$KUNNR[] = $fce->T_DATA->row["KUNNR"];
				$BELNR[] = $fce->T_DATA->row["BELNR"];
				$GJAHR[] = $fce->T_DATA->row["GJAHR"];
				$BUDAT[] = $fce->T_DATA->row["BUDAT"];
				$BLDAT[] = $fce->T_DATA->row["BLDAT"];
				$XBLNR[] = $fce->T_DATA->row["XBLNR"];
				$AWKEY[] = $fce->T_DATA->row["AWKEY"];
				$WRBTRC[] = $fce->T_DATA->row["WRBTRC"];
				$DMBTRC[] = $fce->T_DATA->row["DMBTRC"];
				$WAERS[] = $fce->T_DATA->row["WAERS"];
				$HKONT[] = $fce->T_DATA->row["HKONT"];
				$FLAG[] = $fce->T_DATA->row["FLAG"];

				$WRBTR[] = $fce->T_DATA->row["WRBTR"];
				$DMBTR[] = $fce->T_DATA->row["DMBTR"];
				$BUZEI[] = $fce->T_DATA->row["BUZEI"];
				$BLART[] = $fce->T_DATA->row["BLART"];
				$SGTXT[] = $fce->T_DATA->row["SGTXT"];
			}
			$arroutx = array(
				$BUKRS,
				$KUNNR,
				$BELNR,
				$GJAHR,
				$BUDAT,
				$BLDAT,
				$AWKEY,
				$WRBTRC,
				$DMBTRC,
				$WAERS,
				$HKONT,
				$FLAG,
				$WRBTR,
				$DMBTR,
				$BUZEI,
				$BLART,
				$XBLNR,
				$SGTXT
			);
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_select_invoice_with_diskon($orgx, $disidx, $tglxx, $kmke)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_AR_OI_VO");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan
		$fce->PI_BUKRS = $orgx;
		$fce->PI_KUNNR = $disidx;
		$fce->PI_STIDA = $tglxx;
		$fce->PI_KMK = $kmke;
		$fce->PI_DISCOUNT = "X";
		#if($_SESSION['user_org']=='3000'){
		$fce->DUE_DATE_SORT = 'X';
		#}


		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->PT_ITEMSX->Reset();
			while ($fce->PT_ITEMSX->Next()) {
				$cpcd[] = $fce->PT_ITEMSX->row["BUKRS"];
				$kddist[] = $fce->PT_ITEMSX->row["KUNNR"];
				$year[] = $fce->PT_ITEMSX->row["GJAHR"];
				$noinva[] = $fce->PT_ITEMSX->row["BELNR"];
				$buz[] = $fce->PT_ITEMSX->row["BUZEI"];
				$noinv[] = $fce->PT_ITEMSX->row["INVOI"];
				$bl[] = $fce->PT_ITEMSX->row["BLART"];
				$invdt[] = $fce->PT_ITEMSX->row["ZFBDT"];
				$duedt[] = $fce->PT_ITEMSX->row["FAEDN"];
				$duedt2[] = $fce->PT_ITEMSX->row["FAEDT"];
				$curren[] = $fce->PT_ITEMSX->row["WAERS"];
				$grossamtd[] = $fce->PT_ITEMSX->row["GROSD"] * 100;
				$grossamtl[] = $fce->PT_ITEMSX->row["GROSL"] * 100;
				$openamtd[] = $fce->PT_ITEMSX->row["OPEND"] * 100;
				$openamtl[] = $fce->PT_ITEMSX->row["OPENL"] * 100;
				$payamtd[] = $fce->PT_ITEMSX->row["PAYMD"] * 100;
				$payamtl[] = $fce->PT_ITEMSX->row["PAYML"] * 100;
				$saldamtd[] = $fce->PT_ITEMSX->row["SALDD"] * 100;
				$saldamtl[] = $fce->PT_ITEMSX->row["SALDL"] * 100;
				$sgtxt[] = $fce->PT_ITEMSX->row["SGTXT"];
				$zterm[] = $fce->PT_ITEMSX->row["ZTERM"];
				$aubel[] = $fce->PT_ITEMSX->row["AUBEL"];
				$erdat[] = $fce->PT_ITEMSX->row["ERDAT"];
				$zbd1t[] = $fce->PT_ITEMSX->row["ZBD1T"]; //Day 1
				$zbd2t[] = $fce->PT_ITEMSX->row["ZBD2T"]; //Day 2
				$zbd3t[] = $fce->PT_ITEMSX->row["ZBD3T"]; //Net Payment Terms Period
				$zbd1p[] = $fce->PT_ITEMSX->row["ZBD1P"]; //Discount 1
				$zbd2p[] = $fce->PT_ITEMSX->row["ZBD2P"]; //Discount 2
				$skfbt[] = $fce->PT_ITEMSX->row["SKFBT"]; //Amount Eligible for Cash Discount in Document Currency
				$selisih[] = $fce->PT_ITEMSX->row["SELISIH"];
				$potong[] = $fce->PT_ITEMSX->row["POTONG"] * 100;
				$discb[] = $fce->PT_ITEMSX->row["DISCB"] * 100;
			}
			$arroutx = array(
				$cpcd,
				$kddist,
				$year,
				$noinva,
				$buz,
				$noinv,
				$bl,
				$invdt,
				$duedt,
				$curren,
				$grossamtd,
				$grossamtl,
				$openamtd,
				$openamtl,
				$payamtd,
				$payamtl,
				$saldamtd,
				$saldamtl,
				$sgtxt,
				$zterm,
				$duedt2,
				$aubel,
				$erdat,
				$zbd1t,
				$zbd2t,
				$zbd3t,
				$zbd1p,
				$zbd2p,
				$skfbt,
				$selisih,
				$potong,
				$discb
			);
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	function cms_update_potongan($rnnum, $item)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("Z_ZCFI_CMS_AR_POTONGAN_RN");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		//parameter inputan
		$fce->PI_RNNUM = $rnnum;

		$total = count($item);
		$fce->T_DATA->Reset();

		for ($i = 0; $i < $total; $i++) {
			$fce->T_DATA->row["BUKRS"] = $item[$i][BUKRS];
			$fce->T_DATA->row["KUNNR"] = $item[$i][KUNNR];
			$fce->T_DATA->row["BELNR"] = $item[$i][BELNR];
			$fce->T_DATA->row["GJAHR"] = $item[$i][GJAHR];
			$fce->T_DATA->row["BUDAT"] = $item[$i][BUDAT];
			$fce->T_DATA->row["BLDAT"] = $item[$i][BLDAT];
			$fce->T_DATA->row["XBLNR"] = $item[$i][XBLNR];
			$fce->T_DATA->row["AWKEY"] = $item[$i][AWKEY];
			$fce->T_DATA->row["WRBTR"] = $item[$i][WRBTR];
			$fce->T_DATA->row["DMBTR"] = $item[$i][DMBTR];
			$fce->T_DATA->row["WAERS"] = $item[$i][WAERS];
			$fce->T_DATA->row["WRBTRC"] = $item[$i][WRBTRC] / 100;
			$fce->T_DATA->row["DMBTRC"] = $item[$i][DMBTRC] / 100;
			$fce->T_DATA->row["HKONT"] = $item[$i][HKONT];
			$fce->T_DATA->row["RNNUM"] = $item[$i][RNNUM];
			$fce->T_DATA->row["FLAG"] = $item[$i][FLAG];
			$fce->T_DATA->Append($fce->T_DATA->row);
		}

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK) {
			$fce->T_DATA->Reset();
			while ($fce->T_DATA->Next()) {
				$BUKRS[] = $fce->T_DATA->row["BUKRS"];
				$KUNNR[] = $fce->T_DATA->row["KUNNR"];
				$BELNR[] = $fce->T_DATA->row["BELNR"];
				$GJAHR[] = $fce->T_DATA->row["GJAHR"];
				$BUDAT[] = $fce->T_DATA->row["BUDAT"];
				$BLDAT[] = $fce->T_DATA->row["BLDAT"];
				$XBLNR[] = $fce->T_DATA->row["XBLNR"];
				$AWKEY[] = $fce->T_DATA->row["AWKEY"];
				$WRBTR[] = $fce->T_DATA->row["WRBTR"];
				$DMBTR[] = $fce->T_DATA->row["DMBTR"];
				$WRBTRC[] = $fce->T_DATA->row["WRBTRC"] * 100;
				$DMBTRC[] = $fce->T_DATA->row["DMBTRC"] * 100;
				$WAERS[] = $fce->T_DATA->row["WAERS"];
				$HKONT[] = $fce->T_DATA->row["HKONT"];
				$RNNUM[] = $fce->T_DATA->row["RNNUM"];
				$FLAG[] = $fce->T_DATA->row["FLAG"];
			}
			$arroutx = array(
				$BUKRS,
				$KUNNR,
				$BELNR,
				$GJAHR,
				$BUDAT,
				$BLDAT,
				$AWKEY,
				$WRBTRC,
				$DMBTRC,
				$WAERS,
				$HKONT,
				$FLAG,
				$WRBTR,
				$DMBTR,
				$RNNUM
			);
			return $arroutx;
		} else
			$fce->PrintStatus();

		$fce->Close();
		$sap->Close();
	}

	public function getDataDF($company, $distr_id, $start, $end, $status, $bank, $type_out)
	{
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf");

		if ($sap->GetStatus() == SAPRFC_OK)
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			echo $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("ZCFI_SEL_INV_DF");
		if ($fce == false) {
			$sap->PrintStatus();
			exit;
		}

		$fce->TYPE_OUT = $type_out;
		$fce->P_BANK = $bank;

		$fce->R_BUKRS->row['SIGN'] = 'I';
		$fce->R_BUKRS->row['OPTION'] = 'EQ';
		$fce->R_BUKRS->row['LOW'] = $company;
		$fce->R_BUKRS->row['HIGH'] = '';
		$fce->R_BUKRS->Append($fce->R_BUKRS->row);

		if ($status != 0 && $status != '') {
			$fce->R_STATUS->row['SIGN'] = 'I';
			$fce->R_STATUS->row['OPTION'] = 'EQ';
			$fce->R_STATUS->row['LOW'] = $status;
			$fce->R_STATUS->row['HIGH'] = '';
			$fce->R_STATUS->Append($fce->R_STATUS->row);
		}

		if ($start != null && $end != null) {
			$fce->R_DUEDATE->row['SIGN'] = 'I';
			$fce->R_DUEDATE->row['OPTION'] = 'BT';
			$fce->R_DUEDATE->row['LOW'] = $start; //'********';
			$fce->R_DUEDATE->row['HIGH'] = $end; //'********';
			$fce->R_DUEDATE->Append($fce->R_DUEDATE->row);
		}


		$fce->R_KUNNR->row['SIGN'] = 'I';
		$fce->R_KUNNR->row['OPTION'] = 'EQ';
		$fce->R_KUNNR->row['LOW'] = $distr_id;
		$fce->R_KUNNR->row['HIGH'] = '';
		$fce->R_KUNNR->Append($fce->R_KUNNR->row);

		$fce->Call();
		$dataTagihan = array('status' => 0);
		if ($fce->GetStatus() == SAPRFC_OK && $fce->E_SUCCESS == 'S') {
			$dataTagihan['data'] = array();

			$fce->IT_OUTPUT->Reset();
			while ($fce->IT_OUTPUT->Next()) {
				$dataTagihan['data'][] = $fce->IT_OUTPUT->row;
			}

			$dataTagihan['status'] = 1;
		}

		// echo "<pre>";var_dump($fce);die();
		return $dataTagihan;
	}
}
