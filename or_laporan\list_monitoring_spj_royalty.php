<?php
session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);


include('../include/or_fungsi.php');
include('../include/validasi.php');
include('../include/API.php');
$fungsi = new or_fungsi();
$conn = $fungsi->or_koneksi();

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->getmainhalam_id($conn, $dirr);
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];
$user_tipe = $_SESSION['user_tipe'];
$user_name = $_SESSION['user_name'];

$role_penagihan = null;
$sql = "
    SELECT ORG FROM AUTH_PENAGIHAN WHERE USER_ID = '".$user_id."' AND DELETE_MARK != '1'
";
$query = oci_parse($conn, $sql);
oci_execute($query);
$data = array();
while ($row = oci_fetch_array($query)) {
    $role_penagihan = $row['ORG'];
}

$sqlPlant = "
    SELECT WERKS, NAME1 FROM RFC_Z_ZAPP_SELECT_SYSPLAN WHERE XPARAM = '".$role_penagihan."' 
";
$queryPlant = oci_parse($conn, $sqlPlant);
oci_execute($queryPlant);
$plantFilter = array();

$itr = 0;
while ($row = oci_fetch_array($queryPlant)) {
    $plantFilter[$itr]['CODE'] = $row['WERKS'];
    $plantFilter[$itr]['NAME'] = $row['NAME1'];

    $itr++;
}

$mp_coics = $fungsi->getComin($conn, $user_org);
if (count($mp_coics) > 0) {
    unset($inorg);
    $orgcounter = 0;
    foreach ($mp_coics as $keyOrg => $valorgm) {
        $inorg .= "'" . $keyOrg . "',";
        $orgcounter++;
    }
    $inorg = rtrim($inorg, ',');
} else {
    $inorg = $user_org;
}
if ($fungsi->keamanan($halaman_id, $user_id) == 0) {
?>
    <SCRIPT LANGUAGE="JavaScript">
        <!--
        alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
        //
        -->
    </SCRIPT>

    <a href="../index.php">Login....</a>
<?php

    exit();
}

$korpeArr = array();
$sqlKorpe = "
    SELECT DISTRICT, KOORDINATOR_AREA FROM ZMD_KOORDINATOR_PENJUALAN WHERE DEL = '0'
";
$queryKorpe = oci_parse($conn, $sqlKorpe);
oci_execute($queryKorpe);
while ($rowKorpe = oci_fetch_array($queryKorpe)) {
    $korpeArr[$rowKorpe['DISTRICT']] = $rowKorpe['KOORDINATOR_AREA'];
}

$page = "list_monitoring_spj_royalty.php";
$company_code = isset($_POST['COMPANY_CODE']) ? $_POST['COMPANY_CODE'] : '';
$sales_org = isset($_POST['SALES_ORG']) ? $_POST['SALES_ORG'] : '';
$data_pov = isset($_POST['data_pov']) ? $_POST['data_pov'] : '';
$plant_f = isset($_POST['PLANT']) ? $_POST['PLANT'] : '';

if (isset($_POST['data_pov'])) {
    if ($data_pov == 'pov_pemilik_brand') {
        $company_code = isset($role_penagihan) ? $role_penagihan : '';
        $sales_org = isset($_POST['SALES_ORG']) ? $_POST['SALES_ORG'] : '';
    } elseif ($data_pov == 'pov_produsen') {
        $company_code = isset($_POST['COMPANY_CODE']) ? $_POST['COMPANY_CODE'] : '';
        $sales_org = isset($role_penagihan) ? $role_penagihan : '';
    }
}

$tgl_from_fo = isset($_POST['TGL_FROM']) ? $_POST['TGL_FROM'] : '';
$tgl_to_fo = isset($_POST['TGL_TO']) ? $_POST['TGL_TO'] : '';

if (isset($_POST['TGL_FROM'])) {
    $tgl_from = explode('-', $_POST['TGL_FROM']);
    krsort($tgl_from);
}

if (isset($_POST['TGL_TO'])) {
    $tgl_to = explode('-', $_POST['TGL_TO']);
    krsort($tgl_to);
}

$tgl_create_from = isset($_POST['TGL_FROM']) ? implode('-', $tgl_from) : '';
$tgl_create_to = isset($_POST['TGL_TO']) ? implode('-', $tgl_to) : '';

$currentPage = "list_monitoring_spj_royalty.php";
if (isset($_POST['cari'])) {

    var_dump($_POST);
    $royal='';
    $msa=''; 
    if($_POST['ROYALTI']){
        $royal='X';
    }
    if($_POST['MSA']){
        $msa='X';
    }
   // die;
    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK)
        $sap->Open();
    if ($sap->GetStatus() != SAPRFC_OK) {
        $sap->PrintStatus();
        exit;
    }

    $fce = $sap->NewFunction("ZCSD2155_PENJUALAN_SPJ_TUR_MDR");
    
    // echo "mantap";
    if ($fce == false) {
        $sap->PrintStatus();
        exit;
    }

    $fce->SO_VKORG = $sales_org;
    $fce->I_INNER_JOIN = 'X';
    $fce->I_ROYALTI = $royal;
    $fce->I_MSA = $msa;
    $fce->PEMILIK_BRAND = $company_code;

    $fce->SO_LFART->row["SIGN"]='I';
    $fce->SO_LFART->row["OPTION"]='CP';
    $fce->SO_LFART->row["LOW"]='*';
    $fce->SO_LFART->row["HIGH"]='';
    $fce->SO_LFART->Append($fce->SO_LFART->row);

    if ($plant_f) {
        $fce->SO_WERKS->row["SIGN"]='I';
        $fce->SO_WERKS->row["OPTION"]='EQ';
        $fce->SO_WERKS->row["LOW"]=$plant_f;
        $fce->SO_WERKS->row["HIGH"]='';
        $fce->SO_WERKS->Append($fce->SO_WERKS->row);
    }
    
    
    $TGL_CREATE_BASTB_FROM = !empty($tgl_create_from) ? strval(date('Ymd', strtotime($tgl_create_from))) : "";
    
    $TGL_CREATE_BASTB_TO = !empty($tgl_create_to) ? strval(date('Ymd', strtotime($tgl_create_to))) : "";

    $fce->SO_WADAT->row["SIGN"]='I';
    $fce->SO_WADAT->row["OPTION"]='BT';
    $fce->SO_WADAT->row["LOW"]= $TGL_CREATE_BASTB_FROM;
    $fce->SO_WADAT->row["HIGH"]=$TGL_CREATE_BASTB_TO;
    $fce->SO_WADAT->Append($fce->SO_WADAT->row);

    $fce->Call();
    
    $item_data = array();
    if ($fce->GetStatus() == SAPRFC_OK) {
        $fce->T_RETURN->Reset();
        while ($fce->T_RETURN->Next()) {
            $fce->T_RETURN->row['KORPE'] = isset($korpeArr[$fce->T_RETURN->row['BZIRK']]) ? $korpeArr[$fce->T_RETURN->row['BZIRK']] : "";
            $list_bastp[] = $fce->T_RETURN->row;
        }
    }
    // echo "<pre>";
    // print_r($list_bastp);
    // echo "</pre>";
    
    $fce->Close();
    $sap->Close();
  // var_dump($list_bastp);
    $total = count($list_bastp);
    if ($total < 1) $komen = "Tidak Ada Data Yang Ditemukan";
}
?>
<script language=javascript>
    var message = "You dont have permission to right click";

    function clickIE() {
        if (document.all) {
            (message);
            return false;
        }
    }

    function clickNS(e) {
        if (document.layers || (document.getElementById && !document.all)) {
            if (e.which == 2 || e.which == 3) {
                (message);
                return false;
            }
        }
    }

    if (document.layers) {
        document.captureEvents(Event.MOUSEDOWN);
        document.onmousedown = clickNS;
    } else {
        document.onmouseup = clickNS;
        document.oncontextmenu = clickIE;
    }

    document.oncontextmenu = new Function("return false")

    function getXMLHTTP() {
        var xmlhttp = false;
        try {
            xmlhttp = new XMLHttpRequest();
        } catch (e) {
            try {
                xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (e) {
                try {
                    xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                } catch (e1) {
                    xmlhttp = false;
                }
            }
        }
        return xmlhttp;
    }
   function validateCheckboxSelection() {
    const royalti = document.getElementById('ROYALTI').checked;
    const msa = document.getElementById('MSA').checked;
    const warning = document.getElementById('warning_datediff');

    // Bersihkan pesan sebelumnya
    warning.innerText = '';

    // Jika dua-duanya dicentang atau dua-duanya tidak dicentang
    if ( (!royalti && !msa)) {
        warning.innerText = 'Silakan pilih salah satu: Royalti atau MSA.';
        return false; // tidak valid
    }

    return true; // valid
}
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title>List Report SPJ Royalty</title>
    <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
    <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
    <!-- import the calendar script -->
    <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
    <!-- import the language module -->
    <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
    <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
    <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
    <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script type="text/javascript" src="../include/jquery.min.js"></script>

    <style>
        .animate-top {
            position: relative;
            animation: animatetop 0.4s
        }

        @keyframes animatetop {
            from {
                top: -300px;
                opacity: 0
            }

            to {
                top: 0;
                opacity: 1
            }
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.275);
        }

        .modal-content {
            margin: 5% auto;
            width: 2000px;
            max-width: 90%;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.175);
            border-radius: .3rem;
            outline: 0;
        }

        .modal-header {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: start;
            -ms-flex-align: start;
            align-items: flex-start;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            padding: 5px 1rem 5px 1rem;
            border-bottom: 1px solid #e9ecef;
            border-top-left-radius: .3rem;
            border-top-right-radius: .3rem;
        }

        .modal-title {
            margin-bottom: 0;
            line-height: 1.5;
            margin-top: 0;
            font-size: 1.25rem;
        }

        .modal-header .close {
            float: right;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: .5;
            padding: 1rem;
            margin: -1rem -1rem -1rem auto;
            background-color: transparent;
            border: 0;
        }

        .close:not(:disabled):not(.disabled) {
            cursor: pointer;
        }

        .modal-body {
            flex: 1 1 auto;
            padding: 1rem;
        }

        .modal-body p {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .modal-footer {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: end;
            -ms-flex-pack: end;
            justify-content: flex-end;
            padding: 5px 1rem 5px 1rem;
            border-top: 1px solid #e9ecef;
        }

        .modal-footer>* {
            margin: 5px;
        }

        /* buttons */
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            border: 1px solid transparent;
            padding: 5px 10px;
            cursor: pointer;
            font-size: inherit;
        }

        .btn:focus,
        .btn:hover {
            text-decoration: none;
        }

        .btn-primary {
            color: #fff;
            background-color: #da3e24;
            border-color: #da3e24;
        }

        .btn-primary:hover {
            color: #fff;
            background-color: #d33a21;
            border-color: #d33a21;
        }

        .btn-secondary {
            color: #fff;
            background-color: #7c8287;
            border-color: #7c8287;
        }

        .btn-secondary:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .readonly {
            border: solid 2px #33333312;
            background: #f6f6f6;
        }

        button.disabled {
            color: #a7a7a7;
        }

        .btn-success {
            margin-top: 30px;
            background: green;
            color: #fff;
            padding: 10px 20px;
            margin-bottom: 15px;
        }

        .total_detail{
            background: #fbfcd2;
        }
    </style>
</head>
<script>
    function detailBASTB(bukrs, bastb) {
        var strURL = "invoice_royalty.php?bukrs=" + bukrs + "&bastb=" + bastb;
        popUp(strURL);
    }
</script>

<body>
    <div align="center">
        <table width="600" align="center" class="adminheading" border="0">
            <tr>
                <th class="kb2">List Report SPJ Royalty</th>
            </tr>
        </table>
    </div>
    <?
    if ($role_penagihan == '') {
    ?>
    <div align="center" class="login">
        Anda belum memiliki akses Monitoring Penagihan
    </div>
    <?
    } else{
    ?>
        <?
        if ($total < 1) {
        ?>

            <div align="center">
                <table width="600" align="center" class="adminlist">
                    <tr>
                        <th align="left" colspan="4"> &nbsp;Form Search List Report SPJ Royalty</th>
                    </tr>
                </table>
            </div>

            <form id="form1" name="form1" method="post" action="<? echo $page; ?>">
                <table width="600" align="center" class="adminform">
                    <tr width="174">
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <?
                    if ($user_tipe == 'admin') {
                        ?>
                        <tr width="174">
                            <td class="puso">Company Brand </td>
                            <td class="puso">:</td>
                            <td>
                                <select name="COMPANY_CODE" id="COMPANY_CODE">
                                    <option value="" <?= $company_code == '' ? 'selected' : '' ?> >All</option>
                                    <option value="3000" <?= $company_code == '3000' ? 'selected' : '' ?> >3000 - Semen Padang</option>
                                    <option value="4000" <?= $company_code == '4000' ? 'selected' : '' ?> >4000 - Semen Tonasa</option>
                                    <option value="5000" <?= $company_code == '5000' ? 'selected' : '' ?> >5000 - Semen Gresik</option>
                                    <option value="7000" <?= $company_code == '7000' ? 'selected' : '' ?> >7000 - Semen Indonesia</option>
                                    <option value="1000" <?= $company_code == '1000' ? 'selected' : '' ?> >SMBR</option>
                                    <option value="PTSC" <?= $company_code == 'PTSC' ? 'selected' : '' ?> >SBI</option>
                                    <option value="ID50" <?= $company_code == 'ID50' ? 'selected' : '' ?> >SBA</option>
                                </select>
                            </td>
                        </tr>
                        <tr width="174">
                            <td class="puso">Company Produsen </td>
                            <td class="puso">:</td>
                            <td>
                                <select name="SALES_ORG" id="SALES_ORG">
                                    <option value="3000" <?= $sales_org == '3000' ? 'selected' : '' ?> >3000 - Semen Padang</option>
                                    <option value="4000" <?= $sales_org == '4000' ? 'selected' : '' ?> >4000 - Semen Tonasa</option>
                                    <option value="5000" <?= $sales_org == '5000' ? 'selected' : '' ?> >5000 - Semen Gresik</option>
                                    <option value="7000" <?= $sales_org == '7000' ? 'selected' : '' ?> >7000 - Semen Indonesia</option>
                                    <option value="1000" <?= $sales_org == '1000' ? 'selected' : '' ?> >SMBR</option>
                                    <option value="PTSC" <?= $sales_org == 'PTSC' ? 'selected' : '' ?> >SBI</option>
                                    <option value="ID50" <?= $sales_org == 'ID50' ? 'selected' : '' ?> >SBA</option>
                                </select>
                            </td>
                        </tr>
                        <?
                        } else {
                        ?>
                        <tr width="174">
                            <td class="puso">Data </td>
                            <td class="puso">:</td>
                            <td>
                                <select name="data_pov" id="data_pov" onchange="changeDataPov(event)" required>
                                    <option value="" >Pilih data</option>
                                    <option value="pov_pemilik_brand">Pemilik Brand</option>
                                    <option value="pov_produsen">Produsen</option>
                            </td>
                        </tr>
                        <tr width="174" class="session_role_tr" style="display: none;">
                            <td class="puso" id="session_role"> </td>
                            <td class="puso">:</td>
                            <td>
                                <span> &nbsp;<strong><?= $role_penagihan ?></strong></span>
                            </td>
                        </tr>
                        <tr width="174" class="c_pemilik_brand" style="display: none;">
                            <td class="puso">Company Pemilik Brand </td>
                            <td class="puso">:</td>
                            <td>
                                <select name="COMPANY_CODE" id="COMPANY_CODE">
                                    <option value="" <?= $company_code == '' ? 'selected' : '' ?> >All</option>
                                    <option value="3000" <?= $company_code == '3000' ? 'selected' : '' ?> >3000 - Semen Padang</option>
                                    <option value="4000" <?= $company_code == '4000' ? 'selected' : '' ?> >4000 - Semen Tonasa</option>
                                    <option value="5000" <?= $company_code == '5000' ? 'selected' : '' ?> >5000 - Semen Gresik</option>
                                    <option value="7000" <?= $company_code == '7000' ? 'selected' : '' ?> >7000 - Semen Indonesia</option>
                                    <option value="1000" <?= $company_code == '1000' ? 'selected' : '' ?> >SMBR</option>
                                    <option value="PTSC" <?= $company_code == 'PTSC' ? 'selected' : '' ?> >SBI</option>
                                    <option value="ID50" <?= $company_code == 'ID50' ? 'selected' : '' ?> >SBA</option>
                                </select>
                            </td>
                        </tr>
                        <tr width="174" class="c_produsen" style="display: none;">
                            <td class="puso">Company Produsen </td>
                            <td class="puso">:</td>
                            <td>
                                <select name="SALES_ORG" id="SALES_ORG">
                                    <option value="3000" <?= $sales_org == '3000' ? 'selected' : '' ?> >3000 - Semen Padang</option>
                                    <option value="4000" <?= $sales_org == '4000' ? 'selected' : '' ?> >4000 - Semen Tonasa</option>
                                    <option value="5000" <?= $sales_org == '5000' ? 'selected' : '' ?> >5000 - Semen Gresik</option>
                                    <option value="7000" <?= $sales_org == '7000' ? 'selected' : '' ?> >7000 - Semen Indonesia</option>
                                    <option value="1000" <?= $sales_org == '1000' ? 'selected' : '' ?> >SMBR</option>
                                    <option value="PTSC" <?= $sales_org == 'PTSC' ? 'selected' : '' ?> >SBI</option>
                                    <option value="ID50" <?= $sales_org == 'ID50' ? 'selected' : '' ?> >SBA</option>
                                </select>
                            </td>
                        </tr>
                        <?
                        }
                        ?>
                    <tr>
                        <td class="puso">PLANT </td>
                        <td class="puso">:</td>
                        <td>
                            <?
                                if ($user_tipe == 'admin') {
                            ?>
                            <input name="PLANT" type="text" id="PLANT" size=12 value="<?= $plant_f; ?>" />
                            <? 
                                }else {
                            ?>
                            <select name="PLANT" id="PLANT">
                                <option value="">All</option>
                                <?
                                    foreach ($plantFilter as $k => $v) {
                                ?>
                                <option value="<?= $v['CODE'] ?>" <?= $plant_f == $v['CODE'] ? 'selected' : '' ?>><?= $v['CODE'] ?> - <?= $v['NAME'] ?></option>
                                <?
                                    }
                                ?>
                            </select>
                            <? 
                                }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="puso">Tanggal Mulai SPJ</td>
                        <td class="puso">:</td>
                        <td><input name="TGL_FROM" type="text" id="TGL_FROM" size=12 value="<?= $tgl_from_fo; ?>" onClick="return showCalendar('TGL_FROM');" required="true" /></td>
                    </tr>
                    <tr>
                        <td class="puso">Tanggal Akhir SPJ</td>
                        <td class="puso">:</td>
                        <td><input name="TGL_TO" type="text" id="TGL_TO" size=12 value="<?= $tgl_to_fo; ?>" onClick="return showCalendar('TGL_TO');" required="true" /><i style="color: #d50505;" id="warning_datediff"></i></td>
                    </tr>
                    <tr>
                        <td class="puso">Royalti</td>
                        <td class="puso">:</td>
                        <td><input name="ROYALTI" type="checkbox" id="ROYALTI" size=12  /><i style="color: #d50505;" id="warning_datediff"></i></td>
                    </tr>
                    <tr>
                        <td class="puso">MSA</td>
                        <td class="puso">:</td>                 
                        <td><input name="MSA" type="checkbox" id="MSA" size=12 /><i style="color: #d50505;" id="warning_datediff"></i></td>
                    </tr>
                    <tr>
                        <td class="ThemeOfficeMenu">&nbsp;</td>
                        <td class="ThemeOfficeMenu">&nbsp;</td>
                        <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find"  onclick="return validateCheckboxSelection()"/>
                    </tr>
                    <tr>
                        <td class="ThemeOfficeMenu">&nbsp;</td>
                        <td class="ThemeOfficeMenu">&nbsp;</td>
                    </tr>
                </table>
            </form>
        <? } ?>
        <br />
        <br />
        <? if ($total > 0) { ?>
            <div align="center">
                <table width="95%" align="center">
                    <tr>
                        <th align="right" colspan="4"><span>
                            </span></th>
                    </tr>
                </table>
            </div>
            <div align="center">
                <table width="95%" align="center" class="adminlist">
                    <tr>
                        <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data List SPJ Royalty </span></th>
                    </tr>
                    <tr>
                        <th align="left" colspan="2" style="width: 200px;">
                            <a href="list_monitoring_spj_royalty.php" class="button">Back</a>
                        </th>
                        <th align="right" colspan="2">
                            <button id="btnExport" onclick="fnExcelReport();"> Export excel </button>
                        </th>
                    </tr>
                </table>
            </div>
            <div align="center">
                <table width="95%" align="center" class="adminlist" id="tableData">
                    <tr class="quote">
                        <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
                        <td align="center"><strong>Produsen</strong></td>
                        <td align="center"><strong>Pemilik BRAND</strong></td>
                        <td align="center"><strong>Koordinator Area</strong></td>
                        <td align="center"><strong>Distrik</strong></td>
                        <td align="center"><strong>Plant</strong></td>
                        <td align="center"><strong>No. Billing</strong></td>
                        <td align="center"><strong>No. SO</strong></td>
                        <td align="center"><strong>FLAG</strong></td>
                        <td align="center"><strong>No. Shipment</strong></td>
                        <td align="center"><strong>Tgl. Actual GI</strong></td>
                        <td align="center"><strong>Tgl. Billing</strong></td>
                        <td align="center"><strong>Brand</strong></td>
                        <td align="center"><strong>Kode Material</strong></td>
                        <td align="center"><strong>Material</strong></td>
                        <td align="center"><strong>Volume</strong></td>
                        <td align="center"><strong>UOM</strong></td>
                        <td align="center"><strong>Nilai Billing</strong></td>
                        <td align="center"><strong>Royalty</strong></td>
                        <td align="center"><strong>PPN Royalty</strong></td>
                        <td align="center"><strong>Tot. Royalty</strong></td>
                        <td align="center"><strong>MSA</strong></td>
                        <td align="center"><strong>PPN MSA</strong></td>
                        <td align="center"><strong>Tot. MSA</strong></td>
                    </tr>
                    <?php
                    $totalTOTRYLT = 0;
                    foreach ($list_bastp as $no => $_row) {
                        // $class = ($no % 2) == 0 ? 'row0' : 'row1';
                        // $totalTOTRYLT += $_row['TOTRYLT'] * 100;
                        // $dataAttributes = htmlspecialchars(json_encode($list_item_bastb[$_row['BASTB']]));
                    ?>
                        <tr class="<?= $class ?>">
                            <td align="center"><?= ($no + 1) ?></td>
                            <td align="center"><?= $_row['VKORG'] ?></td>
                            <td align="center"><?= $_row['PEMILIK_BRAND'] ?></td>
                            <td align="center"><?= $_row['KORPE'] ?></td>
                            <td align="center"><?= $_row['BZIRK'] ?></td>
                            <td align="center"><?= $_row['WERKS'] ?></td>
                            <td align="center"><?= $_row['VBELN_BILL'] ?></td>
                            <td align="center"><?= $_row['VGBEL'] ?></td>
                            <td align="center"><?= $_row['ROYALTI'] ?></td>
                            <td align="center"><?= $_row['EXTI1'] ?></td>
                            <td align="center"><?= $_row['WADAT_IST'] == "00000000" ? "-" : date('Y-M-d', strtotime($_row['WADAT_IST'])) ?></td>
                            <td align="center"><?= $_row['FKDAT'] == "00000000" ? "-" : date('Y-M-d', strtotime($_row['FKDAT'])) ?></td>
                            <td align="center"><?= $_row['BRAND'] ?></td>
                            <td align="center"><?= $_row['MATNR'] ?></td>
                            <td align="center"><?= $_row['ARKTX'] ?></td>
                            <td align="center"><?= number_format($_row['TON'], 2)  ?></td>
                            <td align="center"><?= $_row['KET_TON'] ?></td>
                            <td align="center"><?= number_format($_row['NET'] , 2) ?></td>
                            <td align="center"><?= number_format($_row['NROYALTI'] , 2) ?></td>
                            <td align="center"><?= number_format($_row['PPNROYALTI'] , 2) ?></td>
                            <td align="center"><?= number_format($_row['TOTROYALTI'] , 2) ?></td>

                            <td align="center"><?= number_format($_row['NET_MSA']  , 2) ?></td>
                            <td align="center"><?= number_format($_row['PPNMSA']  , 2) ?></td>
                            <td align="center"><?= number_format($_row['TOTMSA']  , 2) ?></td>
                        </tr>

                    <?php } ?>
                    <!-- <tr>
                    <td colspan="13" align="right"><strong>Total Royalty:</strong></td>
                    <td align="center"><strong><?= number_format($totalTOTRYLT, 3) ?></strong></td>
                    </tr> -->
                </table>
            </div>
            <? } else if (isset($_POST['cari'])) { ?>
            <div align="center" class="login">
                Data tidak ditemukan
            </div>
            <?
            }
            ?>
            <!-- The Modal -->
            <div id="modalDialog" class="modal">
                <div class="modal-content animate-top">
                    <div class="modal-header">
                        <div id="header_item"></div>
                        <button type="button" class="close">
                            <span aria-hidden="true">x</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <table width="95%" align="center" class="adminlist" style="margin: auto;">
                            <thead>
                                <tr class="quote">
                                     <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
                                    <td align="center"><strong>Produsen</strong></td>
                                    <td align="center"><strong>Pemilik BRAND</strong></td>
                                    <td align="center"><strong>Plant</strong></td>
                                    <td align="center"><strong>No. Billing</strong></td>
                                    <td align="center"><strong>No. SO</strong></td>
                                    <td align="center"><strong>FLAG</strong></td>
                                    <td align="center"><strong>No. Shipment</strong></td>
                                    <td align="center"><strong>Tgl. Actual GI</strong></td>
                                    <td align="center"><strong>Tgl. Billing</strong></td>
                                    <td align="center"><strong>Brand</strong></td>
                                    <td align="center"><strong>Kode Material</strong></td>
                                    <td align="center"><strong>Material</strong></td>
                                    <td align="center"><strong>Volume</strong></td>
                                    <td align="center"><strong>UOM</strong></td>
                                    <td align="center"><strong>Nilai Billing</strong></td>
                                    <td align="center"><strong>Royalty</strong></td>
                                    <td align="center"><strong>PPN</strong></td>
                                    <td align="center"><strong>Tot. Royalty</strong></td>
                                </tr>
                            </thead>
                            <tbody id="itemDetail">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <iframe id="txtArea1" style="display:none"></iframe>

            <script>
                let modal = $('#modalDialog');
                let btn = $(".mbtn");
                let span = $(".close");

                function fnExcelReport() {
                    var tab_text = "<table border='2px'><tr bgcolor='#87AFC6'>";
                    var j = 0;
                    var tab = document.getElementById('tableData'); // id of table

                    for (j = 0; j < tab.rows.length; j++) {
                        tab_text = tab_text + tab.rows[j].innerHTML + "</tr>";
                        //tab_text=tab_text+"</tr>";
                    }

                    tab_text = tab_text + "</table>";
                    tab_text = tab_text.replace(/<A[^>]*>|<\/A>/g, "");//remove if u want links in your table
                    tab_text = tab_text.replace(/<img[^>]*>/gi, ""); // remove if u want images in your table
                    tab_text = tab_text.replace(/<input[^>]*>|<\/input>/gi, ""); // reomves input params

                    var a = document.createElement('a');
                    var data_type = 'data:application/vnd.ms-excel';
                    a.href = data_type + ', ' + encodeURIComponent(tab_text);
                    a.download = 'data_report_spj_royalty.xls';
                    a.click();

                    // var msie = window.navigator.userAgent.indexOf("MSIE ");

                    // // If Internet Explorer
                    // if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
                    //     txtArea1.document.open("txt/html", "replace");
                    //     txtArea1.document.write(tab_text);
                    //     txtArea1.document.close();
                    //     txtArea1.focus();

                    //     sa = txtArea1.document.execCommand("SaveAs", true, "Say Thanks to Sumit.xls");
                    // } else {
                    //     // other browser not tested on IE 11
                    //     sa = window.open('data:application/vnd.ms-excel,' + encodeURIComponent(tab_text));
                    // }

                    // return sa;
                }

                $('#form1').on('submit', function() {
                    var tgl1 = $('#TGL_FROM').val();
                    var tgl2 = $('#TGL_TO').val();

                    if (tgl1 == "" && tgl2 == "") {
                        return true;
                    } else {
                        var arr_tgl1 = tgl1.split("-");
                        var arr_tgl2 = tgl2.split("-");
                        var f_tgl1 = arr_tgl1[2]+'-'+arr_tgl1[1]+'-'+arr_tgl1[0];
                        var f_tgl2 = arr_tgl2[2]+'-'+arr_tgl2[1]+'-'+arr_tgl2[0];
                        const date1 = new Date(f_tgl1);
                        const date2 = new Date(f_tgl2);
                        const diffTime = Math.abs(date2 - date1);
                        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)); 
                        
                        // do validation here
                        if(diffDays > 31) {
                            $('#warning_datediff').html(" *Filter tanggal hanya bisa 31 Hari");
                            return false;
                        }else{
                            return true;
                        }
                    } 
                });

                function changeDataPov(e) {
                    var pov = e.target.value;
                    var classPovProd = document.getElementsByClassName('c_produsen');
                    var classPovBrand = document.getElementsByClassName('c_pemilik_brand');
                    var classSession = document.getElementsByClassName('session_role_tr');
                    
                    if (pov == 'pov_pemilik_brand') {
                        classPovProd[0].style.display = '';
                        classPovBrand[0].style.display = 'none';
                        $("#session_role").html("Company Pemilik Brand");
                        classSession[0].style.display = '';
                    }else if (pov == 'pov_produsen') {
                        classPovProd[0].style.display = 'none';
                        classPovBrand[0].style.display = '';
                        $("#session_role").html("Company Produsen");
                        classSession[0].style.display = '';
                    }else{
                        classPovProd[0].style.display = 'none';
                        classPovBrand[0].style.display = 'none';
                        classSession[0].style.display = 'none';
                    }
                }

                function number_format(number, decimals, dec_point, thousands_sep) {
                    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
                    let n = !isFinite(+number) ? 0 : +number,
                        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
                        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
                        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
                        s = '',
                        toFixedFix = function(n, prec) {
                            let k = Math.pow(10, prec);
                            return '' + (Math.round(n * k) / k).toFixed(prec);
                        };

                    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
                    if (s[0].length > 3) {
                        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
                    }
                    if ((s[1] || '').length < prec) {
                        s[1] = s[1] || '';
                        s[1] += new Array(prec - s[1].length + 1).join('0');
                    }
                    return s.join(dec);
                }


                $(document).ready(function() {

                    function clearModalInputs() {
                        $('#modalDialog input').val('');
                    }

                    btn.on('click', function() {
                        $("#itemDetail").html('');
                        let rowData = $(this).data('detail');
                        console.log(rowData);
                        let bastb = ``;
                        let bodyRow = ``;
                        var totalNTGEW = 0;
                        var totalTOTBIL = 0;
                        var totalNETWR = 0;
                        var totalPPN = 0;
                        var totalTOTRYLT = 0;
                        for (const i in rowData) {
                            totalNTGEW += parseFloat(rowData[i].NTGEW);
                            totalTOTBIL += parseFloat(rowData[i].TOTBIL) * 100;
                            totalNETWR += parseFloat(rowData[i].NETWR) * 100;
                            totalPPN += parseFloat(rowData[i].PPN) * 100;
                            totalTOTRYLT += parseFloat(rowData[i].TOTRYLT) * 100;
                            bodyRow += `
                            <tr>
                                <td align="center">`+(parseInt(i)+1)+`</td>
                                <td align="center">`+rowData[i].NO_BILLING+`</td>
                                <td align="center">`+rowData[i].NO_SO+`</td>
                                <td align="center">`+rowData[i].NO_SHIPMENT+`</td>
                                <td align="center">`+rowData[i].BILLING_DATE+`</td>
                                <td align="center">`+rowData[i].MATERIAL+`</td>
                                <td align="center">`+number_format(parseFloat(rowData[i].NTGEW), 2, '.', ',')+`</td>
                                <td align="center">`+rowData[i].GEWEI+`</td>
                                <td align="center">`+number_format(parseFloat(rowData[i].TOTBIL) * 100, 2, '.', ',')+`</td>
                                <td align="center">`+number_format(parseFloat(rowData[i].NETWR) * 100, 2, '.', ',')+`</td>
                                <td align="center">`+number_format(parseFloat(rowData[i].PPN) * 100, 2, '.', ',')+`</td>
                                <td align="center">`+number_format(parseFloat(rowData[i].TOTRYLT) * 100, 2, '.', ',')+`</td>
                            </tr>
                            `;
                            bastb = `<h2>Detail Item BARR - `+ rowData[i].BASTB +`</h2>`;
                            
                        }
                        bodyRow += `
                            <tr class="total_detail">
                                <td align="center" colspan="6"><b>Total</b></td>
                                <td align="center"><b>`+number_format(totalNTGEW, 2, '.', ',')+`</b></td>
                                <td align="center"></td>
                                <td align="center"><b>`+number_format(totalTOTBIL, 2, '.', ',')+`</b></td>
                                <td align="center"><b>`+number_format(totalNETWR, 2, '.', ',')+`</b></td>
                                <td align="center"><b>`+number_format(totalPPN, 2, '.', ',')+`</b></td>
                                <td align="center"><b>`+number_format(totalTOTRYLT, 2, '.', ',')+`</b></td>
                            </tr>
                        `;

                        $("#itemDetail").html(bodyRow);
                        $("#header_item").html(bastb);
                        modal.show();
                    });

                    span.on('click', function() {
                        modal.fadeOut();
                    });
                });

                $('body').bind('click', function(e) {
                    if ($(e.target).hasClass("modal")) {
                        modal.fadeOut();
                        $("#modalDownload").fadeOut();
                        $("#modalNotif").fadeOut();
                        $("#modalUpload").fadeOut();
                    }
                });
            </script>
    <?
    }
    ?>
    
