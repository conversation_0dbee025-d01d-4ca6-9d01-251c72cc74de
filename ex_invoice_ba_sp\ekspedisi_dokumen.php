<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$halaman_id = 172;
$user_id = $_SESSION['user_id'];
$user_org = $_SESSION['user_org'];

if ($user_org == '1000') {
  $display_upload = "inline";
  $action = 'ekspedisi_dokumen_bendahara_smbr';
} else {
  $display_upload = "none";
  $action = 'ekspedisi_dokumen_bendahara';
}

$orgIn = $user_org;

$page = "ekspedisi_dokumen.php";
$vendor = $fungsi->ex_find_vendor($conn, $user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_shipment = $_POST['no_shipment'];
$distributor = $_POST['distributor'];
$tipe_transaksi = $_POST['tipe_transaksi'];
$tanggal_mulai = $_POST['tanggal_mulai'];
$tanggal_selesai = $_POST['tanggal_selesai'];
$warna_plat = $_POST['warna_plat'];
$no_invoice = $_POST['no_invoice'];
$no_invoice_expeditur = $_POST['no_invoice_expeditur'];

$currentPage = "ekspedisi_dokumen.php";
$komen = "";
if (isset($_POST['cari'])) {
  if ($no_shipment == "" and $distributor == "" and $vendor == "" and $tipe_transaksi == "" and $tanggal_mulai == "" and $tanggal_selesai == "" and $warna_plat == "" and $no_invoice == "" and $no_invoice_expeditur == "") {
    $sql = "SELECT DISTINCT
        EI.NO_INVOICE, 
        EI.TGL_INVOICE, 
        EI.ORG,
        EI.NO_VENDOR,
        EI.NAMA_VENDOR, 
        EI.KETERANGAN,
        EI.NO_PAJAK_EX,
        EI.INV_DOC_NUMBER,
        EI.INV_DOC_NUMBER_CONV,
        EBI.STATUS_BA_INVOICE,
        EBI.KOMENTAR_REJECT, EBI.DIPAKAI, EBI.NO_BA,
        C.WARNA_PLAT
      FROM
        EX_INVOICE EI 
        LEFT JOIN EX_TRANS_HDR C ON EI.NO_INVOICE = C.NO_INVOICE
        LEFT JOIN EX_BA_INVOICE EBI ON EBI.NO_INVOICE = EI.NO_INVOICE 
        AND EBI.DIPAKAI = 1 
        where  (EBI.STATUS_BA_INVOICE = 110 OR EBI.STATUS_BA_INVOICE = 90) AND EI.ORG in ($orgIn) ORDER BY EI.NO_INVOICE DESC";
  } else {
    $pakeor = 0;
    $sql = "SELECT DISTINCT
        EI.NO_INVOICE, 
        EI.TGL_INVOICE, 
        EI.ORG,
        EI.NO_VENDOR,
        EI.NAMA_VENDOR, 
        EI.KETERANGAN,
        EI.NO_PAJAK_EX,
        EI.INV_DOC_NUMBER,
        EI.INV_DOC_NUMBER_CONV,
        EBI.STATUS_BA_INVOICE,
        EBI.KOMENTAR_REJECT, EBI.DIPAKAI, EBI.NO_BA,
        C.WARNA_PLAT
      FROM
        EX_INVOICE EI 
        LEFT JOIN EX_TRANS_HDR C ON EI.NO_INVOICE = C.NO_INVOICE
        LEFT JOIN EX_BA_INVOICE EBI ON EBI.NO_INVOICE = EI.NO_INVOICE 
        AND EBI.DIPAKAI = 1 
        where  EBI.STATUS_BA_INVOICE = 110 ";
    if ($no_shipment != "") {
      $sql .= " NO_SHP_TRN LIKE '$no_shipment' ";
      $pakeor = 1;
    }
    $pakeor = 1;
    if ($distributor != "") {
      if ($pakeor == 1) {
        $sql .= " AND ( NAMA_SOLD_TO LIKE '$distributor' OR SOLD_TO LIKE '$distributor' ) ";
      } else {
        $sql .= " ( NAMA_SOLD_TO LIKE '$distributor' OR SOLD_TO LIKE '$distributor' ) ";
        $pakeor = 1;
      }
    }
    if ($vendor != "") {
      if ($pakeor == 1) {
        $sql .= " AND ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";
      } else {
        $sql .= " ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";
        $pakeor = 1;
      }
    }
    if ($tipe_transaksi != "") {
      if ($pakeor == 1) {
        $sql .= " AND TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
      } else {
        $sql .= " TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";
        $pakeor = 1;
      }
    }
    if ($warna_plat != "") {
      if ($pakeor == 1) {
        $sql .= " AND WARNA_PLAT LIKE '$warna_plat' ";
      } else {
        $sql .= " WARNA_PLAT LIKE '$warna_plat' ";
        $pakeor = 1;
      }
    }
    if ($no_invoice_expeditur != "") {
      if ($pakeor == 1) {
        $sql .= " AND NO_INV_VENDOR LIKE '$no_invoice_expeditur' ";
      } else {
        $sql .= " NO_INV_VENDOR LIKE '$no_invoice_expeditur' ";
        $pakeor = 1;
      }
    }
    if ($no_invoice != "") {
      if ($pakeor == 1) {
        $sql .= " AND EI.NO_INVOICE LIKE '$no_invoice' ";
      } else {
        $sql .= " EI.NO_INVOICE LIKE '$no_invoice' ";
        $pakeor = 1;
      }
    }
    $sql .= " AND EI.DELETE_MARK = '0' AND EI.ORG in ($orgIn) ORDER BY EI.NO_INVOICE DESC";
  }
  //  echo 'query '.$sql;
  $query = oci_parse($conn, $sql);
  oci_execute($query);

  while ($row = oci_fetch_array($query)) {
    $com[] = $row[ORG];
    $no_invoice_v[] = $row[NO_INVOICE]; 
    $tgl_invoice_v[] = $row[TGL_INVOICE]; 
    $tahun_invoice_v[] = date("Y", strtotime($row[TGL_INVOICE])); 
    $no_vendor_v[] = $row[NO_VENDOR]; 
    $nama_vendor_v[] = $row[NAMA_VENDOR]; 
    $tgl_invoice_v[] = $row[TGL_INVOICE]; 
    $status_ba_v[] = $row[STATUS_BA_INVOICE]; 
    $id_v[] = $row[NO_BA];  
    $status_id = $row[STATUS_BA_INVOICE]; 
    $no_dokumen_inv_v[] = $row[INV_DOC_NUMBER_CONV]; 
    $no_mir7_v[] = $row[INV_DOC_NUMBER];  
    if ($status_id == 110) {
      $status_name_v[] = 'APPROVED  BY SM VERIFIKASI';
    } else {
      $status_name_v[] = "";
    }
    
    $warna_plat_v[] = $row[WARNA_PLAT]; 
  }
  $total = count($no_invoice_v);
  if ($total < 1) $komen = "Tidak Ada Data Yang Ditemukan";
}
?>

<script language=javascript>

var message = "You dont have permission to right click";

function clickIE() {
  if (document.all) {
    (message);
    return false;
  }
}

function clickNS(e) {
  if (document.layers || (document.getElementById && !document.all)) {
    if (e.which == 2 || e.which == 3) {
      (message);
      return false;
    }
  }
}

if (document.layers) {
  document.captureEvents(Event.MOUSEDOWN);
  document.onmousedown = clickNS;
} else {
  document.onmouseup = clickNS;
  document.oncontextmenu = clickIE;
}

document.oncontextmenu = new Function("return false")
</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
  <title>Aplikasi SGG Online: Input Cost Claim :)</title>
  <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
  <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
  <!-- import the calendar script -->
  <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
  <!-- import the language module -->
  <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
  <script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
  <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
  <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
  <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
  <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

  <script type="text/javascript">
    checked = false;
    function checkedAll(frm1) {
      var aa = document.getElementById(frm1);
      if (checked == false) {
        checked = true
        markAllRows(frm1);
      } else {
        checked = false
        unMarkAllRows(frm1)
      }
    }

    function markAllRows(container_id) {
      var rows = document.getElementById(container_id).getElementsByTagName('tr');
      var checkbox;

      for (var i = 0; i < rows.length; i++) {
        checkbox = rows[i].getElementsByTagName('input')[0];

        if (checkbox && checkbox.type == 'checkbox') {
          if (checkbox.checked != true) {
            checkbox.checked = true;
            rows[i].className += ' selected';
          }
        }
      }

      return true;
    }

    function unMarkAllRows(container_id) {
      var rows = document.getElementById(container_id).getElementsByTagName('tr');
      var checkbox;

      for (var i = 0; i < rows.length; i++) {
        checkbox = rows[i].getElementsByTagName('input')[0];

        if (checkbox && checkbox.type == 'checkbox') {
          if (checkbox.checked != false) {
            checkbox.checked = false;
            rows[i].className = rows[i].className.replace(' selected', '');
          }
        }
      }

      return true;
    }
  </script> 

  <style type="text/css">
    body {
      background: #fff;
    }
    table {
      border: 0;
      border-collapse: collapse;
    }
    td {
      padding: 4px;
    }
    tr.odd1 {
      background: #F9F9F9;
    }
    tr.odd0 {
      background: #FFFFFF;
    }
    tr.highlight {
      background: #BDA9A2;
    }
    tr.selected {
      background: orange;
      color: #fff;
    }
  </style>

  <script type="text/javascript">
    function addLoadEvent(func) {
      var oldonload = window.onload;
      if (typeof window.onload != 'function') {
        window.onload = func;
      } else {
        window.onload = function() {
          oldonload();
          func();
        }
      }
    }

    function addClass(element, value) {
      if (!element.className) {
        element.className = value;
      } else {
        newClassName = element.className;
        newClassName += " ";
        newClassName += value;
        element.className = newClassName;
      }
    }

    function stripeTables() {
      var tables = document.getElementsByTagName("table");
      for (var m = 0; m < tables.length; m++) {
        if (tables[m].className == "pickme") {
          var tbodies = tables[m].getElementsByTagName("tbody");
          for (var i = 0; i < tbodies.length; i++) {
            var odd = true;
            var rows = tbodies[i].getElementsByTagName("tr");
            for (var j = 0; j < rows.length; j++) {
              if (odd == false) {
                odd = true;
                addClass(rows[j], "odd1");
              } else {
                addClass(rows[j], "odd0");
                odd = false;
              }
            }
          }
        }
      }
    }

    function highlightRows() {
      if (!document.getElementsByTagName) return false;
      var tables = document.getElementsByTagName("table");
      for (var m = 0; m < tables.length; m++) {
        if (tables[m].className == "pickme") {
          var tbodies = tables[m].getElementsByTagName("tbody");
          for (var j = 0; j < tbodies.length; j++) {
            var rows = tbodies[j].getElementsByTagName("tr");
            for (var i = 0; i < rows.length; i++) {
              rows[i].oldClassName = rows[i].className
              rows[i].onmouseover = function() {
                if (this.className.indexOf("selected") == -1)
                  addClass(this, " highlight");
              }
              rows[i].onmouseout = function() {
                if (this.className.indexOf("selected") == -1)
                  this.className = this.oldClassName
              }
            }
          }
        }
      }
    }

    function selectRowCheckbox(row) {
      var checkbox = row.getElementsByTagName("input")[0];
      if (checkbox.checked == true) {
        checkbox.checked = false;
      } else if (checkbox.checked == false) {
        checkbox.checked = true;
      }
    }

    function lockRow() {
      var tables = document.getElementsByTagName("table");
      for (var m = 0; m < tables.length; m++) {
        if (tables[m].className == "pickme") {
          var tbodies = tables[m].getElementsByTagName("tbody");
          for (var j = 0; j < tbodies.length; j++) {
            var rows = tbodies[j].getElementsByTagName("tr");
            for (var i = 0; i < rows.length; i++) {
              rows[i].oldClassName = rows[i].className;
              rows[i].onclick = function() {
                if (this.className.indexOf("selected") != -1) {
                  this.className = this.oldClassName;
                } else {
                  addClass(this, " selected");
                }
                selectRowCheckbox(this);
              }
            }
          }
        }
      }
    }

    addLoadEvent(stripeTables);
    addLoadEvent(highlightRows);
    addLoadEvent(lockRow);

    function lockRowUsingCheckbox() {
      var tables = document.getElementsByTagName("table");
      for (var m = 0; m < tables.length; m++) {
        if (tables[m].className == "pickme") {
          var tbodies = tables[m].getElementsByTagName("tbody");
          for (var j = 0; j < tbodies.length; j++) {
            var checkboxes = tbodies[j].getElementsByTagName("input");
            for (var i = 0; i < checkboxes.length; i++) {
              checkboxes[i].onclick = function(evt) {
                if (this.parentNode.parentNode.className.indexOf("selected") != -1) {
                  this.parentNode.parentNode.className = this.parentNode.parentNode.oldClassName;
                } else {
                  addClass(this.parentNode.parentNode, " selected");
                }
                if (window.event && !window.event.cancelBubble) {
                  window.event.cancelBubble = "true";
                } else {
                  evt.stopPropagation();
                }
              }
            }
          }
        }
      }
    }
    addLoadEvent(lockRowUsingCheckbox);
  </script>
</head>

<body>
  <script type="text/javascript" language="JavaScript">
    //ini ni yang buat div tapi kita hidden... ocre....
    document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
  </script>
  <div id="halaman_tampil" style="display:inline">

    <div align="center">
      <table width="600" align="center" class="adminheading" border="0">
        <tr>
          <th class="kb2">Ekspedisi Dokumen </th>
        </tr>
      </table>
    </div>
    <?
    if ($total < 1) {
    ?>

    <div align="center">
      <table width="600" align="center" class="adminlist">
        <tr>
          <th align="left" colspan="4"> &nbsp;Form Search </th>
        </tr>
      </table>
    </div>

    <form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
      <table width="600" align="center" class="adminform">
        <tr width="174">
          <td class="puso">&nbsp;</td>
          <td class="puso">&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr width="174">
          <td class="puso">No Invoice</td>
          <td class="puso">:</td>
          <td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice?>"/></td>
        </tr>
        <tr width="174" style="display:none">
          <td class="puso">No Invoice Expeditur </td>
          <td class="puso">:</td>
          <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_expeditur?>"/></td>
        </tr>
        <tr width="174" style="display:none">
          <td class="puso">No SPJ </td>
          <td class="puso">:</td>
          <td><input type="text" id="no_shipment" name="no_shipment" value="<?=$no_shipment?>"/></td>
        </tr>
        <tr width="174" style="display:none">
          <td  class="puso">Distributor</td>
          <td  class="puso">:</td>
          <td ><input type="text" id="distributor" name="distributor"  value="<?=$distributor?>" /></td>
        </tr>
        <tr width="174" style="display:none">
          <td  class="puso">Periode Shipment </td>
          <td  class="puso">:</td>
          <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> value="<?=$tanggal_mulai?>" />
            <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." />
            &nbsp;&nbsp;&nbsp;
            s/d &nbsp;&nbsp;&nbsp;
            <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> value="<?=$tanggal_selesai?>" />
            <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." /></td>
        </tr>
        <tr width="174" style="display:none">
          <td  class="puso">Warna Plat </td>
          <td  class="puso">:</td>
          <td ><select name="warna_plat" id="warna_plat">
              <option value="">---Pilih---</option>
              <? $fungsi->ex_warna_plat($warna_plat);?>
            </select></td>
        </tr>
        <tr width="174">
          <td class="puso">&nbsp;</td>
          <td class="puso">&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <td class="ThemeOfficeMenu">&nbsp;</td>
          <td class="ThemeOfficeMenu">&nbsp;</td>
          <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" /> </td>
        </tr>
        <tr>
          <td class="ThemeOfficeMenu">&nbsp;</td>
          <td class="ThemeOfficeMenu">&nbsp;</td>
        </tr>
      </table>
    </form>
    <? } ?>
    <br />
    <br />
    <?
    if ($total > 0) {
    ?>
    <form id="data_claim" name="data_claim" method="post" action="komentar.php" >

      <div align="center">
        <table width="95%" align="center">
          <tr>
            <th align="right" colspan="4"><span>
             </span></th>
          </tr>
        </table>
      </div> 
      <div align="center">
        <table width="95%" align="center" class="adminlist">
          <tr>
            <th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Ekspedisi Dokumen </span></th>
          </tr>
        </table>
      </div> 
      <div align="center">
        <table id="myScrollTable" width="95%" align="center" class="pickme">
          <thead>
            <tr class="quote">
              <td ><strong><input type="button" class="button" onClick="checkedAll('data_claim');" value="Pilih Semua"></strong></td> 
              <td align="center"><strong >No BA </strong></td>
              <td align="center"><strong >No Invoice </strong></td>
              <td align="center"><strong >Tgl Invoice </strong></td>
              <td align="center"><strong >No MIR7 </strong></td>
              <td align="center"><strong >No FI Doc </strong></td>
              <td align="center"><strong >Org</strong></td>
              <td align="center"><strong >Tahun </strong></td>
              <td align="center"><strong>Vendor</strong></td>
              <td align="center"><strong>Warna Plat</strong></td>
              <td align="center"><strong>No PO</strong></td>
              <td align="center"><strong>Tipe PO</strong></td>
              <td align="center"><strong>Total</strong></td>
              <td align="center"><strong>Status</strong></td>
              <td align="center"><strong>Action</strong></td>
            </tr >
          </thead>
          <tbody>
            <?  for ($i = 0; $i < $total; $i++) {

              $b = $i + 1;
              $rowke = "rowke" . $i;
              $idke = "idke" . $i;
              $appke = $id_v[$i];
              $urutke = "urutke" . $i;
              $orgCom = "orgke" . $i;
              
              if (($i % 2) == 0) {	 
                echo "<tr class='row0' id='$rowke' >";
              } else {	
                echo "<tr class='row1'  id='$rowke' >";
              }	

              ?>  
              <td align="center"><input name="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" class="shipment-checkbox" /><? echo $b; ?></td> 
              <td align="center"><? echo $id_v[$i]; ?></td>
              <td align="center"><? echo $no_invoice_v[$i]; ?></td>
              <td align="center"><? echo $tgl_invoice_v[$i]; ?></td>
              <td align="center"><? echo $no_mir7_v[$i]; ?></td>
              <td align="center"><? echo $no_dokumen_inv_v[$i]; ?></td>
              <td align="center"><? echo $com[$i]; ?></td> 
              <td align="center"><? echo $tahun_invoice_v[$i]; ?></td>
              <td align="center"><? echo $no_vendor_v[$i] . "-" . $nama_vendor_v[$i]; ?></td>
              <td align="center"><? echo $warna_plat_v[$i]; ?></td>
              <td align="center"><? echo "-"; ?></td>
              <td align="center"><? echo "-"; ?></td>
              <td align="center"><? echo "-"; ?></td>
              <td align="center"><? echo $status_name_v[$i]; ?></td>
              <td align="center"><a href="javascript:popUp('ppl_detail.php?no_invoice=<?=$no_invoice_v[$i];?>')" class="button"  style="font-size: 11px;margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 2px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Detail</a> </td>
            </tr>
            <? } ?>
          </tbody>
          <tfoot>
            <tr class="quote">
              <td colspan="15" align="center">
                <input type="button" value="Upload" name="btn-upload" class="button" style="margin-left:30px;display:<?= $display_upload ?>;" onClick="upload('<?= $no_invoice_v[$i] ?>')" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input name="simpan" type="submit" class="button" id="simpan" value="Ekspedisi" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <a href="ekspedisi_dokumen.php" target="isi" class="button">Cancel</a>
                <input name="total" type="hidden" value="<?=$total;?>" />
                <input name="action" type="hidden" value="<?= $action ?>" />
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
      <?
    }?>
    <div align="center">
      <?
      echo $komen;
      ?>
    </div>
  </form>

	<form id="recalcForm" action="ekspedisi_dokumen_upload.php" method="post" target="_blank">
		<input type="hidden" name="ids" id="idsInput">
	</form>

  <p>&nbsp;</p>
  <? if ($total > 11) { ?>
  <script type="text/javascript">
    var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
  </script>
  <? } ?>
  </p>
  </div>
  <? include ('../include/ekor.php'); ?>
  <script language=javascript>
    //We write the table and the div to hide the content out, so older browsers won't see it
    obj = document.getElementById("tunggu_ya");
    obj.style.display = "none";
    obj_tampil = document.getElementById("halaman_tampil");
    obj_tampil.style.display = "inline";

		function upload() {
      var checkedValues = [];
			var tables = document.getElementsByTagName("table");

			for (var m = 0; m < tables.length; m++) {
				if (tables[m].className == "pickme") {
				var checkboxes = tables[m].getElementsByTagName("input");
				for (var i = 0; i < checkboxes.length; i++) {
					if (checkboxes[i].type === "checkbox" && checkboxes[i].checked) {
					checkedValues.push(checkboxes[i].value);
					}
				}
				}
			}

			const idsString = checkedValues.join(',');
			document.getElementById('idsInput').value = idsString;
			const popup = window.open('', 'popupWindow', 'width=800,height=600,scrollbars=yes');
			const form = document.getElementById('recalcForm');
			form.target = 'popupWindow';
			form.submit();
		}
  </script>

</body>
</html>
