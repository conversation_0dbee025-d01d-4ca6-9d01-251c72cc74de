<?
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$titlepage='Maintenance Mapping Approval Target SCM';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];


$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
if (false) {
?>
<SCRIPT LANGUAGE="JavaScript">
alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
</SCRIPT>
<a href="../login.php">Login....</a>
<?

exit();
}

?>

<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <title><?=$titlepage;?></title>
    <!-- import easyui -->
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
    <link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
    <script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
    <script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
    <script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
    <!-- <script type="text/javascript" src="../js/easyui/src/jquery.propertygrid.js"></script> -->
    <script type="text/javascript" src="../js/easyui/datagrid-groupview.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>

<body>

    <div align="center">
        <table id="dg" title="Pengajuan Target SCM" class="easyui-datagrid" style="width:100%;height:350px">
            <!-- idField="itemid" rownumbers="true" pagination="true"> -->
            <thead>
                <tr>
                    <th field="ID" hidden="true" align="center">ID</th>
                    <th field="ck" checkbox="true"></th>
                    <th field="REQUEST_NUMBER" align="center" width="10%">REQUEST NUMBER</th>
                    <th field="ATTACHMENT" align="center" width="20%">ATTACHMENT</th>
                    <th field="NOTE" align="center" width="20%">NOTE</th>
                    <th field="STATUS_F" align="center" width="20%">STATUS</th>
                    <th field="REQUESTED_BY" align="center" width="15%">REQUESTED BY</th>
                    <th field="REQUESTED_AT" align="center" width="15%">REQUESTED AT</th>
                </tr>
            </thead>
        </table>
        <div id="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
                onclick="newAct()">New</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
                onclick="editAct()">Edit</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
                onclick="deleteAct()">Delete</a>
            <a class="easyui-linkbutton" plain="true" iconCls="icon-excel"
                href="template_xls/template_request_target_scm.xls">Download Template</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload"
                onclick="uploadAct()">Upload Excel</a>
        </div>

        <!-- AWAL TAMBAH DATA -->
        <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
            buttons="#dlg-buttons">
            <div class="ftitle">Create data</div>
            <form id="fm" enctype="multipart/form-data" method="post" novalidate>
                <div class="fitem">
                    <label>Lampiran</label>
                    <input class="easyui-filebox" label="Pilih Lampiran :" labelPosition="top" id="attachment"
                    name="attachment" data-options="prompt:'Pilih File Lampiran'" style="width:300px">
                </div>
                <div class="fitem">
                    <label>Note</label>
                    <input name="note" id="note" class="easyui-textbox">
                </div>
            </form>
        </div>
        <div id="dlg-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()"
                style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>
    <!-- AKHIR TAMBAH DATA -->

    <!-- AWAL EDIT DAN DELETE DATA -->
    <div id="dlgEdit" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
        buttons="#dlg-buttons-edit">
        <div class="ftitle" id="titleEdit">Detail data</div>
        <form id="fmEdit" enctype="multipart/form-data" method="post" novalidate>
            <!-- AWAL ID -->
            <div class="fitem" style="visibility:hidden;position:fixed">
                <label>ID</label>
                <input name="id" id="idEdit" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Lampiran</label>
                <input class="easyui-filebox" label="Pilih Lampiran :" labelPosition="top" id="attachment"
                name="attachment" data-options="prompt:'Pilih File Lampiran'" style="width:300px">
            </div>
            <div class="fitem">
                <label>Note</label>
                <input name="note" id="noteEdit" class="easyui-textbox">
            </div>
        </form>
    </div>
    <div id="dlg-buttons-edit">
        <a href="javascript:void(0)" id="modal-submit" class="easyui-linkbutton c6" iconCls="icon-ok"
            onclick="saveEditAct()" style="width:90px" id="savedata">Oke</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
            onclick="javascript:$('#dlgEdit').dialog('close')" style="width:90px">Cancel</a>
    </div>
    </div>
    <!-- AKHIR EDIT DAN DELETE DATA -->
        
    <!-- AWAL TAMBAH DATA DETAIL -->
        <div id="dlgDtl" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
            buttons="#dlg-buttons-dtl">
            <div class="ftitle">Create data detail</div>
            <form id="fm_dtl" method="post" novalidate>
                <div class="fitem" style="visibility:hidden;position:fixed">
                    <label>REQUEST ID</label>
                    <input name="request_id" id="request_id" class="easyui-textbox">
                </div>
                <div class="fitem">
                    <label>Periode</label>
                    <input type="text" class="easyui-textbox" id="periode" name="periode" style="width:200px;" required="true">
                    <i style="color: #d50505;"> *Format periode (bulan-tahun), ex: "01-2024"</i>
                    <!-- <input type="hidden"  id="shipto_name" name="shipto_name"> -->
                </div>

                <div class="fitem">
                    <label>Brand</label>
                    <input type="text" class="easyui-combogrid form_brand" id="brand" name="brand" style="width:200px;" required="true">
                </div>

                <div class="fitem">
                    <label>Plant</label>
                    <input type="text" class="easyui-combogrid form_plant" id="plant" name="plant" style="width:200px;" required="true">
                </div>

                <div class="fitem">
                    <label>Tipe Order</label>
                    <input type="text" class="easyui-combogrid form_tipe_order" id="tipe_order" name="tipe_order" style="width:200px;" required="true">
                </div>

                <div class="fitem">
                    <label>Material</label>
                    <input type="text" class="easyui-combogrid form_material" id="material" name="material" style="width:200px;" required="true">
                    <i style="color: #d50505;" id="warning_material"></i>
                </div>

                <div class="fitem">
                    <label>Distrik</label>
                    <input type="text" class="easyui-textbox form_distrik" id="distrik" name="distrik" style="width:200px;" required="true">
                </div>

                <div class="fitem">
                    <label>Incoterm</label>
                    <input type="text" class="easyui-textbox" id="incoterm" name="incoterm" style="width:200px;" required="true">
                </div>

                <div class="fitem">
                    <label>QTY Bulanan</label>
                    <input type="text" class="easyui-textbox" id="qty_bulanan" name="qty_bulanan" style="width:200px;" required="true">
                </div>

                <div class="fitem" id="hidepr">
                    <label>Prioritas</label>
                    <input type="text" class="easyui-textbox" id="prioritas" name="prioritas" style="width:200px;" required="true">
                </div>
            </form>
        </div>
        <div id="dlg-buttons-dtl">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveDtlAct()"
                style="width:90px" id="savedata">Save</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlgDtl').dialog('close')" style="width:90px">Cancel</a>
        </div>
    </div>
    <!-- AKHIR TAMBAH DATA DETAIL -->

    <!-- AWAL EDIT DAN DELETE DATA DETAIL -->
    <div id="dlgDtlEdit" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px" closed="true"
        buttons="#dlg-buttons-edit-dtl">
        <div class="ftitle" id="titleEdit">Detail data</div>
        <form id="fmDtlEdit" method="post" novalidate>
            <!-- AWAL ID -->
            <div class="fitem" style="visibility:hidden;position:fixed">
                <label>ID</label>
                <input name="id" id="idDtlEdit" class="easyui-textbox">
            </div>
            <div class="fitem" style="visibility:hidden;position:fixed">
                <label>REQUEST ID</label>
                <input name="request_id" id="request_idEdit" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Periode</label>
                <input type="text" class="easyui-textbox" id="periodeEdit" name="periode" style="width:200px;" required="true">
                <i style="color: #d50505;"> *Format periode (bulan-tahun), ex: "01-2024"</i>
                <!-- <input type="hidden"  id="shipto_name" name="shipto_name"> -->
            </div>

            <div class="fitem">
                <label>Brand</label>
                <input type="text" class="easyui-combogrid form_brand" id="brandEdit" name="brand" style="width:200px;" required="true">
            </div>

            <div class="fitem">
                <label>Plant</label>
                <input type="text" class="easyui-combogrid form_plant" id="plantEdit" name="plant" style="width:200px;" required="true">
            </div>

            <div class="fitem">
                <label>Tipe Order</label>
                <input type="text" class="easyui-combogrid form_tipe_order" id="tipe_orderEdit" name="tipe_order" style="width:200px;" required="true">
            </div>

            <div class="fitem">
                <label>Material</label>
                <input type="text" class="easyui-combogrid form_material" id="materialEdit" name="material" style="width:200px;" required="true">
                <i style="color: #d50505;" id="warning_material"></i>
            </div>

            <div class="fitem">
                <label>Distrik</label>
                <input type="text" class="easyui-textbox form_distrik" id="distrikEdit" name="distrik" style="width:200px;" required="true">
            </div>

            <div class="fitem">
                <label>Incoterm</label>
                <input type="text" class="easyui-textbox" id="incotermEdit" name="incoterm" style="width:200px;" required="true">
            </div>

            <div class="fitem">
                <label>QTY Bulanan</label>
                <input type="text" class="easyui-textbox" id="qty_bulananEdit" name="qty_bulanan" style="width:200px;" required="true">
            </div>

            <div class="fitem" id="hidepr">
                <label>Prioritas</label>
                <input type="text" class="easyui-textbox" id="prioritasEdit" name="prioritas" style="width:200px;" required="true">
            </div>
        </form>
    </div>
    <div id="dlg-buttons-edit-dtl">
        <a href="javascript:void(0)" id="modal-submit-dtl" class="easyui-linkbutton c6" iconCls="icon-ok"
            onclick="saveEditAct()" style="width:90px" id="savedata">Oke</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
            onclick="javascript:$('#dlgEdit').dialog('close')" style="width:90px">Cancel</a>
    </div>
    </div>
    <!-- AKHIR EDIT DAN DELETE DATA DETAIL -->

    <!-- AWAL UPLOAD FILE XLS -->
    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true"
        buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div class="fitem">
                <label>Lampiran</label>
                <input class="easyui-filebox" label="Pilih Lampiran :" labelPosition="top" id="attachment"
                name="attachment" data-options="prompt:'Pilih File Lampiran'" style="width:300px">
            </div>
            <div class="fitem">
                <label>Note</label>
                <input name="note" id="note" class="easyui-textbox">
            </div>
            <div class="fitem">
                <label>Data Upload</label>
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload"
                    name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="saveUploadAct()" style="width:90px"
                id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel"
                onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>
    <!-- AKHIR UPLOAD FILE XLS -->

    <script type="text/javascript">
    var id_request;
    $(function() {
        $("#dg").datagrid({
            url: 'request_scm_target_act.php?act=show',
            // singleSelect: true,
            pagination: false,
            pageList: [5, 10, 20, 30, 40, 50, 100, 200, 300],
            pageSize: 10,
            rownumbers: true,
            loadMsg: 'Processing,please wait',
            height: 'auto',
            toolbar: '#toolbar',
            rowStyler:function(index,row){
                return 'font-weight:bold;';
            }

        });

        $('#dg').datagrid({
            view: detailview,
            detailFormatter:function(index,row){
                let htmlStr = '';
                // alert(row.id);
                htmlStr += '<div style="padding:5px;position:relative;"><div style="margin-bottom:3px"></div><table id="dg_detail_'+row.ID+'" class="ddv" title="Detail Pengajuan"></table></div>';
                return htmlStr;
            },
            onExpandRow: function(index,row){
                id_request = row.ID;
                var dgDtl = $(this).datagrid('getRowDetail',index).find('table.ddv');

                var toolbar = [
                {
                    text:'Add',
                    iconCls:'icon-add',
                    handler:function(){
                        newDtlAct(row.ID, row.APPROVED);
                    }
                },{
                    text:'Edit',
                    iconCls:'icon-edit',
                    handler:function(){
                        editDtlAct(row.ID, row.APPROVED);
                    }
                },{
                    text:'Delete',
                    iconCls:'icon-remove',
                    handler:function(){
                        deleteDtlAct(row.ID, row.APPROVED);
                    }
                }];

                dgDtl.datagrid({
                    url:'request_scm_target_act.php?act=show_detail&id_event='+row.ID,
                    fitColumns:true,
                    // singleSelect:true,
                    pagination:true,
                    rownumbers:true,
                    loadMsg:'',
                    height:'auto',
                    columns:[[
                        {field:'ID',title:'ID', hidden:true},
                        {field:'ck', checkbox:true},
                        {field:'PERIODE',title:'PERIODE',width:100},
                        {field:'BRAND',title:'BRAND',width:100},
                        {field:'PRIORITAS',title:'PRIORITAS',width:100},
                        {field:'PLANT',title:'PLANT',width:100},
                        {field:'PLANT_NAME',title:'PLANT_NAME',width:100},
                        {field:'TIPE_ORDER',title:'TIPE_ORDER',width:100},
                        {field:'DISTRIK',title:'DISTRIK',width:100},
                        {field:'CITY_NAME',title:'CITY_NAME',width:100},
                        {field:'MATERIAL',title:'MATERIAL',width:100},
                        {field:'MATERIAL_NAME',title:'MATERIAL_NAME',width:100},
                        {field:'INCOTERM',title:'INCOTERM',width:100},
                        {field:'QTY_BULANAN',title:'QTY_BULANAN',width:100},
                    ]],
                    onLoad:function(){
                        $('#dg').datagrid('fixDetailRowHeight',index);
                    },
                    toolbar: toolbar,
                    
                });

                $('#dg').datagrid('fixDetailRowHeight',index);
                dgDtl.datagrid('disableFilter');
                dgDtl.datagrid('enableFilter');
            }
        });

        $('#dg').datagrid('enableFilter');
    });
    

    $('#dlg').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });
    $('#dlgEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });
    
    $('#dlgDtl').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });
    $('#dlgDtlEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });

    $('#dlgEdit').dialog({
        title: 'My Dialog',
        // width: 277,
        // height: 277,
        closed: true,
        cache: false,
        // href: 'get_content.php',
        modal: true
    });

    $('#brand').combogrid({
        panelWidth: 200,
        url: 'cSourcePlant_v2.php?act=getBrand',
        idField: 'BRAND',
        textField: 'BRAND',
        fitColumns: true,
        mode: 'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index, row) {
            var nama = row.NAME1;
            $('#shipto_name').val(nama);
        },
        columns: [
            [{
                field: 'BRAND',
                title: 'BRAND',
                align: 'BRAND',
                width: 100
            }, ]
        ]
    });

    $('#brandEdit').combogrid({
        panelWidth: 200,
        url: 'cSourcePlant_v2.php?act=getBrand',
        idField: 'BRAND',
        textField: 'BRAND',
        fitColumns: true,
        mode: 'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index, row) {
            var nama = row.NAME1;
            $('#shipto_name').val(nama);
        },
        columns: [
            [{
                field: 'BRAND',
                title: 'BRAND',
                align: 'BRAND',
                width: 100
            }, ]
        ]
    });

    $('#tipe_order').combogrid({
        panelWidth: 200,
        data: [{
                value: '0',
                text: 'Standart'
            },
            {
                value: '1',
                text: 'Project'
            }
        ],
        idField: 'value',
        textField: 'text',
        editable: false,
        onSelect: function(index, row) {
            let tipe_order = row.value;
            let tipe_order_text = (tipe_order === '0') ? 'Standart' : (tipe_order === '1') ? 'Project' : null;
            $('#tipe_order').val(tipe_order_text);
        },
        columns: [
            [{
                field: 'text',
                title: 'TIPE ORDER',
                align: 'TIPE ORDER',
                width: 200
            }, ]
        ]
    });
    
    $('#tipe_orderEdit').combogrid({
        panelWidth: 200,
        data: [{
                value: '0',
                text: 'Standart'
            },
            {
                value: '1',
                text: 'Project'
            }
        ],
        idField: 'value',
        textField: 'text',
        editable: false,
        onSelect: function(index, row) {
            let tipe_order = row.value;
            let tipe_order_text = (tipe_order === '0') ? 'Standart' : (tipe_order === '1') ? 'Project' : null;
            $('#tipe_orderEdit').val(tipe_order_text);
        },
        columns: [
            [{
                field: 'text',
                title: 'TIPE ORDER',
                align: 'TIPE ORDER',
                width: 200
            }, ]
        ]
    });

    $('#plant').combogrid({
        panelWidth: 200,
        url: 'cSourcePlant_v2.php?act=getPlant',
        idField: 'WERKS',
        textField: 'NAME1',
        fitColumns: true,
        mode: 'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index, row) {
            var kode = row.WERKS;
            getMat(kode);
        },
        columns: [
            [{
                    field: 'WERKS',
                    title: 'PLANT',
                    align: 'PLANT',
                    width: 50
                },
                {
                    field: 'NAME1',
                    title: 'NAMA PLANT',
                    align: 'NAMA PLANT',
                    width: 150
                },
            ]
        ]
    });
    
    $('#plantEdit').combogrid({
        panelWidth: 200,
        url: 'cSourcePlant_v2.php?act=getPlant',
        idField: 'WERKS',
        textField: 'NAME1',
        fitColumns: true,
        mode: 'remote',
        loadMsg: 'Searching...',
        // pagination: true,
        onSelect: function(index, row) {
            var kode = row.WERKS;
            getMatEdit(kode);
        },
        columns: [
            [{
                    field: 'WERKS',
                    title: 'PLANT',
                    align: 'PLANT',
                    width: 50
                },
                {
                    field: 'NAME1',
                    title: 'NAMA PLANT',
                    align: 'NAMA PLANT',
                    width: 150
                },
            ]
        ]
    });


    function getMat(param) {
        $('#material').combogrid({
            panelWidth: 400,
            url: 'cSourcePlant_v2.php?act=getMaterial&plant=' + param,
            idField: 'MATNR',
            textField: 'MAKTX',
            fitColumns: true,
            mode: 'remote',
            loadMsg: 'Searching...',
            // pagination: true,
            onSelect: function(index, row) {
                var kode = row.MATNR;
                getKt(kode);
            },
            onBeforeSelect: function(index,row){
                $('#warning_material').html("")
                $('#material').combogrid('setValue', "");
                if(row.RUNCOST != '1'){
                    $('#warning_material').html("Material belum melakukan Run Cost")
                    return false;
                }
            },
            columns: [
                [{
                        field: 'MATNR',
                        title: 'Material',
                        align: 'Material',
                        width: 150
                    },
                    {
                        field: 'MAKTX',
                        title: 'Kode Material',
                        align: 'Kode Material',
                        width: 250
                    },
                ]
            ]
        });
    }
    
    function getMatEdit(param) {
        $('#materialEdit').combogrid({
            panelWidth: 400,
            url: 'cSourcePlant_v2.php?act=getMaterial&plant=' + param,
            idField: 'MATNR',
            textField: 'MAKTX',
            fitColumns: true,
            mode: 'remote',
            loadMsg: 'Searching...',
            // pagination: true,
            onSelect: function(index, row) {
                var kode = row.MATNR;
                getKtEdit(kode);
            },
            onBeforeSelect: function(index,row){
                $('#warning_material').html("")
                $('#materialEdit').combogrid('setValue', "");
                if(row.RUNCOST != '1'){
                    $('#warning_material').html("Material belum melakukan Run Cost")
                    return false;
                }
            },
            columns: [
                [{
                        field: 'MATNR',
                        title: 'Material',
                        align: 'Material',
                        width: 150
                    },
                    {
                        field: 'MAKTX',
                        title: 'Kode Material',
                        align: 'Kode Material',
                        width: 250
                    },
                ]
            ]
        });
    }

    function getKt(param) {
        $('#distrik').combogrid({
            panelWidth: 500,
            url: 'cSourcePlant_v2.php?act=getKota&material=' + param,
            idField: 'KODE_DISTRIK',
            textField: 'NAMA_DISTRIK',
            fitColumns: true,
            mode: 'remote',
            loadMsg: 'Searching...',
            // pagination: true,
            onSelect: function(index, row) {
                var nama = row.NM_KOTA;
                $('#ktName').val(nama);
            },
            columns: [
                [{
                        field: 'KODE_DISTRIK',
                        title: 'KODE DISTRIK',
                        align: 'KODE DISTRIK',
                        width: 100
                    },
                    {
                        field: 'NAMA_DISTRIK',
                        title: 'NAMA DISTRIK',
                        align: 'NAMA DISTRIK',
                        width: 100
                    }
                ]
            ]
        });
    }
    
    function getKtEdit(param) {
        $('#distrikEdit').combogrid({
            panelWidth: 500,
            url: 'cSourcePlant_v2.php?act=getKota&material=' + param,
            idField: 'KODE_DISTRIK',
            textField: 'NAMA_DISTRIK',
            fitColumns: true,
            mode: 'remote',
            loadMsg: 'Searching...',
            // pagination: true,
            onSelect: function(index, row) {
                var nama = row.NM_KOTA;
                $('#ktName').val(nama);
            },
            columns: [
                [{
                        field: 'KODE_DISTRIK',
                        title: 'KODE DISTRIK',
                        align: 'KODE DISTRIK',
                        width: 100
                    },
                    {
                        field: 'NAMA_DISTRIK',
                        title: 'NAMA DISTRIK',
                        align: 'NAMA DISTRIK',
                        width: 100
                    }
                ]
            ]
        });
    }

    var url;

    function newAct() {
        $('#dlg').dialog('open').dialog('setTitle', 'New Data');
        $('#fm').form('clear');
        url = 'request_scm_target_act.php?act=add';
    }
    
    function newDtlAct(request_id, approved) {
        if (approved == '1') {
            $.messager.show({
                title: 'Error',
                msg: "Data tidak dapat di edit, data telah di approve",
                width: 400, // Atur lebar (dalam piksel)
                height: 100, // Atur tinggi (dalam piksel)
                timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                // showType: 'slide', // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                showType:'fade',
                style:{
                    right:'',
                    bottom:''
                }
            });
        }else{
            $('#dlgDtl').dialog('open').dialog('setTitle', 'New Data Detail');
            $('#fm_dtl').form('clear');
            $('#request_id').textbox('enable');
    
            // Set nilai lainnya ke form
            $('#request_id').textbox('setValue', request_id);
    
            url = 'request_scm_target_act.php?act=add_detail&id_event='+request_id;
        }

    }

    function saveAct() {
        $('#fm').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg').dialog('close');
                    $('#dg').datagrid('reload');
                }
            }
        });
    }
    
    function saveDtlAct() {
        $('#fm_dtl').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    // $('#dlgDtl').dialog('close');
                    // $('#dg').datagrid('reload');
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgDtl').dialog('close');
                    $('#dg_detail_'+id_request).datagrid('reload');
                }
            }
        });
    }

    function editAct() {
        var row = $('#dg').datagrid('getSelected'); // Ambil data baris yang dipilih
        console.log(row);
        
        if (row) {
            if (row.APPROVED == '1') {
                $.messager.show({
                    title: 'Error',
                    msg: "Data tidak dapat di edit, data telah di approve",
                    width: 400, // Atur lebar (dalam piksel)
                    height: 100, // Atur tinggi (dalam piksel)
                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                    // showType: 'slide', // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    showType:'fade',
                    style:{
                        right:'',
                        bottom:''
                    }
                });
            }else{
                document.getElementById("titleEdit").textContent = "Edit Data";
                $('#modal-submit').attr('onclick', 'saveEditAct()');
                $('#dlgEdit').dialog('open').dialog('setTitle', 'Edit Data');

                // enable semua input
                $('#noteEdit').textbox('enable');

                // Set nilai lainnya ke form
                $('#noteEdit').textbox('setValue', row.NOTE);
                $('#idEdit').textbox('setValue', row.ID);

                url = 'request_scm_target_act.php?act=edit';
            }
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }
    
    function editDtlAct(request_id, approved) {
        var row = $('#dg_detail_'+request_id).datagrid('getSelected'); // Ambil data baris yang dipilih
        if (row) {
            if (approved == '1') {
                $.messager.show({
                    title: 'Error',
                    msg: "Data tidak dapat di edit, data telah di approve",
                    width: 400, // Atur lebar (dalam piksel)
                    height: 100, // Atur tinggi (dalam piksel)
                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                    // showType: 'slide', // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    showType:'fade',
                    style:{
                        right:'',
                        bottom:''
                    }
                });
            }else{
                document.getElementById("titleEdit").textContent = "Edit Data Detail";
                $('#modal-submit-dtl').attr('onclick', 'saveEditDtlAct('+request_id+')');
                $('#dlgDtlEdit').dialog('open').dialog('setTitle', 'Edit Data Detail');

                // Set nilai lainnya ke form
                $("#periodeEdit").textbox('setValue', row.PERIODE);
                $("#brandEdit").textbox('setValue', row.BRAND);
                $("#plantEdit").textbox('setValue', row.PLANT);
                $("#tipe_orderEdit").combogrid('setValue', row.TIPE_ORDER);
                $("#distrikEdit").textbox('setValue', row.DISTRIK);
                $("#materialEdit").textbox('setValue', row.MATERIAL);
                $("#incotermEdit").textbox('setValue', row.INCOTERM);
                $("#qty_bulananEdit").textbox('setValue', row.QTY_BULANAN);
                $("#prioritasEdit").textbox('setValue', row.PRIORITAS);
                
                $('#request_idEdit').textbox('setValue', id_request);
                $('#idDtlEdit').textbox('setValue', row.ID);

                url = 'request_scm_target_act.php?act=edit_dtl&id_event='+request_id
            }
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function saveEditAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }
    
    function saveEditDtlAct() {
        $('#fmDtlEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    // $('#dlgEdit').dialog('close'); // close the dialog
                    // $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgDtlEdit').dialog('close');
                    $('#dg_detail_'+id_request).datagrid('reload');
                }
            }
        });
    }

    function deleteAct() {
        var row = $('#dg').datagrid('getSelected');
        var rows = $('#dg').datagrid('getSelections');
        
        if (rows) {
            if (rows.length > 1) {
                var approvedCount = 0;
                for (const i in rows) {
                    if (rows[i].APPROVED == '1') {
                        approvedCount++;
                    }
                }
                if (approvedCount > 0) {
                    $.messager.show({
                        title: 'Error',
                        msg: "Data tidak dapat di edit, terdapat data yang telah di approve",
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        // showType: 'slide', // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                        showType:'fade',
                        style:{
                            right:'',
                            bottom:''
                        }
                    });
                }else{
                    $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
                        if (r){
                            $.post('request_scm_target_act.php?act=multipleDel&',{data:rows},function(result){
                            // $.post('request_scm_target_act.php?act=multipleDel&',{data:rows.ID},function(result){
                                if (result.success){
                                    $.messager.show({
                                        title: 'Success',
                                        msg: result.success,
                                        width: 400, // Atur lebar (dalam piksel)
                                        height: 100, // Atur tinggi (dalam piksel)
                                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                    });
                                    $('#dg').datagrid('reload'); // reload the user data
                                } else {
                                    $.messager.show({ // show error message
                                        title: 'Error',
                                        msg: result.errorMsg,
                                        width: 400, // Atur lebar (dalam piksel)
                                        height: 100, // Atur tinggi (dalam piksel)
                                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                        showType: 'show' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                    });
                                }
                            },'json');
                        }
                    });
                }
            } else {
                if (row.APPROVED == '1') {
                    $.messager.show({
                        title: 'Error',
                        msg: "Data tidak dapat di edit, data telah di approve",
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        // showType: 'slide', // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                        showType:'fade',
                        style:{
                            right:'',
                            bottom:''
                        }
                    });
                }else{
                    document.getElementById("titleEdit").textContent = "Delete Master Data";
                    $('#modal-submit').attr('onclick', 'saveDeleteAct()');
                    $('#dlgEdit').dialog('open').dialog('setTitle', 'Delete Data');
        
                    // disable semua input
                    $('#noteEdit').textbox('disable');
        
                    // Set nilai lainnya ke form
                    $('#noteEdit').textbox('setValue', row.NOTE);
                    $('#idEdit').textbox('setValue', row.ID);
        
                    // URL untuk request delete
                    url = 'request_scm_target_act.php?act=delete';
                }
            }
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }
    
    function deleteDtlAct(request_id, approved) {
        var row = $('#dg_detail_'+request_id).datagrid('getSelected');
        var rows = $('#dg_detail_'+request_id).datagrid('getSelections');
        
        if (rows) {
            if (approved == '1') {
                $.messager.show({
                    title: 'Error',
                    msg: "Data tidak dapat di edit, data telah di approve",
                    width: 400, // Atur lebar (dalam piksel)
                    height: 100, // Atur tinggi (dalam piksel)
                    timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                    // showType: 'slide', // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    showType:'fade',
                    style:{
                        right:'',
                        bottom:''
                    }
                });
            }else{
                if (rows.length > 1) {
                    $.messager.confirm('Confirm','are you sure to delete this transaction?',function(r){
                        if (r){
                            $.post('request_scm_target_act.php?act=multipleDelDtl&',{data:rows},function(result){
                            // $.post('request_scm_target_act.php?act=multipleDel&',{data:rows.ID},function(result){
                                if (result.success){
                                    $.messager.show({
                                        title: 'Success',
                                        msg: result.success,
                                        width: 400, // Atur lebar (dalam piksel)
                                        height: 100, // Atur tinggi (dalam piksel)
                                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                    });
                                    $('#dg_detail_'+request_id).datagrid('reload'); // reload the user data
                                } else {
                                    $.messager.show({ // show error message
                                        title: 'Error',
                                        msg: result.errorMsg,
                                        width: 400, // Atur lebar (dalam piksel)
                                        height: 100, // Atur tinggi (dalam piksel)
                                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                                        showType: 'show' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                                    });
                                }
                            },'json');
                        }
                    });
                } else {
                    document.getElementById("titleEdit").textContent = "Delete Master Data";
                    $('#modal-submit-dtl').attr('onclick', 'saveDeleteDtlAct('+request_id+')');
                    $('#dlgDtlEdit').dialog('open').dialog('setTitle', 'Delete Data');
        
                    // disable semua input
                    $('#noteEdit').textbox('disable');
                    $("#periodeEdit").textbox('disable');
                    $("#brandEdit").textbox('disable');
                    $("#plantEdit").textbox('disable');
                    $("#tipe_orderEdit").combogrid('disable');
                    $("#distrikEdit").textbox('disable');
                    $("#materialEdit").textbox('disable');
                    $("#incotermEdit").textbox('disable');
                    $("#qty_bulananEdit").textbox('disable');
                    $("#prioritasEdit").textbox('disable');
                    
                    $('#request_idEdit').textbox('setValue', id_request);
                    $('#idDtlEdit').textbox('setValue', row.ID);
        
                    // Set nilai lainnya ke form
                    $("#periodeEdit").textbox('setValue', row.PERIODE);
                    $("#brandEdit").textbox('setValue', row.BRAND);
                    $("#plantEdit").textbox('setValue', row.PLANT);
                    $("#tipe_orderEdit").combogrid('setValue', row.TIPE_ORDER);
                    $("#distrikEdit").textbox('setValue', row.DISTRIK);
                    $("#materialEdit").textbox('setValue', row.MATERIAL);
                    $("#incotermEdit").textbox('setValue', row.INCOTERM);
                    $("#qty_bulananEdit").textbox('setValue', row.QTY_BULANAN);
                    $("#prioritasEdit").textbox('setValue', row.PRIORITAS);
                    
                    $('#request_idEdit').textbox('setValue', id_request);
                    $('#idDtlEdit').textbox('setValue', row.ID);
        
                    // URL untuk request delete
                    url = 'request_scm_target_act.php?act=delete_dtl';
                }
            }
        } else {
            alert('Pilih baris data terlebih dahulu!');
        }
    }

    function uploadAct() {
        $('#dlg_upload').dialog('open').dialog('setTitle', 'Upload Excel Data');
        $('#uploadForm').form('clear');
    }

    function saveUploadAct() {
        $('#uploadForm').form('submit', {
            url: 'request_scm_target_act.php?act=upload_file',
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.data,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function saveDeleteAct() {
        $('#fmEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgEdit').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
    }
    
    function saveDeleteDtlAct(request_id) {
        $('#fmDtlEdit').form('submit', {
            url: url,
            onSubmit: function() {
                return $(this).form('validate');
            },
            success: function(result) {
                var result = eval('(' + result + ')');
                if (result.errorMsg) {
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgDtlEdit').dialog('close'); // close the dialog
                    $('#dg_detail_'+request_id).datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({
                        title: 'Success',
                        msg: result.info,
                        width: 400, // Atur lebar (dalam piksel)
                        height: 100, // Atur tinggi (dalam piksel)
                        timeout: 2000, // Durasi munculnya popup (dalam milidetik)
                        showType: 'slide' // Efek animasi (bisa: 'fade', 'slide', atau 'show')
                    });
                    $('#dlgDtlEdit').dialog('close'); // close the dialog
                    $('#dg_detail_'+request_id).datagrid('reload'); // reload the user data
                }
            }
        });
    }

    function exportToExcel() {
        // var rows = data.rows;
        var rows = $('#dg').datagrid('getRows');

        // Filter the rows to include only the displayed columns and adjust FLAGING values
        var filteredRows = rows.map(function(row) {
            return {
                APPROVAL_GROUP: row.APPROVAL_GROUP,
                APPROVAL_NAME: row.APPROVAL_NAME,
                USERNAME: row.USERNAME,
                EMAIL: row.EMAIL,
                LEVEL: row.APPROVAL_LEVEL,
                CREATED_AT: row.CREATED_AT,
                CREATED_BY: row.CREATED_BY,
                UPDATED_AT: row.UPDATED_AT,
                UPDATED_BY: row.UPDATED_BY
            };
        });

        // Convert the filtered data to a worksheet
        var worksheet = XLSX.utils.json_to_sheet(filteredRows);
        var workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // Save the workbook as an Excel file
        XLSX.writeFile(workbook, "mapping_approval_target_scm.xls");
    }
    </script>

    <style type="text/css">
    .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .icon-upload {
        background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
        background: transparent url("icon/excel.png") no-repeat scroll center center;
    }


    .btn:not([disabled]) {
        color: white;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }

    thead th {
        text-align: left;
        padding: 7px;
    }

    tbody td {
        border-top: 1px solid #e3e3e3;
        padding: 7px;
    }

    #fm {
        margin: 0;
        padding: 10px;
    }

    .ftitle {
        font-size: 14px;
        font-weight: bold;
        padding: 5px 0;
        margin-bottom: 10px;
        border-bottom: 1px solid #ccc;
    }

    .fitem {
        margin-bottom: 5px;
    }

    .fitem label {
        display: inline-block;
        width: 101px;
        margin-bottom: 2px;
    }

    .fitem input {
        width: 190px;
        margin-bottom: 5px;
    }

    .fitem select {
        width: 195px;
        margin-bottom: 5px;
    }

    #dlg,
    #dlgEdit {
        padding: 10px 0 10px 10px;
    }

    .switch-button {
        position: relative;
        width: 50px;
        height: 25px;
        background-color: #ccc;
        border-radius: 15px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 0 auto;             /* Memastikan switch button terpusat secara horizontal */
    }

    .switch-button[data-checked="checked"] {
        background-color: #4caf50;
    }

    .switch-button-handle {
        position: absolute;
        width: 23px;
        height: 23px;
        background-color: white;
        border-radius: 50%;
        transition: left 0.3s ease;
        top: 1px;                   /* Menjaga posisi vertikal handle di tengah */
        left: 1px;                  /* Posisi handle di kiri ketika tidak aktif */
    }

    .switch-button .switch-button-handle.active {
        left: 26px;                 /* Memindahkan handle ke kanan ketika aktif */
    }

    .datagrid-cell {
        display: flex;
        align-items: center;        /* Vertikal tengah */
        justify-content: center;    /* Horizontal tengah */
        padding: 0;                 /* Menghilangkan padding jika ada */
    }

    .request-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        background: #f9f9f9;
        padding: 12px;
        border-radius: 0px;
        border: 0px solid #ddd;
        }

        .request-item {
        background: white;
        padding: 10px 14px;
        border-radius: 6px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        display: flex;
        flex-direction: column;
        }

        .label {
        font-weight: 600;
        color: #555;
        font-size: 10px;
        }

        .value {
        margin-top: 4px;
        font-size: 11px;
        color: #222;
        }
    </style>
    </div>
    <? 
include ('../include/ekor.php'); 
?>
</body>

</html>
