<?php

session_start();
//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

//$ids = htmlspecialchars($_REQUEST['id']);
$ID = htmlspecialchars($_REQUEST['ID']);
$NO_PP = htmlspecialchars($_REQUEST['NO_PP']);
$NO_SHP_OLD = htmlspecialchars($_REQUEST['NO_SHP_OLD']);
$STATUS_LINE = htmlspecialchars($_REQUEST['STATUS_LINE']);
$KODE_PRODUK = htmlspecialchars($_REQUEST['KODE_PRODUK']);
$NAMA_PRODUK = htmlspecialchars($_REQUEST['NAMA_PRODUK']);
$KODE_TUJUAN = htmlspecialchars($_REQUEST['KODE_TUJUAN']);
$NAMA_TUJUAN = htmlspecialchars($_REQUEST['NAMA_TUJUAN']);
$NM_PROV = htmlspecialchars($_REQUEST['NM_PROV']);
$KD_PROV = htmlspecialchars($_REQUEST['KD_PROV']);
$DELETE_MARK = htmlspecialchars($_REQUEST['DELETE_MARK']);


if(isset ($aksi)){
//    if($aksi == 'show'){
//        displayData($conn,$sort,$order);
//        
//    }
    switch($aksi) { 
        case 'show' :
    {         
        $where = '';
        if($WNOPP==''){
            $where  .= "";
        }else{
           $distr =  $WNOPP;
		$panjang=strlen(strval($WNOPP));
		if($panjang==1) $pp='000000000'.$distr;
		if($panjang==2) $pp='00000000'.$distr;
		if($panjang==3) $pp='0000000'.$distr;
		if($panjang==4) $pp='000000'.$distr;
		if($panjang==5) $pp='00000'.$distr;
		if($panjang==6) $pp='0000'.$distr;
		if($panjang==7) $pp='000'.$distr;
		if($panjang==8) $pp='00'.$distr;
		if($panjang==9) $pp='0'.$distr;
		if($panjang==10)$pp=$distr;
            $where  .= "  NO_PP = '{$pp}'";  
        }
  
        if($WSHPOLD==''){
            $where  .= "";
        }else{
            $distr =  $WSHPOLD;
		$panjang=strlen(strval($WSHPOLD));
		if($panjang==1) $pp='000000000'.$distr;
		if($panjang==2) $pp='00000000'.$distr;
		if($panjang==3) $pp='0000000'.$distr;
		if($panjang==4) $pp='000000'.$distr;
		if($panjang==5) $pp='00000'.$distr;
		if($panjang==6) $pp='0000'.$distr;
		if($panjang==7) $pp='000'.$distr;
		if($panjang==8) $pp='00'.$distr;
		if($panjang==9) $pp='0'.$distr;
		if($panjang==10)$pp=$distr;
            $where  .= " AND NO_SHP_OLD = '{$pp}'"; 
        }
        if($where == ''){
			
            echo json_encode('tidak ada data yang dicari');
			
        }else{
            $sql= "
                SELECT * FROM 
					OR_TRANS_DTL 
				WHERE
					{$where} ORDER BY NO_PP ASC
            ";   
//echo $sql;
			//echo $query;
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            while($row=oci_fetch_array($query)){            
                array_push($result, $row);
            }    
            echo json_encode($result);
            }
     }
     break;
        
        case 'update' :
        {
            $sql= "
            UPDATE OR_TRANS_DTL  set
            NO_SHP_OLD = '$NO_SHP_OLD',
            STATUS_LINE = '$STATUS_LINE',
            KODE_PRODUK = '$KODE_PRODUK',
            NAMA_PRODUK = '$NAMA_PRODUK',
            KODE_TUJUAN = '$KODE_TUJUAN',
            NAMA_TUJUAN = '$NAMA_TUJUAN',
            NM_PROV = '$NM_PROV',
            KD_PROV = '$KD_PROV',
            DELETE_MARK = '$DELETE_MARK'
              					   
            where
            ID   = '$ID'
			AND
			NO_PP = '$NO_PP'
			";
              //  echo $sql;
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);
            
        
            
            if ($result){
                    echo json_encode(array('success'=>true));
            } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
    }
    

    
    
}


?>
