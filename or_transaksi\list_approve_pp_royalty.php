<? 
session_start();
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);


include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$no_pp_v = array();
$total = 0;

$halaman_id=20;
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];

$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?php

exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="list_approve_pp_royalty.php";
$no_pp = isset($_POST['no_pp']) ? $fungsi->sapcode($_POST['no_pp']) : '';
$branch_plant = isset($_POST['branch_plant']) ? $_POST['branch_plant'] : '';
$sold_to = isset($_POST['sold_to']) ? $fungsi->sapcode($_POST['sold_to']) : '';
$ship_to = isset($_POST['ship_to']) ? $_POST['ship_to'] : '';
$produk = isset($_POST['produk']) ? $_POST['produk'] : '';
$jenis_kirim = isset($_POST['jenis_kirim']) ? $_POST['jenis_kirim'] : '';
$propinsi = isset($_POST['propinsi']) ? $_POST['propinsi'] : '';
$tgl_kirim = isset($_POST['tgl_kirim']) ? trim($_POST['tgl_kirim']) : '';
$plant_asal_up = isset($_POST['plant']) ? trim($_POST['plant']) : '';
$nama_plant_up = isset($_POST['nama_plant']) ? trim($_POST['nama_plant']) : '';


$currentPage="list_approve_pp_royalty.php";
$komen="";
if(isset($_POST['cari'])){
	$params = array();
	if($no_pp=="" and $branch_plant=="" and $sold_to == "" and $ship_to == "" and $propinsi=="" and $plant_asal_up=="" and $produk == "" and $tgl_kirim == "" and $jenis_kirim == ""){
		$sql= "SELECT TO_CHAR (TGL_PP, 'DD-MM-YYYY HH:MM:SS') AS CREATE_DATE1,TO_CHAR (TGL_PP, 'DD-MM-YYYY HH:MM:SS') AS LAST_UPDATE_DATE1, OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE DELETE_MARK = '0' AND ORG in ($inorg) AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND no_shp_old is null and FLAG_LELANG is null AND SO_TYPE<>'ZPR' AND STATUS_LINE IN ('OPEN','PROCESS') AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME=:user_name AND zuvk.DEL=0 AND zkp.DEL=0) AND SO_TYPE <> 'ZRE' AND KODE_PRODUK like '121-301-7%' AND (NOTE != 'ics_pp' OR NOTE IS NULL) ORDER BY NO_PP ASC";
	}else {
		$pakeor=0;
		$sql= "SELECT TO_CHAR (TGL_PP, 'DD-MM-YYYY HH:MM:SS') AS CREATE_DATE1,TO_CHAR (TGL_PP, 'DD-MM-YYYY HH:MM:SS') AS LAST_UPDATE_DATE1, OR_TRANS_HDR_V.*, to_char(TGL_PP,'DD-MM-YYYY') as TGL_PP1,to_char(TGL_KIRIM_PP,'DD-MM-YYYY') as TGL_KIRIM_PP1,to_char(TGL_KIRIM_APPROVE,'DD-MM-YYYY') as TGL_KIRIM_APPROVE1, to_char(TGL_TERIMA,'DD-MM-YYYY') as TGL_TERIMA1 FROM OR_TRANS_HDR_V WHERE STATUS_LINE IN ('OPEN','PROCESS') AND SO_TYPE<>'ZPR' AND ORG in ($inorg) AND (BPLANT <> 'OTHER' OR BPLANT is NULL) AND KODE_PRODUK like '121-301-7%' AND ";
		if ($no_pp != "") {
			$sql .= ($pakeor ? " AND " : "") . "NO_PP LIKE :no_pp ";
			$params[':no_pp'] = $no_pp;
			$pakeor = 1;
		}

		if ($branch_plant != "") {
			$sql .= ($pakeor ? " AND " : "") . "BPLANT LIKE :branch_plant ";
			$params[':branch_plant'] = $branch_plant;
			$pakeor = 1;
		}

		if ($sold_to != "") {
			$sql .= ($pakeor ? " AND " : "") . "SOLD_TO LIKE :sold_to ";
			$params[':sold_to'] = $sold_to;
			$pakeor = 1;
		}

		if ($ship_to != "") {
			$sql .= ($pakeor ? " AND " : "") . "SHIP_TO LIKE :ship_to ";
			$params[':ship_to'] = $ship_to;
			$pakeor = 1;
		}

		if ($produk != "") {
			$sql .= ($pakeor ? " AND " : "") . "KODE_PRODUK LIKE :produk ";
			$params[':produk'] = "%$produk%";
			$pakeor = 1;
		}

		if ($propinsi != "") {
			$sql .= ($pakeor ? " AND " : "") . "KD_PROV LIKE :propinsi ";
			$params[':propinsi'] = "%$propinsi%";
			$pakeor = 1;
		}

		if ($jenis_kirim != "") {
			$sql .= ($pakeor ? " AND " : "") . "INCOTERM LIKE :jenis_kirim ";
			$params[':jenis_kirim'] = $jenis_kirim;
			$pakeor = 1;
		}

		if ($tgl_kirim != "") {
			list($day, $month, $year) = explode("-", $tgl_kirim);
			$tglm = $year . $month . $day;
			$sql .= ($pakeor ? " AND " : "") . "to_char(TGL_KIRIM_PP,'YYYYMMDD') = :tglm ";
			$params[':tglm'] = $tglm;
			$pakeor = 1;
		}

		if ($plant_asal_up != "") {
			$sql .= ($pakeor ? " AND " : "") . "PLANT_ASAL = :plant_asal_up ";
			$params[':plant_asal_up'] = $plant_asal_up;
			$pakeor = 1;
		}

		$sql.=" AND no_shp_old is null and FLAG_LELANG is null AND SO_TYPE <> 'ZRE' AND DELETE_MARK = '0' AND KODE_TUJUAN IN (SELECT DISTRICT
FROM ZMD_KOORDINATOR_PENJUALAN zkp
LEFT JOIN ZMD_USER_VS_KOORDINATOR zuvk
  ON zuvk.KOORDINATOR_KODE = zkp.KOORDINATOR_AREA WHERE zuvk.USERNAME=:user_name AND zuvk.DEL=0 AND zkp.DEL=0) ORDER BY NO_PP ASC";
	}

echo $sql;

	$query= oci_parse($conn, $sql);
		foreach ($params as $key => $value) {
		oci_bind_by_name($query, $key, $params[$key]);
	}

	oci_bind_by_name($query, ':user_name', $user_name);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_pp_v[]=$row[NO_PP];
		$tgl_kirim_v[]=$row[TGL_KIRIM_PP];
		$tgl_pp_v[]=$row[TGL_PP];
		$kd_produk_v[]=$row[KODE_PRODUK];
		$produk_v[]=$row[NAMA_PRODUK];
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$kd_ship_to_v[]=$row[SHIP_TO];
		$ship_to_v[]=$row[NAMA_SHIP_TO];
		$alamat_v[]=$row[ALAMAT_SHIP_TO];
		$kddistrik_v[]=$row[KODE_TUJUAN];
		$nmdistrik_v[]=$row[NAMA_TUJUAN];
		$qty_v[]=$row[QTY_PP];
                $route[]=$row[ROUTE];
                $incoterm[]=$row[INCOTERM];
		$id_v[]=$row[ID];
		$item_v[]=$row[ITEM_NUMBER];  
		$status_v[]=$row[STATUS_LINE];  
                $plant_v[]=$row[PLANT_ASAL];
                $createdate_v[]=$row[CREATE_DATE1]; 
                $createby_v[]=$row[CREATED_BY]; 
                $updatedate_v[]=$row[LAST_UPDATE_DATE1]; 
                $updateby_v[]=$row[LAST_UPDATED_BY]; 
                $royalty_v[]=$row[IS_ROYALTY];
				$msa_v[]=$row[IS_MSA];
				/////////////////////
							$mysqlmsadistrik = "
							    SELECT
									kp.KOORDINATOR_AREA, kp.DISTRICT, othv.KODE_TUJUAN, othv.NO_PP, zmp.COM_OPCO 
								FROM
									ZMD_KOORDINATOR_PENJUALAN kp
								LEFT JOIN
									or_trans_hdr_v othv
									ON kp.DISTRICT = othv.KODE_TUJUAN
								LEFT JOIN ZMD_MAPPING_PLANT zmp
									ON othv.PLANT_ASAL = zmp.PLANT_MD 
								WHERE
									kp.DEL = '0'
									AND othv.DELETE_MARK = '0'
								AND othv.KODE_TUJUAN='$row[KODE_TUJUAN]'
								AND othv.NO_PP='$row[NO_PP]'";
                            $mysql_msadistrik=oci_parse($conn,$mysqlmsadistrik);
                            oci_execute($mysql_msadistrik);
                            $row_msadistrik=oci_fetch_assoc($mysql_msadistrik);
							$plant_flag=substr($row[PLANT_ASAL], 0, 2);
								$opco_koordinator[]=$row_msadistrik[KOORDINATOR_AREA];
							if($plant_flag==='79'){
								$org_v[]=$row_msadistrik[COM_OPCO];
							}else{
								// $opco_koordinator[]=7000;
								$org_v[]=7000;
							}
				////////////////////
	}
	$total=count($no_pp_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

}

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Permintaan Pembelian :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Approval Permintaan Pembelian 1</th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form SearchApproval Permintaan Pembelian </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Permintaan Pembelian </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_pp" name="no_pp" value="<?=$no_pp?>"/><input name="org" type="hidden" id="org" value="<?=$user_org?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Branch Plant </td>
      <td  class="puso">:</td>
      <td ><select name="branch_plant" id="Branch Plant" >
        <option value="">---Pilih---</option>
        <? $fungsi->or_jns_plant($branch_plant); ?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Sold To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="sold_to" name="sold_to"  value="<?=$sold_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Ship To </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="ship_to" name="ship_to"  value="<?=$ship_to?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Propinsi </td>
      <td  class="puso">:</td>
      <td >
          <select name="propinsi" id="propinsi">
            <option value=''>--All Propinsi--</option>
            <?
            $sqlpropinsi= "
                        SELECT KD_PROV,NM_PROV FROM TB_USER_VS_PROV WHERE USER_ID='$user_id' AND DELETE_MARK=0
                        order by KD_PROV asc
                        ";

            $query2= oci_parse($conn, $sqlpropinsi);
            oci_execute($query2);
            while($rowProv=oci_fetch_array($query2)){ 
                $KDprov=$rowProv['KD_PROV'];
                $NAMEprov=$rowProv['NM_PROV'];
                if($propinsi==$KDprov){ $selectedVi=" selected ";}else{$selectedVi="";}
            ?>
                 <option value='<?=$KDprov;?>' <?=$selectedVi;?> ><?=$KDprov;?>&nbsp;&nbsp;<?=$NAMEprov;?></option>
            <?
            }
            ?>
        </select>
      
      </td>
    </tr>  
    <tr>
            <td  class="puso">Jenis Semen</td>
            <td  class="puso">:</td>
            <td>		
                    <SELECT NAME="produk">
							<option value="" <? if($produk =='') echo ' selected' ?>>&nbsp;All&nbsp;</option>
                            <option value="121-301" <? if($produk =='121-301') echo ' selected' ?>>&nbsp;Zak&nbsp;</option>
                            <option value="121-302" <? if($produk =='121-302') echo ' selected' ?>>&nbsp;Curah&nbsp;</option>

                    </SELECT>
            </td>
    </tr>  
    <tr>
        <td ><strong>Plant</strong></td>
        <td width="10"><strong>:</strong></td>
        <td width="153" colspan="2">
            <div id="plantdiv">
                <input name="plant" type="text" class="inputlabel" id="plant" value="<?=$plant_asal_up?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>&nbsp;&nbsp;&nbsp;&nbsp;
                <input name="nama_plant" type="text" id="nama_plant" value="<?=$nama_plant_up?>" readonly="true"size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;
                <input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
                <input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
              </div>
        </td>
    </tr>   
    <tr>
      <td  class="puso">Jenis Pengiriman </td>
      <td  class="puso">:</td>
      <td ><select name="jenis_kirim" id="Jenis Pengiriman" >
          <option value="">---Pilih---</option>
          <? $fungsi->or_jenis_kirim($jenis_kirim); ?>
      </select></td>
    </tr>
     <tr>
      <td  class="puso">Tanggal Kirim </td>
      <td  class="puso">:</td>
      <td ><input name="tgl_kirim" type="text" id="tgl_kirim" size=12 value="<?=$tgl_kirim;?>" onClick="return showCalendar('tgl_kirim');" /></td>
    </tr>   
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="95%" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Approval Permintaan Pembelian </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
                <td align="center"><strong >Kode Plant</strong></td>
				<td align="center"><strong >Kode Pemilik Plant</strong></td>
		<td align="center"><strong >Tanggal  PP</strong></td>
		<td align="center"><strong >Kode </strong></td>
		<td align="center"><strong >Distributor</strong></td>
		 <td align="center"><strong>Kode   </strong></td>
		 <td align="center"><strong>Distrik   </strong></td>
		<td align="center"><strong >Kode Opco Koordinator</strong></td>			 
		 <td align="center"><strong>Kode   </strong></td>
		 <td align="center"><strong>Ship To   </strong></td>
		 <td align="center"><strong>Produk   </strong></td>
		 <td align="center"><strong>Produk</strong></td>
                 <td align="center"><strong>Qty</strong></td>                    
		 <td align="center"><strong>Incoterm</strong></td>
                 <td align="center"><strong>Route</strong></td>
		 <td align="center"><strong>Tgl Kirim</strong></td>
		 <td align="center"><strong>Status</strong></td>
         <td align="center"><strong>Royalty</strong></td>
		 <td align="center"><strong>MSA</strong></td>
                 <td align="center"><strong>Create Date</strong></td>
                 <td align="center"><strong>Create By</strong></td>
                 <td align="center"><strong>Update Date</strong></td>
                 <td align="center"><strong>Update By</strong></td>
		 <td align="center"><strong>Approval</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	

		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp_v[$i]; ?></td>
                <td align="center"><? echo $plant_v[$i]; ?></td>
				<td align="center"><? echo $org_v[$i]; ?></td>				
		<td align="center"><? echo $tgl_pp_v[$i]; ?></td>
		<td align="left"><? echo $sold_to_v[$i]; ?></td>
		<td align="left"><? echo $nama_sold_to_v[$i]; ?></td>
		<td align="left"><? echo $kddistrik_v[$i]; ?></td>	
		<td align="left"><? echo $nmdistrik_v[$i]; ?></td>
		<td align="center"><? echo $opco_koordinator[$i]; ?></td>			
		<td align="left"><? echo $kd_ship_to_v[$i]; ?></td>
		<td align="left"><? echo ''.$ship_to_v[$i].', '.$alamat_v[$i].''; ?></td>
		<td align="left"><? echo $kd_produk_v[$i]; ?></td>
		<td align="left"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? 
				$tbl="OR_TRANS_APP";
				$field=array('NO_PP','ITEM_NUMBER');
				$findby=array("$no_pp_v[$i]","$item_v[$i]");
				$qtyrel=$fungsi->findSumByMany($conn,$tbl,$field,$findby,'QTY_APPROVE'); 
				$qtysisa=$qty_v[$i] - $qtyrel;
				echo number_format($qtysisa,0,",","."); ?></td>
                <td align="center"><? echo $incoterm[$i]; ?></td>
                <td align="center"><? echo $route[$i]; ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $status_v[$i]; ?></td>
        <td align="center"><? echo ($royalty_v[$i]=='X' ? 'YES' : 'NO'); ?></td>
		<td align="center"><? echo ($msa_v[$i]=='X' ? 'YES' : 'NO'); ?></td>
                <td align="center"><? echo $createdate_v[$i]; ?></td>
                <td align="center"><? echo $createby_v[$i]; ?></td>
                <td align="center"><? echo $updatedate_v[$i]; ?></td>
                <td align="center"><? echo $updateby_v[$i]; ?></td>
		<td align="center"><a href="approve_pp_royalty.php?id_detail=<?=$id_v[$i]?>">APPROVE</a></td>
		</tr>
	  <? } ?>
	  <tr class="quote">
		<td colspan="15" align="center">
		<a href="list_approve_pp_royalty.php" target="isi" class="button">Back</a>		 </td>
	    </tr>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
