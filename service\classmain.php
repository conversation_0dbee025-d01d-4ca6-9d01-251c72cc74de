<?
include('../include/sapclasses/sap.php');

class classmain
{
        // var $usernamedevssd_conn = "DEVSD";
        // var $passworddevssd_conn = "gresik45";
        // var $dbdevsd='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = dev-sggdata3.sggrp.com )(PORT = 1521))) (CONNECT_DATA = (SID = DEVSGG)(SERVER = DEDICATED)))';
		
		//var $usernamedevssd_conn = "APPBISD";
        //var $passworddevssd_conn = "gresik45smigone1";
        //var $dbdevsd='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = *********** )(PORT = 1521))) (CONNECT_DATA = (SERVICE_NAME = pdbsi)(SERVER = DEDICATED)))';

        var $usernamedevssd_conn = "dev";
        var $passworddevssd_conn = "semeru2";
        var $dbdevsd='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';

        public function __construct() {            
            @header('Content-Type: text/html; charset=utf-8');
        }

	public function koneksi()
	{
		//return $this->koneksidvsd();

                $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_030.php');//dev
                //$oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_210.php');//prod
		$conn = oci_connect($oraConfig['username_conn'], $oraConfig['password_conn'], $oraConfig['db'] , 'AL32UTF8');
		if (!$conn)
			return false;
		else
		 return $conn;
	}

	public function con_sp()
	{
		$conn = oci_connect("cmstes", "semeru2", '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = devsgg)(SERVER = DEDICATED)))', 'AL32UTF8');
		if (!$conn)
			return false;
		else
		 return $conn;
	}
        
        public function koneksidev()
	{
		return $this->koneksidvsd();

                $oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_030.php');//dev
                //$oraConfig = require ('/opt/lampp/htdocs/sgg/include/connect/ora_sd_210.php');//prod
		$conn = oci_connect($oraConfig['username_conn'], $oraConfig['password_conn'], $oraConfig['db'] , 'AL32UTF8');
		if (!$conn)
			return false;
		else
		 return $conn;
	}
        
  public function koneksidvsd()
	{
    $conn = oci_connect($this->usernamedevssd_conn, $this->passworddevssd_conn, $this->dbdevsd , 'AL32UTF8');
		if (!$conn)
			return false;
		else
		return $conn;
	}

	public function con_devsd()
	{
		$data = array(
			'username_conn'=>'DEV',
			'password_conn' => 'Semengresik1',
			'db'=>'(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = **********)(PORT = 1521))) (CONNECT_DATA = (SID = ORCL1)(SERVER = DEDICATED)))'
		);

		$conn = oci_connect($data['username_conn'], $data['password_conn'], $data['db'] , 'AL32UTF8');
		if (!$conn)
			return false;
		else
		return $conn;
	}

}
?>
