<?
/*
 * Target Volume dan Index Volume per periode ,tiap masing-masing 
 */

session_start();
include ('../include/ex_fungsi.php');
require_once '../include/oracleDev.php'; 
$fungsi=new conntoracleDEVSD();
$conn=$fungsi->DEVSDdb();

$targetVolume='targetvolume_gp.php';
$user_id=$_SESSION['user_id'];
//$user_id='mady';

//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}
$com='2000';
$item_no='121-301';
$komen2='';
$sql="";
$waktu=date("d-m-Y");

//Koneksi SAP
//require_once ("/opt/lampp/htdocs/sgg/include/connect/SAPDataModule_Connection.php");

function updateEks_GP(){
        //$ok = new SAPDataModule_Connection();
        //$sap = $ok->getConnSAP_Dev();
        
        $fungsi=new conntoracleDEVSD();
        $conn=$fungsi->DEVSDdb();
       
        //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; 
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        //$sap->Connect($link_koneksi_sap);
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                 if ($sap->GetStatus() != SAPRFC_OK ) {
                 echo $sap->PrintStatus();
                 exit;
         }
        
        if($sap) {
            $fce = &$sap->NewFunction ("Z_ZAPPSD_INDEX_EXPEDITUR");
            if ($fce == false ) { $sap->PrintStatus(); exit; }
            $dt = date("dmY");
            $querySQL = "select a.* from ZREPORT_TARGET_EXP a where a.updated = to_date('".$dt."','DDMMYYYY') and BRAN1 is not NULL";
           // echo  $querySQL;   
            $query= oci_parse($conn, $querySQL);
            oci_execute($query);
            //echo "<pre>";
            //print_r($dataRec1);
            //echo "</pre>";
            while($dataRec1=oci_fetch_array($query)){ 
                $com = $dataRec1[COM];
                $plant = $dataRec1[PLANT];
                $no_expeditur=$dataRec1[NO_EXPEDITUR];
                $kota = $dataRec1[KOTA];
                $kode_kec = $dataRec1[BRAN1];
                $tipe = $dataRec1[ITEM_NO];
                $thnbln = $dataRec1[TAHUN].$dataRec1[BULAN];
                $index = $dataRec1[VOL_INDEX_GP];
                $adjustmant = $dataRec1[ADJUSTMANT_GP];
                $plant_gp = intval($dataRec1[PLANT_GP]);                
                $kontrak = $dataRec1[KONTRAK_VOL_GP];
                $petugas = $dataRec1[PETUGAS_GP];
                $status = $dataRec1[STATUS_GP];
                
                if(trim($tipe)=='121-301') $tipe = "ZAK";
                else $tipe = "TO";
                $t_data = array(
                "ZNMORG" => "$com",
                "SPMON" => $thnbln,
                "KUNNR" => $no_expeditur,
                "WERKS" => $plant,
                "BZIRK" => $kota,
                "KODE_KEC"=>$kode_kec,
                "BASME" => $tipe,
                "STATUS_INDEX" => $status,
                "GUDANG_GP" => $index,
                "ADJUSTMENT_GP"=>$adjustmant,
                "PLANT_B"=>$plant_gp,
                "UPDATE_BY" => $petugas,
                "UPDATE_DATE" => date("Ymd")
                ); 
                $fce->T_DATA->Append($t_data);
            }
            

            $fce->I_CHECK = "X";

            $fce->Call();	
            if ($fce->GetStatus() == SAPRFC_OK ) {	
                    return $fce->STATUS.$fce->STATUS2;
            } else 
                $fce->PrintStatus();
          }
        
}

/*$halaman_id=1553;


if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
 */

if(isset($_POST['Save'])){
    $sampai=$_POST['total'];
    $bulanVolum=$_POST['bulanC'];
    $tahunVolum=$_POST['tahunC'];
    $nmplant=$_POST['plantC'];
    for($k=0;$k<$sampai;$k++){
	$urutke="urutke".$k;
        $INDEXval="index".$k;
        $ADJUSTMANTval="ADJUSTMANTval".$k;
        $KONTRAVOLval="KONTRAVOL".$k;
        $STATUSval="STATUSval".$k;
        $ID_TARGETval="ID_TARGET".$k;
        $VOLUMEval="VOLUME".$k;
        $KOTAval="KOTA".$k;
        $BRAN1val="BRAN1v".$k;
        $nourut=" no urut ke ".$k+1;
        if (isset($_POST[$urutke])){
			if (($_POST[$ADJUSTMANTval] == "")||($_POST[$KONTRAVOLval] == "")){
				$check = false;
				$k=$sampai+2;
			}else{
				$check = true;
			}
	}
    }
    if ($check){ 
	for($k=0;$k<$sampai;$k++){
            $urutke="urutke".$k;
            $INDEXval="index".$k;
            $ADJUSTMANTval="ADJUSTMANTval".$k;
            $KONTRAVOLval="KONTRAVOL".$k;
            $STATUSval="STATUSval".$k;
            $ID_TARGETval="ID_TARGET".$k;
            $VOLUMEval="VOLUME".$k;
            $KOTAval="KOTA".$k;
            $BRAN1val="BRAN1v".$k;
           
            $KOTAvalarray=array();
            $$BRAN1valarray=array();
            if(isset($_POST[$urutke])){
             $nourutdata=$k+1;   
             $INDEXval=$_POST[$INDEXval];
             $ADJUSTMANTval=$_POST[$ADJUSTMANTval];
             $KONTRAVOLval=$_POST[$KONTRAVOLval];
             $ID_TARGETval=$_POST[$ID_TARGETval];
             $STATUSval=$_POST[$STATUSval];
             $VOLUMEval=$_POST[$VOLUMEval];
             $KOTAvalarray[]=$_POST[$KOTAval];
             $BRAN1valarray[]=$_POST[$BRAN1val];
             
             if($STATUSval==1){
                 $updateIndex2=',VOL_INDEX_GP=0 ';
             }            
             $sqlUpdate="UPDATE ZREPORT_TARGET_EXP SET ADJUSTMANT_GP=$ADJUSTMANTval,PETUGAS='$user_id',STATUS_GP='$STATUSval',KONTRAK_VOL_GP='$KONTRAVOLval',UPDATED=to_date('$waktu','DD-MM-YYYY') $updateIndex2 where ID_TARGET_EXP='$ID_TARGETval'";
             //echo $sqlUpdate;
             $query= oci_parse($conn, $sqlUpdate);
             oci_execute($query);
             //echo "<script>alert('Data ke $nourutdata di update... !!!');</script>";
              
            }
           
        }
        $BRAN1valarrayunik=array_unique($BRAN1valarray);
       // print_r($BRAN1valarrayunik);
        foreach ($BRAN1valarrayunik as $bran1Unik => $keyUnik)
            {    
                  $sqlUpdateSelect="select sum (VOLUME_GP) as TOTAL_VOLUME from ZREPORT_TARGET_EXP where 
                    BULAN='$bulanVolum' and TAHUN='$tahunVolum' and
                    PLANT='$nmplant' and COM='$com' and ITEM_NO='$item_no'
                    and BRAN1='$keyUnik' and STATUS_GP=0 and BRAN1 is not null
                   ";
               //  echo $sqlUpdateSelect;
                 $querySelect= oci_parse($conn, $sqlUpdateSelect);
                 oci_execute($querySelect);
                 $row=oci_fetch_array($querySelect);
                $TOTAL_VOLUME=$row['TOTAL_VOLUME'];

                 $sqlUpdate2="UPDATE ZREPORT_TARGET_EXP SET VOL_INDEX_GP=(VOLUME_GP/$TOTAL_VOLUME)*100,UPDATED=to_date('$waktu','DD-MM-YYYY'),PETUGAS='$user_id'  where BULAN='$bulanVolum' and TAHUN='$tahunVolum' and
                    PLANT='$nmplant' and COM='$com' and ITEM_NO='$item_no'
                    and BRAN1='$keyUnik' and STATUS_GP=0 and BRAN1 is not null
                 ";
                 //echo $sqlUpdate2;
                 $query2= oci_parse($conn, $sqlUpdate2);
                 oci_execute($query2);
            }
            $messSAP=updateEks_GP();  
            $komen2 = "Update data (SAP $messSAP) telah dilakukan, Silahkan Pilh parameter yang ada..!!!";
            $sql= "
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN,TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN12 as BRAN1,PLANT_GP,NAME1,
            TARGET_GP            
            from(  
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN2 as BULAN,TAHUN2 as TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN1 as BRAN12,PLANT_GP,
            TARGET_GP            
            from(            
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN as BULAN2,TAHUN as TAHUN2,PLANT,COM,BRAN1,PLANT_GP,VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP as STATUS_GP2,KONTRAK_VOL_GP,ITEM_NO as ITEM_NO2
                        from ZREPORT_TARGET_EXP where BULAN='$bulanVolum' and TAHUN='$tahunVolum' and
                        PLANT='$nmplant' and COM='$com' and ITEM_NO='$item_no' and BRAN1 is not null
            ) LEFT JOIN ZREPORT_TARGET_PLANT a ON (
            a.KD_PLANT=PLANT and a.BULAN=BULAN2 and a.TAHUN=TAHUN2 and a.ITEM_NO=ITEM_NO2 and a.KD_KOTA is null and a.BRAN12=BRAN1 and a.STATUS_GP=0

            )
            )left join M_CUSTOMER on (KUNNR=PLANT_GP)
            ";   

        $komen = " Tidak Ada Data Yang Di Temukan..!!!";
                //echo "<script type=\"text/javascript\">location.href=\"/$targetVolume\"</script>";
    }else{
	echo "Data Untuk $nourut Salah.. Silahkan Input Ulang... <br>";

    }
}else if(isset($_POST['Submit']))
{
    $bulanVolum=$_POST['bulanVolum'];
    $tahunVolum=$_POST['tahunVolum'];
    $nmplant=$_POST['nmplant'];
    if(($bulanVolum=='00') || ($nmplant=='00') || ($tahunVolum==''))
    {
      echo "<script>alert('Silahkan cek kembali parameter yang bertanda merah...!!');</script>";  
    }else{   
        
      $sqlCek= "
            select count(BULAN) AS JUMLAH from ZREPORT_TARGET_EXP where BULAN='$bulanVolum' and TAHUN='$tahunVolum' and PLANT='$nmplant' 
            and COM='$com' and ITEM_NO='$item_no' and BRAN1 is not null
            ";   
       //  echo $sqlCek;
        $queryCek= oci_parse($conn, $sqlCek);
        oci_execute($queryCek);
        $row=oci_fetch_array($queryCek);
        $jumlahData=$row['JUMLAH'];
        if($jumlahData=='0'){
             require_once 'formulavolume_gp.php'; 
             //echo "<script>alert('Data Baru di dibuat');</script>";
             $messSAP=updateEks_GP();  
             $komen2 = "Data (SAP $messSAP)baru telah dibuat ..!!!";
        }
       // echo $user_id." ".$nmplan." ".$tahunVolum." ".$bulanVolum;
        $sql= "
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN,TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN12 as BRAN1,PLANT_GP,NAME1,
            TARGET_GP            
            from(  
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN2 as BULAN,TAHUN2 as TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN1 as BRAN12,PLANT_GP,
            TARGET_GP            
            from(            
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN as BULAN2,TAHUN as TAHUN2,PLANT,COM,BRAN1,PLANT_GP,VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP as STATUS_GP2,KONTRAK_VOL_GP,ITEM_NO as ITEM_NO2
                        from ZREPORT_TARGET_EXP where BULAN='$bulanVolum' and TAHUN='$tahunVolum' and
                        PLANT='$nmplant' and COM='$com' and ITEM_NO='$item_no' and BRAN1 is not null
            ) LEFT JOIN ZREPORT_TARGET_PLANT a ON (
            a.KD_PLANT=PLANT and a.BULAN=BULAN2 and a.TAHUN=TAHUN2 and a.ITEM_NO=ITEM_NO2 and a.KD_KOTA is null and a.BRAN12=BRAN1 and a.STATUS_GP=0

            )
            )left join M_CUSTOMER on (KUNNR=PLANT_GP)
            ";   

        $komen = " Tidak Ada Data Yang Di Temukan..!!!";
     
        
    }
}else if (isset($_POST['Filter']))
{
    $bulanVolum=$_POST['filterBulan'];
    $tahunVolum=$_POST['filterTahun'];
    $nmplant=$_POST['filterPlant'];
    $filterKota=$_POST['filterKota'];
    if ($filterKota=='00')
    {
        $filViewKota="";
    }else{
        $filViewKota="and PLANT_GP='$filterKota'";
    } $filterKota=$_POST['filterKota'];   
    $sql= "
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN,TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN12 as BRAN1,PLANT_GP,NAME1,
            TARGET_GP            
            from(
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN2 as BULAN,TAHUN2 as TAHUN,PLANT,COM,
            VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP2,KONTRAK_VOL_GP,BRAN1 as BRAN12,PLANT_GP,
            TARGET_GP            
            from(            
            select ID_TARGET_EXP,NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN as BULAN2,TAHUN as TAHUN2,PLANT,COM,BRAN1,PLANT_GP,VOLUME_GP,VOL_INDEX_GP ,ADJUSTMANT_GP,STATUS_GP as STATUS_GP2,KONTRAK_VOL_GP,ITEM_NO as ITEM_NO2
                        from ZREPORT_TARGET_EXP where BULAN='$bulanVolum' and TAHUN='$tahunVolum' and
                        PLANT='$nmplant' and COM='$com' and ITEM_NO='$item_no' and BRAN1 is not null $filViewKota
            ) LEFT JOIN ZREPORT_TARGET_PLANT a ON (
            a.KD_PLANT=PLANT and a.BULAN=BULAN2 and a.TAHUN=TAHUN2 and a.ITEM_NO=ITEM_NO2 and a.KD_KOTA is null and a.BRAN12=BRAN1 and a.STATUS_GP=0

            )
            )left join M_CUSTOMER on (KUNNR=PLANT_GP)
        ";
}
if($sql!=''){
    //echo $sql;
    $query= oci_parse($conn, $sql);
    oci_execute($query);
    $q = 0;
    while($row=oci_fetch_array($query)){            
		$ID_TARGET_EXP[$q]=$row['ID_TARGET_EXP'];
		$NO_EXPEDITUR[$q]=$row['NO_EXPEDITUR'];
		$NAMA_EXPEDITUR[$q]=$row['NAMA_EXPEDITUR'];
		$KOTA[$q]=$row['KOTA'];
                $NM_KOTA[$q]=$row['NM_KOTA'];
                $TARGET_KOTA[$q]=$row['TARGET_GP'];
                $BULAN[$q]=$row['BULAN'];  
                $TAHUN[$q]=$row['TAHUN'];
                $PLANT[$q]=$row['PLANT'];
                $COM[$q]=$row['COM'];
		$VOLUME[$q]=$row['VOLUME_GP'];  
                $VOL_INDEX[$q]=$row['VOL_INDEX_GP'];  
                $ADJUSTMANT[$q]=$row['ADJUSTMANT_GP'];  
                $STATUS[$q]=$row['STATUS_GP2'];
                $KONTRAVOL[$q]=$row['KONTRAK_VOL_GP'];
                $BRAN1[$q]=$row['BRAN1'];
                $PLANT_GP[$q]=$row['PLANT_GP'];
                $NAME_GP[$q]=$row['NAME1'];
                $q++;
                
    }
    $total = count($ID_TARGET_EXP);
}       
        


?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Data Target Volume dan Index GP (ZAK)</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
    <script type="text/javascript">

checked=false;
function checkedAll (frm1) {
	var aa= document.getElementById('formVolume');
	 if (checked == false)
          {
           checked = true
		   markAllRows('formVolume');
          }
        else
          {
          checked = false
		  unMarkAllRows('formVolume')
          }
 }

function markAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var input;
    var input2;
    var select;
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
			if (checkbox.checked != true){
				checkbox.checked = true;
                                checkbox.value = '1';
				rows[i].className += ' selected';
			}
        }
        input = rows[i].getElementsByTagName( 'input' )[1];

        if ( input && input.type == 'text' ) {
			if (input.checked != true){
				input.checked = true;
                                input.disabled = '';
			}
        }
         input2 = rows[i].getElementsByTagName( 'input' )[2];

        if ( input2 && input2.type == 'text' ) {
			if (input2.checked != true){
				input2.checked = true;
                                input2.disabled = '';
			}
        }
        select = rows[i].getElementsByTagName( 'select' )[0];

        if ( select ) {
			if (select.checked != true){
				select.checked = true;
                                select.disabled = '';
			}
        }
        
    }

    return true;
}

function unMarkAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var input;
    var input2;
    var select;
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
			if (checkbox.checked != false){
			checkbox.checked = false;   
                        checkbox.value = '0';
                        rows[i].className = rows[i].className.replace(' selected', '');
                        
                    }
        }
        input = rows[i].getElementsByTagName( 'input' )[1];

        if ( input && input.type == 'text' ) {
			if (input.checked != false){
				input.checked = false;
                                input.disabled = 'disabled';
			}
        }
        input2 = rows[i].getElementsByTagName( 'input' )[2];

        if ( input2 && input2.type == 'text' ) {
			if (input2.checked != false){
				input2.checked = false;
                                input2.disabled = 'disabled';
			}
        }
        select = rows[i].getElementsByTagName( 'select' )[0];

        if ( select ) {
			if (select.checked != false){
				select.checked = false;
                                select.disabled = 'disabled';
			}
        }
    }

    return true;
}

</script> 

<script> 

function cek_last(id_cek) {
		var obj = document.getElementById(id_cek);
		var cek = obj.value;	
		var kec;
		var satu_data = "0";  

		for (var keb = 0; keb < cek; keb++){
			kec = keb + 1;
                        var ADJUSTMANTval = 'ADJUSTMANTval'+keb;
                        var com_ADJUSTMANTval = document.getElementById(ADJUSTMANTval);
			var KONTRAVOLval = 'KONTRAVOL'+keb;
                        var com_KONTRAVOLval = document.getElementById(KONTRAVOLval);
                        var rowke = 'urutke'+keb;
			var com_rowke = document.getElementById(rowke);

			if (com_rowke.checked == true)  {
			
			satu_data = "1";  

				if ((com_ADJUSTMANTval.value == "" )||(com_KONTRAVOLval.value == "" )) {
					alert(" ADJUSTMANT atau KONTRAK VOLUME cek kemabali " + kec + " Harus Diisi.. " );
					return document.hasil = false;
					keb = cek + 2;
				}		
			}
		}
		if (satu_data == "0") {
			alert("Minimal Pilih Satu Data...");
			return document.hasil = false;
		}
		return document.hasil = true;
}
function checkForother(obj,kei) {  
	if (!document.layers) {  
          
	
		var ADJUSTMANTval = 'ADJUSTMANTval'+kei;
		var com_ADJUSTMANTval = document.getElementById(ADJUSTMANTval);
                var KONTRAVOLval = 'KONTRAVOL'+kei;
		var com_KONTRAVOLval = document.getElementById(KONTRAVOLval);
                var STATUSval = 'STATUSval'+kei;
		var com_STATUSval = document.getElementById(STATUSval);
		var rowke = 'rowke'+kei;
		var com_rowke = document.getElementById(rowke);
		if (obj.value == "0") {  
			com_ADJUSTMANTval.disabled="";
                        com_KONTRAVOLval.disabled="";
			com_STATUSval.disabled="";
			obj.value = "1"; 
		} else {  
			com_ADJUSTMANTval.disabled="disabled"; 
                        com_KONTRAVOLval.disabled="disabled"; 
                        com_STATUSval.disabled="disabled";
			obj.value = "0";  
			
		}  
	} 
} 

function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9...!");
			 return false;
			 }
		  }
	 } 
   }

function IsNumeric2(obj,volIndex)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789.";
   var strChar;
   var strString = obj.value;
   var valVolIndex = volIndex;
   //alert ("dsda "+valVolIndex);
   if (strString.length == 0){
        alert("Harus Diisi Angka..!!!");
	 return false;
//	} else if (strString > valVolIndex){
//            alert("Adjusmant tidak boleh lebih dari Indexnya ..!!!");
//            return false;            
        }else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9...!");
			 return false;
			 }
		  }
	 } 
   }

</script> 
<body>
<script type="text/javascript" language="JavaScript">
    document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
</script>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Daftar Data Target Volume dan Index GP (ZAK)</th>
</tr></table>
</div>

    <form  id="form1" name="form1" method="post" action="<?=$targetVolume;?>">
	   <table width="989" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		  <td class="puso">&nbsp;&nbsp;&nbsp;PERIODE</td>
		  <td class="puso">:</td>
		  <td><select name="bulanVolum" id="bulanVolum">
            <option value='00'>--Pilih Bulan--</option>
            <option value='01'>Januari</option>
            <option value='02'>Pebruari</option>
            <option value='03'>Maret</option>
            <option value='04'>April</option>
            <option value='05'>Mei</option>
            <option value='06'>Juni</option>
            <option value='07'>Juli</option>
            <option value='08'>Agustus</option>
            <option value='09'>September</option>
            <option value='10'>Oktober</option>
            <option value='11'>Nopember</option>
            <option value='12'>Desember</option>
          </select>
	      <input name="tahunVolum" type="text" id="tahunVolum" size="4" maxlength="10" onBlur="javascript:IsNumeric(this)" />
                <span style="color: red;">*</span>
                </td>
                </tr>
		<tr>
		<td width="132" class="puso">&nbsp;&nbsp;&nbsp;PLANT</td>
		<td width="20" class="puso">:</td>
		<td width="632">
                    <select name="nmplant" id="nmplant">
                        <option value='00'>--Pilih Plant--</option>
                        <?
                        $sqlPlant= "select KD_PLANT,NAME from ZREPORT_M_PLANT where ORG='$com' and
                                    KD_PLANT IN 
                                    (
                                    select DISTINCT PLANT from(
                                    select DISTINCT PLANT from ZREPORT_RPT_REAL
                                    )INNER JOIN ZREPORT_TARGET_PLANT ON(

                                    KD_PLANT=PLANT
                                    )
                                    )
                                    order by KD_PLANT ASC";

                        //echo $sqlPlant;
                        $query2= oci_parse($conn, $sqlPlant);
                        oci_execute($query2);
                 
                        while($rowPlant=oci_fetch_array($query2)){ 
                            $KDplant=$rowPlant['KD_PLANT'];
                            $NAMEplant=$rowPlant['NAME'];
                        ?>
                             <option value='<?=$KDplant;?>'><?=$KDplant;?>&nbsp;&nbsp;<?=$NAMEplant;?></option>
                        <?
                        }
                        ?>
                    </select><span style="color: red;">*</span>
                 </td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
                    <input name="Submit" type="submit" class="button" value="Show"/>
		<input type="hidden" id="user_id" name="user_id" value="<?=$user_id?>" />
        	</td>
		</tr>
		<tr>
		<td><a href="import_targetvolume_gp.php" title="Import Exel" target="_blank">Import Target Volume</a></td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<p></p>
<div align="center">
    <?
    echo $komen2;
    ?>
    <table width="989" align="center" class="adminlist">
	<tr>
         <form id="form2" name="form2" method="post" action="<?=$targetVolume;?>">   
           <th align="left" colspan="7">
                <span class="style5">&nbsp;Tabel Target Volume dan Index  >> 
                <?php
                
                     if ($total >0){
                    echo  "Bulan: ".$bulanVolum." Tahun: ".$tahunVolum." PLANT: ".$nmplant." COM: $com";
                    }
                ?>   
                </span> 
           <? if($total>0){ ?>
           <span style="float:right;">
            <?
            $arrayGab=array();
            for($r=0; $r<$total;$r++) {
            $arrayGab[]= $PLANT_GP[$r]."#".$NAME_GP[$r];
            }
            $uniqArray=array_unique($arrayGab);
            ?>
            <select name="filterKota">
            <option value='00'>--Pilih PLANT GP--</option>   
            <?   
            foreach ($uniqArray as $unik => $keyUnik)
            {    
            $pisahKota=explode("#", $keyUnik);    
            ?>
            <option value='<? echo $pisahKota['0'];?>'><? echo $pisahKota['1'];?></option>
            <? } ?>    
            </select>    
               <input name="filterBulan" type="hidden" value="<? echo $bulanVolum;?>" />
               <input name="filterTahun" type="hidden" value="<? echo $tahunVolum;?>" />
               <input name="filterPlant" type="hidden" value="<? echo $nmplant;?>" />
                <input name="Filter" type="submit" class="button" value="Filter" />      
            </span>
           <? } ?>
            </th>    
        </form>
        </tr>
    </table>
</div> 
<div align="center">
<form  name="formVolume" id="formVolume" method="post" action="<?=$targetVolume;?>" onSubmit="cek_last('total');return document.hasil">
	<table id="test1" width="989" align="center" class="adminlist">
	<thead >
         <tr class="quote">
	   <td width="20"><input type="button" class="button" onClick="checkedAll('formVolume');" value="CEK" title="CEK ALL"></strong></td>
            <td width="20"><div align="center"><strong>No.</strong></div></td>
           <td width="136" align="center"><strong>NO EXPEDITUR </strong></td>
	   <td width="183" align="center"><strong>NAMA EXPEDITUR </strong></td>
	   <td width="70" align="center"><strong>KODE KOTA </strong></td>
	   <td width="93" align="center"><strong>NAMA KOTA </strong></td>
           <td width="93" align="center"><strong>KECAMATAN </strong></td>
           <td width="93" align="center"><strong>PLANT GP </strong></td>
           <td width="93" align="center"><strong>NAME GP </strong></td>
           <td width="63" align="center"><strong>TARGET KOTA </strong></td>
	   <!--<td width="71" align="center"><strong>VOLUME</strong></td>-->
           <td width="75" align="center"><strong>INDEX</strong></td>
	   <td width="43" align="center"><strong>ADJUSTMANT</strong></td>
           <td width="43" align="center"><strong>KONTRAK VOLUME</strong></td>
           <td width="40" align="center"><strong>STATUS</strong></td>
	  </tr>
        </thead>
	  <tbody >
<?
if($total>0){
?>              
              
                    <?  for($i=0; $i<$total;$i++) {
                        $rowke="rowke".$i;
			if(($i % 2) == 0){	 
                            echo "<tr class='row0' id='$rowke' >";
			}
			else{	
			   echo "<tr class='row1' id='$rowke' >";
			}	
                        $no=$i+1;
                        $kei=$i;
                        $urutke="urutke".$i;
                        $INDEXval="index".$i;
                        $ADJUSTMANTval="ADJUSTMANTval".$i;
                        $STATUSval="STATUSval".$i;
                        $ID_TARGETval="ID_TARGET".$i;
                        $KONTRAVOLval="KONTRAVOL".$i;
                        $VOLUMEval="VOLUME".$i;
                        $KOTAval="KOTA".$i;
                        $BRAN1val="BRAN1v".$i;
                    ?>
			
                            <td align="center">
                                <input type="checkbox" id="<?=$urutke;?>" name="<?=$urutke;?>" value="0" onChange="checkForother(this,'<?=$kei?>')" />
                            </td>   
                            <td align="center"><? echo $no.".";?></td>
                            <td align="center"><? echo $NO_EXPEDITUR[$i]; ?></td>
                            <td align="center"><? echo $NAMA_EXPEDITUR[$i]; ?></td>
                            <td align="center"><? echo $KOTA[$i]; ?></td>
                            <td align="center"><? echo $NM_KOTA[$i]; ?></td>
                            <td align="center"><? echo $BRAN1[$i]; ?></td>
                            <td align="center"><? echo $PLANT_GP[$i]; ?></td>
                            <td align="center"><? echo $NAME_GP[$i]; ?></td>
                            <td align="center"><? echo showNilai2($TARGET_KOTA[$i]); ?></td>
                            <!--<td align="right"><? echo showNilai2($VOLUME[$i]); ?></td>-->
                            <td align="right"><? echo showNilai2($VOL_INDEX[$i]);$keyIndex=showNilai2($VOL_INDEX[$i]);?></td>
                            <td align="left">
                            <input type="text" id="<?=$ADJUSTMANTval;?>" name="<?=$ADJUSTMANTval;?>" value="<? echo showNilai2($ADJUSTMANT[$i]);?>" onBlur="javascript:IsNumeric2(this,'<?=$keyIndex;?>')" disabled="disabled"/>
                            </td>
                            <td align="left">
                            <input type="text" id="<?=$KONTRAVOLval;?>" name="<?=$KONTRAVOLval;?>" value="<? echo showNilai2($KONTRAVOL[$i]);?>" onBlur="javascript:IsNumeric2(this,'<?=$keyIndex;?>')" disabled="disabled"/>
                            </td>
                            <td align="left">
                                <select id="<?=$STATUSval;?>" name="<?=$STATUSval;?>" disabled="disabled">
                                            <?                                            
                                            if($STATUS[$i]==1)
                                            {   
                                            ?>                                                                               
                                                    <option   value='0' >Aktif</option>
                                                    <option  value='1' selected>Inaktif</option>
                                            <?        
                                            }else{
                                                ?>
                                                    <option   value='0' selected>Aktif</option>
                                                    <option  value='1'>Inaktif</option>
                                                <?
                                            }
                                            ?>
                                            
                                </select>
                            </td>
                          
                            
                            <input id="userUpdate" name="userUpdate" type="hidden" value="<? echo $user_id;?>" />
                            <input id="<?=$INDEXval;?>" name="<?=$INDEXval;?>" type="hidden" value="<? echo showNilai2($VOL_INDEX[$i]); ?>" />
                            <input id="<?=$ID_TARGETval;?>" name="<?=$ID_TARGETval;?>" type="hidden" value="<? echo $ID_TARGET_EXP[$i]; ?>" />
                            <input id="<?=$VOLUMEval;?>" name="<?=$VOLUMEval;?>" type="hidden" value="<? echo $VOLUME[$i]; ?>" />
                            <input id="<?=$KOTAval;?>" name="<?=$KOTAval;?>" type="hidden" value="<? echo $KOTA[$i]; ?>" />
                            <input id="<?=$BRAN1val;?>" name="<?=$BRAN1val;?>" type="hidden" value="<? echo $BRAN1[$i]; ?>" />
                            
			</tr>
		  <?
                  }
	
}else {
?>
        <tr class="row1"><td align="center" colspan="14"><? echo $komen;?></td></tr>
<?
}
 
if($total>0){ ?>
          <tr class="quote">
		<td colspan="14" align="center">
                    <?
                    if(($filterKota=='')||($filterKota=='00')){
                    ?>
                <a href="cetakexcel_targetvolume_gp.php<?php printf("?bulanVolum=$bulanVolum&tahunVolum=$tahunVolum&nmplant=$nmplant"); ?>"class="button">Excel</a>
                <? } ?>
		<input name="Save" type="submit" class="button" id="Save" value="Save" />
		<a href="<?=$targetVolume;?>" target="isi" class="button">Cancel</a>
                <input id="total" name="total" type="hidden" value="<?=$total;?>" />
                <input id="bulanC" name="bulanC" type="hidden" value="<?=$bulanVolum;?>" />
                <input id="tahunC" name="tahunC" type="hidden" value="<?=$tahunVolum;?>" />
                <input id="plantC" name="plantC" type="hidden" value="<?=$nmplant;?>" />
               </td>
	    </tr>
<? } ?>        
	  </tbody>
	</table>

</form>

<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
