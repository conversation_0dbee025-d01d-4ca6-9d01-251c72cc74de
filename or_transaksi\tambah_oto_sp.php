<?
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$bo_conn=$fungsi->bo_koneksi();

$halaman_id=4213;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$user_id=$_SESSION['user_id'];
$distr_id=$_SESSION['distr_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$fungsi->sapcode($distr_id);
$distr_nm=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","DISTRIBUTOR_ID",$distr_id,"NAMA_DISTRIBUTOR");

/*
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}

*/

$sold_to=$distr_id;
$nama_sold_to=$distr_nm;
$halaman_aksi = "komentar.php";
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/template_css.css" rel="stylesheet" type="text/css" />
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
	
	

</script>
<script language="javascript">

	function getCreditlimitAging(){
var com_sold_to = document.getElementById("sold_to");
var com_org = document.getElementById('org');	


var strURL="caricreditlimit_aging.php?org=<?php  echo $user_org;?>&distr=<?php  echo $distr_id;?>";
var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                    if (req.readyState == 4) {
                            // only if "OK"
                            if (req.status == 200) {						
                                    document.getElementById('creditlimit').innerHTML=req.responseText;				
									
                            } else {
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }				
            }			
            req.open("GET", strURL, true);
            req.send(null);
}
}

<!--
var j=1;
    function start_add() {
	//if (j==11){alert('ma\'af maksimal 10');return false;}
        j++;
	var cek=j-1;
			if(validasi('shipto'+cek+'','','R','produk'+cek+'','','R','com_qty'+cek+'','','RisNum','tgl_kirim'+cek+'','','R')){

		if(cek>1){
			for (var i = 1; i < cek; i++){
				var obj_tokoi = document.getElementById('shipto'+i+'');
				var nilai_tokoi = obj_tokoi.value;	
	
				var obj_tokocek = document.getElementById('shipto'+cek+'');
				var nilai_tokocek = obj_tokocek.value;	

				var obj_produki = document.getElementById('produk'+i+'');
				var nilai_produki = obj_produki.value;	
	
				var obj_produkcek = document.getElementById('produk'+cek+'');
				var nilai_produkcek = obj_produkcek.value;	

				var obj_tgl_kirimi = document.getElementById('tgl_kirim'+i+'');
				var nilai_tgl_kirimi = obj_tgl_kirimi.value;	

				var obj_tgl_kirimcek = document.getElementById('tgl_kirim'+cek+'');
				var nilai_tgl_kirimcek = obj_tgl_kirimcek.value;	

				var obj_konfirmasi = document.getElementById('reason');
				var nilai_konfirmasi = obj_konfirmasi.value;	
	
				if (nilai_tokoi == nilai_tokocek && nilai_produki == nilai_produkcek && nilai_tgl_kirimi == nilai_tgl_kirimcek && nilai_konfirmasi != 'Z02'){
						alert("Data Toko, Produk dan Tanggal Kirim Telah Diinputkan \n Silahkan Input Ulang...");
						document.hasil = false;
						j--;
						return false;
				}
			} 
		}

		var body1 = document.getElementById("coba");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "dd"+j); 
		newdiv.innerHTML='<table width = "1500"class="adminlist"><tr><td align="left"><div id="shiptodiv'+j+'"><input type="text" class="inputlabel" value="" size="10" id="shipto'+j+'" name="shipto'+j+'" onchange="ketik_shipto(this,'+j+')"/><input type="text" value="" size="20" class="inputlabel" id="nama_shipto'+j+'" readonly="true" name="nama_shipto'+j+'"><input type="text" value="" size="18" id="alamat'+j+'" class="inputlabel" name="alamat'+j+'" readonly="true"><input type="hidden" value="" id="kode_distrik'+j+'" name="kode_distrik'+j+'" ><input type="text" value="" class="inputlabel" size="8" id="nama_distrik'+j+'" name="nama_distrik'+j+'" readonly="true"><input type="hidden" value="" id="kode_prov'+j+'" name="kode_prov'+j+'" ><input type="hidden" value="" id="nama_prov'+j+'" name="nama_prov'+j+'" ><input name="btn_shipto'+j+'" type="button" class="button" id="btn_shipto'+j+'" value="..." onClick="findshipto('+j+')"></div></td><td align="left"><div id="produkdiv'+j+'"><input type="text" value="" size="12" class="inputlabel" id="produk'+j+'" name="produk'+j+'" onchange="ketik_produk(this,'+j+')"/><input type="text" value="" class="inputlabel" readonly="true" id="nama_produk'+j+'" name="nama_produk'+j+'" size="20"/><input type="text" value="" class="inputlabel" readonly="true" id="uom'+j+'" name="uom'+j+'" size="4"/><input name="btn_produk'+j+'" type="button" class="button" id="btn_produk'+j+'" value="..." onClick="findproduk('+j+')"></div></td><td align="left"><input type="text" value="" id="com_qty'+j+'" name="com_qty'+j+'" size="6" maxlength="6" onBlur="javascript:IsNumeric(this)"/></td><td align="left"><input name="tgl_kirim'+j+'" size="12" type="text" id="tgl_kirim'+j+'"  onClick="return showCalendar(\'tgl_kirim'+j+'\');" onblur="cektanggal(\'tgl_kirim'+j+'\')"/></td><td align="center"><input size="10" name="com_kontrak'+j+'" type="text" id="com_kontrak'+j+'" value="" readonly="true"/><input size="10" name="com_posnr'+j+'" type="hidden" id="com_posnr'+j+'" value=""/><input size="10" name="com_sisa'+j+'" type="hidden" id="com_sisa'+j+'" value=""/><input id="btn_com_kontrak'+j+'" name="btn_com_kontrak'+j+'" type="button" class="button" onClick="findkontrak('+j+');" value="..." /></td><td align="left"><input size="10" name="com_shipment'+j+'" type="text" id="com_shipment'+j+'" value="" maxlength="10" /></td><td width="100"></td></tr></table>';
		body1.appendChild(newdiv);
		document.getElementById('jumlah').value=j;
		}else{
		j--;
		}
    }
    function cek_last(id_cek) {
		var obj = document.getElementById(id_cek);
		var cek = obj.value;	
		if(validasi('shipto'+cek+'','','R','produk'+cek+'','','R','com_qty'+cek+'','','RisNum','tgl_kirim'+cek+'','','R')){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_tokoi = document.getElementById('shipto'+i+'');
					var nilai_tokoi = obj_tokoi.value;	
		
					var obj_tokocek = document.getElementById('shipto'+cek+'');
					var nilai_tokocek = obj_tokocek.value;	
	
					var obj_produki = document.getElementById('produk'+i+'');
					var nilai_produki = obj_produki.value;	
		
					var obj_produkcek = document.getElementById('produk'+cek+'');
					var nilai_produkcek = obj_produkcek.value;	

					var obj_tgl_kirimi = document.getElementById('tgl_kirim'+i+'');
					var nilai_tgl_kirimi = obj_tgl_kirimi.value;	

					var obj_tgl_kirimcek = document.getElementById('tgl_kirim'+cek+'');
					var nilai_tgl_kirimcek = obj_tgl_kirimcek.value;	
	
					var obj_konfirmasi = document.getElementById('reason');
					var nilai_konfirmasi = obj_konfirmasi.value;	

					if (nilai_tokoi == nilai_tokocek && nilai_produki == nilai_produkcek && nilai_tgl_kirimi == nilai_tgl_kirimcek && nilai_konfirmasi != 'Z02'){
						alert("Data Toko, Produk dan Tanggal Kirim Telah Diinputkan \n Silahkan Input Ulang...");
						document.hasil = false;
						return false;
					}
				} 
			}
			return true;	
		}else{
		document.hasil = false;
		return false;
		}
    }

	function stop_add()
	{
	if (j==1){alert('Maaf Minimal 1 Item Permintaan..');return false;}
	k=j;
	k=k.toString();
    var body1 = document.getElementById("coba");
	var buang = document.getElementById("dd"+k);
    body1.removeChild(buang);
	j=j-1;
	document.tambah.jumlah.value=j;
	}
	
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789.,";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
		 return false;
		 }	  
	 } 
   }

function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant_sp.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
 
function getroute(muat) {		
		var strURL="cariroute_sp.php?muat="+muat;
		var req = getXMLHTTP();
		
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('routediv').innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
   
   function getinco() {	
var com_sold = document.getElementById('sold_to');
		var com_plant = document.getElementById('plant');
		var com_type = document.getElementById('so_type');
		var strURL="cariinco.php?distr_id="+com_sold.value+"&plant="+com_plant.value+"&so_type="+com_type.value;
		var req = getXMLHTTP();
		
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('divinco').innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
	
	
	 function getplan() {	
		var com_sold = document.getElementById('sold_to');
		var com_type = document.getElementById('so_type');
		
		var strURL="cariplant.php?distr_id="+com_sold.value+"&so_type="+com_type.value;
		var req = getXMLHTTP();
		
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('divplant').innerHTML=req.responseText;						
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
	
	
function findshipto() {	
		var so_type = document.getElementById('so_type');
		var com_sold = document.getElementById('sold_to');
		var com_plant = document.getElementById('plant');
		var com_inco = document.getElementById('jenis_kirim');
		var strURL="cari_shipto_sp.php?nourut="+j+"&sold_to="+com_sold.value+"&plant="+com_plant.value+"&inco="+com_inco.value+"&so_type="+so_type.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shiptoadm.php?shipto="+obj.value+"&nourut="+j+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv"+j).innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function finddistr(org) {
		var com_org = document.getElementById('org');		
		var strURL="cari_distr.php?org="+com_org.value;
		popUp(strURL);
		} 
		  
function ketik_distr(obj) {
	var com_org = document.getElementById('org');		
	var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() { 
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {	
					document.getElementById("distrdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}


function ketik_produk(obj,ke) {
	var strURL="ketik_produk.php?produk="+obj.value+"&nourut="+ke;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("produkdiv"+ke).innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findproduk(ke) {	
	    var com_sold = document.getElementById('sold_to');
		var com_plant = document.getElementById('plant');
		var com_inco = document.getElementById('jenis_kirim');
		var com_shipto = document.getElementById('shipto1');
		
		var strURL="cari_produk_sp.php?plant="+com_plant.value+"&nourut="+ke+"&sold_to="+com_sold.value+"&inco="+com_inco.value+"&shipto="+com_shipto.value;
		popUp(strURL);
}
function findkapal() {	
    var com = document.getElementById("org");
	var strURL="cari_kapal.php?org="+com.value;
	popUp(strURL);
}
function ketik_kapal(obj) {
    var com = document.getElementById("org");
	var strURL="ketik_kapal.php?org="+com.value+"&kapal="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('kapaldiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function norebate() {	
		var com_price = document.getElementById('pricelist');
		var com_kontrak = document.getElementById('kontrak');
		var com_lblk = document.getElementById('labelk');
		var com_btn = document.getElementById('btn_kontrak');
		if ( com_price.value == '07') {
		com_kontrak.style.visibility = 'visible'; 
		com_lblk.style.visibility = 'visible'; 
		com_btn.style.visibility = 'visible'; 
		}
		else {
		com_kontrak.style.visibility = 'hidden'; 
		com_lblk.style.visibility = 'hidden'; 
		com_btn.style.visibility = 'hidden'; 
		}
}
function nolc() {	
		var com_so = document.getElementById('so_type');
		var com_lc = document.getElementById('lcnum');
		var com_lbl = document.getElementById('labelc');
		if ( com_so.value == 'ZEX') {
		com_lc.style.visibility = 'visible'; 
		com_lbl.style.visibility = 'visible'; 
		}
		else {
		com_lc.style.visibility = 'hidden'; 
		com_lbl.style.visibility = 'hidden'; 
		}
}
function cekkonfirm() {	
		var com_reason = document.getElementById('reason');
		var com_so = document.getElementById('oldso');
		var com_lblso = document.getElementById('lblso');
		var com_price = document.getElementById('price_date');
		var com_lblprice = document.getElementById('lblprice');

		if ( com_reason.value == 'Z02') {
			com_so.style.visibility = 'visible'; 
			com_lblso.style.visibility = 'visible'; 
			com_price.style.visibility = 'visible'; 
			com_lblprice.style.visibility = 'visible'; 
		}
		else {
			com_so.style.visibility = 'hidden'; 
			com_lblso.style.visibility = 'hidden'; 
			com_price.style.visibility = 'hidden'; 
			com_lblprice.style.visibility = 'hidden'; 

		}
}

function cekkontrak() {	
		var com_price = document.getElementById('pricelist');
		var com_tipe = document.getElementById('so_type');
		var com_kontrak = document.getElementById('btn_kontrak1');

		if ( (com_price.value == '07') && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; } 
		else if ( com_price.value == '01' && (com_tipe.value == 'ZPR') ) { com_kontrak.disabled=""; }
                else if ( com_price.value == '04' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
                else if ( com_price.value == '09' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
		else { com_kontrak.disabled="disabled"; }
}

function findkontrak(kei) {	
		var comorg = document.getElementById('org');
		var comdistr = document.getElementById('sold_to');
		var comdistrik = document.getElementById('kode_distrik'+kei);
		var comshipto = document.getElementById('shipto'+kei);
		var comproduk = document.getElementById('produk'+kei);
		var comprice = document.getElementById('pricelist');
                var comreason = document.getElementById('reason');
		var comqty = document.getElementById('com_qty'+kei);

		if ( comprice.value == '07') {
		var strURL="cari_kontrakreb.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value+"&comreason="+comreason.value;
                } else if ( comprice.value == '09') {
		var strURL="cari_kontrakreb.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value+"&comreason="+comreason.value;
		} else {
		var strURL="cari_kontrak.php?org="+comorg.value+"&sold_to="+comdistr.value+"&distrik="+comdistrik.value+"&produk="+comproduk.value+"&comqty="+comqty.value+"&nourut="+kei+"&shipto="+comshipto.value;
		}
		popUp(strURL);
}
function cektanggal(obj) {
var com_tgl = document.getElementById(obj);
var com_kn = document.getElementById('tglnya');
var tgl = com_tgl.value;
var kn = com_kn.value;
var tgl1 = parseInt(tgl.substr(0,2));
var bln1 = parseInt(tgl.substr(3,2));
var th1 = parseInt(tgl.substr(6,4));
var tglo = bln1+"/"+tgl1+"/"+th1;
var tglx = new Date(tglo);
var tgl2 = parseInt(kn.substr(0,2));
var bln2 = parseInt(kn.substr(3,2));
var th2 = parseInt(kn.substr(6,4));
var tgln = bln2+"/"+tgl2+"/"+th2;
var knx = new Date(tgln);
	if( (tglx >= knx) )
	{
	 com_tgl.value=tgl;
	} else { com_tgl.value=kn; }
}
function cek_data() {
		var obj = document.getElementById('jumlah');
		var cek = obj.value;
		var com_price = document.getElementById('pricelist');
		var com_tipe = document.getElementById('so_type');
		
		for (var i = 1; i <= cek; i++){	
		if ( (com_price.value == '07') && (com_tipe.value == 'ZOR') ) {
		var kontrak = 'com_kontrak'+i; }
		else if ( com_price.value == '01' && (com_tipe.value == 'ZPR') ) {
		var kontrak = 'com_kontrak'+i; }
		else { var kontrak = ''; }
		if (validasi('so_type','','R','sold_to','','R','plant','','R','jenis_kirim','','R','top','','R','shipto'+i+'','','R','produk'+i+'','','R','com_qty'+i+'','','RisNum','tgl_kirim'+i+'','','R',kontrak,'','R')) {
		
		}else{
		document.hasil = false;
		return false;
		}		
	}	
 }
   
//-->
</script>
<?
if($user_org=='3000'){
    echo '<script language="JavaScript" type="text/javascript">
          // getCreditlimitAging();
         </script>';
}  



//include('../include/sapclasses/sap.php');
$sap = new SAPConnection();
$sap->Connect("../include/sapclasses/logon_data.conf");
if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
if ($sap->GetStatus() != SAPRFC_OK ) {
   echo $sap->PrintStatus();
   exit;
}

	$fce = $sap->NewFunction ("Z_CREDIT_EXPOSURE");
	if ($fce == false ) {
	   $sap->PrintStatus();
	   exit;
	}
	// entri parameter
        $fce->X_KKBER = $user_org;
        $fce->X_KUNNR = $distr_id;//$distributor;
        //$fce->X_DATE_CREDIT_EXPOSURE='31.12.9999';
        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK ) {		
                        $credit=$fce->Z_CREDITLIMIT*100; //credit limit
                        $delivery=$fce->Z_OPEN_DELIVERY*100; // do yg blom di billing
                        $minsum=$fce->Z_SUM_FLAG;
                        $usecredit0=$fce->Z_SUM_OPENS*100;
                        $usecredit=$minsum.$usecredit0;
                        $minsp=$fce->Z_OPEN_SP_FLAG;
                        $special0=$fce->Z_OPEN_SPECIALS*100;
                        $special=$minsp.$special0;
						$sisa=$credit-$usecredit; //sisa kredit
                      

        }else
                $fce->PrintStatus();

        
        
        //Aging Pituang
        $fce = &$sap->NewFunction("Z_ZAPPSD_AR_AGING");
        if ($fce == false) {
            $sap->PrintStatus();
            exit;
        }

        $fce->I_DATE = date('Ymd');
        $fce->I_BUKRS = $user_org;
        $fce->I_KUNNR = $distr_id;

        $fce->Call();
		$fce->T_AGING->Reset();
		$fce->T_AGING->Next();
		$aginTOTAL=$fce->T_AGING->row['TOTAL'];
        $aginFUTURE=$fce->T_AGING->row['FUTURE'];
        $aginDUE5=$fce->T_AGING->row['DUE5'];
        $aginDUE10=$fce->T_AGING->row['DUE10'];
        $aginDUE15=$fce->T_AGING->row['DUE15'];
        $aginDUE20=$fce->T_AGING->row['DUE20'];
        $aginDUE25=$fce->T_AGING->row['DUE25'];
        $aginDUE30=$fce->T_AGING->row['DUE30'];
        $aginDUE60=$fce->T_AGING->row['DUE60'];
        $aginDUE180=$fce->T_AGING->row['DUE180'];
        $aginDUE365=$fce->T_AGING->row['DUE365'];
        $aginDUEXXX=$fce->T_AGING->row['DUEXXX'];
        $aginAGING_MAX=$fce->T_AGING->row['AGING_MAX'];
       $totHutangJatuhTempo=$aginTOTAL-$aginFUTURE;
        
        $fce->Close();	
        $sap->Close();	

?>

<title>Aplikasi SGG Online: Input Permintaan Pembelian :)</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="ba1">Input Permintaan Pembelian </th>
</tr></table></div>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Form Input Permintaan Pembelian </th>
</tr>
</table></div>
<div align="center">
<form  action="komentar.php" method="post" name="tambah" id="tambah" onSubmit="cek_data();return document.hasil">
<table width="600" border="0" class="adminform" align="center">
 <tr>
    <td width="175"><strong>Tanggal </strong></td>
    <td width="12"><strong>:</strong></td>
    <td colspan="2"><?=gmdate("d-m-Y",time()+60*60*7);?><input type="hidden" id="tglnya" name="tglnya" value="<?=gmdate("d-m-Y",time()+60*60*7);?>" />
	</td>
 </tr>
 
 
<tr>
	<td ><strong>Sisa Kredit Limit</strong></td>
  <td  class="puso">:</td>
  <td colspan="2"><?php echo number_format($sisa); ?>
  <input type='hidden' name='sisa_cl' value='<?php echo ($sisa); ?>' >
  
  </td> 
</tr>

<tr>
	<td ><strong>Total Hutang Jatuh Tempo</strong></td>
  <td  class="puso">:</td>
  <td colspan="2"><?php echo number_format($aginTOTAL) ?>
  <input type='hidden' name='hutang_jt' value='<?php echo ($aginTOTAL*1); ?>' >
  
  </td>
</tr>
	
 <tr>
	<td ><strong>Umur Hutang jatuh Tempo Tertinggi</strong></td>
  <td  class="puso">:</td>
  <td colspan="2"><?php echo $aginAGING_MAX ?>/ hari
  <input type='hidden' name='umur_hutang_jt' value='<?php echo ($aginAGING_MAX*1); ?>' >
  
  
  </td>
</tr>
	
	
	
 <tr>
  <td colspan="4"><div id="creditlimit" ></div></td>
</tr>
 <tr>
   <td ><strong>Tipe Order</strong></td>
   <td><strong>:</strong></td>
   <td colspan="2">
   <?
    //$fungsi->or_order_type('ZOR'); 
		$mialo_inco= "select TIPE_ORDER, UPPER(TIPE_ORDER_NAMA) as TIPE_ORDER_NAMA  from TB_MASTER_TOP where KODE_DIST='$distr_id'  and del<>1 group  by TIPE_ORDER,UPPER(TIPE_ORDER_NAMA) ";
		$querymialo_inco= oci_parse($bo_conn, $mialo_inco);
		oci_execute($querymialo_inco);
			
			?>
   <select name="so_type" id="so_type" onChange="document.tambah.nama_so_type.value=this.options[this.selectedIndex].title;nolc();getplan()">
		<option value="">---Pilih Tipe Order---</option>
		<?
		while($datamia_inco=oci_fetch_array($querymialo_inco))
		{
		?>
		<option value='<? echo $datamia_inco['TIPE_ORDER'] ;?>' title='<? echo $datamia_inco['TIPE_ORDER_NAMA'] ?>' ><? echo $datamia_inco['TIPE_ORDER']."-".$datamia_inco['TIPE_ORDER_NAMA']; ?></option>
		<?
		}
		?>     
		
		</select>	
		<input type="hidden" value="Sales Standart" id="nama_so_type" name="nama_so_type" />&nbsp;&nbsp;
		<label id="labelc" style="visibility:hidden">Nomor LC : </label>
		<input type="text" style="visibility:hidden" value="" id="lcnum" name="lcnum" size="10"/></td>
 </tr>
	  <tr>
	<td ><strong>Distributor</strong></td>
      <td  class="puso">:</td>
      <td colspan="2"><input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="distrdiv">
	  <input name="sold_to" id="sold_to" type="text" size="10" maxlength="10" value="<?php  echo $distr_id;?>" readonly="true"/>
	  <input name="nama_sold_to" id="nama_sold_to" type="text" size="30" value="<?php echo $distr_nm ?>" readonly="true"/></div></td>
    </tr>
	
	
	<tr>
   <td ><strong>Plant</strong></td>
   <td><strong>:</strong></td>
   <td colspan="2">
  
		
		
		</select>	
		
		<div id='divplant'></div>
		<input name="nama_plant" type="hidden" id="nama_plant" readonly="true"size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;
      <input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
		<input type="text" style="visibility:hidden" value="" id="lcnum" name="lcnum" size="10"/></td>
 </tr>
 
 
 

 <tr>
    <td><strong>Syarat Penyerahan </div></strong></td>
    <td width="10"><strong>:</strong></td>
    <td width="153">
		
		 <div id='divinco'></div>
		<input type="hidden" value="" id="nama_kirim" name="nama_kirim" /></td>
  </tr>	
  

  
  <tr>
    <td><strong>Route</strong></td>
    <td width="10"><strong>:</strong></td>
       <td><div id="routediv">
          <select name="route" id="route">
            <option value="">---Route---</option>
          </select>	
			</td>
    </tr>	
	<tr>
	  <td><strong>Pembayaran</strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">	 
		<input name="top" type="text" id="top"  readonly >	
		<input name="nama_top" type="text" id="nama_top" readonly >	
	  </td>
	</tr>
	<tr>
	  <td><strong>Price List </strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">
		<input name="pricelist" type="text" id="pricelist" readonly>
		<input name="nama_pricelist" type="text" id="nama_pricelist" readonly>
		
		
		</td>
	</tr>	
	<tr>
	  <td><strong>Order Reason</strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">
		<input name="reason" type="text" id="reason" readonly>	
		<input name="nama_reason" type="text" id="nama_reason"  readonly>	
	  </td>
	</tr>
    <tr>
	  <td><strong>Note</strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2"><input name="keterangan" type="text" class="inputlabel" id="keterangan" value="<?=$keterangan?>" <?=$hanyabaca?> size="70" maxlength="250"/>  </td>
	</tr>
	<tr>
	  <td><strong></strong></td>
	  <td><strong></strong></td>
	<td colspan="2"><label id="lblso" style="visibility:hidden">No SO Lama : </label>
		<input type="text" style="visibility:hidden" value="" id="oldso" name="oldso" size="10" maxlength="10"/>
		<label id="lblprice" style="visibility:hidden">Price Date : </label>
	<input name="price_date" style="visibility:hidden" type="text" id="price_date" size=12 value="" onClick="return showCalendar('price_date');" />
	</td>	
	</tr>
</table> 
<br/><br/>
<table width="1500" align="center" class="adminlist">
  <tr>
	<th align="left" colspan="4"> Item Permintaan Pembelian</th>
  </tr>
</table>
<table width="1500" align="center" class="adminlist">
<tr class="quote">
	<td align="left">Kode Toko / Nama Toko </td>
	<td align="left">Kode Produk / Nama Produk</td>
	<td > QTY </td>
	<td> Tgl Kirim </td> 
	<td>No Kontrak</td> 
	<td> No Shp </td>
	<td> </td>
</tr>
<tr>
<td align="left">
	<div id="shiptodiv1">
      <input name="shipto1" type="text" class="inputlabel" id="shipto1" value="<?=$shipto?>" onChange="ketik_shipto(this)" maxlength="12" size="10"/>
      <input name="nama_shipto1" type="text" class="inputlabel" id="nama_shipto1" value="<?=$nama_shipto?>" readonly="true"  size="20"/>
	  <input type="text" value="" id="alamat1" name="alamat1" size="20"  readonly="true" >
	  <input type="hidden" value="" id="kode_distrik1" name="kode_distrik1" >
	  <input type="text" value="" id="nama_distrik1" name="nama_distrik1"  size="10"  readonly="true" >
	  <input type="hidden" value="" id="kode_prov1" name="kode_prov1" >
	  <input type="hidden" value="" id="nama_prov1" name="nama_prov1" >	  
      <input name="btn_shipto1" type="button" class="button" id="btn_shipto1" value="..." onClick="findshipto()"/>
      <input name="val_error_shipto1" type="hidden" id="val_error_shipto1" value="0" />
    </div>
</td>
<td align="left">
	<div id="produkdiv1">
	  <input name="produk1" type="text" class="inputlabel" id="produk1" value="<?=$produk?>" readonly onChange="ketik_produk(this,'1')" maxlength="20" size="12"/>
      <input name="nama_produk1" type="text" class="inputlabel" id="nama_produk1" value="<?=$nama_produk?>" readonly="true"  size="20"/>
	  <input name="uom1" type="text" class="inputlabel" id="uom1" value="<?=$uom?>" readonly="true"  size="4"/>
      <input name="btn_produk1" type="button" class="button" id="btn_produk1" value="..." onClick="findproduk('1')"/>
      <input name="val_error_produk1" type="hidden" id="val_error_produk1" value="0" />
    </div>
</td>
<td align="left">
	<input type="text" value="" id="com_qty1" name="com_qty1" size="6" maxlength="6" onBlur="javascript:IsNumeric(this)"/>
</td>
<td align="left">
	<input name="tgl_kirim1" type="text" id="tgl_kirim1" size=12 value="" onClick="return showCalendar('tgl_kirim1');" onBlur="cektanggal('tgl_kirim1')"/></td>
<td align="center">
		<input size="10" name="com_kontrak1" type="text" id="com_kontrak1" value="<?=$kontrak;?>" readonly="true"/>
		<input size="10" name="com_posnr1" type="hidden" id="com_posnr1" value="<?=$posnr;?>"/>
		<input size="10" name="com_sisa1" type="hidden" id="com_sisa1" value="<?=$sisa;?>"/>		
		<!--<input id="btn_kontrak1" name="btn_kontrak1" type="button" class="button" onClick="findkontrak('1');" disabled="disabled" value="..." />-->
		</td>
<td align="left">
		<input size="10" readonly name="com_shipment1" type="text" maxlength="10" id="com_shipment1" value="" />
</td>
<td width="100">
	<!--<input type="button" value=" + " name="tambah" onClick="return start_add();" />
	<input type="button" value="  -  " name="kurang" onClick="return stop_add();" /> -->
</td>
</tr>
</table>
<div id="coba"></div>
</div>
<br>
<br>
  <table width="600" align="center" class="adminform">
    <tr>
      <td colspan="2"><div align="center">
		<input type="hidden" value="1" name="jumlah" id="jumlah" />
        <input name="save" type="submit" class="button" id="save" value="Simpan" />
        <input name="action" type="hidden" value="buatsoadmin_oto" />
        <a href="tambahadmin.php" class="button">Cancel</a></div></td>
    </tr>
  </table>

</form>


<br/><br/>
 
</body>
</html>
