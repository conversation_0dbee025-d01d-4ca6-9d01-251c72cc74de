<?php
require_once 'Excel/reader.php';
require_once ('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

function showMessage($message)
{
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Result!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
        </div>
    </div>
<?php
}

if (isset($_POST['ids'])) {
    // Pisahkan jadi array
    $ids = $_POST['ids'];
}else{
    $ids = "";
}

if (isset($_POST['upload']) && $_FILES['excel_file']['error'] == 0) {
    

    $file_name = $_FILES['excel_file']['name'];
    $file_tmp = $_FILES['excel_file']['tmp_name'];

    $check = validate_excel($file_name, $file_tmp);
    if(!$check['status']){
        echo "<script>alert('" . $check['message'] ."');</script>";
        exit;
    }

    $data = new Spreadsheet_Excel_Reader();
    $data->setOutputEncoding('CP1251');
    $data->read($file_tmp);

    $sheet = $data->sheets[0];

    // Hitung jumlah baris
    $rows = count($sheet['cells']);
    if($rows <= 1){
        echo "Data upload kosong";
    }
    
    $msg = "";

    for ($i = 2; $i <= $rows; $i++) {
        $col1 = isset($sheet['cells'][$i][1]) ? $sheet['cells'][$i][1] : ''; // NO_SHP_TRN
        $col2 = isset($sheet['cells'][$i][2]) ? $sheet['cells'][$i][2] : ''; // TKNUM
        $col3 = isset($sheet['cells'][$i][3]) ? $sheet['cells'][$i][3] : ''; // FKNUM
        $col4 = isset($sheet['cells'][$i][4]) ? $sheet['cells'][$i][4] : ''; // FKPOS
        $col5 = isset($sheet['cells'][$i][5]) ? $sheet['cells'][$i][5] : ''; // NETWR
        $col6 = isset($sheet['cells'][$i][6]) ? $sheet['cells'][$i][6] : ''; // FKPTY
        $col7 = isset($sheet['cells'][$i][7]) ? $sheet['cells'][$i][7] : ''; // WERKS
        $col8 = isset($sheet['cells'][$i][8]) ? $sheet['cells'][$i][8] : ''; // EBELN
        $col9 = isset($sheet['cells'][$i][9]) ? $sheet['cells'][$i][9] : ''; // EBELP
        $col10 = isset($sheet['cells'][$i][10]) ? $sheet['cells'][$i][10] : ''; // LBLNI
        $col11 = isset($sheet['cells'][$i][11]) ? $sheet['cells'][$i][11] : ''; // STABR
        $col12 = isset($sheet['cells'][$i][12]) ? $sheet['cells'][$i][12] : ''; // KOSTL
        $col13 = isset($sheet['cells'][$i][13]) ? $sheet['cells'][$i][13] : ''; // PRCTR
        $col14 = isset($sheet['cells'][$i][14]) ? $sheet['cells'][$i][14] : ''; // BANKN
        $col15 = isset($sheet['cells'][$i][15]) ? $sheet['cells'][$i][15] : ''; // BANKA
        $col16 = isset($sheet['cells'][$i][16]) ? $sheet['cells'][$i][16] : ''; // BRNCH
        $col17 = isset($sheet['cells'][$i][17]) ? $sheet['cells'][$i][17] : ''; // SAKTO
        $col18 = isset($sheet['cells'][$i][18]) ? $sheet['cells'][$i][18] : ''; // NETWR_DO
        $col19 = isset($sheet['cells'][$i][19]) ? $sheet['cells'][$i][19] : ''; // NTGEW
        $col20 = isset($sheet['cells'][$i][20]) ? $sheet['cells'][$i][20] : ''; // LIFNR
        $col21 = isset($sheet['cells'][$i][21]) ? $sheet['cells'][$i][21] : ''; // NAME1
        $col22 = isset($sheet['cells'][$i][22]) ? $sheet['cells'][$i][22] : ''; // BVTYP
        $col23 = isset($sheet['cells'][$i][23]) ? $sheet['cells'][$i][23] : ''; // BRAN1
        $col24 = isset($sheet['cells'][$i][24]) ? $sheet['cells'][$i][24] : ''; // VTEXTX

        // $sql = "SELECT COUNT(*) AS JML FROM EX_INVOICE_SMBR_SHIPMENT_COST WHERE NO_SHP_TRN = '$col1'";
        // $stmt = oci_parse($conn, $sql);
        // oci_execute($stmt);
        // $row = oci_fetch_assoc($stmt);
        // if($row['JML'] > 0){
        //     $msg .= "$col1 => already exist <br>";
        //     continue;
        // }

        $sql = "DELETE FROM EX_INVOICE_SMBR_SHIPMENT_COST WHERE NO_SHP_TRN = '$col1'";
        $stmt = oci_parse($conn, $sql);
        oci_execute($stmt);
        $affected_rows = oci_num_rows($stmt);

        $field_names = array(
            'NO_SHP_TRN', 'TKNUM', 'EXTI1', 'FKNUM', 'FKPOS', 'NETWR', 'FKPTY',
            'WERKS', 'EBELN', 'EBELP', 'LBLNI', 'STABR', 'KOSTL', 'PRCTR',
            'BANKN', 'BANKA', 'BRNCH', 'SAKTO', 'NETWR_DO', 'NTGEW',
            'LIFNR', 'NAME1', 'BVTYP', 'BRAN1', 'VTEXTX'
        );

        $field_data = array(
            $col1, $col2, $col1, $col3, $col4, $col5, $col6,
            $col7, $col8, $col9, $col10, $col11, $col12, $col13,
            $col14, $col15, $col16, $col17, $col18, $col19,
            $col20, $col21, $col22, $col23, $col24
        );

        $tablename = "EX_INVOICE_SMBR_SHIPMENT_COST";

        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

        if($affected_rows > 0){
            $msg .= "$col1 => Updated <br>";
        }else{
            $msg .= "$col1 => Added <br>";
        }

        // 
    }

    showMessage($msg);
    exit;
}
?>

<!-- <form method="post" enctype="multipart/form-data">
    Pilih file Excel (.xls): <input type="file" name="excel_file" />
    <input type="submit" name="upload" value="Upload" />
</form>

<form name="export" method="post" action="recalculate_upload_template_xls.php">
    <input type="hidden" name="ids" id="idsInput" value="<?= $ids; ?>">
    <input type="submit" name="download_template" value="Download Template" />
</form> -->


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Data SPJ</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Upload Shipment Cost</th>
</tr></table>
</div>

<form method="post" name="upload" id="import" enctype="multipart/form-data">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
            <td class="puso">:</td>
            <td> <input name="excel_file" type="file"  class="button" accept=".xls, application/vnd.ms-excel" required></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="upload" type="submit"  class="button" value="Upload"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>

          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>

<form method="post" name="download" id="import" enctype="multipart/form-data" action="recalculate_upload_template_xls.php">
    <table width="800" align="center" class="adminform">
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>

        <tr>
            <td colspan="3" style="text-align:center;">
                <input type="hidden" name="ids" id="idsInput" value="<?= $ids; ?>">
                <input type="submit" name="download_template" value="Download Template" />
            </td>
        </tr>

        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
