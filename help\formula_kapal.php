<?
session_start();
// mulai session untuk semua formula
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<style type="text/css">
</style>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Untitled Document</title>
</head>

<body>
<p>&nbsp;</p>
<p>&nbsp;</p>
<? 
include ('../include/ex_fungsi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$action=$_POST['action'];
include ('formula_kapal.php');
?>
<div align="center" class="login">
<?
echo $show_ket;
?>
<br/>
<a href="<? echo $habis ?>">&lt;&lt; &nbsp;kembali&nbsp;&gt;&gt;</a>
</div>
<? include ('ekor.php'); ?>
</p>
<p>&nbsp;</p>
<p>&nbsp;</p>

</body>
</html>

<?if($_POST['cari']){
//============================================================================================================================
switch($action){
case "update_kapal":
	$user_id=$_SESSION['user_id'];
	$user_name=$_SESSION['user_name'];
	$ke_cek = 0;
	$total_cek 	= $_POST['total'];
	$kapal 		= $_POST['NAMA_KAPAL'];
	$supir 		= $_POST['SUPIR'];
	$shipto 	= $_POST['SHIP_TO'];
	$nm_shipto 	= $_POST['NAMA_SHIP_TO'];
	$shipto 	= $_POST['SHIP_TO'];
	$nm_soldto 	= $_POST['NAMA_SOLD_TO'];
	

	for($i=0;$i<$total_cek;$i++){
		$idke="id_app".$i;
	
		if(isset($_POST[$idke])){
			$ke_cek ++;
			$id_data=$_POST[$idke];
			if ($ke_cek == 1){
			$sql_in .= " AND NO_SHP_TRN IN (";
			$sql_in .= "'$id_data'";
			}else
			$sql_in .= " , '$id_data'";
		
		}
	}
		if ($ke_cek >= 1)
		$sql_in .= " )";
		else
		$sql_in = " AND NO_SHP_TRN = '1'";
	
	}

if ($ke_cek >= 1){


$sql_del ="UPDATE EX_TRANS_HDR SET SOLD_TO = $shipto, NAMA_SHIP_TO = $nm_shipto, NAMA_KAPAL = $sopir WHERE SOLD_TO IS NULL AND DELETE_MARK = '0' $sql_in";
echo $sql_del;
$query_del= oci_parse($conn, $sql_del);
oci_execute($query_del);

	$sql= "SELECT NO_SHP_TRN,SOLD_TO,SHIP_TO,QTY_SHP,PLANT FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' $sql_in  ";
	echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_shp_x=$row[NO_SHP_TRN]; 
		$qty_v=$row[QTY_SHP];
		$sold_to_v=$row[SOLD_TO];
		if ($sold_to_v == '0000002403' or $sold_to_v == '0000007403')$hajar = "T";
		else $hajar = "F";
		
		$sql_hdr= "SELECT * FROM EX_TRANS_HDR WHERE NO_SHP_TRN = '$no_shp_x' ";
		$query_hdr= oci_parse($conn, $sql_hdr);
		oci_execute($query_hdr);
	
		$row_dtl=oci_fetch_array($query_hdr);
		$no_so_v=$row_dtl[NO_SO];  
		$no_po_v=$row_dtl[NO_PO];  
		$posnr_v=$row_dtl[POSNR];  
		$kode_prod=$row_dtl[KODE_PRODUK];
		$kode_prod_cek = substr($kode_prod, 0, -5);

                        
					$mwsbp_do_in= 0;// pajak di nolkan
					$harga_tebus = $netwr_do_in + $mwsbp_do_in;//ambil harga satuan
					if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $exti1_in == $no_shp_x and $stabr_in == "C" and $lifnr_in != "" and $bvtyp_in != "" or $hajar == "T"){
						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200"  or $hajar == "T")){
							if ($cek_inke == 0){
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x"); 
							$tablename="EX_TRANS_COST";
							$fungsi->delete($conn,$tablename,$field_id,$value_id);
							}
						$cek_inke = 1;
	
						$field_names=array('NO_SHP_TRN','KODE_SHP_COST','SHP_COST','NO_ENTRY_SHEET','TYPE','FKPOS','DELETE_MARK','EBELN','EBELP');
						$field_data=array("$no_shp_x","$fknum_in","$netwr_in","$lblni_in","$fkpty_in","$fkpos_in","0","$ebeln_in","$ebelp_in"); 
						$tablename="EX_TRANS_COST";
						$fungsi->insert($conn,$field_names,$field_data,$tablename);  
						}
					}				
				
				}
				
					$tipe = "";
					$msg = "";
					$tipe=$fce->RETURN["TYPE"];			
					$msg=$fce->RETURN["MESSAGE"];	
				
					if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $no_shp_x == $exti1_in and  $stabr_in == "C" and $lifnr_in != "" or $hajar == "T"){

						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200" or $hajar == "T" )){

							$tarif_cost=round($netwr_in/$qty_v);
							$field_names=array('STATUS2','TANGGAL_SIAP_TAGIH','NO_SHP_SAP','SHP_COST','KODE_SHP_COST','NO_ENTRY_SHEET','TARIF_COST','EBELN','EBELP','KOSTL','PRCTR','NO_REK_DIS','NAMA_BANK_DIS','BANK_CABANG_DIS','HARGA_TEBUS','KET_ERROR','NO_GL_SHP','PLANT','UM_REZ','QTY_KTG_RUSAK','QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG','TOTAL_SEMEN_RUSAK','PDPKS','TOTAL_KLAIM_SEMEN','TOTAL_KLAIM_ALL','TANGGAL_DATANG','TANGGAL_BONGKAR','PENGELOLA','NAMA_PENGELOLA','BVTYP','KODE_KECAMATAN','NAMA_KECAMATAN');
							$field_data=array("OPEN","SYSDATE","$tknum_in","$total_netwr","$fknum_in","$lblni_in","$tarif_cost","$ebeln_in","$ebelp_in","$kostl_in","$prctr_in","$bankn_in","$banka_in","$brnch_in","$harga_tebus","OK","$sakto_in","$werks_in","$ntgew_in","","","","","","","","","","","","$lifnr_in","$name1_in","$bvtyp_in","$bran1_in","$vtextx_in"); 		
							$tablename="EX_TRANS_HDR";
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");
							$fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
							$show_ket.=" OK SHP $no_shp_x, ";
							$show_ket.= '<br>';
						}else{
							$show_ket.="Gagal Update SHP $no_turunan, ";
							if ($tipe == 'E')
							$show_ket.= $msg;
							else $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
							$show_ket.= '<br>';
							$msg .="Gagal Update SHP $no_turunan";
							$field_names=array('KET_ERROR');
							$field_data=array("$msg"); 		
							$tablename="EX_TRANS_HDR";
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");
							$fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
						
						}
					}else
						$show_ket.="Gagal Update SHP $no_turunan, ";
						if ($tipe == 'E')
						$show_ket.= $msg;
						else $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
						$show_ket.= '<br>';
						$msg .="Gagal Update SHP $no_turunan";
						$field_names=array('KET_ERROR');
						$field_data=array("$msg"); 		
						$tablename="EX_TRANS_HDR";
						$field_id=array('NO_SHP_TRN');
						$value_id=array("$no_shp_x");
						$fungsi->update($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
		}
					
			$fce->Close();	

	$habis = "update_kapal.php";
	break;
}
//============================================================================================================================

