<?
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

//$halaman_id=1740;//dev
//$halaman_id=2875;//prod
$user_id=$_SESSION['user_id'];
$distr_id=$_SESSION['distr_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$fungsi->sapcode($distr_id);
$distr_nm=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","DISTRIBUTOR_ID",$distr_id,"NAMA_DISTRIBUTOR");
$dirr = $_SERVER['PHP_SELF'];    
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
                <SCRIPT LANGUAGE="JavaScript">
            
                    alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
                
                 </SCRIPT> 

     <a href="../index.php">Login....</a>
<?

exit();
}
//$action_page=$fungsi->security($conn,$user_id,$halaman_id);

$sold_to=$distr_id;
$nama_sold_to=$distr_nm;
$cara_bayar='CREDIT';
$tglkirimnext=date("d-m-Y");
$halaman_aksi = "komentar_pp_royalty.php";
// formulasocc();
//////////////////////////////////////////
// $cr_limit = number_format($fungsi->or_creditlimit($distr_id,$user_org));
// $cr_limit = str_replace( ',', '', $cr_limit );


    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
    if ($sap->GetStatus() != SAPRFC_OK ) {
       $sap->PrintStatus();
       exit;
    }

$fce = $sap->NewFunction ("Z_CREDIT_EXPOSURE");
if ($fce == false ) {
   $sap->PrintStatus();
   exit;
}
// entri parameter
        // $connfc=$this->or_koneksi();
        $orgmm=$fungsi->findOneByOne($conn,"OR_COM_COAREA","COM",$user_org,"CO_AREA"); 
        if($orgmm!=''){
        $fce->X_KKBER = $orgmm;
        $fce->X_KUNNR = $distr_id;//$distributor;
        }else{
        $fce->X_KKBER = $user_org;
        $fce->X_KUNNR = $distr_id;//$distributor;
        }
$fce->X_DATE_CREDIT_EXPOSURE='31.12.9999';
$fce->Call();
if ($fce->GetStatus() == SAPRFC_OK ) {		
        $delta_cl=$fce->Z_DELTA_TO_LIMIT2*100;
}else
        $fce->PrintStatus();

$fce->Close();	
$sap->Close();	

///////////////
$tescl = str_replace( '.', '', $delta_cl);
$cr_limit = intval($tescl);
// echo $tescl = str_replace( ',', '', $tescl);
if($cr_limit<0){
    // $tescl=str_replace(',', '', $fungsi->or_creditlimit($distr_id,$user_org))*-1;
    // $creditlimitnya=number_format(intval($tescl),2,'.',',');
    $creditlimitnya = number_format($fungsi->or_creditlimit($distr_id,$user_org)*-1,2,'.',',');
    // $tescl = number_format($fungsi->or_creditlimit($distr_id,$user_org))*-1;
    // $creditlimitnya = str_replace( ',', '', $tescl);
}else{
    // $tescl=str_replace(',', '', $fungsi->or_creditlimit($distr_id,$user_org));
    // $creditlimitnya=number_format(intval($tescl),2,'.',',');
    $creditlimitnya = number_format($fungsi->or_creditlimit($distr_id,$user_org),2,'.',',');
    // $tescl = number_format($fungsi->or_creditlimit($distr_id,$user_org));
    // $creditlimitnya = str_replace( ',', '', $tescl);
}
$cr_limit=$creditlimitnya;
///////////////

$mysql="SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='MIN_CREDIT_LIMIT' and delete_mark='0'";
// $conn = $this->or_koneksi();					
$mysql_set=oci_parse($conn,$mysql);
oci_execute($mysql_set);
$row_cr=oci_fetch_assoc($mysql_set);
$cr_min_limit=$row_cr[CONFIG];

$cr_min_limit = str_replace( ',', '', $cr_min_limit );
///////////////////////////////////////////
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/template_css.css" rel="stylesheet" type="text/css" />
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script type="text/javascript" src="../include/jquery.min.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

// function clickIE()
 
// {if (document.all)
// {(message);return false;}}
 
// function clickNS(e) {
// if
// (document.layers||(document.getElementById&&!document.all))
// {
// if (e.which==2||e.which==3) {(message);return false;}}}
// if (document.layers)
// {document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
// else
// {document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
// document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }


</script>
<script language="javascript">
<!--
var nokontrak = '';
var sisaqtykontrak = 0;
var kontrakshipto='';
var kontrakmaterial = '';
var kontrakposnr = '';
var kodeprop = '';
var tglkontrakvalid = '';
var harianbulanan = 'harian';
var j=1;
    function start_add() {
	//if (j==11){alert('ma\'af maksimal 10');return false;}
        j++;
        var readolyshipto='';
        var readolyproduk= '';
        var dsbshipto = '';
        var dsbproduk = '';
        if (kontrakmaterial != '') {
            readolyproduk = 'readonly';
            dsbproduk = 'disabled';
        }
        if (kontrakshipto != '') {
            readolyshipto = 'readonly';
            dsbshipto = 'disabled';
        }
	var cek=j-1;
                if(validasi('shipto'+cek+'','','R','produk'+cek+'','','R','qty'+cek+'','','RisNum')){

		if(cek>1){
			for (var i = 1; i < cek; i++){
				var obj_tokoi = document.getElementById('shipto'+i+'');
				var nilai_tokoi = obj_tokoi.value;	
	
				var obj_tokocek = document.getElementById('shipto'+cek+'');
				var nilai_tokocek = obj_tokocek.value;	

				var obj_produki = document.getElementById('produk'+i+'');
				var nilai_produki = obj_produki.value;	
	
				var obj_produkcek = document.getElementById('produk'+cek+'');
				var nilai_produkcek = obj_produkcek.value;	
                                
                                var obj_tgl_kirimi = document.getElementById('tgl_kirim'+i+'');
				var nilai_tgl_kirimi = obj_tgl_kirimi.value;	

				var obj_tgl_kirimcek = document.getElementById('tgl_kirim'+cek+'');
				var nilai_tgl_kirimcek = obj_tgl_kirimcek.value;

				if (nilai_tokoi == nilai_tokocek && nilai_produki == nilai_produkcek && nilai_tgl_kirimi == nilai_tgl_kirimcek){
						alert("Data Toko, Produk, Plant dan Tanggal Kirim Telah Diinputkan data ke "+cek+" \n Silahkan Input Ulang...");
						document.hasil = false;
						return false;
				}
                                }
			} 

		
		var body1 = document.getElementById("coba");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "dd"+j); 
                var orgvl = document.getElementById("org");
                newdiv.innerHTML='<table width = "1195" class="adminlist">\n\
                                <tr>\n\
                                <td align="left">\n\
                                    <input name="tgl_kirim'+j+'" type="text" id="tgl_kirim'+j+'" size=12 value="" onClick="return showCalendar(\'tgl_kirim'+j+'\');"  onBlur="cektanggal(\'tgl_kirim'+j+'\');loadSISATARGET('+j+');" />\n\
                                </td>\n\
                                <td align="left">\n\
                                        <input type="text" value="" id="fsisa_target'+j+'" name="fsisa_target'+j+'" size="5" readonly="true" />&nbsp;\n\
                                        <input name="sisa_target'+j+'" type="hidden" class="inputlabel" id="sisa_target'+j+'" value="" readonly="true" size="5" />\n\
                                </td>\n\
                                <td align="left">\n\
                                <div id="shiptodiv'+j+'">\n\
                                <input type="text" class="inputlabel" value="'+kontrakshipto+'" size="10" id="shipto'+j+'" name="shipto'+j+'" onchange="ketik_shipto(this,'+j+')" '+readolyshipto+' />\n\
                                <input type="text" value="" size="20" class="inputlabel" id="nama_shipto'+j+'" readonly="true" name="nama_shipto'+j+'">\n\
                                <input type="text" value="" id="alamat'+j+'" name="alamat'+j+'" readonly="true" >\n\
                                <input type="hidden" value="" id="kode_distrik'+j+'" name="kode_distrik'+j+'" >\n\
                                <input type="text" value="" id="nama_distrik'+j+'" name="nama_distrik'+j+'" readonly="true" >\n\
                                <input type="hidden" value="" id="kode_prov'+j+'" name="kode_prov'+j+'" >\n\
                                <input type="hidden" value="" id="nama_prov'+j+'" name="nama_prov'+j+'" >\n\
                                <input type="hidden" value="" id="typetruck'+j+'" name="typetruck'+j+'" >\n\
                                <input name="btn_shipto'+j+'" type="button" class="button" id="btn_shipto'+j+'" value="..." onClick="findshipto('+j+')" '+dsbshipto+'>\n\
                                </div>\n\
                                </td>\n\
                                <td align="left">\n\
                                <div id="produkdiv'+j+'">\n\
                                <input type="text" value="'+kontrakmaterial+'" size="12" class="inputlabel" id="produk'+j+'" name="produk'+j+'" onchange="ketik_produk(this,'+j+')" '+readolyproduk+' />\n\
                                <input type="text" value="" class="inputlabel" readonly="true" id="nama_produk'+j+'" name="nama_produk'+j+'" size="20"/>\n\
                                <input type="text" value="" class="inputlabel" readonly="true" id="uom'+j+'" name="uom'+j+'" size="4"/>\n\
                                <input name="qto'+j+'" type="hidden" class="inputlabel" id="qto'+j+'" value="" readonly="true"  size="4"/>\n\
                                <input name="btn_produk'+j+'" type="button" class="button" id="btn_produk'+j+'" value="..." onClick="findproduk('+j+')" '+dsbproduk+' >\n\
                                </div>\n\
                                </td>\n\
                                <td align="left">\n\
                                <input type="text" value="" id="qty'+j+'" name="qty'+j+'" size="6" maxlength="6" onBlur="javascript:IsNumeric(this);quantum_soccv2(this,'+j+');"/>\n\
                                <input type="hidden" readonly="true" value="" id="qtycek'+j+'" name="qtycek'+j+'" size="6" maxlength="6"/>\n\
                                </td>\n\
                                <td align="center"><input size="10" name="com_kontrak'+j+'" type="text" id="com_kontrak'+j+'" value="'+nokontrak+'" readonly="true"/></td>\n\
                                <td width="100">\n\
                                        <input type="button" onclick="return stop_add('+j+');" id="kurang'+j+'" name="kurang'+j+'" value="  -  ">\n\
                                </td>\n\
                                </tr>\n\
                                </table>';
                body1.appendChild(newdiv);
		document.getElementById('jumlah').value=j;
		}else{
		j--;
		}
    }


    function cekBrand(params) {
        // jika dia curah atau mortar maka brand di hide 
        // alert(params);
        if (params == 'CURAH' || params == 'MORTAR ZAK' || params == 'MORTAR CURAH') {
            // div_brand.style.display = 'none';
            const inputElement = document.getElementById('brand');
            inputElement.disabled = true;
            $("#brand").val('');
            
        }else{
            const inputElement = document.getElementById('brand');
            inputElement.disabled = false;
        }
    }

    function cekPlantKota(){
        var jenis_krm = $("#jenis_kirim").val();

        if (jenis_krm == 'FOT') {
             document.getElementById('kota_tujuan').selectedIndex = 0;     
        }
    }



    function cekPlanya(params) {

        document.getElementById('kota_tujuan').value = ''; 
        // reset dropdown select option plant
        var select = document.getElementById('plant');
        select.value = '';
        select.innerHTML = '';
        var defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.text = '-- Pilih Plant --';
        select.appendChild(defaultOption);

        // reset material
        document.getElementById('produk1').value = '';
        document.getElementById('nama_produk1').value = '';
        document.getElementById('uom1').value = '';
        // reset span plant_warning
        const warningElem = document.getElementById('plant_warning');
        if (warningElem) {
            warningElem.innerHTML = '';
        }

        const plantRow = document.getElementById('plant_row');
        // document.getElementById('plant_info').innerHTML = ''; 
       if (params == 'FRC' || params == 'CIF') {
        const inputElement = document.getElementById('plant');
        inputElement.disabled = true;
        // hide plant if frc or cif
        if (plantRow) {
            plantRow.style.display = 'none';
        }

       }else{
        const inputElement = document.getElementById('plant');
        inputElement.disabled = false;
        if (plantRow) {
            plantRow.style.display = '';
        }
       }
    }

    function tes() {
        var plant=$("#plant").val();
        alert(plant);
    }

    function cekQtyBulanan() {

        var brand = encodeURIComponent($("#brand").val());
        var distrik = $("#kota_tujuan").val();
        var material = $("#produk1").val(); //jenis_kemasan tgl_kirim1
        var incoterm = $("#jenis_kirim").val();
        var jkemasan=$("#jenis_kemasan").val();
        var tanggal=$("#tgl_kirim1").val();
        var plant=$("#plant").val();
        var com =document.getElementById('org');
        var qty =document.getElementById('qty1');

        var strURL="loadexplanningpp_royalty.php?jkemasan="+jkemasan+"&distrik="+distrik+'&material='+material+'&brand='+brand+'&incoterm='+incoterm+'&tanggal='+tanggal+'&plant='+plant+'&org='+com.value+'&qty='+qty.value+'&action=getQtyBulanan';
        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            // only if "OK"
                            if (req.status == 200) { 
                                lodingact(1); 
                                if (req.responseText != '') {
                                alert(req.responseText);
                                document.getElementById('tgl_kirim1').value = '';
                                document.getElementById('qty1').value = ''; 
                                }
                            } else {
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
        }
    }

    function cek_last(id_cek) {
		var obj = document.getElementById(id_cek);
		var cek = obj.value;                
                var counter=0;
		
		if(validasi('shipto'+cek+'','','R','produk'+cek+'','','R','qty'+cek+'','','RisNum')){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_tokoi = document.getElementById('shipto'+i+'');
					var nilai_tokoi = obj_tokoi.value;	
		
					var obj_tokocek = document.getElementById('shipto'+cek+'');
					var nilai_tokocek = obj_tokocek.value;	
	
					var obj_produki = document.getElementById('produk'+i+'');
					var nilai_produki = obj_produki.value;	
		
					var obj_produkcek = document.getElementById('produk'+cek+'');
					var nilai_produkcek = obj_produkcek.value;                                        
                                      
                                        var obj_tgl_kirimi = document.getElementById('tgl_kirim'+i+'');
                                        var nilai_tgl_kirimi = obj_tgl_kirimi.value;	

                                        var obj_tgl_kirimcek = document.getElementById('tgl_kirim'+cek+'');
                                        var nilai_tgl_kirimcek = obj_tgl_kirimcek.value;        
					if (nilai_tokoi == nilai_tokocek && nilai_produki == nilai_produkcek && nilai_tgl_kirimi == nilai_tgl_kirimcek){
						alert("Data Toko, Produk dan Tanggal Kirim Telah Diinputkan data ke "+cek+" \n Silahkan Input Ulang...");
						document.hasil = false;
						return false;
					}
                                        
                    var ceks = document.getElementById('cek');
                    var ceks = ceks.value;
                    if(ceks !=0 && nilai_tokoi != nilai_tokocek){
                        alert("Kode Shipto Khusus Tidak Sama \n Silahkan Input Ulang...");
                        document.hasil = false;
                        return false;
                    }

                    if (obj_tgl_kirimi == ''){
                        document.hasil = false;
                        return false;
                    }
				}
			}
		}else{
                    document.hasil = false;
                    return false;
		}	
                
                getsisasementara();
                var tgl_str='';
                for (var t = 1; t <= cek; t++){
                    var targetqtykcek = document.getElementById('fsisa_target'+t+'');
                    var Hceksisa = parseFloat(targetqtykcek.value);                    
                    var rtgl_kirimcek = document.getElementById('tgl_kirim'+t+'');
                    var ntgl_kirimcek = rtgl_kirimcek.value;
                    if(Hceksisa < 0){
                        counter += 1;
                        tgl_str +=" "+ntgl_kirimcek+" ";
                    }
                }
                
                if(counter>0){                  
                   alert("Target Distributor kurang pada tanggal "+tgl_str+" ..!!"); 
                   document.hasil = false;
                   return false;
                }
                
    }
        function tukar(idtk){
            var objrec = document.getElementById('jumlah');
            var cekrec = objrec.value;
            var ty = 0;
            for (var a = 2; a < cekrec + 1; a++){  
                ty=a-1;
                if(idtk!=a && a>=idtk){            
                    if(ty<=cekrec){
                        //alert (a+" ke "+ty);
                        document.getElementById("dd"+a).setAttribute("id", "dd"+ty);              
                        document.getElementById("tgl_kirim"+a).setAttribute("onclick", "return showCalendar('tgl_kirim"+ty+"');");
                        document.getElementById("tgl_kirim"+a).setAttribute("onBlur", "cektanggal('tgl_kirim"+ty+"');loadSISATARGET('"+ty+"');");
                        document.getElementById("tgl_kirim"+a).setAttribute("name", "tgl_kirim"+ty);
                        document.getElementById("tgl_kirim"+a).setAttribute("id", "tgl_kirim"+ty);
                        document.getElementById("fsisa_target"+a).setAttribute("name", "fsisa_target"+ty);
                        document.getElementById("fsisa_target"+a).setAttribute("id", "fsisa_target"+ty);
                        document.getElementById("sisa_target"+a).setAttribute("name", "sisa_target"+ty);
                        document.getElementById("sisa_target"+a).setAttribute("id", "sisa_target"+ty);                    
                        document.getElementById("shiptodiv"+a).setAttribute("id", "shiptodiv"+ty);
                        document.getElementById("shipto"+a).setAttribute("onChange", "ketik_shipto(this,'"+ty+"');");
                        document.getElementById("shipto"+a).setAttribute("name", "shipto"+ty);
                        document.getElementById("shipto"+a).setAttribute("id", "shipto"+ty);
                        document.getElementById("nama_shipto"+a).setAttribute("name", "nama_shipto"+ty);
                        document.getElementById("nama_shipto"+a).setAttribute("id", "nama_shipto"+ty);
                        document.getElementById("alamat"+a).setAttribute("name", "alamat"+ty);
                        document.getElementById("alamat"+a).setAttribute("id", "alamat"+ty);
                        document.getElementById("kode_distrik"+a).setAttribute("name", "kode_distrik"+ty);
                        document.getElementById("kode_distrik"+a).setAttribute("id", "kode_distrik"+ty);
                        document.getElementById("nama_distrik"+a).setAttribute("name", "nama_distrik"+ty);
                        document.getElementById("nama_distrik"+a).setAttribute("id", "nama_distrik"+ty);
                        document.getElementById("kode_prov"+a).setAttribute("name", "kode_prov"+ty);
                        document.getElementById("kode_prov"+a).setAttribute("id", "kode_prov"+ty);
                        document.getElementById("nama_prov"+a).setAttribute("name", "nama_prov"+ty);
                        document.getElementById("nama_prov"+a).setAttribute("id", "nama_prov"+ty);
                        document.getElementById("typetruck"+a).setAttribute("name", "typetruck"+ty);
                        document.getElementById("typetruck"+a).setAttribute("id", "typetruck"+ty);
                        document.getElementById("btn_shipto"+a).setAttribute("onClick", "findshipto('"+ty+"');");
                        document.getElementById("btn_shipto"+a).setAttribute("name", "btn_shipto"+ty);
                        document.getElementById("btn_shipto"+a).setAttribute("id", "btn_shipto"+ty);                    
                        document.getElementById("produkdiv"+a).setAttribute("id", "produkdiv"+ty);
                        document.getElementById("produk"+a).setAttribute("onchange", "ketik_produk(this,'"+ty+"');");
                        document.getElementById("produk"+a).setAttribute("name", "produk"+ty);
                        document.getElementById("produk"+a).setAttribute("id", "produk"+ty);
                        document.getElementById("nama_produk"+a).setAttribute("name", "nama_produk"+ty);
                        document.getElementById("nama_produk"+a).setAttribute("id", "nama_produk"+ty);
                        document.getElementById("uom"+a).setAttribute("name", "uom"+ty);
                        document.getElementById("uom"+a).setAttribute("id", "uom"+ty);
                        document.getElementById("qto"+a).setAttribute("name", "qto"+ty);
                        document.getElementById("qto"+a).setAttribute("id", "qto"+ty);
                        document.getElementById("btn_produk"+a).setAttribute("onClick", "findproduk('"+ty+"');");
                        document.getElementById("btn_produk"+a).setAttribute("name", "btn_produk"+ty);
                        document.getElementById("btn_produk"+a).setAttribute("id", "btn_produk"+ty);
                        document.getElementById("qty"+a).setAttribute("onBlur", "javascript:IsNumeric(this);quantum_soccv2(this,'"+ty+"');");
                        document.getElementById("qty"+a).setAttribute("name", "qty"+ty);
                        document.getElementById("qty"+a).setAttribute("id", "qty"+ty);
                        document.getElementById("qtycek"+a).setAttribute("name", "qtycek"+ty);
                        document.getElementById("qtycek"+a).setAttribute("id", "qtycek"+ty);
                        document.getElementById("kurang"+a).setAttribute("onclick", "return stop_add("+ty+");");
                        document.getElementById("kurang"+a).setAttribute("name", "kurang"+ty);
                        document.getElementById("kurang"+a).setAttribute("id", "kurang"+ty);
                    }                    
                }
            }                        
        }
	function stop_add(obj)
	{
            if (j==1){alert('Maaf Minimal 1 Item Permintaan..');return false;}
            //k=j;
            //k=k.obj.value;
            k=obj;
                var body1 = document.getElementById("coba");
                var buang = document.getElementById("dd"+k);
                body1.removeChild(buang);
            j=j-1;
            document.tambahNew.jumlah.value=j;
            if(j>1){
                tukar(obj);
            }
            getsisasementara();
	}
	
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
     document.getElementById('qty1').value='';
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
                  document.getElementById('qty1').value='';
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
         document.getElementById('qty1').value='';
		 return false;
		 }	  
	 } 
   }

   function IsNumeric2(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
     document.getElementById('qty1').value='';
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
                  document.getElementById('qty1').value='';
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
         document.getElementById('qty1').value='';
		 return false;
		 }	  
	 } 
   }

function lodingact(obj){    
    //alert(obj);
    if(obj==1){
        document.getElementById("loadingd").style.display = "none";
    }else{
        document.getElementById("loadingd").style.display = "block";
    }
}
function resetQty(ke){
     document.getElementById('qty'+ke).value='';
     document.getElementById('qtycek'+ke).value='';
}
 
function resetForm(tmreset){
    if(tmreset==1){
        document.getElementById('plant').selectedIndex = 0; 
        document.getElementById('kota_tujuan').selectedIndex = 0;        
        // document.getElementById('top').selectedIndex = 0;   
        $("#top").val('');
        $("#nama_top").val('');     
        document.getElementById('com_kontrak').value = '';        
        document.getElementById('com_posnr').value = '';        
        document.getElementById('com_sisa').value = '';        
    }
    var obj = document.getElementById('jumlah');
    var cek = obj.value;
    for (var i = 1; i <= cek; i++){	
        document.getElementById('tgl_kirim'+i+'').value='';
        document.getElementById('fsisa_target'+i+'').value='';
        document.getElementById('sisa_target'+i+'').value='';
        document.getElementById('shipto'+i+'').value='';
        document.getElementById('nama_shipto'+i+'').value='';
        document.getElementById('alamat'+i+'').value='';
        document.getElementById('kode_distrik'+i+'').value='';
        document.getElementById('nama_distrik'+i+'').value='';
        document.getElementById('kode_prov'+i+'').value='';
        document.getElementById('nama_prov'+i+'').value='';
        document.getElementById('typetruck'+i+'').value='';
        // document.getElementById('produk'+i+'').value='';
        // document.getElementById('nama_produk'+i+'').value='';
        // document.getElementById('uom'+i+'').value='';
        // document.getElementById('qto'+i+'').value='';
        document.getElementById('qty'+i+'').value='';
        document.getElementById('com_kontrak'+i+'').value='';
    }
    var jml = document.getElementById('jumlah');
    for(var i = 1 ;i <= jml.value;i++){
        $("#btn_shipto"+i).attr('disabled',false);
        $("#btn_produk"+i).attr('disabled',false);
        $("#shipto"+i).attr('readonly',false);
        $("#produk"+i).attr('readonly',false);
    }
}


function load_top(ke) {
    if(ke!='1'){ 
        showhideprice();
        document.getElementById('com_kontrak').value = '';        
        document.getElementById('com_posnr').value = '';        
        document.getElementById('com_sisa').value = ''; 
    }else{
        showhideprice(1);
    }    
    // var jkemasan=document.getElementById('jenis_kemasan');    
    // var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';  
    // var route = document.getElementById('route'); 
    // var com=document.getElementById('org');
    // var shipto=document.getElementById('shipto1');
    // var plant=document.getElementById('plant');
    // var so_type = document.getElementById('so_type');

    var distrik=document.getElementById('kota_tujuan');
        var val_distrik=distrik.options[distrik.selectedIndex].value;
        var plant=document.getElementById('plant');
        var val_plant=plant.value; 
        var shiptoval_plant=plant.options[plant.selectedIndex].value;
        var pricelist=document.getElementById('pricelist');
        var val_pricelist=pricelist.options[pricelist.selectedIndex].value;
        
    var jkemasan=document.getElementById('jenis_kemasan');    
    var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';  
    var route = document.getElementById('route'); 
    var shipto=document.getElementById('shipto1');
    
    var kodeprop = $("#kota_tujuan option:selected").data().prop;
    var dist=document.getElementById('sold_to');
    var com=document.getElementById('org');
    var so_type = document.getElementById('so_type'); 
    var _brand = encodeURIComponent($('#brand').val());
    //set tipe 
    if (!kodeprop || !route.value || !jkemasan.value) {
        $("#top").val('');
        $("#nama_top").val('');
        return false;
    }
    if (route.value == 'ZR0001') {
        routess = 'sea';
    }else{
        routess = 'land';
    }
    console.log(jkemasan.value,distid,kodeprop,routess,com.value,shipto.value,plant.value,so_type.value);

    // var strURL="loadexplanningpp_royalty.php?jkemasan="+jkemasan.value+"&distid="+distid+'&prop='+kodeprop+'&route='+routess+'&com='+com.value+'&shipto='+shipto.value+'&plant='+plant.value+'&so_type='+so_type.value+'&action=viewtopcombo';
    var strURL="loadexplanningpp_royalty.php?jkemasan="+jkemasan.value+"&distid="+distid+'&prop='+kodeprop+'&route='+routess+'&com='+com.value+'&shipto='+shipto.value+'&plant='+plant.value+'&so_type='+so_type.value+'&action=viewtopcombo&brand='+_brand;
    //  var strURL ="cari_shipto_ex.php?nourut="+ke+'&distrik='+distid+'&filplant='+val_plant+'&kodeprop='+kodeprop+'&jkemasan='+jkemasan.value+'&com='+com.value+'&distid='+distid+'&route='+routess+'&so_type='+so_type.value+'&pricelist='+val_pricelist+'&soldto='+dist.value;
    
    var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            // only if "OK"
                            if (req.status == 200) { 
                                lodingact(1); 
                                    document.getElementById('loadtopcombo').innerHTML=req.responseText; 
                                    //$("#nama_top").val($("#top option[value='"+req.responseText+"']").text());
                            } else {
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
    }
}
var tipe='121-301';
function onloadPlant(){
    // resetForm('1');  

    var jenis_krm = $("#jenis_kirim").val();
    if (jenis_krm == 'FRC' || jenis_krm == 'CIF') {
        const inputElement = document.getElementById('plant');
        inputElement.disabled = true;
        
       }else{
        const inputElement = document.getElementById('plant');
        inputElement.disabled = false;

        var jkemasan=document.getElementById('jenis_kemasan');  
   
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }  
    var com=document.getElementById('org'); 
    com.value = '<?=$user_org;?>';   
    var dist=document.getElementById('sold_to'); 
    var tgl1nya=document.getElementById('tgl1nya');   
    var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tgl1nya='+tgl1nya.value+'&produkin='+tipe+'&action=viewplant2';
    var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            // only if "OK"
                            if (req.status == 200) {
                                   lodingact(1);
                                    document.getElementById('loadplant').innerHTML=req.responseText;
                            } else {
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }				
            }			
            req.open("GET", strURL, true);
            req.send(null);
    }

       }

       
    
    // loadKota();
    // var val_plant=plant.options[plant.selectedIndex].value;
    // document.tambahNew.nama_plant.value=plant.options[plant.selectedIndex].title;
    //  loadShipcond(val_plant)
    var plant=document.getElementById('plant');
      loadShipcond(plant);
      load_top();
      cekharianbulanan(); 
}  
function xeccompany(){
    var comorg = document.getElementById('org');
    var comorgappplant = document.getElementById('plant').value;
    var orgpl = comorgappplant.substr(0, 1);
    if(orgpl=="")
        orgpl = comorg.value;
    else
        orgpl += "000";
    comorg.value=<?=$user_org;?>;
} 

function lihatPlant(params) {
    var selectElement = document.getElementById('plant');
    selectElement.selectedIndex = 0;
}

function loadShipcond(kd_plant){

    var com=document.getElementById('org');    
    var dist=document.getElementById('sold_to');
    var plant=document.getElementById('plant');
    var tgl1nya=document.getElementById('tgl1nya');
    var val_plant=plant.options[plant.selectedIndex].value;
    document.tambahNew.nama_plant.value=plant.options[plant.selectedIndex].title; 
        var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tgl1nya='+tgl1nya.value+'&plant='+val_plant+'&produkin='+tipe+'&action=viewshipcond';
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    lodingact(0);
                        if (req.readyState == 4) {                         
                                // only if "OK"
                                if (req.status == 200) {	
                                    lodingact(1);
                                    document.getElementById('loadshipcond').innerHTML=req.responseText;
                                } else {             
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                        }				
                }			
                req.open("GET", strURL, true);
                req.send(null);
        }
}

function setPlant() {
    if (incoterm != 'FRC' || incoterm != 'CIF') {
        const inputElement = document.getElementById('plant');
        document.getElementById('plantBayangan').value = inputElement.value; 
    }

}


function getPlant() {
    var brand = encodeURIComponent($("#brand").val());
    var distrik = $("#kota_tujuan").val();
    var material = $("#produk1").val();
    var incoterm = $("#jenis_kirim").val();
    var jkemasan=document.getElementById('jenis_kemasan');  
    
    
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }  

    if (brand == '') {
        brand = jkemasan.value;
    }
    var com=document.getElementById('org'); 
    com.value = '<?=$user_org;?>';   
    var dist=document.getElementById('sold_to'); 
    var tgl1nya=document.getElementById('tgl1nya');  

    if (incoterm == 'FRC' || incoterm == 'CIF') {
    var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tgl1nya='+tgl1nya.value+'&produkin='+tipe+'&brand='+brand+'&distrik='+distrik+'&material='+material+'&incoterm='+incoterm+'&action=getPlant';
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    lodingact(0);
                        if (req.readyState == 4) {                         
                                // only if "OK"
                                if (req.status == 200) {	
                                    lodingact(1);
            // ================ 
            document.getElementById('loadplant').innerHTML = req.responseText;
            const inputElement = document.getElementById('plant');
            // clear/restart span plan_warning
            const warningElem = document.getElementById('plant_warning');
            if (warningElem) {
                warningElem.innerHTML = '';
            }

            if (inputElement) {
                // cek jumlah option
                if (inputElement.options.length <= 1) {
                } else {
                    document.getElementById('plantBayangan').value = inputElement.value;
                    inputElement.disabled = true;

                    const plantRow = document.getElementById('plant_row');
                    if (plantRow) {
                        plantRow.style.display = 'none';
                    }
                }
                inputElement.readOnly = true;
            }
            // ============
                                } else {             
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                                const inputElement = document.getElementById('plant');
                                inputElement.readonly = true;   
                        }				
                }			
                req.open("GET", strURL, true);
                req.send(null);
        }
    }else{
        // var jenis_krm = $("#jenis_kirim").val();
    
        const inputElement = document.getElementById('plant');
        inputElement.disabled = false;

        var jkemasan=document.getElementById('jenis_kemasan');  
        var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tgl1nya='+tgl1nya.value+'&produkin='+tipe+'&brand='+brand+'&distrik='+distrik+'&material='+material+'&incoterm='+incoterm+'&action=getPlant';
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    lodingact(0);
                        if (req.readyState == 4) {                         
                                // only if "OK"
                                if (req.status == 200) {	
                                    lodingact(1);
                                    document.getElementById('loadplant').innerHTML=req.responseText;
                                    const inputElement = document.getElementById('plant');
                                    inputElement.disabled = false;
                                } else {             
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                                const inputElement = document.getElementById('plant');
                                inputElement.disabled = true;   
                        }			
                        const inputElement = document.getElementById('plant');
                        inputElement.disabled = false;	
                }			
                req.open("GET", strURL, true);
                req.send(null);
        }

    //     var val_plant =plant.options[plant.selectedIndex].value;
    //  document.tambahNew.nama_plant.value=plant.options[plant.selectedIndex].title;
    var plant=document.getElementById('plant');
    
    
    
}
    // loadShipcond(plant)
    // loadShipcond();
    // load_top();
    // cekharianbulanan(); 

    // loadKota();
    
}

function setPlantException(plantText){
    console.log(plantText);
    var plants = document.getElementById("plant").options;
    console.log(plants);
    var idx = 0;
    var newPlant = '<select name="plant" onclick="setPlant()" id="plant" onChange="clearData();">';
    newPlant += '<option value="" title="">---Pilih Plant---</option>';
    for(idx = 0; idx < plants.length; idx++){
        newPlant += '<option value="'+plants[idx].value+'">'+plants[idx].text+'</option>';
    }
    newText = plantText;
    var array = plantText.split(" ");
    newValue = array[0];
    newPlant += '<option value="'+newValue+'" selected>'+newText+'</option>';
    newPlant += '</select>';

    console.log(newPlant);

    document.getElementById("jenis_kirim").value = 'FOT';
    document.getElementById('loadplant').innerHTML=newPlant;
    // Hide multiple plant
    var plantInfoDiv = document.getElementById("plant_info");
    if (plantInfoDiv !== null) {
        plantInfoDiv.style.display = "none";
    }
}

function loadKota(){
    kodeprop = '';
    xeccompany();
    // resetForm('0');  
    var com=document.getElementById('org');    
    var dist=document.getElementById('sold_to');
    var plant=document.getElementById('plant');
    var tgl1nya=document.getElementById('tgl1nya');
    var listMaterial =document.getElementById('uom1');

    var brand = encodeURIComponent($("#brand").val());
    var material = $("#produk1").val();

    var jenis_krm = $("#jenis_kirim").val();
    if (jenis_krm == '') {
        alert('Silahkan pilih Jenis Pengiriman !!');
        // document.getElementById('plant').selectedIndex = 0; 
        return false;
    }
    var jkemasan=document.getElementById('jenis_kemasan');  
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }
    

    // var val_plant=plant.options[plant.selectedIndex].value;
    // document.tambahNew.nama_plant.value=plant.options[plant.selectedIndex].title;
    // if(val_plant!=''){
        var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tgl1nya='+tgl1nya.value+'&produkin='+tipe+'&brand='+brand+'&material='+material+'&action='+(['CURAH','MORTAR ZAK','MORTAR CURAH','M3','M3 PALLET1'].includes(jkemasan.value) ? 'getKota' : 'getKotaRoyalti');
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    lodingact(0);
                        if (req.readyState == 4) {                         
                                // only if "OK"
                                if (req.status == 200) {	
                                    lodingact(1);
                                    document.getElementById('loadkota').innerHTML=req.responseText;
                                } else {             
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                        }				
                }			
                req.open("GET", strURL, true);
                req.send(null);
        }
        
        // onloadPlant();
        //  loadShipcond(val_plant)
    //   }
    //   load_top();
    //   cekharianbulanan(); 
}


function resetKota(el){
    kodeprop = $("#kota_tujuan option:selected").data().prop;
    load_top();
    cekplant('kota_tujuan');
    // console.info(kodeprop);
    resetForm('0');
}

function loadSISATARGET(obj){
    var jkemasan=document.getElementById('jenis_kemasan');  
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }
    var so_type = document.getElementById('so_type').value; 
    var com=document.getElementById('org');    
    var dist=document.getElementById('sold_to');
    var tglkirim=document.getElementById('tgl_kirim'+obj);
    var plant=document.getElementById('plant');
    var val_plant=plant.options[plant.selectedIndex].value;
    var kotatj=document.getElementById('kota_tujuan');
    var val_kotatj=kotatj.options[kotatj.selectedIndex].value;
    var pricelist = $("#pricelist").val();
    var brand = encodeURIComponent($('#brand').val());
    if(tglkirim.value!='' && val_kotatj!='' && val_plant!=''){
                if(pricelist == '01' && so_type == 'ZOR' && tipe == '121-301'){
                    console.log("PP yang diinput : material ZAK, tipe SO standard, dan pricelist 01-(Project).");
                }else{
                    if (pricelist != '02' && pricelist != '06' && pricelist != '14' &&  pricelist != '64'  &&  pricelist != '68' && pricelist != '76' && pricelist != '77' && pricelist != '78' && pricelist != '10') {
                        if (!tglkontrakvalid) {
                        alert("Silahkan pilih kontrak terlebih dahulu..!!!");
                        resetForm('0');  
                        return false;
                        }
                    }
                }

        var plantValues = [];
        var inputs = document.querySelectorAll('[name^="prioritasplant_"]');
        inputs.forEach(function(input) {
            plantValues.push(input.value);
        });

        if (plantValues && plantValues.length > 0) {
            var failedCount = 0;
            for (indexPlant = 0; indexPlant < plantValues.length; indexPlant++) {
                (function(currentPlantValue, indexPlant) { 
                    if (tglkontrakvalid) {
                        var strURL="loadexplanning3_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tglkirim='+tglkirim.value+'&plant='+currentPlantValue+'&produkin='+tipe+'&kodedistrik='+val_kotatj+'&action=viewsisa&tglkontrak='+tglkontrakvalid+'&brand='+brand;
                        }else{
                        var strURL="loadexplanning3_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tglkirim='+tglkirim.value+'&plant='+currentPlantValue+'&produkin='+tipe+'&kodedistrik='+val_kotatj+'&action=viewsisa&brand='+brand;
                        }
                        var req = getXMLHTTP();
                        if (req) {
                                req.onreadystatechange = function() {
                                    lodingact(0);
                                        if (req.readyState == 4) {
                                                // only if "OK"
                                                if (req.status == 200) {    
                                                        lodingact(1);
                                                        if (req.responseText == 'tgldiluarkontrak') {
                                                            alert('tanggal kirim tidak boleh melebihi tanggal kontrak !!!');
                                                            document.getElementById('sisa_target'+obj).value=0;
                                                            document.getElementById('fsisa_target'+obj).value=0;
                                                            $("#tgl_kirim"+obj).val('');
                                                        } else{
                                                            // mencegah list material kosong
                                                            document.getElementById('qty'+obj+'').value='';  
                                                            document.getElementById('qtycek'+obj+'').value=''; 
                                                            var hasil = JSON.parse(req.responseText);
                                                            var cobTo2=parseFloat(hasil.SISA);
                                                            if (cobTo2 <= 0) {
                                                                if(hasil.STATUS_BRAND=='MB')
                                                                    alert('kuota '+brand+' anda habis!!');                                           
                                                                else
                                                                    alert('kuota '+cobTo2+' anda habis, silahkan order main brand terlebih dahulu !!');
                                                            }
                                                            //pesan kuota
                                                            // // add in socc v2.0
                                                            // var strURL="loadexplanning3.php?orgin="+com.value+"&soldin="+dist.value+'&plant='+val_plant+'&kodedistrik='+val_kotatj+'&action=cekmapbrand';
                                                            // // var strURL="loadexplanning3.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=config_umur_so";
                                                            //     var reqs = getXMLHTTP();
                                                            //     if (reqs) {
                                                            //             reqs.onreadystatechange = function() {
                                                            //                 lodingact(0);
                                                            //                     if (reqs.readyState == 4) {
                                                            //                             if (reqs.status == 200) { 
                                                            //                                 lodingact(1); 
                                                            //                                     config=reqs.responseText;
                                                            //                                     if(cobTo2<=0 && config=="FB"){
                                                            //                                         alert('kuota fighting brand anda habis, silahkan order main brand terlebih dahulu !!');
                                                            //                                     }else if (cobTo2<=0){
                                                            //                                         alert('kuota target telah habis.');
                                                            //                                     }
                                                            //                             } else {
                                                            //                                     lodingact(1);
                                                            //                                     alert("There was a problem while using XMLHTTP:\n" + reqs.statusText);
                                                            //                             }
                                                            //                     }               
                                                            //             }           
                                                            //             reqs.open("GET", strURL, true);
                                                            //             reqs.send(null);
                                                            //     }
                                                            //
                                                            if(cobTo2<=0){
                                                                    // alert('kuota fighting brand anda habis, silahkan order main brand terlebih dahulu !!');
                                                                    document.getElementById('tgl_kirim'+obj).value='';
                                                                    // document.getElementById('sisa_target'+obj).value='';
                                                                    // document.getElementById('fsisa_target'+obj).value='';
                                                            }
                                                            document.getElementById('sisa_target'+obj).value=cobTo2;
                                                            document.getElementById('fsisa_target'+obj).value=cobTo2;
                                                            // else{
                                                            //     document.getElementById('sisa_target'+obj).value=cobTo2;
                                                            //     document.getElementById('fsisa_target'+obj).value=cobTo2;
                                                            // }
                                                            


                                                            var shiptosi = $("#shipto"+obj).val();
                                                            var produksi = $("#produk"+obj).val();
                                                            if (pricelist != '01' && pricelist != '04' && pricelist != '02' && pricelist != '15'&& pricelist != '19'&& pricelist != '07') {
                                                                if (shiptosi != '') {
                                                                    ketik_shipto(document.getElementById('shipto'+obj),obj);
                                                                }
                                                                
                                                                if (produksi != '') {
                                                                    ketik_produk(document.getElementById('produk'+obj),obj);
                                                                }
                                                            }
                                                                
                                                            // if (produksi != '') {
                                                            //     ketik_produk(document.getElementById('produk'+obj),obj);
                                                            // }
                                                        }
                                                } else {
                                                        lodingact(1);
                                                        alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                                }
                                        }               
                                }           
                                req.open("GET", strURL, true);
                                req.send(null);
                        }                
                })(plantValues[indexPlant], indexPlant);
                }
            } else {
            if (tglkontrakvalid) {
            var strURL="loadexplanning3_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tglkirim='+tglkirim.value+'&plant='+val_plant+'&produkin='+tipe+'&kodedistrik='+val_kotatj+'&action=viewsisa&tglkontrak='+tglkontrakvalid+'&brand='+brand;
            }else{
            var strURL="loadexplanning3_royalty.php?orgin="+com.value+"&soldin="+dist.value+'&tglkirim='+tglkirim.value+'&plant='+val_plant+'&produkin='+tipe+'&kodedistrik='+val_kotatj+'&action=viewsisa&brand='+brand;
            }
            var req = getXMLHTTP();
            if (req) {
                    req.onreadystatechange = function() {
                        lodingact(0);
                            if (req.readyState == 4) {
                                    // only if "OK"
                                    if (req.status == 200) {    
                                            lodingact(1);
                                            if (req.responseText == 'tgldiluarkontrak') {
                                                alert('tanggal kirim tidak boleh melebihi tanggal kontrak !!!');
                                                document.getElementById('sisa_target'+obj).value=0;
                                                document.getElementById('fsisa_target'+obj).value=0;
                                                $("#tgl_kirim"+obj).val('');
                                            } else{
                                                // mencegah list material kosong
                                                document.getElementById('qty'+obj+'').value='';  
                                                document.getElementById('qtycek'+obj+'').value=''; 
                                                var hasil = JSON.parse(req.responseText);
                                                var cobTo2=parseFloat(hasil.SISA);
                                                if (cobTo2 <= 0) {
                                                    if(hasil.STATUS_BRAND=='MB')
                                                        alert('kuota '+brand+' anda habis!!');                                           
                                                    else
                                                        alert('kuota '+cobTo2+' anda habis, silahkan order main brand terlebih dahulu !!');
                                                }
                                                //pesan kuota
                                                // // add in socc v2.0
                                                // var strURL="loadexplanning3.php?orgin="+com.value+"&soldin="+dist.value+'&plant='+val_plant+'&kodedistrik='+val_kotatj+'&action=cekmapbrand';
                                                // // var strURL="loadexplanning3.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=config_umur_so";
                                                //     var reqs = getXMLHTTP();
                                                //     if (reqs) {
                                                //             reqs.onreadystatechange = function() {
                                                //                 lodingact(0);
                                                //                     if (reqs.readyState == 4) {
                                                //                             if (reqs.status == 200) { 
                                                //                                 lodingact(1); 
                                                //                                     config=reqs.responseText;
                                                //                                     if(cobTo2<=0 && config=="FB"){
                                                //                                         alert('kuota fighting brand anda habis, silahkan order main brand terlebih dahulu !!');
                                                //                                     }else if (cobTo2<=0){
                                                //                                         alert('kuota target telah habis.');
                                                //                                     }
                                                //                             } else {
                                                //                                     lodingact(1);
                                                //                                     alert("There was a problem while using XMLHTTP:\n" + reqs.statusText);
                                                //                             }
                                                //                     }               
                                                //             }           
                                                //             reqs.open("GET", strURL, true);
                                                //             reqs.send(null);
                                                //     }
                                                //
                                                if(cobTo2<=0){
                                                        // alert('kuota fighting brand anda habis, silahkan order main brand terlebih dahulu !!');
                                                        document.getElementById('tgl_kirim'+obj).value='';
                                                        // document.getElementById('sisa_target'+obj).value='';
                                                        // document.getElementById('fsisa_target'+obj).value='';
                                                }
                                                document.getElementById('sisa_target'+obj).value=cobTo2;
                                                document.getElementById('fsisa_target'+obj).value=cobTo2;
                                                // else{
                                                //     document.getElementById('sisa_target'+obj).value=cobTo2;
                                                //     document.getElementById('fsisa_target'+obj).value=cobTo2;
                                                // }
                                                


                                                var shiptosi = $("#shipto"+obj).val();
                                                var produksi = $("#produk"+obj).val();
                                                 if (pricelist != '01' && pricelist != '04' && pricelist != '02' && pricelist != '15'&& pricelist != '19'&& pricelist != '07') {
                                                    if (shiptosi != '') {
                                                        ketik_shipto(document.getElementById('shipto'+obj),obj);
                                                    }
                                                    
                                                    if (produksi != '') {
                                                        ketik_produk(document.getElementById('produk'+obj),obj);
                                                    }
                                                }
                                                    
                                                // if (produksi != '') {
                                                //     ketik_produk(document.getElementById('produk'+obj),obj);
                                                // }
                                            }
                                    } else {
                                            lodingact(1);
                                            alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                    }
                            }               
                    }           
                    req.open("GET", strURL, true);
                    req.send(null);
                }
            }

    }else{
        alert("Silahkan pilih Plant dan Kota terlebih dahulu..!!!");
    }
}

function clearData() {
    document.getElementById('tgl_kirim1').value='';
    document.getElementById('sisa_target1').value='';
    document.getElementById('fsisa_target1').value='';
    document.getElementById('shipto1').value='';
    document.getElementById('nama_shipto1').value='';
    document.getElementById('alamat1').value='';
    document.getElementById('qty1').value='';
    document.getElementById('com_kontrak1').value='';
    document.getElementById('kode_distrik1').value='';
    document.getElementById('nama_distrik1').value='';
    document.getElementById('top').selectedIndex = 0;   
}


function getsisasementara(){ 
        //load sisa target
            var quantumqtykcek = document.getElementById('qtycek1').value;
            var nilaiTarget = document.getElementById('sisa_target1').value;
            var com = document.getElementById('org'); 
            var distrik = $("#kota_tujuan").val();
            var material = $("#produk1").val();
            var incoterm = $("#jenis_kirim").val();
            var plant = $("#plant").val();
            var jkemasan=document.getElementById('jenis_kemasan'); 

            var strURL="loadexplanningpp_royalty.php?org="+com.value+"&material="+material+'&plant='+plant+'&qty_in='+quantumqtykcek+'&action=getKonversi';
            var req = getXMLHTTP();
            if (req) {
                    req.onreadystatechange = function() {
                        // lodingact(1);
                            if (req.readyState == 4) {                         
                                    // only if "OK"
                                    if (req.status == 200) {	
                                        konversi = req.responseText;
                                            var sisaTarget = nilaiTarget - konversi;
                                            if (isNaN(parseFloat(sisaTarget))) {
                                                sisaTarget = nilaiTarget;
                                            }
                                            //
                                            var tgrt = document.getElementById('fsisa_target1').value;
                                            if (tgrt == '' || tgrt < 0 || tgrt == null) {
                                                alert('nilai QTY pengajuan lebih tinggi dari sisa target');
                                                // clearData();
                                                document.getElementById('qty1').value = '';
                                                loadSISATARGET('1');
                                            }else{
                                                document.getElementById('fsisa_target1').value = parseFloat(sisaTarget);

                                                if (sisaTarget < 0) {
                                                    alert('nilai QTY pengajuan lebih tinggi dari sisa target');
                                                    document.getElementById('qty1').value = '';
                                                    loadSISATARGET('1');
                                                }
                                        }

                                        

                                    } 
                            }				
                    }			
                    req.open("GET", strURL, true);
                req.send(null);
            }

}

function quantum_soccv2(obj,ke){  
    if (!obj.value) {
        obj.value = 0;
    }
    var btnsimpan2 = 'save';
    var simpanorderpp = document.getElementById(btnsimpan2);
    simpanorderpp.disabled= true; 
    $(document).on("keypress", 'form', function (e) {
    var code = e.keyCode || e.which;
    console.log(code);
    if (code == 13) {
        console.log('Inside');
        e.preventDefault();
        return false;
    }
});
    // document.getElementById("tambah").disabled = true;
    // cekgudang(obj,ke);
    var com_plant = document.getElementById('plant');
    var com_plantval = com_plant.value;
    var com_produk = document.getElementById('produk'+ke);
    var com_produkval = com_produk.value;
    var com_converto = document.getElementById('qto'+ke);
    var com_convertoval = parseFloat(com_converto.value);
    var com_typetruck = document.getElementById('typetruck'+ke);
    var com_typetruckval = com_typetruck.value;
    var nilai_qty =parseFloat(obj.value);
    
    //perubahan qty kelipatan 8 tanpa pengkondisian khusus

    var com_firstdate = document.getElementById('plant');
    var com_firstdateval = com_firstdate.value;
    var jkirim=document.getElementById('jenis_kirim');
    var val_jkirim=jkirim.options[jkirim.selectedIndex].value;
    var com=document.getElementById('org');
    var route_jav = document.getElementById('route'); 
    var jenis_kirim_jav = document.getElementById('jenis_kirim');    
    if(com_plantval!='' && com_produkval!=''){  
        
        ///////////////////////////////////////////
        var kotatj=document.getElementById('kota_tujuan');
        var com=document.getElementById('org'); 
        var val_kotatj=kotatj.options[kotatj.selectedIndex].value;
        var jkemasan=document.getElementById('jenis_kemasan');  
        var dist=document.getElementById('sold_to');
        var shipto=document.getElementById('shipto1');
        var tgl_leadtimenya = document.getElementById('tgl_kirim1').value;
        switch (jkemasan.value) {
            case 'CURAH':
                tipe = '121-302';
                break;
            case 'MORTAR ZAK':
                tipe = '121-701';
                break;
            case 'MORTAR CURAH':
                tipe = '121-702';
                break;
            case 'M3':
                tipe = '121-800';
                break;
            case 'M3 PALLET1':
                tipe = '121-800';
                break;
            default:
                tipe = '121-301';
        }   
        
        var strURL="loadexplanning3_royalty.php?orgin="+com.value+"&loadtype="+val_jkirim+"&produkin="+tipe+"&plant="+com_plantval+"&action=config_rdd_typeload";
        var incoterm3rd = getXMLHTTP();
        if (incoterm3rd) {
            incoterm3rd.onreadystatechange = function() {
                    lodingact(0);
                        if (incoterm3rd.readyState == 4) {
                                if (incoterm3rd.status == 200) { 
                                    lodingact(1); 
                                    var incoterm=incoterm3rd.responseText;
                                    const rddconfigs_material = incoterm.split("/");
                                    var loadingtype1 = rddconfigs_material[0];
                                    var materialtype1 = rddconfigs_material[1];
                                    loadingtype1=loadingtype1.substr(0,3);
        // Add Thamrin
        if (route_jav.value!='ZR0001') {
        if ((jenis_kirim_jav.value!='FOT') || (loadingtype1=='FRC')) {
            var btnsimpan2 = 'save';
            var simpanorderpp = document.getElementById(btnsimpan2);
            simpanorderpp.disabled= true; 
        
            var strURL="loadexplanning3_royalty.php?orgin="+com.value+"&tgl_ldtime="+tgl_leadtimenya+"&soldin="+dist.value+"&kodedistrik="+val_kotatj+'&shipto='+shipto.value+'&plant='+com_plantval+'&produkin='+tipe+'&action=getdatagudang';
            var req = getXMLHTTP();
            if (req) {
                    req.onreadystatechange = function() {
                        lodingact(0);
                            if (req.readyState == 4) {                         
                                    // only if "OK"
                                    if (req.status == 200) {    
                                        lodingact(1);
                                        var mdxlconf = req.responseText;
                                        orderqtymdxl = parseInt(mdxlconf);
                                        if(orderqtymdxl<1){
                                            orderqtymdxl2=0;
                                        }
                                        // const mdxlconfigs = mdxlconf.split("/");
                                        // var orderqtymdxl = mdxlconfigs[0];
                                        // orderqtymdxl = parseInt(orderqtymdxl);
                                        // var kapasitas_bongkar_mdxl = mdxlconfigs[1];
                                        // kapasitas_bongkar_mdxl = parseInt(kapasitas_bongkar_mdxl);
                                        // alert("get data mdxl"+orderqtymdxl+kapasitas_bongkar_mdxl);
                                        /////////////////////////////////////////////////
                                        if (tipe == '121-301') {
                                        var cob=nilai_qty;//(nilai_qty*com_convertoval);
                                        var cob1 = cob % 32;
                                        var cob8 = cob % 8;
                                        var cob16 = cob % 16;
                                                ////////////////
                                                            /////////////////////////////////////////////////////////////////////
                                                            // if(cob8!=0){
                                                            //     alert("Quantum harus kelipatan 8 TO..!!!");
                                                            //     rumus=(cob*(com_convertoval*1000))/1000;
                                                            //                     rumusnya = 8 * Math.floor(rumus/8);
                                                            //                     rumuszak=(rumusnya*1000)/(com_convertoval*1000);
                                                            //                     // obj.value=(cob-cob1)/com_convertoval;
                                                            //                     // cob = (cob-cob1)/com_convertoval;
                                                            //                     // obj.value=rumuszak;
                                                            //                     cob = rumuszak;
                                                            //                     cob8 = cob % 8;
                                                            // }                                                                

                                                                rumuszak=(cob*(com_convertoval*1000))/1000;
                                                                console.log(cob,com_convertoval);
                                                                // alert(rumuszak)
                                                                // if(rumuszak<8){
                                                                //     alert("Order Quantum Minimum 8 TO.");
                                                                //     cob='';
                                                                // }
                                                            /////////////////////////////////////////////////////////////////////
                                                                        /////////////////////////////////////////////
                                                                        // if(cob > kapasitas_bongkar_mdxl && orderqtymdxl>1){
                                                                        if((cob > orderqtymdxl) && (orderqtymdxl>1)){
                                                                        alert("Quantum Melebihi Kemampuan Bongkar : "+orderqtymdxl);
                                                                            cob = orderqtymdxl;
                                                                            rumuszak=(cob*(com_convertoval*1000))/1000;
                                                                            if(rumuszak<8){
                                                                                // alert("Order Quantum Minimum 8 TO.");
                                                                                cob='';
                                                                            }
                                                                        }else if(orderqtymdxl<1){
                                                                            alert("Order Quantum Telah Melebihi Stock Gudang dan Intransit : "+orderqtymdxl2);
                                                                            cob ='';
                                                                        }else{
                                                                            cob = cob;
                                                                        }

                                                                        obj.value=cob;

                                                                        //     if((cob > orderqtymdxl) && (orderqtymdxl>1)){
                                                                        // alert("Quantum Melebihi Kemampuan Kapasitas Gudang : "+orderqtymdxl);
                                                                        // // obj.value=orderqtymdxl;
                                                                        // cob = orderqtymdxl;
                                                                        // rumus=(cob*(com_convertoval*1000))/1000;
                                                                        //         rumusnya = 8 * Math.floor(rumus/8);
                                                                        //         rumuszak=(rumusnya*1000)/(com_convertoval*1000);
                                                                        //         obj.value=rumuszak;
                                                                        //         cob = rumuszak;
                                                                        // }else if(orderqtymdxl<1){
                                                                        //     alert("Order Quantum Telah Melebihi Stock Gudang dan Intransit : "+orderqtymdxl2);
                                                                        //     // obj.value=orderqtymdxl;
                                                                        //     cob = orderqtymdxl;
                                                                        //     rumus=(cob*(com_convertoval*1000))/1000;
                                                                        //         rumusnya = 8 * Math.floor(rumus/8);
                                                                        //         rumuszak=(rumusnya*1000)/(com_convertoval*1000);
                                                                        //         obj.value='';
                                                                        //         cob ='';
                                                                        // }else{
                                                                        //     if(cob8!=0){
                                                                        //         rumus=(cob*(com_convertoval*1000))/1000;
                                                                        //         rumusnya = 8 * Math.floor(rumus/8);
                                                                        //         rumuszak=(rumusnya*1000)/(com_convertoval*1000);
                                                                        //         //// obj.value=(cob-cob1)/com_convertoval;
                                                                        //         //// cob = (cob-cob1)/com_convertoval;
                                                                        //         obj.value=rumuszak;
                                                                        //         cob = rumuszak;
                                                                        //     }else{
                                                                        //         obj.value=cob;
                                                                        //         cob = cob;
                                                                        //     }

                                                                        // }
                                                                        //////////////////////////////////////////////////////
                                                                //}
                                                            // }                                                            
                                                // ////////////////
                                                // // alert("mdxl");
                                                // if(cob8!=0){
                                                //     alert("Quantum harus kelipatan 8 TO..!!!");
                                                //     obj.value=(cob-cob1)/com_convertoval;
                                                //     cob = (cob-cob1)/com_convertoval;
                                                //     // alert("Quantum harus kelipatan 8 TO..!!!");
                                                //     // if(cob8<=0 || cob<=0 || ((cob-cob1)/com_convertoval<=0)){
                                                //     //     obj.value="";
                                                //     //     cob="";
                                                //     // }else{
                                                //     //     obj.value=(cob-cob1)/com_convertoval;
                                                //     //     cob = (cob-cob1)/com_convertoval;
                                                //     // }
                                                // }else{
                                                //     if(parseInt(orderqtymdxl)<0){
                                                //         orderqtymdxl=0;
                                                //     }
                                                //     if(cob > kapasitas_bongkar_mdxl){
                                                //         alert("Quantum Melebihi Kemampuan Kapasitas Gudang");
                                                //         obj.value=orderqtymdxl;
                                                //     }else{
                                                //         obj.value=orderqtymdxl;
                                                //         alert("Quantum Melebihi Kemampuan Kapasitas Gudang");
                                                //     }
                                                // }
                                        }else{
                                            var cob = nilai_qty;
                                                obj.value=cob;//(cob-cob1);
                                        }
                                            var btnsimpan2 = 'save';
                                            var simpanorderpp = document.getElementById(btnsimpan2);
                                            simpanorderpp.disabled=""; 
                                        console.info(cob);
                                        var cobTo=parseFloat(cob);
                                        if (isNaN(cobTo)) {
                                                cobTo= 0;
                                        }
                                        document.getElementById('qtycek'+ke).value=cobTo;
                                        getsisasementara(ke);
                                        ////////////////////////////////////////////////
                                    } else {             
                                        lodingact(1);
                                        console.log("There was a problem while using XMLHTTP:\n" + req.statusText);
                                        obj.value='';
                                        // alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                    }
                            }               
                    }           
                    req.open("GET", strURL, true);
                    req.send(null);
            }


        }else{
            var btnsimpan2 = 'save';
            var simpanorderpp = document.getElementById(btnsimpan2);
            simpanorderpp.disabled="";
        }
        }else{
            var btnsimpan2 = 'save';
            var simpanorderpp = document.getElementById(btnsimpan2);
            simpanorderpp.disabled="";
        }
        ////////////////////////////////////////// 
    } else {
                                        lodingact(1);
                                        // alert("There was a problem while using XMLHTTP:\n" + incoterm3rd.statusText);
                                        // obj.value='';
                                }
                        }               
                }           
                incoterm3rd.open("GET", strURL, true);
                incoterm3rd.send(null);
        }
        // cekkbongkar(ke);
    }else{
        obj.value="";
        alert("Tanggal, Plant dan Kode Produk tidak boleh kosong..!!!");
        document.getElementById('qtycek'+ke).value='';
    }
    
    if (route_jav.value=='ZR0001' || jenis_kirim_jav.value=='FOT') {
        if (tipe == '121-301') {
            var cob = nilai_qty;
            obj.value=cob;
        //    var cob=nilai_qty;
        //    console.info('com_convertoval');
        //    console.info(com_convertoval);
        //    cob=(cob*(com_convertoval*1000))/1000;
        } else {
            var cob = nilai_qty;
            obj.value=cob;
        }
        console.info(cob);
        var cobTo=parseFloat(cob);
        if (isNaN(cobTo)) {
                cobTo= 0;
        }
        console.info(cobTo);
        document.getElementById('qtycek'+ke).value=cobTo;
        getsisasementara(ke);   
        IsNumeric2(cobTo);     
    }
    
}

function quantum(obj,ke){  
    if (!obj.value) {
        obj.value = 0;
    }
    var com_plant = document.getElementById('plant');
    var com_plantval = com_plant.value;
    var com_produk = document.getElementById('produk'+ke);
    var com_produkval = com_produk.value;
    var com_converto = document.getElementById('qto'+ke);
    var com_convertoval = parseFloat(com_converto.value);
    var com_typetruck = document.getElementById('typetruck'+ke);
    var com_typetruckval = com_typetruck.value;
    var nilai_qty =parseFloat(obj.value);
    
    //perubahan qty kelipatan 8 tanpa pengkondisian khusus

    var com_firstdate = document.getElementById('plant');
    var com_firstdateval = com_firstdate.value;
    
    if(com_plantval!='' && com_produkval!=''){   
        if (tipe == '121-301') {
            var cob=nilai_qty;//(nilai_qty*com_convertoval);
            var cob1 = cob % 32;
            var cob8 = cob % 8;
            var cob16 = cob % 16;

            if(com_plantval=='7403' || com_plantval=='2403'){           
                if(com_typetruckval=='305'){
                    if((cob1==0) || (cob==16)){
                    }else{    
                      alert("Quantum harus kelipatan 32 atau sama dengan 16 TO..!!!");
                      obj.value=(cob-cob16)/com_convertoval;
                        cob = (cob-cob16)/com_convertoval;
                    }  
                }else{
                     if(cob1!=0){
                        alert("Quantum harus kelipatan 32 TO..!!!");
                        obj.value=(cob-cob1)/com_convertoval;
                        cob = (cob-cob1)/com_convertoval;
                    }
                }
                
            }else if(com_plantval=='7401'){
                if(cob8!=0){
                    alert("Quantum harus kelipatan 8 TO..!!!");
                    obj.value=(cob-cob1)/com_convertoval;
                    cob = (cob-cob1)/com_convertoval;
                }
            }
        }else{
            var cob = nilai_qty;
//            var cob1 = cob%35;
//            if(cob1!=0){
               // alert("Quantum harus kelipatan 35 TO..!!!");
                obj.value=cob;//(cob-cob1);
                //cob = (cob-cob1);
            //}
        } 
            console.info(cob);
            // console.info(cob8); 
        var cobTo=parseFloat(cob);
        if (isNaN(cobTo)) {
                cobTo= 0;
        }
        document.getElementById('qtycek'+ke).value=cobTo;
        getsisasementara(ke); 
        cekkbongkar(ke);
        // cekgudang(obj,ke);
    }else{
        obj.value="";
        alert("Plant dan Kode Produk tidak boleh kosong..!!!");
        document.getElementById('qtycek'+ke).value='';
    }
    
}

// function quantum(obj,ke){  
//     if (!obj.value) {
//         obj.value = 0;
//     }
//     var com_plant = document.getElementById('plant');
//     var com_plantval = com_plant.value;
//     var com_produk = document.getElementById('produk'+ke);
//     var com_produkval = com_produk.value;
//     var com_converto = document.getElementById('qto'+ke);
//     var com_convertoval = parseFloat(com_converto.value);
//     var com_typetruck = document.getElementById('typetruck'+ke);
//     var com_typetruckval = com_typetruck.value;
//     var nilai_qty =parseFloat(obj.value);
    
//     var com_firstdate = document.getElementById('plant');
//     var com_firstdateval = com_firstdate.value;
    
//     if(com_plantval!='' && com_produkval!=''){   
//         if (tipe == '121-301') {
//             var cob=nilai_qty;//(nilai_qty*com_convertoval);
//             var cob1 = cob % 32;
//             var cob8 = cob % 8;
//             var cob16 = cob % 16;

//             if(com_plantval=='7403' || com_plantval=='2403'){           
//                 if(com_typetruckval=='305'){
//                     if((cob1==0) || (cob==16)){
//                     }else{    
//                       alert("Quantum harus kelipatan 32 atau sama dengan 16 TO..!!!");
//                       obj.value=(cob-cob16)/com_convertoval;
//                         cob = (cob-cob16)/com_convertoval;
//                     }  
//                 }else{
//                      if(cob1!=0){
//                         alert("Quantum harus kelipatan 32 TO..!!!");
//                         obj.value=(cob-cob1)/com_convertoval;
//                         cob = (cob-cob1)/com_convertoval;
//                     }
//                 }
                
//             }else if(com_plantval=='7401'){
//                 if(cob8!=0){
//                     alert("Quantum harus kelipatan 8 TO..!!!");
//                     obj.value=(cob-cob1)/com_convertoval;
//                     cob = (cob-cob1)/com_convertoval;
//                 }
//             }
//         }else{
//             var cob = nilai_qty;
// //            var cob1 = cob%35;
// //            if(cob1!=0){
//                // alert("Quantum harus kelipatan 35 TO..!!!");
//                 obj.value=cob;//(cob-cob1);
//                 //cob = (cob-cob1);
//             //}
//         } 
//             console.info(cob);
//             // console.info(cob8); 
//         var cobTo=parseFloat(cob);
//         if (isNaN(cobTo)) {
//                 cobTo= 0;
//         }
//         document.getElementById('qtycek'+ke).value=cobTo;
//         getsisasementara(ke); 
//         cekkbongkar(ke);
//     }else{
//         obj.value="";
//         alert("Plant dan Kode Produk tidak boleh kosong..!!!");
//         document.getElementById('qtycek'+ke).value='';
//     }
    
// }
   
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function getroute(muat) {		
		var strURL="cariroute.php?muat="+muat+"&tipe=new";
		var req = getXMLHTTP();
		
		if (req) {
			req.onreadystatechange = function() {
				if (req.readyState == 4) {
					// only if "OK"
					if (req.status == 200) {						
						document.getElementById('routediv').innerHTML=req.responseText; 		
					} else {
						alert("There was a problem while using XMLHTTP:\n" + req.statusText);
					}
				}				
			}			
			req.open("GET", strURL, true);
			req.send(null);
		}
	}
   
    function cekHoliday(){
    // tgl_kirim1 ini ID tanggal kirim 
    var tanggal = document.getElementById('tgl_kirim1').value;
    var shipto = document.getElementById('shipto1').value;
    var jenis_kirim = document.getElementById('jenis_kirim').value;
    var pricelist = document.getElementById('pricelist').value;
    var plant = document.getElementById('plant').value;
    
    var soldto = "<?= $distr_id ?>";
    
    
  
    if(tanggal!='' && shipto!='' && soldto!=''){
        
        if (pricelist == '01' || pricelist == 01 || pricelist == 06 || pricelist == '06') {
            return true;
        }else{
            var strURL="cekIsHolidayNew.php?tanggal="+tanggal+"&shipto="+shipto+"&soldto="+soldto+"&jenis_kirim="+jenis_kirim+"&plant="+plant;
                var req = getXMLHTTP();
                if (req) {
                    req.onreadystatechange = function() {
                        lodingact(0);
                            if (req.readyState == 4) {
                                // only if "OK"responseText
                                if (req.responseText != '') {
                                    if (req.responseText== 'Free Day') {
                                        alert('Anda Memilih Hari Libur, Silahkan Pilih Tanggal Lain!');
                                        document.getElementById('tgl_kirim1').value = '';
                                        clearData();
                                    }else{
                                        alert('Anda Memilih Hari Libur Nasional ('+req.responseText+')');
                                        document.getElementById('tgl_kirim1').value = '';
                                        clearData();
                                    }
                                } 
                                lodingact(1);
                            }               
                    }
                }
                req.open("GET", strURL, true);
                req.send(null);
        }
        
    }
}

function ketik_shipto(obj,ke) {
    var distrik=document.getElementById('kota_tujuan');
    var val_distrik=distrik.options[distrik.selectedIndex].value;
    var plant=document.getElementById('plant');
    var val_plant=plant.options[plant.selectedIndex].value;
    var pricelist=document.getElementById('pricelist');
    var pricelistValue2 = document.getElementById('pricelist').value;
    var val_pricelist=pricelist.options[pricelist.selectedIndex].value;
    if(val_distrik!='' && val_plant!=''){
            
            var strURL="ketik_shiptosi_staging.php?shipto="+obj.value+"&nourut="+ke+'&distrik='+val_distrik+'&filplant='+val_plant+'&sp='+kontrakshipto+'&pricelist='+val_pricelist;
            var req = getXMLHTTP();
            if (req) {
                    req.onreadystatechange = function() {
                    lodingact(0);
                            if (req.readyState == 4) {
                                    // only if "OK"
                                    if (req.status == 200) {                        
                                            document.getElementById("shiptodiv"+ke).innerHTML=req.responseText;
                                            load_top('1');
                                            cekshiptotop();
                                    } else {
                                            alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                    }
                                lodingact(1);
                            }               
                    }           
                    req.open("GET", strURL, true);
                    req.send(null);
                    resetQty(ke);
            }
        }else{
            obj.value="";
            alert("Distrik dan Plant tidak boleh kosong..!!!");
        }
}

function cekshiptoexception(urut){
   var jkemasan=document.getElementById('jenis_kemasan').value;    
    var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';  
    var route = document.getElementById('route').value; 
    var shipto=document.getElementById('shipto1').value;
    
    var kodeprop = $("#kota_tujuan option:selected").data().prop;
    var dist=document.getElementById('sold_to').value;
    var com=document.getElementById('org').value;
    var so_type = document.getElementById('so_type').value;
    var brand = encodeURIComponent(document.getElementById('brand').value);
    var kd_material = $("#produk1").val();
    var plant=document.getElementById('plant').value;
    var soldto = "<?= $distr_id ?>";

    var URLshiptoEX="loadexplanningpp_royalty.php?jkemasan="+jkemasan+"&distid="+distid+'&prop='+kodeprop+'&route='+route+'&com='+com+'&shipto='+shipto+'&plant='+plant+'&soldto='+soldto+'&brand='+brand+'&kd_material='+kd_material+'&action=cekShiptoEx';
	var reqShiptoEx = getXMLHTTP();
      if(reqShiptoEx) {
              reqShiptoEx.onreadystatechange = function() {
                  
                      if (reqShiptoEx.readyState == 4) {
                              
                              if (reqShiptoEx.status == 200) {
								
							
								document.getElementById('produk'+urut).readonly = false;
								document.getElementById('btn_produk'+urut).disabled = false;//array[0];
								if (reqShiptoEx.responseText.replaceAll(';','').trim().length > 0) {
                                                                
                                    const detail_exception = reqShiptoEx.responseText.split(';');
                                    // Mendapatkan referensi ke elemen select
                                    var selectElement = document.getElementById("plant");
                                    var selectElement2 = document.getElementById("plantBayangan");
                                    var tanggal=document.getElementById('tgl_kirim1');
                                    let plantOptions = [...selectElement.options].map(o => o.value);
                                    // Membuat opsi baru
                                    if(!plantOptions.includes(detail_exception[0])){
                                        var newOption = document.createElement("option");

                                        // Mengatur teks untuk opsi baru
                                        newOption.text = detail_exception[0]+" "+detail_exception[1];//['NM_PLANT'];
                                        // Mengatur nilai untuk opsi baru (opsional)

                                        // var array = reqShiptoEx.responseText.split(" ");
                                        newOption.value = detail_exception[0];//['WERKS'];
                                        tanggal.value='';
                                        // Menambahkan opsi baru ke elemen select
                                        selectElement.add(newOption);
                                        
                                        // selectElement.disabled = true;
                                        newOption.selected = true;
                                    }
                                    else{
                                        selectElement.value = detail_exception[0];
                                    }
                                    // selectElement.disabled = false;
                                    selectElement2.value = detail_exception[0];//array[0];
                                    document.getElementById('plantBayangan').value = detail_exception[0];//array[0]; 

                                    document.getElementById('produk'+urut).value = detail_exception[2];//array[0]; 
                                    document.getElementById('nama_produk'+urut).value = detail_exception[3];//array[0]; 
                                    document.getElementById('uom'+urut).value = detail_exception[4];//array[0]; 
                                }

                                 
                              } else { 
                                      alert("There was a problem while using XMLHTTP:\n" + reqShiptoEx.statusText);
                              }
                      }               
              }           
              reqShiptoEx.open("GET", URLshiptoEX, true);
              reqShiptoEx.send(null);
      }
}

// function ketik_shipto(obj,ke) {
//         var distrik=document.getElementById('kota_tujuan');
//         var val_distrik=distrik.options[distrik.selectedIndex].value;
//         var plant=document.getElementById('plant');
//         var val_plant=plant.options[plant.selectedIndex].value;
//         var pricelist=document.getElementById('pricelist');
//         var val_pricelist=pricelist.options[pricelist.selectedIndex].value;

//         var dist=document.getElementById('sold_to');

//         if(val_distrik!='' && val_plant!=''){
//             var strURL="ketik_shipto_ex.php?shipto="+obj.value+"&nourut="+ke+'&distrik='+val_distrik+'&filplant='+val_plant+'&sp='+kontrakshipto+'&pricelist='+val_pricelist+'&soldto='+dist.value;
//             var req = getXMLHTTP();
//             if (req) {
//                     req.onreadystatechange = function() {
//                     lodingact(0);
//                             if (req.readyState == 4) {
//                                     // only if "OK"
//                                     if (req.status == 200) {						
//                                             document.getElementById("shiptodiv"+ke).innerHTML=req.responseText;
//                                             load_top('1');
//                                             cekshiptotop();
//                                     } else {
//                                             alert("There was a problem while using XMLHTTP:\n" + req.statusText);
//                                     }
//                                 lodingact(1);
//                             }				
//                     }			
//                     req.open("GET", strURL, true);
//                     req.send(null);
//                     resetQty(ke);
//             }
//         }else{
//             obj.value="";
//             alert("Distrik dan Plant tidak boleh kosong..!!!");
//         }
// }
function findshipto(ke) {
        var distrik=document.getElementById('kota_tujuan');
        var val_distrik=distrik.options[distrik.selectedIndex].value;
        var plant=document.getElementById('plant');
        var val_plant=plant.value; 
        var shiptoval_plant=plant.options[plant.selectedIndex].value;
        var pricelist=document.getElementById('pricelist');
        var val_pricelist=pricelist.options[pricelist.selectedIndex].value;
        
    var jkemasan=document.getElementById('jenis_kemasan');    
    var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';  
    var route = document.getElementById('route'); 
    var shipto=document.getElementById('shipto1');
    
    var kodeprop = $("#kota_tujuan option:selected").data().prop;
    var dist=document.getElementById('sold_to');
    var com=document.getElementById('org');
    var so_type = document.getElementById('so_type');
    var brand = document.getElementById('brand');
    var material = $("#produk1").val();
    if (route.value == 'ZR0001') {
        routess = 'sea';
    }else{
        routess = 'land';
    } 
        if(val_distrik!='' && val_plant!=''){
                resetQty(ke);
		var strURL="cari_shipto_ex_royalty.php?nourut="+ke+'&distrik='+val_distrik+'&filplant='+val_plant+'&kodeprop='+kodeprop+'&jkemasan='+jkemasan.value+'&com='+com.value+'&distid='+distid+'&route='+routess+'&so_type='+so_type.value+'&pricelist='+val_pricelist+'&soldto='+dist.value+'&brand='+encodeURIComponent(brand.value)+'&kd_material='+material;
		popUp(strURL);
        }else{
            alert("Distrik tidak boleh kosong..!!!");
        }      
                
}

function ketik_produk(obj,ke) {
        var jkmasan = $("#jenis_kemasan").val();
         var brand = encodeURIComponent($("#brand").val());
         var plant =$("#plant").val()
        //var brand = $("#brand").val();
        if (!jkmasan) {
            alert('Jenis Kemasan belum dipilih !');
            return false;
        }
        // if (jkmasan == 'CURAH') {
        //     jkmasan = 'TO';
        // }

        if (brand == '') {
                brand = jkmasan;
        } 

        var distrik=document.getElementById('kota_tujuan');
        var val_distrik=distrik.options[distrik.selectedIndex].value;
        var com_plant = document.getElementById("plant");
	var strURL="ketik_list_material.php?produk="+obj.value+"&brand="+brand+"&nourut="+ke+'&pr='+kontrakmaterial+"&unit="+jkmasan+"&distrik="+val_distrik;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
            lodingact(0);
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("produkdiv"+ke).innerHTML=req.responseText;	
                    if(plant == ''){
                    getPlant();					
                    }
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
                lodingact(1);
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
                resetQty(ke); 
	}
}
function findproduk(ke) {	
        var jkmasan = $("#jenis_kemasan").val();
        var brand = encodeURIComponent($("#brand").val());
        var incoterm = $("#jenis_kirim").val();
        var so_type = $('#so_type').val();
        if (!jkmasan) {
            alert('Jenis Kemasan belum dipilih !');
            return false;
        }
		        var com_org = document.getElementById("org");
                var com_plant = document.getElementById("plant");

             if (brand == '') {
                brand = jkmasan;
             }   

                var distrik=document.getElementById('kota_tujuan');
                var val_distrik=distrik.options[distrik.selectedIndex].value;
		        var strURL="cari_list_material_royalty.php?brand="+brand+"&nourut="+ke+"&unit="+jkmasan+"&distrik="+val_distrik+"&incoterm="+incoterm+"&so_type="+so_type;
		        popUp(strURL);  
                // resetQty(ke);
}
function findkapal() {	
    var com = document.getElementById("org");
	var strURL="cari_kapal.php?org="+com.value;
	popUp(strURL);
}
function ketik_kapal(obj) {
    var com = document.getElementById("org");
	var strURL="ketik_kapal.php?org="+com.value+"&kapal="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('kapaldiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findppref(kei) {	
    var comorg = document.getElementById('org');
    var comdistr = document.getElementById('sold_to');
    var comtgl_kirim = document.getElementById('tgl_kirim');
    var comdtypetruck = document.getElementById('typetruck'+kei);
    var val_plant=plant.options[plant.selectedIndex].value;
    var kotatj=document.getElementById('kota_tujuan');
    var val_kotatj=kotatj.options[kotatj.selectedIndex].value;
    if(comdtypetruck.value==305){
    var strURL="cari_ppref.php?org="+comorg.value+"&sold_to="+comdistr.value+"&typetruck="+comdtypetruck.value+"&tgl_kirim="+comtgl_kirim.value+"&plant="+val_plant+"&kodedistrik="+val_kotatj+"&nourut="+kei;//liyantanto
    popUp(strURL);
    }else{
       alert("Untuk Type Truck 305..!!!");
    } 
}


function get_loadtype(obj){
    var jkirim=document.getElementById('jenis_kirim');
    var val_jkirim=jkirim.options[jkirim.selectedIndex].value;
    var com = document.getElementById('org');
    document.getElementById('tgl_kirim1').value = '';
    document.getElementById('qty1').value = '';
    var plant=document.getElementById('plant');
    var val_plant=plant.options[plant.selectedIndex].value;    
    var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&loadtype="+val_jkirim+"&produkin="+tipe+"&plant="+val_plant+"&action=config_rdd_typeload";
    var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            if (req.status == 200) { 
                                lodingact(1); 
                                var incoterm=req.responseText;
                                const rddconfigs_material = incoterm.split("/");
                                var loadingtype1 = rddconfigs_material[0];
                                var materialtype1 = rddconfigs_material[1];
                                materialtype1=materialtype1.substr(0,3);
                                if(val_jkirim==loadingtype1){
                                    $('#estimasi_tanggal_label').html('Estimasi Tanggal Terima');
                                }else{
                                    $('#estimasi_tanggal_label').html('Estimasi Tanggal Pengambilan');
                                }

                            } else {
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
    }
}
// function cektanggal(obj) {

// var com_tgl = document.getElementById(obj);
// var com_kn = document.getElementById('tglnya');
// var com_knplus1 = document.getElementById('tgl1nya');
// var knplus1 = com_knplus1.value;
// var tgl = com_tgl.value;
// var kn = com_kn.value;
// var tgl1 = parseInt(tgl.substr(0,2),10);
// var bln1 = parseInt(tgl.substr(3,2),10);
// var th1 = parseInt(tgl.substr(6,4),10);
// var tglo = bln1+"/"+tgl1+"/"+th1;
// var tglx = new Date(tglo);
// var tgl2 = parseInt(kn.substr(0,2),10);
// var bln2 = parseInt(kn.substr(3,2),10);
// var th2 = parseInt(kn.substr(6,4),10);
// var tgln = bln2+"/"+tgl2+"/"+th2;
// var knx = new Date(tgln);
// ///plus 1 hari
// var tglkn1 = parseInt(knplus1.substr(0,2),10);
// var blnkn1 = parseInt(knplus1.substr(3,2),10);
// var thkn1 = parseInt(knplus1.substr(6,4),10);
// var tglkno = blnkn1+"/"+tglkn1+"/"+thkn1;
// var tglknx = new Date(tglkno);
// 	if( (tglx >= tglknx) )
// 	{
//             com_tgl.value=tgl;
// 	} else { 
//             com_tgl.value=knplus1; 
//         }
//  //penambahan change 3664, plant 7954 tgl tidak boleh melebihi hari H        
//  var plant=document.getElementById('plant').value;
//  var tglnow = new Date(Date.now());
//         if(plant == '7954' && tglx >= tglnow){
//             alert('Pengiriman dari Plant 7954 tanggal delivery maksimal hari H');
//             com_tgl.value=knplus1; 
//         }       
       
// }
function cektanggal(obj) {
    // var getloadtype = new get_loadtype();
    // alert(getloadtype.loadtype);
    
    // alert(config);
    // alert("mantap");
    $(document).on("keypress", 'form', function (e) {
    var code = e.keyCode || e.which;
    console.log(code);
    if (code == 13) {
        console.log('Inside');
        e.preventDefault();
        return false;
    }
});    
var btnsimpan2 = 'save';
var simpanorderpp = document.getElementById(btnsimpan2);
simpanorderpp.disabled= true;     
var com_tgl = document.getElementById(obj);
var jkirim=document.getElementById('jenis_kirim');
var val_jkirim=jkirim.options[jkirim.selectedIndex].value;

var com_kn = document.getElementById('tglnya');
var com_knplus1 = document.getElementById('tgl1nya');
var knplus1 = com_knplus1.value;
var tgl = com_tgl.value;
var kn = com_kn.value;
var tgl1 = parseInt(tgl.substr(0,2),10);
var bln1 = parseInt(tgl.substr(3,2),10);
var th1 = parseInt(tgl.substr(6,4),10);
var tglo = bln1+"/"+tgl1+"/"+th1;
var tglx = new Date(tglo);
var tgl2 = parseInt(kn.substr(0,2),10);
var bln2 = parseInt(kn.substr(3,2),10);
var th2 = parseInt(kn.substr(6,4),10);
var tgln = bln2+"/"+tgl2+"/"+th2;
var knx = new Date(tgln);
///plus 1 hari
var tglkn1 = parseInt(knplus1.substr(0,2),10);
var blnkn1 = parseInt(knplus1.substr(3,2),10);
var thkn1 = parseInt(knplus1.substr(6,4),10);
var tglkno = blnkn1+"/"+tglkn1+"/"+thkn1;
var ttglknx = new Date(tglkno);
// if(val_jkirim=="FRC"){
////////////////////////////////////// //SOCC v2.0
var jkemasan=document.getElementById('jenis_kemasan');  
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }
    var plant=document.getElementById('plant');
    var val_plant=plant.options[plant.selectedIndex].value;
    var kotatj=document.getElementById('kota_tujuan');
    var val_kotatj=kotatj.options[kotatj.selectedIndex].value;
    var com=document.getElementById('org');
    var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&loadtype="+val_jkirim+"&produkin="+tipe+"&plant="+val_plant+"&action=config_rdd_typeload";
    var reqrdd = getXMLHTTP();
    /////  
    // var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=leadtime_so";
    // var reqrdd = getXMLHTTP();
    reqrdd.onreadystatechange = function() {
                    lodingact(0);
                        if (reqrdd.readyState == 4) {
                            // only if "OK"
                            if (reqrdd.status == 200) {	
                            lodingact(1);
                            var loadtipe=reqrdd.responseText;
                            const rddconfigs_material = loadtipe.split("/");
                            var loadingtype1 = rddconfigs_material[0];
                            var materialtype1 = rddconfigs_material[1];
                            materialtype1=materialtype1.substr(0,7);
                            if(tipe==materialtype1){
                            var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=config_umur_so";
                                           
                            var reqs = getXMLHTTP();
                                                if (reqs) {
                                                        reqs.onreadystatechange = function() {
                                                            lodingact(0);
                                                                if (reqs.readyState == 4) {
                                                                        if (reqs.status == 200) { 
                                                                            lodingact(1); 
                                                                                config=reqs.responseText;
                                                                                    // var com=document.getElementById('org');  
                                                                                            // var strURL="loadexplanningpp.php?orgin="+com.value+"&loadtype="+val_jkirim+"&produkin="+tipe+"&action=config_rdd_typeload";
                                                                                            // var reqrdd = getXMLHTTP();
                                                                                            ///////
                                                                                            var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=leadtime_so";
                                                                                            var req = getXMLHTTP();
                                                                                            if (req) {
                                                                                                req.onreadystatechange = function() {
                                                                                                    if (req.readyState == 4) {
                                                                                                        // only if "OK"
                                                                                                        if (req.status == 200) {	
                                                                                                            var leadtime_so_config=req.responseText;
                                                                                                            const rddconfigs = loadtipe.split("/");
                                                                                                            var loadingtype = rddconfigs[0];
                                                                                                            var materialtype = rddconfigs[1];
                                                                                                            const leadtimeconfigs = leadtime_so_config.split("/");
                                                                                                            var leadtime_so = leadtimeconfigs[0];
                                                                                                            var rddminplus1 = leadtimeconfigs[1];
                                                                                                            var horizon_rdd = leadtimeconfigs[2];                                                                                                            
                                                                                                            // alert(rddconfigs);
                                                                                                            // alert(loadingtype);
                                                                                                            // alert(materialtype);
                                                                                                            loadingtype=loadtipe.substr(0,3);
                                                                                                            if((leadtime_so.length == 0 && val_jkirim==loadingtype)||(leadtime_so.length == 0 && loadingtype=='FRC')){
                                                                                                                alert("leadtime belum diinput, silahkan hubungi SPC untuk berikutnya diteruskan ke SCM");
                                                                                                                com_tgl.value='';
                                                                                                            }else{					
                                                                                                                // alert(loadtipe);
                                                                                                                // alert("masuk RDD");
                                                                                                                // if(leadtime_so.length == 0){
                                                                                                                
                                                                                                                if(horizon_rdd=='' || horizon_rdd==null){
                                                                                                                    var tglkn1 = parseInt(leadtime_so.substr(0,2),10);
                                                                                                                var blnkn1 = parseInt(leadtime_so.substr(3,2),10);
                                                                                                                var thkn1 = parseInt(leadtime_so.substr(6,4),10);
                                                                                                                }else{
                                                                                                                    var tglkn1 = parseInt(knplus1.substr(0,2),10);
                                                                                                                var blnkn1 = parseInt(knplus1.substr(3,2),10);
                                                                                                                var thkn1 = parseInt(knplus1.substr(6,4),10);
                                                                                                                }

                                                                                                                var tglkno = blnkn1+"/"+tglkn1+"/"+thkn1;
                                                                                                                var tglknx = new Date(tglkno);
                                                                                                                ///
                                                                                                                Date.prototype.addDays = function (days) {
                                                                                                                    const date = new Date(this.valueOf());
                                                                                                                    date.setDate(date.getDate() + days);
                                                                                                                    return date;
                                                                                                                };
                                                                                                                const date = new Date(tglknx);
                                                                                                                // }
                                                                                                                // //else--------------------------------------------------
                                                                                                                // else{
                                                                                                                // var tglkn1 = parseInt(leadtime_so.substr(0,2),10);
                                                                                                                // var blnkn1 = parseInt(leadtime_so.substr(3,2),10);
                                                                                                                // var thkn1 = parseInt(leadtime_so.substr(6,4),10);
                                                                                                                // var tglkno = blnkn1+"/"+tglkn1+"/"+thkn1;
                                                                                                                // var tglknx = new Date(tglkno);
                                                                                                                // ///
                                                                                                                // Date.prototype.addDays = function (days) {
                                                                                                                //     const date = new Date(this.valueOf());
                                                                                                                //     date.setDate(date.getDate() + days);
                                                                                                                //     return date;
                                                                                                                // };
                                                                                                                // const date = new Date(tglknx);
                                                                                                                // }

                                                                                                                dates=parseInt(config);
                                                                                                                var tglmaxleadtime = date.addDays(dates);
                                                                                                                var dd = String(tglmaxleadtime.getDate()).padStart(2, '0');
                                                                                                                var mm = String(tglmaxleadtime.getMonth() + 1).padStart(2, '0');
                                                                                                                var yyyy = tglmaxleadtime.getFullYear();
                                                                                                                maxleadtime = dd + '-' + mm + '-' + yyyy;

                                                                                                                ///
                                                                                                                    loadingtype=loadtipe.substr(0,3);
                                                                                                                    materialtype=materialtype.substr(0,7);
                                                                                                                    dates=parseInt(config);
                                                                                                                    // alert(tipe+materialtype);
                                                                                                                    // if((tipe==materialtype)){
                                                                                                                        if((val_jkirim==loadingtype||loadingtype=='FRC')&&(tipe==materialtype)){
                                                                                                                                //rollback request SCM
                                                                                                                                if(rddminplus1!=null || rddminplus1!=''){
                                                                                                                                var tglrddmin = parseInt(leadtime_so.substr(0,2),10);
                                                                                                                                var blnrddmin = parseInt(leadtime_so.substr(3,2),10);
                                                                                                                                var thrddmin = parseInt(leadtime_so.substr(6,4),10);
                                                                                                                                var tglknorddmin = blnrddmin+"/"+tglrddmin+"/"+thrddmin;
                                                                                                                                var tglknxrddmin = new Date(tglknorddmin);
                                                                                                                                // ///
                                                                                                                                Date.prototype.addDays = function (days) {
                                                                                                                                    const date = new Date(this.valueOf());
                                                                                                                                    date.setDate(date.getDate() + days);
                                                                                                                                    return date;
                                                                                                                                };
                                                                                                                                const date = new Date(tglknxrddmin);

                                                                                                                                datesrddmin=Number(rddminplus1);
                                                                                                                                var tglknx = date.addDays(datesrddmin);
                                                                                                                                var ddtglminleadtimerdd = String(tglknx.getDate()).padStart(2, '0');
                                                                                                                                var mmtglminleadtimerdd = String(tglknx.getMonth() + 1).padStart(2, '0');
                                                                                                                                var yyyytglminleadtimerdd = tglknx.getFullYear();

                                                                                                                                
                                                                                                                                leadtime_so = ddtglminleadtimerdd + '-' + mmtglminleadtimerdd + '-' + yyyytglminleadtimerdd;                                                                                                                             
                                                                                                                                }
                                                                                                                                //
                                                                                                                        // alert("masuk RDD2");
                                                                                                                        if((tglx>=tglknx)&&(tglx<=tglmaxleadtime)){
                                                                                                                            //rollback request SCM
                                                                                                                        // if((tglx>=tglminleadtimerdd)&&(tglx<=tglmaxleadtime)){
                                                                                                                            com_tgl.value=tgl;
                                                                                                                        }else{
                                                                                                                            if((tglx>=tglmaxleadtime)){
                                                                                                                                alert("tanggal terima maximum adalah "+maxleadtime);
                                                                                                                                com_tgl.value=maxleadtime;
                                                                                                                            }
                                                                                                                            else{
                                                                                                                                //rollback request SCM
                                                                                                                                // alert("tanggal terima minimum adalah "+rddleadtimemin);
                                                                                                                                // com_tgl.value=rddleadtimemin;
                                                                                                                                alert("tanggal terima minimum adalah "+leadtime_so);
                                                                                                                                com_tgl.value=leadtime_so;
                                                                                                                            }
                                                                                                                        }
                                                                                                                        }else{
                                                                                                                            // alert(tglx++tglmaxleadtime);
                                                                                                                            // if((tglx>=tglknx1)&&(tglx<=tglmaxleadtime)){
                                                                                                                            //     com_tgl.value=tgl;
                                                                                                                            // }else{
                                                                                                                                var tglkn1 = parseInt(knplus1.substr(0,2),10);
                                                                                                                                var blnkn1 = parseInt(knplus1.substr(3,2),10);
                                                                                                                                var thkn1 = parseInt(knplus1.substr(6,4),10);
                                                                                                                                var tglkno = blnkn1+"/"+tglkn1+"/"+thkn1;
                                                                                                                                var tglknx = new Date(tglkno);
                                                                                                                                // ///
                                                                                                                                Date.prototype.addDays = function (days) {
                                                                                                                                    const date = new Date(this.valueOf());
                                                                                                                                    date.setDate(date.getDate() + days);
                                                                                                                                    return date;
                                                                                                                                };
                                                                                                                                const date = new Date(tglknx);

                                                                                                                                dates=parseInt(config);
                                                                                                                                var tglmaxleadtime = date.addDays(dates);
                                                                                                                                var dd = String(tglmaxleadtime.getDate()).padStart(2, '0');
                                                                                                                                var mm = String(tglmaxleadtime.getMonth() + 1).padStart(2, '0');
                                                                                                                                var yyyy = tglmaxleadtime.getFullYear();
                                                                                                                                maxleadtime = dd + '-' + mm + '-' + yyyy;
                                                                                                                                // alert(tglmaxleadtime+" "+tglx);
                                                                                                                                if((tglx>=tglmaxleadtime)){
                                                                                                                                    alert("tanggal terima maximum adalah "+maxleadtime);
                                                                                                                                    com_tgl.value=maxleadtime;
                                                                                                                                }else if ((tglx<ttglknx)){
                                                                                                                                    alert("tanggal terima minimum adalah "+knplus1);
                                                                                                                                    com_tgl.value=knplus1; 
                                                                                                                                }else{
                                                                                                                                    com_tgl.value=tgl;
                                                                                                                                }

                                                                                                                                var btnsimpan2 = 'save';
                                                                                                                                var simpanorderpp = document.getElementById(btnsimpan2);
                                                                                                                                simpanorderpp.disabled="";
                                                                                                                                // }
                                                                                                                        }                                                                                                                        
                                                                                                                    // }

                                                                                                                }
                                                                                                                    } else {
                                                                                                                        alert("There was a problem while using XMLHTTP:\n" + reqrdd.statusText);
                                                                                                                        com_tgl.value='';
                                                                                                                    }
                                                                                                                }				
                                                                                                            }			
                                                                                                req.open("GET", strURL, true);
                                                                                                req.send(null);
                                                                                            }
                                                                                //}
                                                                        } else {
                                                                                lodingact(1);
                                                                                alert("There was a problem while using XMLHTTP:\n" + reqs.statusText);
                                                                                com_tgl.value='';
                                                                        }
                                                                }               
                                                        }           
                                                        reqs.open("GET", strURL, true);
                                                        reqs.send(null);
                                                }
                                            // if((tglx >= tglmaxleadtime))
                                            // {
                                            //         alert("tanggal maximum adalah "+maxleadtime);
                                            //         com_tgl.value=maxleadtime;
                                            // } else if (tglx >= ttglknx) { 
                                            //         alert("tanggal kirim sesuai mapingan leadtime adalah "+leadtime_so);
                                            //         com_tgl.value=leadtime_so;
                                            // }else{
                                            //     alert("tanggal kirim sesuai mapingan leadtime adalah "+leadtime_so);
                                            //     com_tgl.value=leadtime_so;
                                            // }
                                            }else{
                                                if( (tglx >= ttglknx) )
                                            	{
                                                        com_tgl.value=tgl;
                                            	} else { 
                                                        com_tgl.value=knplus1; 
                                                    }   
                                            }
                                } else {
                                        lodingact(1);
                                        alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                        com_tgl.value='';
                                }
                        }				
                }			
                reqrdd.open("GET", strURL, true);
                reqrdd.send(null);
/////////////////////////////////////
// }else{
//     if( (tglx >= ttglknx) )
// 	{
//             com_tgl.value=tgl;
// 	} else { 
//             com_tgl.value=knplus1; 
//         }   
// }
 //penambahan change 3664, plant 7954 tgl tidak boleh melebihi hari H        
 var plant=document.getElementById('plant').value;
 var tglnow = new Date(Date.now());
        // if(plant == '7954' && tglx >= tglnow){
        //     alert('Pengiriman dari Plant 7954 tanggal delivery maksimal hari H');
        //     com_tgl.value=knplus1; 
        // }       
       
}



////////////
function load_config_umur_so(ke) {
    var plant=document.getElementById('plant');
    var val_plant=plant.options[plant.selectedIndex].value;
    var kotatj=document.getElementById('kota_tujuan');
    var val_kotatj=kotatj.options[kotatj.selectedIndex].value;
    var com=document.getElementById('org');  
    var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&plant="+val_plant+"&produkin="+tipe+"&kodedistrik="+val_kotatj+"&action=config_umur_so";
    var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            if (req.status == 200) { 
                                lodingact(1); 
                                    var leadtime_so=req.responseText;

                            } else {
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
    }
}
///////////

function cek_data_input(){
    var qty_sisa =  parseInt($("#com_sisa").val(),10);
    var pricelist_04 = $("#pricelist").val()
    var qty_input =  parseInt($("#qty1").val(),10);

      if(pricelist_04=='04' || pricelist == '15'|| pricelist == '19'|| pricelist == '07'){
        if(qty_sisa < qty_input) {
            alert("Sisa qty kontrak tidak mencukupi");
            $("#qty1").val('');
        }
    }
}
function buttonhilang(){
   const tombolSave = document.getElementById("save");
const loadingElement = document.createElement("span");
loadingElement.id = "loadingd";
loadingElement.name = "loadingd";
loadingElement.style.display = "block";
loadingElement.innerHTML = '<img src="../images/loading.gif" alt="loading" /> Loading...!!';
tombolSave.style.display = "none";
tombolSave.parentNode.insertBefore(loadingElement, tombolSave.nextSibling);
}

var isOverdueValid = false;

function cekOverDue(){
    var org_due = document.getElementById('org').value;
    var distr_due = document.getElementById('sold_to').value;
    var tgl_kirim_due = document.getElementById('tgl_kirim1').value;

    if(org_due !== '' && distr_due !== '' && tgl_kirim_due !== ''){
        var strURL = "loadexplanning3_royalty.php?org_due=" + org_due + "&distr_due=" + distr_due + "&tgl_kirim_due=" + tgl_kirim_due + "&action=cek_overdue";
        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                if (req.readyState == 4) {
                    lodingact(1);
                    if (req.status == 200) {
                        var response = req.responseText.trim();
                        
                        if (response !== 'tidak ada') {
                            alert(response);
                        }
                    } else {
                        alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                    }
                }
            };
            req.open("GET", strURL, true);
            req.send(null);
        }
    }
}
function cek_data() {
    buttonhilang();
    //Add Thamrin
    var credit_limit = <?php echo json_encode($cr_limit)?>;
    var creditlimit = credit_limit.replace(/,/g, '');
    var min_credit_limit= <?php echo json_encode($cr_min_limit)?>;
    intcreditmin = parseInt(min_credit_limit);
    intcredit= parseInt(creditlimit);
    var top_jav = document.getElementById('top').value;
    if(intcredit<=intcreditmin && top_jav!='0001'){
        alert("credit limit "+credit_limit+" tidak mencukupi untuk order PP ");
        document.hasil = false;
        return false;
    }
    //end Thamrin

    ////////////////////
    var so_type = document.getElementById('so_type').value;
    var jkemasan=document.getElementById('jenis_kemasan');  
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }
    ////////////////////
    var validating;
	var obj = document.getElementById('jumlah');
    var pricelist = $("#pricelist").val();
    if (!pricelist) {
        alert('Silahkan pilih pricelist !!');
        document.hasil = false;
        return false;
    }
	   var cek = obj.value;
        var orgvl = document.getElementById("org"); 

        var qtykon = $("#com_sisa").val();
        var qtyaju = 0;
        for (var i = 1; i <= cek; i++){	
                // kondisi smbr penambahan pricelist 10 30 Maret 2023
                if ((pricelist == '01' && so_type == 'ZOR' && tipe == '121-301') || pricelist == '02' || pricelist == '04' || pricelist == '06'  || pricelist == '14'|| pricelist == '64' || pricelist == '68' || pricelist == '76' || pricelist == '77' || pricelist == '78' || pricelist == '10') {
                    validating = validasi('kota_tujuan','','R','top','','R','nama_top','','R','nama_pricelist','','R','plant','','R','Pembayaran','','R','jenis_kirim','','R','shipto'+i+'','R','shiptoshow'+i+'','','','R','produk'+i+'','','R','qty'+i+'','','RisNum','tgl_kirim'+i+'','','R');
                }else{
                    validating = validasi('com_kontrak','','R','com_posnr','','R','com_sisa','','R','nama_shipto1','','R','alamat1','','R','kode_distrik1','','R','nama_distrik1','','R','kode_prov1','','R','nama_prov1','','R','com_data_top','','R','com_desc_top','','R','kota_tujuan','','R','top','','R','nama_top','','R','nama_pricelist','','R','plant','','R','Pembayaran','','R','jenis_kirim','','R','shipto'+i+'','','R','shiptoshow'+i+'','','R','produk'+i+'','','R','qty'+i+'','','RisNum','tgl_kirim'+i+'','','R');
                }
                if (validating) {                        
                    var valqtyc=document.getElementById("qty"+i);
                    // quantum(valqtyc,i);
                    quantum_soccv2(valqtyc,i);
                }else{
                    document.hasil = false;
                    return false;
                }
                qtyaju += parseInt($("#qty"+i).val());		
        }
        console.info(qtyaju);
        console.info(qtykon);  
        
        qtykon = parseInt(qtykon);
        if (isNaN(qtykon)) {
                qtykon= 0;
        }
        qtyaju = parseInt(qtyaju);
        if (isNaN(qtyaju)) {
                qtyaju= 0;
        }
        if (qtykon) {
            if ( qtykon< qtyaju) {
                document.hasil = false;
                alert('quantity pembelian tidak boleh melebihi quantity kontrak !!!');
                return false;
            } 
        } 
        // return false; 
        cek_last('jumlah');
        //cekplant();
        // cegatan untuk user melakukan refresh setelah submit atau klik tombol simpan
         document.addEventListener("keydown", function (e) {
        if (e.key === "F5" || (e.ctrlKey && e.key === "r")) {
            e.preventDefault();
            alert("Halaman tidak bisa di-refresh setelah submit.");
        }
    });


 }
 
function cekplant(obj) {
    var plant=document.getElementById('plant').value;
    var distrik=document.getElementById('kota_tujuan').value;
    var tipesemen=document.getElementById('jenis_kemasan').value;
    if(plant!='' && distrik!='' && tipesemen!=''){
        var strURL="loadexplanningpp_royalty.php?plant="+plant+"&distrik="+distrik+"&tipesemen="+tipesemen+"&action=cekplant";
        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                        // only if "OK"
                        if (req.status == 200) {
                            if (req.responseText == 'hold') {
                                alert('Plant '+plant +' tidak dapat menyuplai distrik '+distrik+'!!!');
                                document.hasil = false;
                                document.getElementById(obj).value = '';
                                resetForm('0');
                                return false;
                            }
                        } else {
                                alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                        }
                        lodingact(1);
                    }               
            }
        }
        req.open("GET", strURL, true);
        req.send(null);
    }
}

function norebate() {   
        var com_price = document.getElementById('pricelist');
        var com_kontrak = document.getElementById('kontrak');
        var com_lblk = document.getElementById('labelk');
        var com_btn = document.getElementById('btn_kontrak');
        if ( com_price.value == '07') {
        com_kontrak.style.visibility = 'visible'; 
        com_lblk.style.visibility = 'visible'; 
        com_btn.style.visibility = 'visible'; 
        }
        else {
        com_kontrak.style.visibility = 'hidden'; 
        com_lblk.style.visibility = 'hidden'; 
        com_btn.style.visibility = 'hidden'; 
        }
}
function nolc() {   
        var com_so = document.getElementById('so_type');
        var com_lc = document.getElementById('lcnum');
        var com_lbl = document.getElementById('labelc');
        if ( com_so.value == 'ZEX') {
        com_lc.style.visibility = 'visible'; 
        com_lbl.style.visibility = 'visible'; 
        }
        else {
        com_lc.style.visibility = 'hidden'; 
        com_lbl.style.visibility = 'hidden'; 
        }
}
function cekkonfirm() { 
        var com_reason = document.getElementById('reason');
        var com_so = document.getElementById('oldso');
        var com_lblso = document.getElementById('lblso');
        var com_price = document.getElementById('price_date');
        var com_lblprice = document.getElementById('lblprice');

        if ( com_reason.value == 'Z02') {
            com_so.style.visibility = 'visible'; 
            com_lblso.style.visibility = 'visible'; 
            com_price.style.visibility = 'visible'; 
            com_lblprice.style.visibility = 'visible'; 
        }
        else {
            com_so.style.visibility = 'hidden'; 
            com_lblso.style.visibility = 'hidden'; 
            com_price.style.visibility = 'hidden'; 
            com_lblprice.style.visibility = 'hidden'; 

        }
}

function cekkontrak() { 
        nokontrak = '';
        sisaqtykontrak = 0;
        kontrakshipto='';
        kontrakmaterial = '';
        tglkontrakvalid = '';
        $("#com_kontrak").val('');
        $("#com_posnr").val('');
        $("#com_sisa").val(0);
        var com_price = document.getElementById('pricelist');
        var com_tipe = document.getElementById('so_type');
        var com_kontrak = document.getElementById('btn_kontrak');
                var comorg = document.getElementById('org');

        // if ( (com_price.value == '07') && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; } 
        // else if ( com_price.value == '01' && (com_tipe.value == 'ZPR') ) { com_kontrak.disabled=""; }
        // else if ( com_price.value == '04' && (com_tipe.value == 'ZPR') && (comorg.value == '4000') ) { com_kontrak.disabled=""; }
        // else if ( com_price.value == '04' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
        // else if ( com_price.value == '09' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
        // else if ( com_price.value == '10' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
        // else if ( com_price.value == '13' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
        // else if ( com_price.value == '16' && (com_tipe.value == 'ZOR') ) { com_kontrak.disabled=""; }
        // else { com_kontrak.disabled="disabled"; }
        if (com_price.value == '02' || com_price.value == '06' || com_price.value == '') {
            $("#btn_kontrak").attr('disabled',true);
        }else{
            $("#btn_kontrak").attr('disabled',false);
        }
        resetForm('0'); 
}

function findkontrak(kei) { 
    var comorg = document.getElementById('org');
        var comdistr = document.getElementById('sold_to');
        var comdistrik=$("#kota_tujuan").val();
        var route = document.getElementById('route');
        var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';
        var comshipto = document.getElementById('shipto'+kei);
        var comproduk = document.getElementById('produk'+kei);
        var comprice = document.getElementById('pricelist');
        var plant = $("#plant").val();
        var matnr = $('#produk1').val();
        var incoterm = $("#jenis_kirim").val();
        var jkemasan=document.getElementById('jenis_kemasan'); 
        var _brand = encodeURIComponent($('#brand').val());
                // var comreason = document.getElementById('reason');
        // var comqty = document.getElementById('com_qty'+kei);
        var filterdistrik='';
        if (tipe == '121-302') {
            filterdistrik = '&distrik='+comdistrik;
        }else{
            if (comprice.value == '07' || comprice.value == '09' || comprice.value == '13') {
                filterdistrik = '';
            }else if(comprice.value == '01' || comprice.value == '10' || comprice.value == '16' || comprice.value == '19' || comprice.value == '07' || comprice.value == '04'){
                filterdistrik = '&distrik='+comdistrik;
            }
        }

        // if ( comprice.value == '07') {
        //     var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value;
        // }else if ( comprice.value == '09') {
        //     var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value;
        // }else if ( comprice.value == '10') {
        //     var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value+"&distrik="+comdistrik.value+"&shipto="+comshipto.value;
        // }else if ( comprice.value == '13') {
        //     var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value;
        // }else if ( comprice.value == '16') {
        //     var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value+"&distrik="+comdistrik.value;
        // }else if ( comprice.value == '01') {
        //     var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&produk="+comproduk.value+"&nourut="+kei+"&comprice="+comprice.value+"&distrik="+comdistrik.value+"&shipto="+comshipto.value;
        // }else {
        //     var strURL="cari_kontrak.php?org="+comorg.value+"&sold_to="+comdistr.value+"&distrik="+comdistrik.value+"&produk="+comproduk.value+"&nourut="+kei+"&shipto="+comshipto.value;
        // }
        if (route.value == 'ZR0001') {
            routess = 'sea';
        }else{
            routess = 'land';
        }
        // var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&comprice="+comprice.value+"&nourut="+kei+filterdistrik+"&produk="+tipe+'&plant='+plant+'&matnr='+matnr+'&route='+routess+'&distid='+distid+"&jkemasan="+jkemasan.value+"&brand="+_brand;
        //     popUp(strURL);
        //     resetForm('0');

           if ( comprice.value == '04' || comprice.value == '01'  || comprice.value == '15'|| comprice.value == '19'|| comprice.value == '07') {
            var strURL="cari_kontrakrebnew_04.php?org="+comorg.value+"&sold_to="+comdistr.value+"&comprice="+comprice.value+"&nourut="+kei+filterdistrik+"&produk="+tipe+'&plant='+plant+'&matnr='+matnr+'&route='+routess+'&distid='+distid+"&jkemasan="+jkemasan.value+"&brand="+_brand+"&incoterm="+incoterm+"&distrik="+comdistrik;
            popUp(strURL);
            resetForm('0'); 
        } else {
            var strURL="cari_kontrakrebnew.php?org="+comorg.value+"&sold_to="+comdistr.value+"&comprice="+comprice.value+"&nourut="+kei+filterdistrik+"&produk="+tipe+'&plant='+plant+'&matnr='+matnr;
        popUp(strURL);
        resetForm('0');
        }
}

function handlePricelistChange() {
    var pricelistValue = document.getElementById("pricelist").value;

    // Jika pricelist 01 dipilih, ubah tipe order menjadi Project Sales
    if (pricelistValue === '01') {
        document.getElementById('so_type').value = 'ZPR'; // Set tipe order ke Project Sales
        document.tambahNew.nama_so_type.value = 'Project Sales'; // Set title yang sesuai
        
        // Tampilkan kembali pricelist 01 jika sebelumnya disembunyikan
        $("#pricelist option[value='01']").removeAttr("style"); 
        $("#pricelist").val("01"); // Set pricelist ke 01 - Project
    }
}

function showhideprice(ke) {
    // console.info($("#so_type").val());
//     Tipe Order Standard Sales :
// 1.     Jenis Kemasan Curah -> 06, 07, 13
// 2.     Jenis Kemasan ZAK -> semua tanpa 06 (dicabut per 10 mei 2020) HF
 
// Tipe Order Project Sales -> Price List cuma 01
    var jkmasan = $("#jenis_kemasan").val();
    $("#pricelist option").removeAttr("style");
    if ($("#so_type").val() == 'ZOR' ) {
        // $('#pricelist option[title="Project"]').css("display", "none");
        if (jkmasan == 'ZAK') {
            // $("#pricelist option").each(function (index, value) { 
            //     if ($(this).attr('title')=='Project') {
            //         $(this).css('display','none');
            //     }else{
            //         $(this).removeAttr("style");
            //     }
            // });
        }else if(jkmasan == 'CURAH'){
            $("#pricelist option").each(function (index, value) { 
                if ($(this).val()=='01'  || $(this).val() == '02'  || $(this).val() == '06'|| $(this).val() == '66') {
                    $(this).removeAttr("style");
                }else{
                    $(this).css('display','none');
                }
            });
        }
    } else if ($("#so_type").val() == 'ZFC' ) {
        $('#pricelist option[title="Project"]').css("display", "none");
        if (jkmasan == 'ZAK') {
            $("#pricelist option").each(function (index, value) { 
                if ($(this).attr('title')=='Project') {
                    $(this).css('display','none');
                }else{
                    $(this).removeAttr("style");
                }
            });
        }else if(jkmasan == 'CURAH'){
            $("#pricelist option").each(function (index, value) { 
                if ($(this).val()=='01'  || $(this).val() == '02'  || $(this).val() == '06'|| $(this).val() == '66') {
                    $(this).removeAttr("style");
                }else{
                    $(this).css('display','none');
                }
            });
        }
    }else if ($("#so_type").val() == 'ZPR' ){
        $("#pricelist option").each(function (index, value) { 
            if ($(this).attr('title')!='Project') {
                $(this).css('display','none');
            }else{
                $(this).removeAttr("style");
            }
          // console.log($(this).attr('title')); 
        });
        // $('#pricelist option[title="Project"]').removeAttr("style");
    }
    
    if(ke!='1'){
        $("#pricelist").val("");
        cekkontrak();
    }    
}

function cekharianbulanan() {
    var plant=document.getElementById('plant').value;
    if(plant!='' && tipe!=''){
        var strURL="loadexplanningpp_royalty.php?plant="+plant+"&produkin="+tipe+"&action=cekharianbulanan";

        var req = getXMLHTTP();
        if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                        // only if "OK"
                        if (req.status == 200) {   
                                if (req.responseText == 'bulanan') {
                                    harianbulanan = 'bulanan';
                                }else{
                                    harianbulanan = 'harian';
                                }
                        } else {
                                alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                        }
                        lodingact(1);
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
        }
    }
}

// function cekshiptotop() { 
//     var jkemasan=document.getElementById('jenis_kemasan');    
//     var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';  
//     var route = document.getElementById('route'); 
//     var com=document.getElementById('org');
//     var shipto=document.getElementById('shipto1');
//     //set tipe 
//     if (!kodeprop || !route.value || !jkemasan.value) {
//         $("#top").val('');
//         $("#nama_top").val('');
//         return false;
//     }
//     if (route.value == 'ZR0001') {
//         routess = 'sea';
//     }else{
//         routess = 'land';
//     }
//     var strURL="loadexplanningpp_royalty.php?com="+com.value+'&shipto='+shipto.value+'&action=cekshiptotop';
//     var req = getXMLHTTP();
//     if (req) {
//             req.onreadystatechange = function() {
//                 lodingact(0);
//                     if (req.readyState == 4) {
//                             // only if "OK"
//                             if (req.status == 200) {
//                                    lodingact(1);
//                                        $("#cek").val(req.responseText);
//                                        //return false;
//                             } else {
//                                     lodingact(1);
//                                     alert("There was a problem while using XMLHTTP:\n" + req.statusText);
//                             }
//                     }               
//             }           
//             req.open("GET", strURL, true);
//             req.send(null);
//     }
// }
function cekshiptotop() { 
    var jkemasan=document.getElementById('jenis_kemasan');    
    var distid='<?=ltrim($_SESSION['distr_id'], "0"); ?>';  
    var route = document.getElementById('route'); 
    var com=document.getElementById('org');
    var shipto=document.getElementById('shipto1');
    //set tipe 
    if (!kodeprop || !route.value || !jkemasan.value) {
        $("#top").val('');
        $("#nama_top").val('');
        return false;
    }
    if (route.value == 'ZR0001') {
        routess = 'sea';
    }else{
        routess = 'land';
    }
    var strURL="loadexplanning3_royalty.php?com="+com.value+'&shipto='+shipto.value+'&action=cekshiptotop';
    var req = getXMLHTTP();
    if (req) {
            req.onreadystatechange = function() {
                lodingact(0);
                    if (req.readyState == 4) {
                            // only if "OK"
                            if (req.status == 200) {
                                   lodingact(1);
                                       $("#cek").val(req.responseText);
                                       //return false;
                            } else {
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                            }
                    }               
            }           
            req.open("GET", strURL, true);
            req.send(null);
    }
}

function cekkbongkar(ke) { 
    var shipto=document.getElementById('shipto'+ke);
    var qty=document.getElementById('qty'+ke);
    var tipesemen=document.getElementById('jenis_kemasan');
    
    if(shipto.value != ''){
        var strURL="loadexplanningpp_royalty.php?qty="+qty.value+'&shipto='+shipto.value+'&tipe='+tipesemen.value+'&action=cekkbongkar';
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    lodingact(0);
                        if (req.readyState == 4) {
                                // only if "OK"
                                if (req.status == 200) {
                                       if (req.responseText != '0') {
                                            alert('QTY tidak boleh melebihi kemampuan bongkar('+req.responseText+')');   
                                            document.getElementById("qty"+ke).value = "";                                            
                                        }
                                        lodingact(1);
                                } else {
                                        lodingact(1);
                                        alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                        }               
                }           
                req.open("GET", strURL, true);
                req.send(null);
        }
    }else{
        alert("Harap pilih shipto terlebih dahulu");
    }
}

function cekgudang(obj,ke){
    if (!obj.value) {
        obj.value = 0;
    }
    var com_plant = document.getElementById('plant');
    var com_plantval = com_plant.value;
    // var com_produk = document.getElementById('produk'+ke);
    // var com_produkval = com_produk.value;
    // var com_converto = document.getElementById('qto'+ke);
    // var com_convertoval = parseFloat(com_converto.value);
    // var com_typetruck = document.getElementById('typetruck'+ke);
    // var com_typetruckval = com_typetruck.value;
    var nilai_qty =parseFloat(obj.value);
    //////////////////////////////////////////////////////////////
    var kotatj=document.getElementById('kota_tujuan');
    var com=document.getElementById('org'); 
    var val_kotatj=kotatj.options[kotatj.selectedIndex].value;
    var jkemasan=document.getElementById('jenis_kemasan');  
    var dist=document.getElementById('sold_to');
    var shipto=document.getElementById('shipto1');
    switch (jkemasan.value) {
        case 'CURAH':
            tipe = '121-302';
            break;
        case 'MORTAR ZAK':
            tipe = '121-701';
            break;
        case 'MORTAR CURAH':
            tipe = '121-702';
            break;
        case 'M3':
            tipe = '121-800';
            break;
        case 'M3 PALLET1':
            tipe = '121-800';
            break;
        default:
            tipe = '121-301';
    }  

        var strURL="loadexplanningpp_royalty.php?orgin="+com.value+"&soldin="+dist.value+"&kodedistrik="+val_kotatj+'&shipto='+shipto.value+'&plant='+com_plantval+'&produkin='+tipe+'&action=getdatagudang';
        var req = getXMLHTTP();
        if (req) {
                req.onreadystatechange = function() {
                    lodingact(0);
                        if (req.readyState == 4) {                         
                                // only if "OK"
                                if (req.status == 200) {	
                                    lodingact(1);
                                    var ok = req.responseText;
                                    alert("get data mdxl"+ok);
                                } else {             
                                    lodingact(1);
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                        }				
                }			
                req.open("GET", strURL, true);
                req.send(null);
        }
    /////////////////////////////////////////////////////////////
}

$(document).on('change','#route',function () {
    load_top();
});



//-->
</script>

<title>Aplikasi SGG Online: Input Permintaan Pembelians :)</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="ba1">Input Permintaan Pembelian</th>
</tr></table></div>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Form Input Permintaan Pembelian </th>
</tr>
</table></div>
<div  align="center">
<form action="komentar_pp_royalty.php" method="post" name="tambahNew" id="tambahNew" onsubmit="return cek_data()" onchange="cekOverDue();">
<table width="600" border="0" class="adminform" align="center">
 <tr>
    <td width="175"><strong>Tanggal </strong></td>
    <td width="12"><strong>:</strong></td>
    <td colspan="2">
        <?=gmdate("d-m-Y",time()+60*60*7);?>
        <input type="hidden" id="org" name="org" value="<?=$user_org;?>" />
	<input type="hidden" id="tglnya" name="tglnya" value="<?=gmdate("d-m-Y",time()+60*60*7);?>" />
        <input type="hidden" id="tgl1nya" name="tgl1nya" value="<?=$tglkirimnext;?>" />
	</td>
 </tr>
 <tr>
    <td width="175"><strong>Sisa Kredit Limit </strong></td>
    <td width="12"><strong>:</strong></td>
    <td colspan="2"><?=$creditlimitnya;?>
	</td>
 </tr>

 <tr>
  <td><strong>Jenis Kemasan</strong></td>
  <td><strong>:</strong></td>
  <td colspan="2">
          <div>
            
            <select name="jenis_kemasan" id="jenis_kemasan" onChange="cekBrand(this.value); resetForm('0'); clearData();" >
    <option value="">---Pilih Jenis Kemasan---</option>
    <option value="ZAK">ZAK</option>
    <!-- <option value="CURAH">CURAH</option>
    <option value="MORTAR ZAK">MORTAR ZAK</option>
    <option value="MORTAR CURAH">MORTAR CURAH</option> -->
    
    </select>
          </div>  
  </td>
</tr>


 <tr >
  <td><strong>Brand</strong></td>
  <td><strong>:</strong></td>
  <td colspan="2">
          <div id="div_brand">
            <select name="brand" id="brand"  onChange ="clearData();">
            <option value="">---Pilih Brand---</option>
            <?php 

                $getData="SELECT BRAND FROM MASTER_BRAND WHERE DEL_MARK != 'Y' GROUP BY BRAND ORDER BY BRAND";
                // $conn = $this->or_koneksi();					
                $setData=oci_parse($conn,$getData);
                oci_execute($setData);
                // $row=oci_fetch_assoc($setData);
                // echo "<select name='produk'>";
                while ($row=oci_fetch_assoc($setData)) {
                    echo "<option value='" . $row['BRAND'] . "'>" . $row['BRAND'] . "</option>";
                }
                // echo "</select>";
            ?>
 
    </select>
          </div>  
  </td>
</tr>


 <tr>
 <td ><strong>Tipe Order</strong></td>
   <td><strong>:</strong></td>
   <td colspan="2">
        <select name="so_type" id="so_type" onChange="document.tambahNew.nama_so_type.value=this.options[this.selectedIndex].title;nolc();cekkontrak();resetForm('1');showhideprice(); cekPlantKota(); clearData()">
		        <option value="">---Pilih Tipe Order---</option>
		        <? $fungsi->or_order_type3('ZOR'); ?>	  
		</select>
		<input type="hidden" value="Sales Standard" id="nama_so_type" name="nama_so_type" />
        <label id="labelc" style="visibility:hidden" >Nomor LC : </label>
        <input type="text" style="visibility:hidden" value="" id="lcnum" name="lcnum" size="10"/>
    </td>
 </tr>

<tr>
	  <td><strong>Jenis Pengiriman </strong></td>
	  <td><strong>:</strong></td>
	  <td width="168"><select name="jenis_kirim" id="jenis_kirim" onChange="document.tambahNew.nama_kirim.value=this.options[this.selectedIndex].title;getroute(this.value);get_loadtype(this.value);cekPlanya(this.value); clearData(); ">
		<option value="">---Pilih Pengiriman---</option>
		<? $fungsi->or_jenis_kirim($jenis_kirim); ?>     
		</select>	
		<input type="hidden" value="" id="nama_kirim" name="nama_kirim" />
	  </td>
          <td>
              <div id="routediv">
                <!--<select name="route" id="route" onchange="load_top();">-->
                <select name="route" id="route" >
                    <option value="">---Route---</option>
                </select>
              </div>
           </td>
	</tr>
<!-- <tr>resetKota
    <td width="175"><strong>Branch Plant </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2">
                <select name="branch_plant" id="Branch Plant" onChange="document.tambahNew.nama_bplant.value=this.options[this.selectedIndex].title">
		<option value="">---Pilih---</option>
		<? //$fungsi->or_jjns_plant($branch_plant); ?>     
		</select>	
		<input type="hidden" value="" id="nama_bplant" name="nama_bplant" />
	</td>
   </tr>-->
   <tr>
      <td><strong>Kota / Provinsi</strong></td>
      <td><strong>:</strong></td>
      <td colspan="2">
              <div id="loadkota">
                <!-- onChange="resetKota(this)" -->
                <select name="kota_tujuan" id="kota_tujuan" onclick="loadKota()" onChange="getPlant(); clearData();"  >
                <option value="">---Pilih Kota / Provinsi---</option>
                </select>
              </div>  
      </td>
    </tr>
        
    <tr>
        <td><strong>List Material</strong></td>
        <td><strong>:</strong></td>
        <td colspan="2">
                <div>
                <!-- <td align="left"> -->
                    <div id="produkdiv1">
                        <input name="produk1" type="text" class="inputlabel" id="produk1" value="<?=$produk?>"  onChange="ketik_produk(this,'1'); clearData();"  maxlength="20" size="12"/>
                        <input name="nama_produk1" type="text" class="inputlabel" id="nama_produk1" value="<?=$nama_produk?>" readonly="true"  size="20"/>
                        <input name="uom1" type="text" class="inputlabel" id="uom1" value="<?=$uom?>" readonly="true"  size="4"/>
                        <input name="qto1" type="hidden" class="inputlabel" id="qto1" value="<?=$kg?>" readonly="true"  size="4"/>
                        <input name="btn_produk1" type="button" class="button" id="btn_produk1" value="..." onClick="findproduk('1')"/>
                        <input name="val_error_produk1" type="hidden" id="val_error_produk1" value="0" />
                    </div>
                <!-- </td>  -->
                </div>  
        </td>
    </tr>

    <tr id="plant_row">
        <td><strong>Plant</strong></td>
        <td><strong>:</strong></td>
        <td colspan="2">
            <input type="hidden" id="plantBayangan" name="plantBayangan"  readonly="true"/>    
            <div id="loadplant">
                    <select name="plant" onclick="setPlant()" id="plant"  onChange="clearData();">
            <option value="" title="">---Pilih Plant---</option>
            </select>
                </div>  
                <input type="hidden" value="" id="nama_plant" name="nama_plant" />
        </td>
    </tr>
        
<!--	<tr>
	  <td><strong>Pembayaran</strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">
                <select name="cara_bayar" id="Pembayaran">
		<option value="">---Pilih Pembayaran---</option>
		<? $fungsi->or_bayar($cara_bayar); ?>     
		</select>	
	  </td>
	</tr>-->
        <input type="hidden" value="<?=$cara_bayar;?>" id="cara_bayar" name="cara_bayar" />
	
        
    <tr>
        <td><strong>Shipping Condition ( <i><small>Optional</small></i> )</strong></td>
      <td><strong>:</strong></td>
      <td colspan="2"> 
              <div id="loadshipcond">
                <select name="ship_condition" id="ship_condition"  >
                    <option value="">---Pilih Shipping Condition---</option>
                </select>
              </div>   
      </td>
    </tr>
<!--	<tr>
	  <td><strong>Route Pengiriman</strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2"><div id="routediv">
          <select name="route" id="route">
            <option value="">---Route---</option>
          </select>
        </div>
	  </td>
	</tr>-->
    <!--<tr>
      <td><strong>Price List </strong></td>
      <td><strong>:</strong></td>
      <td colspan="2"><select name="pricelist" id="pricelist" onChange="document.tambahNew.nama_pricelist.value=this.options[this.selectedIndex].title;cekkontrak(); clearData();">
        <option value="">---Pilih Price List---</option>
        <? $fungsi->or_pricelist3($pricelist); ?>     
        </select>   
        <input name="nama_pricelist" type="hidden" id="nama_pricelist" value="<?=$nama_pricelist;?>" <?=@$hanyabaca?> >
      </td>
    </tr> -->
    <tr>
      <td><strong>Segmen </strong></td>
      <td><strong>:</strong></td>
      <td colspan="2">
      <select name="pricelist" id="pricelist" onChange="updateFormBasedOnPricelist(); document.tambahNew.nama_pricelist.value=this.options[this.selectedIndex].title;cekkontrak(); clearData(); handlePricelistChange();">
        <option value="">---Pilih Segmen---</option>
        <? $fungsi->or_pricelist3($pricelist); ?>     
        </select>   
        <input name="nama_pricelist" type="hidden" id="nama_pricelist" value="<?=$nama_pricelist;?>" <?=@$hanyabaca?> >
      </td>
    </tr>
        <tr style="display:none;">
      <td><strong>Metode Pembayaran</strong></td>
      <td><strong>:</strong></td>
      <td colspan="2">
      <input type="hidden" value="NEW PP" id="tipeCreatePP" name="tipeCreatePP" size="7" readonly="true"/>
     <!-- <select name="top" id="top"  readonly onChange="document.tambahNew.nama_top.value=this.options[this.selectedIndex].title">
        <option value="">---Pilih TOP---</option>
        <? $fungsi->or_cara_bayar('0001'); ?>     
        </select>   
        <input name="nama_top" type="text" id="nama_top" value="<?=$nama_top;?>" readonly />    -->
      </td>
    </tr>
    <!-- <tr>
      <td><strong>Note</strong></td>
      <td><strong>:</strong></td>
      <td colspan="2">
              <input type="hidden" value="NEW PP" id="tipeCreatePP" name="tipeCreatePP" size="7" readonly="true"/>
              <input name="keterangan" type="text" class="inputlabel" id="Keterangan" value="<?=$keterangan?>" <?=@$hanyabaca?> size="70" maxlength="250"/>
          </td>
    </tr> -->
	<tr>
	  <td><strong>No Kontrak</strong></td>
	  <td><strong>:</strong></td>
	  <td colspan="2">
        <input size="10" name="com_kontrak" type="text" id="com_kontrak" value="<?=$kontrak;?>" readonly="true"/>
        <input size="10" name="com_posnr" type="hidden" id="com_posnr" value="<?=$posnr;?>"/>     
        <input id="btn_kontrak" name="btn_kontrak" type="button" class="button" onClick="findkontrak('1');" value="..." />
        </td>
	</tr>
    <tr>
      <td><strong>Sisa Qty Kontrak</strong></td>
      <td><strong>:</strong></td>
      <td colspan="2">
              <input name="com_sisa" readonly type="text" class="inputlabel" id="com_sisa" value="<?=$sisa;?>"/>
          </td>
    </tr>
    <tr>
      <td><strong>Note ( <i><small>Optional</small></i> )</strong></td>
      <td><strong>:</strong></td>
      <td colspan="2">
          <textarea name="keterangan" type="text" class="inputlabel" id="keterangan" rows="4" cols="50"></textarea>
          </td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td colspan="2">      
        <span id="loadingd" name="loadingd" style="display: none"><img src="../images/loading.gif" alt="loding" />Loading...!!</span>
      </td>
    </tr>
</table> 
<br/><br/>
<table width="1195" align="center" class="adminlist">
  <tr>
	<th align="left" colspan="4"> Item Permintaan Pembelian</th>
  </tr>
</table>
<table width="1195" align="center" class="adminlist">
<tr class="quote">
    <!-- <td align="left">Estimasi Tanggal Terima </td> -->
    <td align="left" id="estimasi_tanggal_label">Estimasi Tanggal ... </td>    
    <td align="left">Sisa Target (TON) </td>
    <td align="left">Kode Toko / Nama Toko </td>
    <td align="left">Metode Pembayaran </td>
    <!-- <td align="left">Kode Produk / Nama Produk</td> -->
    <td> QTY </td>
    <td> No Kontrak </td>
    <td> </td>
</tr>
<tr>
<td align="left">
	<input name="tgl_kirim1" type="text" id="tgl_kirim1" size=12 value="" required="true" onClick="return showCalendar('tgl_kirim1');"  onBlur="cektanggal('tgl_kirim1');loadSISATARGET('1');" />
</td>    
<td align="left">
	<input type="text" value="" id="fsisa_target1" name="fsisa_target1" size="5" readonly="true" />
    <input name="sisa_target1" type="hidden" class="inputlabel" id="sisa_target1" value="<?=$fsisa?>" readonly="true" size="5"/>
</td>    

<td align="left">
    <div id="shiptodiv1">
        <input name="shipto1" type="text" class="inputlabel" id="shipto1" value="<?=$shipto?>" onChange="ketik_shipto(this,'1');" maxlength="12" size="10"/>
        <input name="nama_shipto1" type="text" class="inputlabel" id="nama_shipto1" value="<?=$nama_shipto?>" readonly="true" size="20" />
        <input type="text" value="" id="alamat1" name="alamat1" readonly="true" />
        <input type="hidden" value="" id="kode_distrik1" name="kode_distrik1" />
        <input type="text" value="" id="nama_distrik1" name="nama_distrik1" readonly="true" />
        <input type="hidden" value="" id="kode_prov1" name="kode_prov1" />
        <input type="hidden" value="" id="nama_prov1" name="nama_prov1" />
        <input type="hidden" value="" id="typetruck1" name="typetruck1" />
        <input name="btn_shipto1" type="button" class="button" id="btn_shipto1" value="..." onClick="findshipto('1')" />
        <input name="val_error_shipto1" type="hidden" id="val_error_shipto1" value="0" />
    </div>
</td>
<script>
    // Fungsi yang dipanggil ketika pricelist diubah
        function updateFormBasedOnPricelist() {
            var pricelistValue = document.getElementById('pricelist').value; // Mendapatkan nilai dari elemen pricelist

            // Mengambil elemen dengan id 'shiptodiv1' dan 'loadtopcombo'
            var shiptodiv1 = document.getElementById('shiptodiv1');
            var loadtopcombo = document.getElementById('loadtopcombo');

            // Mengecek apakah pricelist adalah '04' atau '01'
           if (pricelistValue == '04' || pricelistValue == '01' || pricelistValue == '15' || pricelistValue == '19' || pricelistValue == '07') {
                // Menghapus semua elemen di dalam div shiptodiv1
                shiptodiv1.innerHTML = ""; 

                // Menambahkan HTML baru ke dalam shiptodiv1
                shiptodiv1.innerHTML = `
            <input name="shiptoshow" type="text" id="shiptoshow" value="" readonly="true" size="10"/>
            <input name="shipto1" type="hidden" id="shipto1" value="" readonly="true" size="10"/>
            <input name="nama_shipto1" type="text" id="nama_shipto1" value="" readonly="true" size="20" />
            <input type="text" value="" id="alamat1" name="alamat" readonly="true" />
            <input type="hidden" value="" id="kode_distrik1" name="kode_distrik1" readonly="true"/>
            <input type="text" value="" id="nama_distrik1" name="nama_distrik1" readonly="true" />
            <input type="hidden" value="" id="kode_prov1" name="kode_prov1" readonly="true"/>
            <input type="hidden" value="" id="nama_prov1" name="nama_prov1" readonly="true" />
            <input type="hidden" value="" id="typetruck1" name="typetruck1" />
        `;

        // Mengganti konten loadtopcombo dengan input hidden baru
        loadtopcombo.innerHTML = `
                    <select name="top" id="top" style="display: none;">
                        <option value="" title="" id="com_data_top" name="com_data_top" selected>TERPILIH</option>
                    </select>
                    <input type="text" value="" id="com_desc_top" readonly="true"/>
                `;
            } else {
                // Menghapus semua elemen di dalam div shiptodiv1
                shiptodiv1.innerHTML = "";

                // Mengembalikan konten lama ke dalam shiptodiv1
                shiptodiv1.innerHTML = `
                    <input name="shipto1" type="text" class="inputlabel" id="shipto1" value="<?=$shipto?>" onChange="ketik_shipto(this,'1');" maxlength="12" size="10" />
                    <input name="nama_shipto1" type="text" class="inputlabel" id="nama_shipto1" value="<?=$nama_shipto?>" readonly="true" size="20" />
                    <input type="text" value="" id="alamat1" name="alamat1" readonly="true" />
                    <input type="hidden" value="" id="kode_distrik1" name="kode_distrik1" />
                    <input type="text" value="" id="nama_distrik1" name="nama_distrik1" readonly="true" />
                    <input type="hidden" value="" id="kode_prov1" name="kode_prov1" />
                    <input type="hidden" value="" id="nama_prov1" name="nama_prov1" />
                    <input type="hidden" value="" id="typetruck1" name="typetruck1" />
                    <input name="btn_shipto1" type="button" class="button" id="btn_shipto1" value="..." onClick="findshipto('1')" />
                    <input name="val_error_shipto1" type="hidden" id="val_error_shipto1" value="0" />
                `;

                // Mengembalikan konten lama ke dalam loadtopcombo (dropdown)
                loadtopcombo.innerHTML = `
                    <select name="top" id="top" readonly onChange="document.tambahNew.nama_top.value=this.options[this.selectedIndex].title">
                        <option value="" title="">-- Pilih TOP --</option>
                    </select>
                `;
            }
        }
</script>

<td align="left"> 
    <div id="loadtopcombo">
        <select name="top" id="top"readonly onChange="document.tambahNew.nama_top.value=this.options[this.selectedIndex].title">
            <option value="" title="">-- Pilih TOP --</option>
        </select>
    </div>
</td>


<!-- <td align="left">
    <div id="produkdiv1">
	  <input name="produk1" type="text" class="inputlabel" id="produk1" value="<?=$produk?>" onChange="ketik_produk(this,'1')" maxlength="20" size="12"/>
          <input name="nama_produk1" type="text" class="inputlabel" id="nama_produk1" value="<?=$nama_produk?>" readonly="true"  size="20"/>
	  <input name="uom1" type="text" class="inputlabel" id="uom1" value="<?=$uom?>" readonly="true"  size="4"/>
          <input name="qto1" type="hidden" class="inputlabel" id="qto1" value="<?=$kg?>" readonly="true"  size="4"/>
          <input name="btn_produk1" type="button" class="button" id="btn_produk1" value="..." onClick="findproduk('1')"/>
          <input name="val_error_produk1" type="hidden" id="val_error_produk1" value="0" />
    </div>
</td>    -->
                                                                                                                                            <!-- cekQtyBulanan(); -->
                                                                                                                                            <!-- jika nanti mau di buat pencegatan kalau qty bulanan habis gak bisa isi ini bisa di aktifkan kembali ya  -->
<td align="left">                                                                                                                                         
        <input type="text" value="" id="qty1" name="qty1" size="6" maxlength="6" onChange="cekHoliday();" onBlur="javascript:IsNumeric(this);quantum_soccv2(this,'1');cekHoliday(); cek_data_input();"/>
        <input type="hidden" readonly="true" value="" id="qtycek1" name="qtycek1" size="6" maxlength="6"/>
        <input type="hidden" readonly="true" value="" id="cek" name="cek" size="6" maxlength="6"/>
</td>

<td align="center">
        <input size="10" name="com_kontrak1" type="text" id="com_kontrak1" value="<?=$kontrak;?>" readonly="true"/>
        <!-- <<input size="10" name="com_posnr1" type="hidden" id="com_posnr1" value="<?=$posnr;?>"/>
        <input size="10" name="com_sisa1" type="hidden" id="com_sisa1" value="<?=$sisa;?>"/>        
        input id="btn_kontrak1" name="btn_kontrak1" type="button" class="button" onClick="findkontrak('1');" disabled="disabled" value="..." /> -->
        </td>
    <!--
<td width="100">
    <input type="button" style="display:none" value=" + " name="tambahNew" onClick="return start_add();" />
	<input type="button" value="  -  " name="kurang" onClick="return stop_add();" /> 
</td>
-->
</tr>
</table>
<div id="coba"></div>
</div>
<br>
<br>


<div align="center">
  <table width="600" border="0" align="center" class="adminlist">
	<tr>
      <td colspan="4"><div align="center">
        <input name="save" type='submit' class="button" id="save" disabled='disabled' value="Simpan" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 	<input name="action" type="hidden" value="create_pp" />
        <input name="lasthalaman" type="hidden" value="create_pp_dist_royalty.php" />
      </div></td>
    </tr>
  </table>
</div>

<input type="hidden" value="1" name="jumlah" id="jumlah" />
<input type="hidden" value="<?=$sold_to?>" name="sold_to" id="sold_to" size="12"/>
<input type="hidden" value="<?=$nama_sold_to?>" name="nama_sold_to" id="nama_sold_to" size="12"/>   
<?
echo '
   <script language="JavaScript" type="text/javascript">
       showhideprice();     
  </script>
';
?>   
</form>
<br/><br/>
<? include ('ekor.php'); ?>
</body>
</html>
