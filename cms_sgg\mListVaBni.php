<?php

/**
 * Description of mEntryBayarVa
 *
 * <AUTHOR> D Munir <<EMAIL>>
 * @since 1.0
 */
class mListVaBni
{
    /**
     *
     * @var dHttpClient
     */
    public $client;
    public $bosUrl = 'https://***********/dev/bni_va_php7';

    public function __construct()
    {
        if (dee::$app->request->isLocal) {
            $this->bosUrl = 'http://sisi.test/dev-bos/bni_va';
        }
        $this->client = new dHttpClient();
        $this->client->baseUrl = $this->bosUrl;
    }

    public function getInvoices($org = null,$vanum = null)
    {
        $sapFunction = dee::$app->sapFunction;
        $params = array(
            'I_ACTION' => 'S',
            'I_BUKRS'  => '2000'
        );

        if (!empty($org)) {
            $params['I_ORG'] = $org;
        }

        if (!empty($vanum)) {
            $params['I_VANUM'] = $vanum;
        }

        $result = $sapFunction->call('ZFI_DISPLAY_VA', $params);

        return $result['T_HEADER'];
    }

    public function getBni($kode)
    {
        $sql = "SELECT * FROM TB_USER_BOOKING WHERE DISTRIBUTOR_ID=" . $kode;
        $db = dee::$app->db;
        return $db->queryOne($sql);
    }
}
