<?php

/**
 * Description of cEntryBayarVa
 *
 * <AUTHOR> D Munir <<EMAIL>>
 * @since 1.0
 */
class cEntryBayarVa extends dController
{

    protected function pageIds()
    {
        return array(
            'index' => true,
            'listInvoice' => true,
            'createVa' => true,
        );
    }

    public function index()
    {
        $orgs = array(
            array('id' => '', 'text' => '-')
        );
        foreach (dee::$app->fungsi->arrayorg() as $key => $value) {
            $orgs[] = array('id' => $key, 'text' => $key . '- ' . $value);
        }
        return $this->render('entry_bayar_va', array('orgs' => $orgs));
    }

    public function listInvoice()
    {
        $kode = $this->request->get('dist');
        if (empty($kode)) {
            return array();
        }
        $kode = str_pad(trim($kode), 10, '0', STR_PAD_LEFT);
        $model = new mEntryBayarVa();
        $dist = $model->getDist($kode);

        $org = $this->request->get('org');
        $invoices = $model->getInvoices($kode, $org);
        if ($dist && isset($dist['NAMA_LENGKAP'])) {
            foreach ($invoices as &$row) {
                $row['VENDOR_NAME'] = $dist['NAMA_LENGKAP'];
            }
        }
        return array(
            'total' => count($invoices), 
            'rows' => $invoices,
        );
    }

    public function createVa()
    {
        $invoices = $this->request->post('invoices', array());

        $model = new mEntryBayarVa();
        var_dump($invoices);
        die;
        $res = $model->createVa(array_values($invoices));
        return $res;
    }
}
