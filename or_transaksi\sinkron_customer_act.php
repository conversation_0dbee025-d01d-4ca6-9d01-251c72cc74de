<?php

session_start();
include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();
$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];
$distr_id=$_SESSION['distr_id'];
$keydistributor=$distr_id;
$distr_id=$fungsi->sapcode($_SESSION['distr_id']);
$distr=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"DISTRIBUTOR_ID");
$mp_coics=$fungsi->getComin($conn,$user_org);

$sql="select * from tb_user_vs_plant where user_id='$user_id' and delete_mark=0";
$query= oci_parse($conn, $sql);
oci_execute($query);unset($werks);
while($row=oci_fetch_array($query)){
    $werks[$row['PLANT']]=$row['PLANT'];
}
$id = htmlspecialchars($_REQUEST['id']);
$aksi = htmlspecialchars($_REQUEST['act']);
$sold_to_x = htmlspecialchars($_REQUEST['SOLD_TO']);
$ship_to_x = htmlspecialchars($_REQUEST['SHIP_TO']);
//$sold_to_x = '357';
//$ship_to_x = '3570000001';
//
//echo 'sold : '.$sold_to_x.'  ';
//echo 'ship : '.$ship_to_x.'  ';

if(isset($aksi)){
switch($aksi) { 
  case 'show' :
    {         
        $where = '';
        if($WSOLDCODE==''){
            $where  .= "";
        }else{
           $distr =  $WSOLDCODE;
		$panjang=strlen(strval($WSOLDCODE));
		if($panjang==1)$soldto='000000000'.$distr;
		if($panjang==2)$soldto='00000000'.$distr;
		if($panjang==3)$soldto='0000000'.$distr;
		if($panjang==4)$soldto='000000'.$distr;
		if($panjang==5)$soldto='00000'.$distr;
		if($panjang==6)$soldto='0000'.$distr;
		if($panjang==7)$soldto='000'.$distr;
		if($panjang==8)$soldto='00'.$distr;
		if($panjang==9)$soldto='0'.$distr;
		if($panjang==10)$soldto=$distr;
            $where  .= " AND KUNNR = '{$soldto}' ";  
        }
  
        if($WSHIPCODE==''){
            $where  .= "";
        }else{
            $distr =  $WSHIPCODE;
		$panjang=strlen(strval($WSHIPCODE));
		if($panjang==1)$soldto='000000000'.$distr;
		if($panjang==2)$soldto='00000000'.$distr;
		if($panjang==3)$soldto='0000000'.$distr;
		if($panjang==4)$soldto='000000'.$distr;
		if($panjang==5)$soldto='00000'.$distr;
		if($panjang==6)$soldto='0000'.$distr;
		if($panjang==7)$soldto='000'.$distr;
		if($panjang==8)$soldto='00'.$distr;
		if($panjang==9)$soldto='0'.$distr;
		if($panjang==10)$soldto=$distr;
            $where  .= " AND KUNN2 = '{$soldto}' "; 
        }
        
        if($where == ''){
            echo json_encode('tidak ada data yang dicari');
        }else{
            $sql= "
				SELECT
					* 
				FROM
					RFC_Z_ZCSD_SHIPTO 
				WHERE 
					KUNN2 is not null
				{$where} ORDER BY KUNN2 ASC
            ";   
            //echo $sql;
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            while($row=oci_fetch_array($query)){            
                array_push($result, $row);
            }    
            echo json_encode($result);
            }
     }
     break;
     case 'add' :
     {
        if($_REQUEST['upload'] == 'excel'){
            require_once '../ex_report/excel_reader2.php';
            $allowedExts = "xls";
            $extension = end(explode(".", $_FILES["file_upload"]["name"]));
            if ($extension==$allowedExts) {
                $cell   = new Spreadsheet_Excel_Reader($_FILES['file_upload']['tmp_name']);
                $jumlah_row = $cell->rowcount($sheet_index=0);

                for ($i = 2; $i <= $jumlah_row; $i++) {
                    if($cell->val( $i,1 ) != '' && $cell->val( $i,2 ) != ''){
                        if($cell->val( $i,3 ) == 1){
                            $return = deleteData($conn, $cell->val( $i,1 ), $cell->val( $i,2 ));
                            if($return) $info[$i-2] = ' '.$cell->val( $i,1 )."-".$cell->val( $i,2 )." berhasil dihapus. ";
                        }else{
                            $return = insertData($conn, $cell->val( $i,1 ), $cell->val( $i,2 ));
                            if($return) $info[$i-2] = ' '.$cell->val( $i,1 )."-".$cell->val( $i,2 )." berhasil ditambah atau diupdate. ";
                        }
                        $return = true;
                    } else
                        break;
                }
            } else {
                echo $return = json_encode(array('errorMsg'=>'Some errors occured.'));
                return;
            }
        }
        else
            $return = insertData($conn, $sold_to_x, $ship_to_x);

        if ($return){
            echo json_encode(array('success'=>true,'info'=>json_encode($info)));
        } else {
            echo json_encode(array('errorMsg'=>'Some errors occured.'));
        }
        
     }
     break;
     case 'delete' :
     {
        $result = deleteData($conn, $soldto, $shipto);
        if ($result){
            echo json_encode(array('success'=>'shipto :'.$shipto.' dengan soldto : '.$soldto.' sudah didelete'));
        } else {
            echo json_encode(array('errorMsg'=>'Some errors occured.'));
        }
     }
     break;
}
}

function insertData($conn, $sold_to_x, $ship_to_x){
    $where = '';
    if($sold_to_x==''){
        $where  .= "";
    }else{
        $distr =  $sold_to_x;
            $panjang=strlen(strval($sold_to_x));
            if($panjang==1)$soldto='000000000'.$distr;
            if($panjang==2)$soldto='00000000'.$distr;
            if($panjang==3)$soldto='0000000'.$distr;
            if($panjang==4)$soldto='000000'.$distr;
            if($panjang==5)$soldto='00000'.$distr;
            if($panjang==6)$soldto='0000'.$distr;
            if($panjang==7)$soldto='000'.$distr;
            if($panjang==8)$soldto='00'.$distr;
            if($panjang==9)$soldto='0'.$distr;
            if($panjang==10)$soldto=$distr;
            $sold_to = $soldto;
        $where  .= "KUNNR = '{$soldto}' ";  
    }

    if($ship_to_x ==''){
        $where  .= "";
    }else{
        $distr =  $ship_to_x ;
            $panjang=strlen(strval($ship_to_x));
            if($panjang==1)$soldto='000000000'.$distr;
            if($panjang==2)$soldto='00000000'.$distr;
            if($panjang==3)$soldto='0000000'.$distr;
            if($panjang==4)$soldto='000000'.$distr;
            if($panjang==5)$soldto='00000'.$distr;
            if($panjang==6)$soldto='0000'.$distr;
            if($panjang==7)$soldto='000'.$distr;
            if($panjang==8)$soldto='00'.$distr;
            if($panjang==9)$soldto='0'.$distr;
            if($panjang==10)$soldto=$distr;
            $ship_to  = $soldto;
        $where  .= " AND KUNN2 = '{$soldto}' "; 
    }
    $sqlcek= "select count(*) as JML from RFC_Z_ZCSD_SHIPTO WHERE {$where}";
    // echo $sqlcek;var_dump($conn);
    $querycek= oci_parse($conn, $sqlcek);
    oci_execute($querycek);
    $rowYY=oci_fetch_array($querycek);
    $nodata=trim($rowYY['JML']);
    
    if($nodata==0){
        //ambil data dari SAP
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
        }

                $fce = $sap->NewFunction ("Z_ZCSD_SHIPTO");
                if ($fce == false ) {
                    $sap->PrintStatus();
                    exit;
                }

                //header entri
                $fce->ZKUNN2 = $ship_to;
                $fce->ZKUNNR = $sold_to;

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK ) {		
                    $fce->RETURN_DATA->Reset();
                    while ($fce->RETURN_DATA->Next() ){
                        $kunnr      = $fce->RETURN_DATA->row["KUNNR"];
                        $name1      = $fce->RETURN_DATA->row["NAME1"];
                        $name2      = $fce->RETURN_DATA->row["NAME2"];
                        $ort01      = $fce->RETURN_DATA->row["ORT01"];
                        $regio      = $fce->RETURN_DATA->row["REGIO"];
                        $stras      = $fce->RETURN_DATA->row["STRAS"];
                        $telf1      = $fce->RETURN_DATA->row["TELF1"];
                        $telfx      = $fce->RETURN_DATA->row["TELFX"];
                        $cityc      = $fce->RETURN_DATA->row["CITYC"];
                        $ktokd      = $fce->RETURN_DATA->row["KTOKD"];
                        $kunn2      = $fce->RETURN_DATA->row["KUNN2"];
                        $bzrik      = $fce->RETURN_DATA->row["BZIRK"];
                        $bztkt      = $fce->RETURN_DATA->row["BZTXT"];
                        $kvgr1      = $fce->RETURN_DATA->row["KVGR1"];
                        $kvgr2      = $fce->RETURN_DATA->row["KVGR2"];
                        $kvgr3      = $fce->RETURN_DATA->row["KVGR3"];
                        $kvgr4      = $fce->RETURN_DATA->row["KVGR4"];
                        $bezei1     = $fce->RETURN_DATA->row["BEZEI1"];
                        $bezei2     = $fce->RETURN_DATA->row["BEZEI2"];
                        $bezei3     = $fce->RETURN_DATA->row["BEZEI3"];
                        $bezei4     = $fce->RETURN_DATA->row["BEZEI4"];
                        $vkbur      = $fce->RETURN_DATA->row["VKBUR"];
                        $bezeb      = $fce->RETURN_DATA->row["BEZEB"];
                        $vkgrp      = $fce->RETURN_DATA->row["VKGRP"];
                        $bezeg      = $fce->RETURN_DATA->row["BEZEG"];
                        $bran1      = $fce->RETURN_DATA->row["BRAN1"];
                        $vtext      = $fce->RETURN_DATA->row["VTEXT"];
                        $shipto_name    = $fce->RETURN_DATA->row["SHIPTO_NAME"];
                        $shipto_addr    = $fce->RETURN_DATA->row["SHIPTO_ADDR"];
                        $inco1      = $fce->RETURN_DATA->row["INCO1"];
                        $pallet     = $fce->RETURN_DATA->row["PALLET"];
                        $vwerk      = $fce->RETURN_DATA->row["VWERK"];
                        $erdat      = $fce->RETURN_DATA->row["ERDAT"];
                        $katr9      = $fce->RETURN_DATA->row["KATR9"];
                        $katr9_text = $fce->RETURN_DATA->row["KATR9_TEXT"];
                        $payer      = $fce->RETURN_DATA->row["PAYER"];
                        $name_payer	= $fce->RETURN_DATA->row["NAME_PAYER"];
                        $bill_to_party	= $fce->RETURN_DATA->row["BILL_TO_PARTY"];
                        $name_bill  = $fce->RETURN_DATA->row["NAME_BILL"];
                    }
                }else
                    $fce->PrintStatus();

                $fce->Close();	
                $sap->Close();	

        $sql= "INSERT INTO RFC_Z_ZCSD_SHIPTO (ZKTOKD1,ZKTOKD2,KUNNR,NAME1,NAME2,ORT01,REGIO,STRAS,TELF1,TELFX,CITYC,KTOKD,KUNN2,BZIRK,BZTXT,KVGR1,KVGR2,KVGR3,KVGR4,BEZEI1,BEZEI2,BEZEI3,BEZEI4,VKBUR,BEZEB,VKGRP,BEZEG,BRAN1,VTEXT,SHIPTO_NAME,SHIPTO_ADDR,INCO1,PALLET,VWERK,ERDAT,KATR9,KATR9_TEXT) 
                VALUES ('ZSG1','ZSG2','$kunnr','$name1','$name2','$ort01','$regio','$stras','$telf1','$telfx','$cityc','$ktokd','$kunn2','$bzrik','$bztkt ','$kvgr1','$kvgr2','$kvgr3 ','$kvgr4','$bezei1','$bezei2','$bezei3','$bezei4','$vkbur','$bezeb','$vkgrp','$bezeg','$bran1','$vtext','$shipto_name','$shipto_addr','$inco1','$pallet','$vwerk','$erdat','$katr9','$katr9_text')";   
        //echo $sql;
        $query= oci_parse($conn, $sql);
        $result=oci_execute($query);
        //$e = oci_error($query);
        return $result;
        
    } else {
        //ambil data dari SAP
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
        }

                $fce = $sap->NewFunction ("Z_ZCSD_SHIPTO");
                if ($fce == false ) {
                    $sap->PrintStatus();
                    exit;
                }

                //header entri
                $fce->ZKUNN2 = $ship_to;
                $fce->ZKUNNR = $sold_to;

                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK ) {		
                    $fce->RETURN_DATA->Reset();
                    while ($fce->RETURN_DATA->Next() ){
                        $kunnr      = $fce->RETURN_DATA->row["KUNNR"];
                        $name1      = $fce->RETURN_DATA->row["NAME1"];
                        $name2      = $fce->RETURN_DATA->row["NAME2"];
                        $ort01      = $fce->RETURN_DATA->row["ORT01"];
                        $regio      = $fce->RETURN_DATA->row["REGIO"];
                        $stras      = $fce->RETURN_DATA->row["STRAS"];
                        $telf1      = $fce->RETURN_DATA->row["TELF1"];
                        $telfx      = $fce->RETURN_DATA->row["TELFX"];
                        $cityc      = $fce->RETURN_DATA->row["CITYC"];
                        $ktokd      = $fce->RETURN_DATA->row["KTOKD"];
                        $kunn2      = $fce->RETURN_DATA->row["KUNN2"];
                        $bzrik      = $fce->RETURN_DATA->row["BZIRK"];
                        $bztkt      = $fce->RETURN_DATA->row["BZTXT"];
                        $kvgr1      = $fce->RETURN_DATA->row["KVGR1"];
                        $kvgr2      = $fce->RETURN_DATA->row["KVGR2"];
                        $kvgr3      = $fce->RETURN_DATA->row["KVGR3"];
                        $kvgr4      = $fce->RETURN_DATA->row["KVGR4"];
                        $bezei1     = $fce->RETURN_DATA->row["BEZEI1"];
                        $bezei2     = $fce->RETURN_DATA->row["BEZEI2"];
                        $bezei3     = $fce->RETURN_DATA->row["BEZEI3"];
                        $bezei4     = $fce->RETURN_DATA->row["BEZEI4"];
                        $vkbur      = $fce->RETURN_DATA->row["VKBUR"];
                        $bezeb      = $fce->RETURN_DATA->row["BEZEB"];
                        $vkgrp      = $fce->RETURN_DATA->row["VKGRP"];
                        $bezeg      = $fce->RETURN_DATA->row["BEZEG"];
                        $bran1      = $fce->RETURN_DATA->row["BRAN1"];
                        $vtext      = $fce->RETURN_DATA->row["VTEXT"];
                        $shipto_name    = $fce->RETURN_DATA->row["SHIPTO_NAME"];
                        $shipto_addr    = $fce->RETURN_DATA->row["SHIPTO_ADDR"];
                        $inco1      = $fce->RETURN_DATA->row["INCO1"];
                        $pallet     = $fce->RETURN_DATA->row["PALLET"];
                        $vwerk      = $fce->RETURN_DATA->row["VWERK"];
                        $erdat      = $fce->RETURN_DATA->row["ERDAT"];
                        $katr9      = $fce->RETURN_DATA->row["KATR9"];
                        $katr9_text = $fce->RETURN_DATA->row["KATR9_TEXT"];
                        $payer      = $fce->RETURN_DATA->row["PAYER"];
                        $name_payer	= $fce->RETURN_DATA->row["NAME_PAYER"];
                        $bill_to_party	= $fce->RETURN_DATA->row["BILL_TO_PARTY"];
                        $name_bill  = $fce->RETURN_DATA->row["NAME_BILL"];
                    }
                }else
                    $fce->PrintStatus();

                $fce->Close();	
                $sap->Close();
        $sql= "UPDATE RFC_Z_ZCSD_SHIPTO SET NAME1 = '$name1', NAME2= '$name2', ORT01 = '$ort01', REGIO = '$regio', STRAS = '$stras', 
                TELF1 = '$telf1', TELFX =  '$telfx', CITYC = '$cityc', KTOKD = '$ktokd', BZIRK = '$bzrik', 
                BZTXT = '$bztkt ', KVGR1 =  '$kvgr1', KVGR2 = '$kvgr2', KVGR3 = '$kvgr3 ', KVGR4 = '$kvgr4', 
                BEZEI1 = '$bezei1', BEZEI2 = '$bezei2', BEZEI3 = '$bezei3', BEZEI4 = '$bezei4', VKBUR = '$vkbur', 
                BEZEB = '$bezeb', VKGRP = '$vkgrp', BEZEG = '$bezeg', BRAN1 = '$bran1', VTEXT = '$vtext', 
                SHIPTO_NAME = '$shipto_name', SHIPTO_ADDR = '$shipto_addr', INCO1 = '$inco1', PALLET = '$pallet', 
                VWERK = '$vwerk', ERDAT = '$erdat', KATR9 = '$katr9', KATR9_TEXT = '$katr9_text' WHERE {$where}"; 
        $query= oci_parse($conn, $sql);
        $result=oci_execute($query);
        //$e = oci_error($query);
        return $result;
    }
}

function deleteData($conn, $soldto, $shipto){
    $panjang=strlen(strval($soldto));
    if($panjang==1)$soldto='000000000'.$soldto;
    elseif($panjang==2)$soldto='00000000'.$soldto;
    elseif($panjang==3)$soldto='0000000'.$soldto;
    elseif($panjang==4)$soldto='000000'.$soldto;
    elseif($panjang==5)$soldto='00000'.$soldto;
    elseif($panjang==6)$soldto='0000'.$soldto;
    elseif($panjang==7)$soldto='000'.$soldto;
    elseif($panjang==8)$soldto='00'.$soldto;
    elseif($panjang==9)$soldto='0'.$soldto;

    $sql= "
        DELETE FROM RFC_Z_ZCSD_SHIPTO
        where
        KUNN2 = '$shipto' AND
        KUNNR = '$soldto' 
        ";
            
    $query= oci_parse($conn, $sql);
    $result=oci_execute($query);
    return $result;
}
?>
