<?
/*
 * @liyantanto
 */
session_start();
include ('../include/my_fungsi.php');
$fungsi=new my_fungsi();
$conn=$fungsi->koneksi();

$targetVolume='sinkron_customer.php';
$titlepage='Sinkron Customer';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

 
$sql="select * from tb_user_vs_plant where user_id='$user_id' and delete_mark=0";
$query= oci_parse($conn, $sql);
oci_execute($query);unset($werksmn);$werksmn='7403';
while($row=oci_fetch_array($query)){
    $werksmn=$row['PLANT'];
}

//$user_id='mady';
$com=$user_org;
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,3);
	else return '0';
}
function tglIndo ($param){
    $tahun=substr($param, 0,4);
    $bulan=substr($param, 4,2);
    $tgl=substr($param, 6,2);
    $format =$tgl."-".$bulan."-".$tahun;
    return $format;
}
function timeIndo ($param){
    $jam=substr($param, 0,2);
    $menit=substr($param, 2,2);
    $detik=substr($param, 4,2);
    $format =$jam.":".$menit.":".$detik;
    return $format;
}
$waktu=date("d-m-Y");
//$halaman_id=1896;//dev
//$halaman_id=3753;//prod
$dirr = $_SERVER['PHP_SELF'];
//taruh sini entar
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?=$titlepage;?></title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>

<style type="text/css">
    .icon-upload {
     background: transparent url("icon/upload.png") no-repeat scroll center center;
    }

    .icon-excel {
     background: transparent url("icon/excel.png") no-repeat scroll center center;
    }
</style>

</head>   
<body>

<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px"
           idField="itemid" rownumbers="true" pagination="true">
    <thead>
    <tr>
        <!-- <th field="ck" checkbox="true"></th> -->
        <th field="ZKTOKD1" width="100">Acc Group 1</th>
        <th field="ZKTOKD2" width="100">Acc Group 2</th>
        <th field="KUNNR" width="100" align="center">Kd Sold to</th>
        <th field="KUNN2" width="100">Kd Ship to</th>
        <th field="NAME1" width="150">Name</th>
        <th field="NAME2" width="150">Name2</th>
        <th field="ORT01" width="100">Distrik</th>
        <th field="REGIO" width="100">Region</th>
        <th field="STRAS" width="200">Alamat</th>        
        <th field="TELF1" width="100">TELF1</th>        
        <th field="TELFX" width="100">TELFX</th>
        <th field="CITYC" width="100">CITYC</th>
        <th field="KTOKD" width="100">Acc group</th>
        <th field="BZIRK" width="100">Kd Distrik</th>
        <th field="BZTXT" width="100">Distrik</th>
        <th field="KVGR1" width="100" >KVGR1</th>
        <th field="KVGR2" width="100">KVGR2</th>
        <th field="KVGR3" width="100">KVGR3</th>        
        <th field="KVGR4" width="100">KVGR4</th>        
        <th field="BEZEI1" width="150">BEZEI1</th>
        <th field="BEZEI2" width="100">BEZEI2</th>
        <th field="BEZEI3" width="100">BEZEI3</th>
        <th field="BEZEI4" width="100">BEZEI4</th>
        <th field="VKBUR" width="100">VKBUR</th>
        <th field="BEZEB" width="200">BEZEB</th>
        <th field="VKGRP" width="100" >VKGRP</th>
        <th field="BEZEG" width="200">BEZEG</th>
        <th field="BRAN1" width="100">BRAN1</th>        
        <th field="VTEXT" width="100">VTEXT</th>        
        <th field="SHIPTO_NAME" width="100">SHIPTO_NAME</th>
        <th field="SHIPTO_ADDR" width="100">SHIPTO_ADDR</th>
        <th field="INCO1" width="100">INCO1</th>
        <th field="PALLET" width="100">PALLET</th>
        <th field="VWERK" width="100">VWERK</th>
        <th field="ERDAT" width="100">ERDAT</th>
        <th field="PAYER" width="100" >PAYER</th>
        <th field="NAME_PAYER" width="100">NAME_PAYER</th>
        <th field="BILL_TO_PA" width="100">BILL_TO_PA</th>
        <th field="NAME_BILL" width="100" >NAME_BILL</th>
        <th field="KATR9" width="100">KATR9</th>
        <th field="KATR9_TEXT" width="100">KATR9 TEXT</th>
    </tr>
    </thead>
    </table>
    <div id="toolbar">
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">New</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" onclick="delAct()">Remove</a>
    <a class="easyui-linkbutton" plain="true" iconCls="icon-excel" href="template_xls/template_sinkron_kustomer.xls" >Download Template</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-upload" onclick="uploadAct()">Upload Excel</a>


    <label>&nbsp&nbsp Kode Sold to :</label>    
    <input id="cari_soldcode" name="cari_soldcode" class="easyui-textbox" >  
    <label> Kode Ship to :</label>    
    <input id="cari_shipcode" name="cari_shipcode" class="easyui-textbox" >  
    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search'" id="newSearch" style="width:80px">Search</a>
    <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>
    </div>
    
    <div id="dlg" class="easyui-dialog" style="padding:10px 20px"
    closed="true" buttons="#dlg-buttons">
    <div class="ftitle">Sinkron Customer</div>
    <form id="fm" method="post" novalidate>
    <div class="fitem">
        <label>Kode Sold to :</label>    
        <input id="SOLD_TO" name="SOLD_TO" required="true" class="easyui-textbox" maxlength="10" style="width:200px;"> 
    </div>  
    <div class="fitem">
        <label>Kode Ship to :</label>    
        <input id="SHIP_TO" name="SHIP_TO" required="true" class="easyui-textbox" maxlength="10" style="width:200px;">  
    
    </div>   
    </form>
    </div>
    <div id="dlg-buttons">  
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
    </div>
    
    <div id="dlgdel" class="easyui-dialog" style="padding:10px 20px"
    closed="true" buttons="#dlg-buttons">
    <div class="ftitle">Data Mapping Customer</div>
    <form id="fmdel" method="post" novalidate>
    <div class="fitem">
        <label>Kode Sold to :</label>    
        <input id="soldtodel" name="soldtodel" class="easyui-textbox" maxlength="10" value="" disabled style="width:200px;"> 
    </div>  
    <div class="fitem">
        <label>Kode Ship to :</label>    
        <input id="shiptodel" name="shiptodel" class="easyui-textbox" maxlength="10" value="" disabled style="width:200px;">  
    
    </div>   
    </form>
    </div>
    <div id="dlg-buttons">  
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="savedelAct()" style="width:90px" id="savedata">Delete</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlgdel').dialog('close')" style="width:90px">Cancel</a>
    </div>

    <div id="dlg_upload" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_upload-buttons">
        <form id="uploadForm" name="import" enctype="multipart/form-data" method="post">
            <div style="margin-bottom:20px">
                <input class="easyui-filebox" label="Pilih File :" labelPosition="top" id="file_upload" name="file_upload" data-options="prompt:'Pilih File Upload'" style="width:100%">
            </div>
        </form>

        <div id="dlg_upload-buttons">
            <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveUploadAct()" style="width:90px" id="saveUpload">Upload</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg_upload').dialog('close')" style="width:90px" id="close_upload">Cancel</a>
        </div>
    </div>

    <div id="dlg_info" class="easyui-dialog" style="width:100%;max-width:500px; padding:30px 60px;" closed="true" buttons="#dlg_info-buttons">
        <pre>
        <label id="label_info"></label>
        </pre>

        <div id="dlg_info-buttons">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="javascript:$('#dlg_info').dialog('close');$('#dg').datagrid('reload')" style="width:90px" id="close_info">OK</a>
        </div>
    </div>

    
<script type="text/javascript">

 $(function(){
    $("#dg").datagrid({
            url:'sinkron_customer_act.php?act=show',
            singleSelect:true,
            pagination:true, 
            pageList:[10,50,100,300,500,1000,5000,10000],
            pageSize:20,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
            
    });
    $('#dg').datagrid('enableFilter');
 });

$('#dlg').dialog({
    title: 'My Dialog',
    width: 350,
    height: 350,
    closed: true,
    cache: false,
    // href: 'get_content.php',
    modal: true
}); 
    
$("#newSearch").click(function() {   
    var w_soldcode = $('#cari_soldcode').textbox('getValue');
    var w_shipcode = $('#cari_shipcode').textbox('getValue');
           if(w_soldcode=="" && w_shipcode=="" ){
               $.messager.show({
                    title: 'Error',
                    msg: 'Isi Filter terlebih dahulu'
                });
          }else{   
         //$('#sold_to, #shipto1, #sold_to_opco, #shipto_opco1').val("")
           $('#dg').datagrid('load',{
                WSOLDCODE: w_soldcode,
                WSHIPCODE: w_shipcode
           }); // reload the user data
          }  
        });
        var url;
    
function readonlyinput(){
//    $('#TARGET_DATEF').datebox({readonly: true});
    $('#alltipei').combo('readonly', true);
//    $('#PLANT').combo({readonly: true});
}    

var url;
function newAct(){
    $('#dlg').dialog('open').dialog('setTitle','New Sinkron Customer');
    $('#fm').form('clear');
    url = 'sinkron_customer_act.php?act=add';
    
//    $('#NO_PO').numberbox({readonly: false});
    
    
}

function delAct(){
    var row = $('#dg').datagrid('getSelected');
    if(row){
        $('#dlgdel').dialog('open').dialog('setTitle','Delete Customer');
        $('#soldtodel').textbox('setValue',row.KUNNR);
        $('#shiptodel').textbox('setValue',row.KUNN2);
        url = 'sinkron_customer_act.php?act=delete&shipto='+row.KUNN2+'&soldto='+row.KUNNR;
    } else {
        alert('Pilih Baris Data yang ingin di delete terlebih dahulu!');
    }
    
}

function saveAct(){
$('#fm').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        } else {
            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        }
    }
});
}

function savedelAct(){
$('#fmdel').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
            $('#dlgdel').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        } else {
            $('#dlgdel').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
            alert(result.success);
        }
    }
});
}

function uploadAct() {
    $('#dlg_upload').dialog('open').dialog('setTitle','Upload Excel');
    $('#uploadForm').form('clear');
    url = 'sinkron_customer_act.php?act=add&upload=excel';
}

function saveUploadAct() {
        $('#uploadForm').form('submit',{
            url: url,
            onSubmit: function(){ 
                return $(this).form('validate');
            },
            success: function(result){
                var result = eval('('+result+')');
                if (result.errorMsg){
                    $.messager.show({
                        title: 'Error',
                        msg: result.errorMsg
                    });
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $('#dlg_upload').dialog('close'); // close the dialog
                    $('#dlg_info').dialog('open').dialog('setTitle','Upload Status');
                    let dataArray = JSON.parse(result.info.trim());
                    let formattedOutput = dataArray.map(item => item.trim()).join('\n')
                    $('#label_info').text(formattedOutput);
                    // $('#dg').datagrid('reload'); // reload the user data
                }
            }
        });
  
}

$("#btnExport").click(function() {        
    var myData = $('#dg').datagrid('getData');        
    var mapForm = document.createElement("form");
    mapForm.id = "formexport";
    mapForm.target = "dialogSave";
    mapForm.method = "POST";
    mapForm.action = "exportMapping.php?act=exportSinkronKustomer";        
    $.each(myData.rows, function(k,v){
        $.each(v, function(k2, v2){
            var hiddenField = document.createElement("input");              
            hiddenField.type = "hidden";
            hiddenField.name = "data[" + k + "][" + k2 + "]";
            hiddenField.value = v2;
            mapForm.appendChild(hiddenField);
        });
    });            
    document.body.appendChild(mapForm);
    mapForm.submit();
    document.body.removeChild(mapForm);
    
});



function forpersenmargin(val,row){
    var val3 = val
    var val1 = parseFloat(val3);
    if(val1>=100){
        return '<span style="background-color:#00C300;color:blue;display:block;">'+val+'</span>';
    }else if(val1>0){
        return '<span style="background-color:#ffee00;color:green;display:block;">'+val+'</span>';
    }else{
        return '<span style="background-color:#FFFBB6;color:red;display:block;">'+val+'</span>';
    }
}

function trim(str){
	    return str.replace(/^\s+|\s+$/g,'');
} 

function myformatter(date){
    var y = date.getFullYear();
    var m = date.getMonth()+1;
    var d = date.getDate();
    return y+'-'+(m<10?('0'+m):m)+'-'+(d<10?('0'+d):d);
}
function myparser(s){
    if (!s) return new Date();
    var ss = (s.split('-'));
    var y = parseInt(ss[0],10);
    var m = parseInt(ss[1],10);
    var d = parseInt(ss[2],10);
    if (!isNaN(y) && !isNaN(m) && !isNaN(d)){
        return new Date(y,m-1,d);
    } else {
        return new Date();
    }
}

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
