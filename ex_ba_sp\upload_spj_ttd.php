<?php
/*
 * upload master supir expeditur
 * @yopi satria
 */

session_start();
include ('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}

//$hakakses=array("admin");
$halaman_id=3095;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
//$conns=$fungsi->ex_koneksi();
//$dirr = $_SERVER['PHP_SELF']; 


$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$dirr = $_SERVER['PHP_SELF']; 
// $halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

//echo "<pre>";
//print_r($_SESSION);
//echo "</pre>";
$no_ba = $_GET['no_spj'];
$importtargetVolume='upload_spj_ttd.php';
$waktu=date("d-m-Y");

// if ($fungsi->keamanan($halaman_id,$user_id)==0) {
// ?>
// 				<SCRIPT LANGUAGE="JavaScript">
// 					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
// 				</SCRIPT>

// 	 <a href="../index.php">Login....</a>
// <?

// exit();
// }

function formatTGL($tgl){
    $a = explode("/", $tgl);
    $tgal = $a[3].$a[2].$a[1];
    return $tgal;
}


if(isset ($_POST['Import'])){
       
            if (isset($_FILES['pdf_file']['name']))
                {
                  $user_name = $_SESSION['user_name'];
                  $file_name = $_FILES['pdf_file']['name'];
                  $file_tmp = $_FILES['pdf_file']['tmp_name'];

                  $check = validate_image($file_name, $file_tmp);

                  $ext = strtolower(substr(strrchr($file_name, '.'), 1));
                  $saved_file_name = "POD2_" . $no_ba . "." . $ext;
         
                  if ($check['status']) {
                        move_uploaded_file($file_tmp,"./upload/".$saved_file_name);
                        $field_names = array('EVIDENCE_POD2','LAST_UPDATE_DATE','LAST_UPDATED_BY');
                        $field_data = array("upload/$saved_file_name", "SYSDATE", "$user_name");
                        $tablename = "EX_TRANS_HDR";
                        $field_id = array('NO_SHP_TRN');
                        $value_id = array("$no_ba");
                        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                        echo "<script>alert('Berhasil upload TTD SPJ');</script>";
                        echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =create_ba.php'>"; 
                        // echo "<script>setTimeout(function() { win.close();}, 3000)</script>";
                    } else {
                        echo "<script>alert('Invalid file...!!, ." . $check['message'] ."');</script>";
                    }
                }
            else
            {
                echo "<script>alert('Invalid file...!!');</script>";  
            }

    
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Data TTD SPJ</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Upload TTD SPJ</th>
</tr></table>
</div>

<form method="post" name="import" id="import" enctype="multipart/form-data" action="upload_spj_ttd.php?no_spj=<?=$no_ba?>">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
            <td class="puso">:</td>
            <td> <input name="pdf_file" type="file"  class="button" accept="image/gif, image/jpeg, image/png" required></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="Import" type="submit"  class="button" value="Import"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
       <!--  <tr>
            <td class="puso" colspan="3">&nbsp;&nbsp;&nbsp;Download template supir <a href="templete_xls/template_supir.xls">disini</a></td>
           
        </tr> -->
          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>