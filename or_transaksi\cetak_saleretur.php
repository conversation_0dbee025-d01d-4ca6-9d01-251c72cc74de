<?
session_start();
$nospjold = '';

include ('../include/or_fungsi.php'); 
include ('../include/validasi.php');
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$currentPage="cetak_saleretur.php";
$halaman_id=1864;
$org=$_SESSION['user_org'];
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
//$nama_lengkap=$_SESSION['nama_lengkap'];
//echo "<pre>";
//print_r($_SESSION);
//echo "</pre>";
$errormsg='';
$table= 'OR_TRANS_HDR';
$table2='OR_TRANS_DTL';

$mp_coics=$fungsi->getComin($conn,$org);
if(count($mp_coics)>0){
    unset($orgIn);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $orgIn .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($orgIn, ',');        
}else{
   $orgIn= $org;
}

//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai);
	else return '0';
}

function tglIndo ($param){
    $tahun=substr($param, 0,4);
    $bulan=substr($param, 4,2);
    $tgl=substr($param, 6,2);
    $format =$tgl."-".$bulan."-".$tahun;
    return $format;
}
function timeIndo ($param){
    $jam=substr($param, 0,2);
    $menit=substr($param, 2,2);
    $detik=substr($param, 4,2);
    $format =$jam.":".$menit.":".$detik;
    return $format;
}

//if ($fungsi->keamanan($halaman_id,$user_id)==0) {
if (($user_id=='')||($org=='')) {
?>
	<SCRIPT LANGUAGE="JavaScript">				
		alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
	</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
} 

    $idNO_SPJ=$_GET['nospjold'];
    $idNO_PP=$_GET['noppnew'];
    $sqlfilter='';
    if($idNO_PP!=''){
       $sqlfilter .=" AND m.NO_PP='$idNO_PP' ";
    }
    if($idNO_SPJ!=''){  
    
    $sql="
        
        SELECT 
        m.*,t.KODE_PRODUK, t.NAMA_PRODUK,t.QTY_PP, t.UOM,
        t.SHIP_TO, t.NAMA_SHIP_TO,t.NAMA_KAPAL,
        t. NO_SO as NEW_NOSO,
        to_char(t.APPROVE_DATE,'DD-MM-YYYY') as APPROVE_DATE,
        to_char(t.APPROVE_DATE,'MM/YYYY') as NOMORSURAT,
         to_char(t.APPROVE_DATE,'YYYY') as TAHUNAPPROVE,
        t.APPROVE_BY,
        t.KD_PROV, t.NM_PROV, t.ALAMAT_SHIP_TO, t.KODE_TUJUAN, t.NAMA_TUJUAN,
        to_char(m.PRICE_DATE,'DD-MM-YYYY') as PRICE_DATEW,t.STATUS_LINE
        FROM $table m, $table2 t
        WHERE
        m.DELETE_MARK='0' 
        AND m.ORG in ($orgIn) AND m.NO_SHP_OLD=:idNO_SPJ  $sqlfilter
        AND t.STATUS_LINE='APPROVE' AND t.DELETE_MARK='0' AND m.NO_PP=t.NO_PP

    ";
//    echo $sql;
    $querysql= oci_parse($conn, $sql);
    oci_bind_by_name($querysql, ":idNO_SPJ", $idNO_SPJ);
    oci_execute($querysql);
    while($row=oci_fetch_array($querysql)){
        
            $nospjold = $row["NO_SHP_OLD"];
            $nopp=$row["NO_PP"];
            $no_pp = $row["NO_PP"];
            $no_so = $row["NO_SO_OLD"];
            $no_spj = $row["NO_SHP_OLD"];
            $no_do = $row["NO_DO"];
            $inco = $row["INCOTERM"];
            $inco_d = $row["NAMA_INCOTERM"];
            $rute = $row["ROUTE"];
            $rute_txt = $row["ROUTE_TXT"];                   
            $qty_do = floatval($row["QTY_PP"]);           
            $kdshipto = $row["SHIP_TO"];
            $nmshipto = $row["NAMA_SHIP_TO"];            
            $alamat = $row["ALAMAT_SHIP_TO"];            
            $kddistrik = $row["KODE_TUJUAN"];
            $nmdistrik = $row["NAMA_TUJUAN"];         
            $soldto = $row["SOLD_TO"];
            $namasold = $row["NAMA_SOLD_TO"];
            $kdplant = $row["PLANT_ASAL"];
            $nmplant = $row["NAMA_PLANT"];
            $kdexp = $row["NO_EXPEDITUR"];
            $nmexp = $row["NAMA_EXPEDITUR"];            
            $produk = $row["KODE_PRODUK"];
            $produk_txt = $row["NAMA_PRODUK"];            
            $uom1 = $row["UOM"];
            $pltype = $row["PRICELIST"];
            $pltype_txt = $row["NAMA_PRICELIST"];           
            $pricedate = $row["PRICE_DATEW"];            
            $kdprov = $row["KD_PROV"];
            $nmprov = $row["NM_PROV"];
            $kapal = $row["NAMA_KAPAL"];
            $tipeso = $row["SO_TYPE"];
            $nmaso = $row["NAMA_SO_TYPE"];
            $note = $row["NOTE"];
            $kdreason = $row["KD_REASON"];
            $namareason = $row["NM_REASON"];
            $satusapp = $row["STATUS_LINE"];
            $new_noso = $row["NEW_NOSO"];
            $approvedate = $row["APPROVE_DATE"];
            $approveby = $row["APPROVE_BY"];
            $createby = $row["CREATED_BY"];
            $nomorsurat = $row["NOMORSURAT"];
            $tahunYmmk = $row["TAHUNAPPROVE"];
            
            
            
    }
    
    $sap = new SAPConnection();
    $sap->Connect("../include/sapclasses/logon_data.conf");
    if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
    if ($sap->GetStatus() != SAPRFC_OK ) {
       echo $sap->PrintStatus();
       exit;
    }

    $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
    if ($fce == false ) {
       $sap->PrintStatus();
       exit;
    }

    //header entri
    $fce->X_VKORG = $org;
    $fce->X_NOSPJ = $idNO_SPJ;
    
    $fce->X_STATUS = '70';
    $fce->X_WERKS = $kdplant;
    $fce->X_VBELN = $no_so;
    //incompany
    if(count($mp_coics)>0){
        foreach ($mp_coics as $keyOrg2 => $valorgm2){
            if($keyOrg2=='2000'){
                continue;
            }
            $fce->LRI_VKORG->row['SIGN']='I';
            $fce->LRI_VKORG->row['OPTION']='EQ';
            $fce->LRI_VKORG->row['LOW']=$keyOrg2;
            $fce->LRI_VKORG->row['HIGH']='';
            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
        }
    }

    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK ) {		
    $fce->ZDATA->Reset();
    $s=0;
    while ( $fce->ZDATA->Next() ){
            $NO_MINTA = $fce->ZDATA->row["NO_MINTA"];
            $NO_SO = $fce->ZDATA->row["NO_SO"];
            $NO_SPJ = $fce->ZDATA->row["NO_SPJ"];
            $NO_DO = $fce->ZDATA->row["NO_DO"];
            $INCOTERM = $fce->ZDATA->row["INCOTERM"];
            $INCOTERM_DESC = $fce->ZDATA->row["INCOTERM_DESC"];
            $ROUTE = $fce->ZDATA->row["ROUTE"];
            $ROUTE_TXT = $fce->ZDATA->row["ROUTE_TXT"];
            $TGL_CMPLT = $fce->ZDATA->row["TGL_CMPLT"];
            $JAM_CMPLT = $fce->ZDATA->row["JAM_CMPLT"];
            $TGL_DO = $fce->ZDATA->row["TGL_DO"];
            $TGL_MINTA = $fce->ZDATA->row["TGL_MINTA"];
            $KWANTUM = floatval($fce->ZDATA->row["KWANTUM"]);
            $NO_POLISI = $fce->ZDATA->row["NO_POLISI"];
            $NO_SPPS = $fce->ZDATA->row["NO_SPPS"];
            $NAMA_SOPIR = $fce->ZDATA->row["NAMA_SOPIR"];
            $KODE_DA = $fce->ZDATA->row["KODE_DA"];
            $NAMA_TOKO = $fce->ZDATA->row["NAMA_TOKO"];
            $ALAMAT_DA = $fce->ZDATA->row["ALAMAT_DA"];
            $AREA = $fce->ZDATA->row["AREA"];
            $NAMA_AREA = $fce->ZDATA->row["NAMA_AREA"];
            $SOLD_TO = $fce->ZDATA->row["SOLD_TO"];
            $NAMA_SOLD_TO = $fce->ZDATA->row["NAMA_SOLD_TO"];
            $PLANT = $fce->ZDATA->row["PLANT"];
            $NAMA_PLANT = $fce->ZDATA->row["NAMA_PLANT"];
            $NO_EXPEDITUR = $fce->ZDATA->row["NO_EXPEDITUR"];
            $NAMA_EXPEDITUR = $fce->ZDATA->row["NAMA_EXPEDITUR"];
            $STATUS = $fce->ZDATA->row["STATUS"];
            $ITEM_NO = $fce->ZDATA->row["ITEM_NO"];
            $PRODUK = $fce->ZDATA->row["PRODUK"];
            $UOM = $fce->ZDATA->row["UOM"];
            $HARGA = $fce->ZDATA->row["HARGA"];
            $KWMENG = $fce->ZDATA->row["KWMENG"];
            $PLTYP = $fce->ZDATA->row["PLTYP"];
            $PTEXT = $fce->ZDATA->row["PTEXT"];
            $PRSDT = $fce->ZDATA->row["PRSDT"];
            $PROPINSI = $fce->ZDATA->row["PROPINSI"];
            $NAMA_PROP = $fce->ZDATA->row["NAMA_PROP"];
            $BRAN1 = $fce->ZDATA->row["BRAN1"];
            $VTEXT = $fce->ZDATA->row["VTEXT"];
            $TGL_SPJ = $fce->ZDATA->row["TGL_SPJ"];
            if ($fce->ZDATA->row["KWMENG"] > 0)
            $harga_satuans =  $fce->ZDATA->row["HARGA"]/$fce->ZDATA->row["KWMENG"];
            else
            $harga_satuan =  0;
            $harga_shp[$s]= $harga_satuan * $fce->ZDATA->row["KWANTUM"];
            $NAMA_KAPAL = $fce->ZDATA->row["NAMA_KAPAL"];
            $AUART = $fce->ZDATA->row["AUART"];

            $s++;
                                
//                    echo "<pre>";
//                    print_r($fce->ZDATA->row);
//                    echo "</pre>";
                    
            }
    }else
    $fce->PrintStatus();
    $fce->Close();
    $sap->Close();
    
    $sap1 = new SAPConnection();
    $sap1->Connect("../include/sapclasses/logon_data.conf");
    if ($sap1->GetStatus() == SAPRFC_OK ) $sap1->Open ();
    if ($sap1->GetStatus() != SAPRFC_OK ) {
       echo $sap1->PrintStatus();
       exit;
    }
    $fce1 = $sap1->NewFunction ("Z_ZAPPSD_DISPLAY_SHIP");
    if ($fce1 == false ) {
       $sap->PrintStatus();
       exit;
    }

    //header entri
    $kd_pl = substr($kdplant,0,2);
    if($kd_pl == '79'){
        $i_vkorg = '7900';
    }else{
        $i_vkorg = substr($kdplant,0,1)."000";
    }
    $fce1->X_VKORG  = $i_vkorg; //organisasi
    $fce1->X_NOSPJL = $idNO_SPJ; //2000107026
    #exit;	
    $fce1->Call();
    if ($fce1->GetStatus() == SAPRFC_OK ) {		
        $fce1->ZDATAL->Reset();
        while ( $fce1->ZDATAL->Next() ){
            $nobilling = $fce1->ZDATAL->row["L_BIL_DOC"];
            $datebilling = $fce1->ZDATAL->row["L_BIL_DOC_DATE"];
            $itembilling = $fce1->ZDATAL->row["L_BIL_ITEM"];
            $salesreturn = $fce1->ZDATAL->row["L_SALES_RET"];
        }
    }else
        $fce1->PrintStatus();

    $fce1->Close();   
    $sap1->Close();	
    
    $sql="     
            SELECT * from TB_USER_BOOKING where NAMA='$createby'
    ";
    //echo $sql;
    $querysql= oci_parse($conn, $sql);
    oci_execute($querysql);
    $row=oci_fetch_array($querysql);
    $nama_lengkap2=$row['NAMA_LENGKAP'];
    
    }else{
        $errormsg="Data tidak ditemukan !";
    }  
    
    $namaberitaacara="PT Semen Indonesia (Persero) Tbk.";
    if($tahunYmmk =='2017'){
       $namaberitaacara="PT. KSO Semen Gresik - Semen Indonesia";
    }
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
table tr td{
    font-size: 13px;
}
-->
</style>
<head>
<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Cetak Sales Konfirmasi</title>
</head>
<body>
<div align="center">
<?=$errormsg;?>    
<br>
<table width="900px" align="center">
<tr>
    <td align="center" colspan="3"><STRONG>BERITA ACARA</STRONG></td>
</tr>    
<tr>
    <td align="center" colspan="3"><STRONG>PENGALIHAN ALAMAT PENGIRIMAN SEMEN</STRONG></td>
</tr>
<tr>
    <td align="center" colspan="3">Nomor : <?=$nopp."/".$nomorsurat;?></td>
</tr>
<tr style="border-top: 4px double #000">
    <td align="left" colspan="3"><b>Kepada Yth.</b></td>
</tr>  
<tr>
    <td align="left" colspan="3">Kasi Administrasi Penjualan</td>
</tr> 
<tr>
    <td align="left" colspan="3"><?=$namaberitaacara;?></td>
</tr>
<tr>
    <td align="left" colspan="3">&nbsp;</td>
</tr>    
<tr>
    <td align="left" colspan="3">Pada Tanggal <?=$approvedate;?> , telah dilakukan pengalihan alamat pengiriman semen sebagai berikut:</td>
</tr>  
<tr>
    <td align="left" colspan="3">
        <table cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td>NO. SPJ</td>
                <td>:</td>
                <td><?=$nospjold;?></td>
            </tr>
             <tr>
                <td>Tgl. SPJ</td>
                <td>:</td>
                <td><?=tglIndo($TGL_SPJ);?></td>
            </tr>
            <tr>
                <td>Plant</td>
                <td>:</td>
                <td><?=$PLANT." (".$NAMA_PLANT.")";?></td>
            </tr>
             <tr>
                <td>Distributor</td>
                <td>:</td>
                <td><?=$SOLD_TO." ".$NAMA_SOLD_TO;?></td>
            </tr>
             <tr>
                <td>No. SO</td>
                <td>:</td>
                <td><?=$NO_SO;?></td>
            </tr>
            <tr>
                <td>No. Kendaraan</td>
                <td>:</td>
                <td><?=$NO_POLISI;?></td>
            </tr>
            <tr>
                <td>Jenis Semen</td>
                <td>:</td>
                <td><?=$ITEM_NO." ".$PRODUK;?></td>
            </tr>
            <tr>
                <td>Kuantum</td>
                <td>:</td>
                <td><?=$KWANTUM." ".$UOM;?></td>
            </tr>
            <tr>
                <td>Ship to Awal</td>
                <td>:</td>
                <td><?=$KODE_DA." ".$NAMA_TOKO;?></td>
            </tr>
            <tr>
                <td>Kota</td>
                <td>:</td>
                <td><?=$BRAN1." ".$VTEXT;?></td>
            </tr>
        </table>    
    </td>
</tr>  
<tr>
    <td align="left" colspan="3">Telah dialihkan pengirimannya ke:</td>
</tr>
<tr>
    <td align="left" colspan="3">
        <table cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td>Distributor</td>
                <td>:</td>
                <td><?=$soldto." ".$namasold;?></td>
            </tr>
             <tr>
                <td>Ship to</td>
                <td>:</td>
                <td><?=$nmshipto;?></td>
            </tr>
             <tr>
                <td>Kode Ship to</td>
                <td>:</td>
                <td><?=$kdshipto;?></td>
            </tr>
             <tr>
                <td>Kota</td>
                <td>:</td>
                <td><?=$kddistrik." ".$nmdistrik." ".$alamat;?></td>
            </tr>
            <tr>
                <td>Alasan</td>
                <td>:</td>
                <td><?=$note;?></td>
            </tr>
        </table>    
    </td>
</tr>   
<tr>
    <td align="left" colspan="3">Demikian Berita Acara ini dibuat untuk dapat dipergunakan sebagaimana mestinya.</td>
</tr>    
<tr>
    <td align="left" colspan="3" style="padding-left: 600px;">Dibuat di,</td>
</tr> 
<tr>
    <td align="left" colspan="3" style="padding-left: 600px;">Tanggal <?=$approvedate;?></td>
</tr>
<tr>
    <td align="left" colspan="3" style="padding-left: 600px;">Hormat Kami</td>
</tr>
<tr>
    <td align="left" colspan="3">Cc.</td>
</tr>
<tr>
    <td align="left" colspan="3">- Kasi Transportasi Darat</td>
</tr>
<tr>
    <td align="left" colspan="3">- Kasi Penjualan</td>
</tr>
<tr>
    <td align="left" colspan="3">- File</td>
</tr>
<tr>
    <td align="left" colspan="3" style="padding-left: 600px;"><u><?=$nama_lengkap2;?></u></td>
</tr> 
<tr>
    <td align="left" colspan="3" style="padding-left: 600px;"><?=$createby;?></td>
</tr> 
<tr>
    <td align="left" colspan="3">&nbsp;</td>
</tr>
<tr style="border: 1px solid #000">
    <td align="left" colspan="3">
        <table cellpadding="0" cellspacing="0" border="0" style="font-weight: bold;">
            <tr>
                <td align="left" colspan="3">Catatan Seksi Adm. Penjualan</td>
            </tr>
            <tr>
                <td align="left" colspan="3">Sudah dibuat koreksi dengan data sbb:</td>
            </tr>
            <tr>
                <td>No. Sales Konfirmasi</td>
                <td>:</td>
                <td><?=$new_noso;?></td>
            </tr>
          </table>      
    </td>
</tr>    
</table>
<? if($org == '5000' || $org == '7000' || $org == '7900'){ ?>
</br></br>
    <div align="center">
        <table style="font-weight: bold;">
            <tr>
                <td>Bill. Doc</td>
                <td>:</td>
                <td><? echo $nobilling ?></td>
            </tr>
            <tr>
                <td>Item</td>
                <td>:</td>
                <td><? echo $itembilling ?></td>
            </tr>
            <tr>
                <td>Bill. Date</td>
                <td>:</td>
                <td><? $thn=substr($datebilling,0,4);
                       $bln=substr($datebilling,4,2);
                       $hr=substr($datebilling,6,2);
                       $tglbil=$hr.'-'.$bln.'-'.$thn; 
                       echo $tglbil?></td>
            </tr>
            <tr>
                <td>SR</td>
                <td>:</td>
                <td><? echo $salesreturn ?></td>
            </tr>
        </table>
    </div>
<? } ?>

    <input name="Print" type="Submit" id="Print" value="Print" class="button" onclick="document.getElementById('Print').setAttribute('type', 'hidden');window.print();window.close();" class="nonPrint"  /> 
</div>
</body>
</html>
