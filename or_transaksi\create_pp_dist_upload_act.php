<?php
session_start();
include ('../include/or_fungsi.php');
require_once('../include/sapclasses/sap.php');
require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

date_default_timezone_set('UTC');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

if($_SERVER['REQUEST_METHOD'] == "POST"){
    if($_FILES['fileExcel']){
        $excelFile  = $_FILES['fileExcel'];
        $uploadDir = 'template_xls/';
        $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
        $allowedExtensions = array('xls', 'xlsx');

        if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
            header('Content-Type: application/json');
            echo json_encode(array(
                "status" => 500,
                "message" => "Please upload file"
            ));
            exit();
        }

        if (!is_readable($excelFile['tmp_name'])) {
            header('Content-Type: application/json');
            echo json_encode(array(
                "status" => 500,
                "message" => "Uploaded file is not readable."
            ));
            exit();
        }
        if (!in_array($fileExtension, $allowedExtensions)) {
            header('Content-Type: application/json');
            echo json_encode(
                array(
                    "status" => 400,
                    "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                    "fileExtension" => $fileExtension
                )
            );
            return;
        }
        $data = readExcel($excelFile['tmp_name']);
        $temp = array();
        foreach($data as $row){
            if(empty($row[2]) && empty($row[3]) && empty($row[4]) && empty($row[5]) && empty($row[6]) && empty($row[7]) && empty($row[8]) && empty($row[9]) && empty($row[10]) && empty($row[11]) && empty($row[12]) && empty($row[13]) && empty($row[14]) && empty($row[15]) && empty($row[16]))
                continue;
            else if (empty($row[2]) || empty($row[3]) || empty($row[4]) || empty($row[5]) || empty($row[6]) || empty($row[7]) || empty($row[8]) || empty($row[9]) || empty($row[10]) || empty($row[12]) || empty($row[13]) || empty($row[14]) || empty($row[15]) || empty($row[16]))
                $row['catatan'] = 'Data tidak lengkap';
            else $row['catatan'] = '';

            $firstDigit = intval(substr($row[10], 0, 1));
            $secondDigit = intval(substr($row[10], 1, 1));
        
            switch ($firstDigit) {
                case 7:
                    if ($secondDigit == 9) {
                        $row['org'] = '7900';
                    } else {
                        $row['org'] = '7000';
                    }
                    break;
                case 6:
                    $row['org'] = '6000';
                    break;
                case 5:
                    $row['org'] = '5000';
                    break;
                case 4:
                    $row['org'] = '4000';
                    break;
                case 3:
                    $row['org'] = '3000';
                    break;
                case 1:
                    $row['org'] = '1000';
                    break;
            }

            $temp[] = $row;
        }
        header('Content-Type: application/json');
        echo json_encode(array(
            "status" => 200,
            "message" => "Success extract excel data.",
            "data" => $temp,
        ));
    } else if($_POST['action'] == 'create_pp') {
       //get detail material
        $kode_mat_blacklist = array();
        $sqlcek = "SELECT KODE_MATERIAL FROM OR_MAP_DISTRIK_MAT WHERE ORG = '".$_POST['org']."' AND DISTRIK = '".$_POST['kode_distrik1']."' AND PLANT = '".$_POST['plant']."' AND DELETE_MARK = '0'";
        $querycek = oci_parse($conn, $sqlcek);
        oci_execute($querycek);
        $kode_mat_blacklist = array();
        while($datacek=oci_fetch_assoc($querycek)){
            array_push($kode_mat_blacklist, $datacek['KODE_MATERIAL']);
        }

        if ($_POST['brand'] == 'CURAH' || $_POST['brand'] == 'MORTAR ZAK' || $_POST['brand'] == 'MORTAR CURAH' ) {
            $sql = "SELECT DISTINCT
            scm.BRAND,
            scm.MATERIAL AS MATNR,
            sal.MAKTX AS MAKTX,
            UPPER( sal.MEINS ) AS MEINS 
            FROM
                ZSD_TARGET_HEADER_SCM scm 
                LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = scm.MATERIAL
            WHERE
            sal.VKORG IN (".$_POST['org'].") AND scm.BRAND = '".$_POST['brand']."' AND  scm.FLAG_DEL != 'Y' AND  ROWNUM <= 1";
            if ($_POST['produk1']) {
                $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                $sql .= "sal.MATNR = '".$_POST['produk1']."'";
            }
            $sql .= "AND ROWNUM <= 1";
        }else{
            $sql = "SELECT DISTINCT
            mm.BRAND,
            mm.KODE_MATERIAL AS MATNR,
            sal.MAKTX as MAKTX,
            UPPER(sal.MEINS) as MEINS
        
            FROM
            MAPPING_MATERIAL_BRAND mm
            LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = mm.KODE_MATERIAL 
            LEFT JOIN ZSD_TARGET_HEADER_SCM scm ON scm.MATERIAL = sal.MATNR
            WHERE
            sal.VKORG IN (".$_POST['org'].") AND mm.BRAND = '".$_POST['brand']."' AND  mm.FLAG_DEL != 'Y'";
            if ($_POST['produk1']) {
                $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                $sql .= "sal.MATNR = '".$_POST['produk1']."'";
            }
            if ($_POST['jenis_kemasan']) {
                if($_POST['jenis_kemasan'] == 'ZAK'){
                    $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                    $sql .= "sal.MEINS IN ('".$_POST['jenis_kemasan']."','BAG')";
                } else {
                    $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                    $sql .= "sal.MEINS = '".$_POST['jenis_kemasan']."'";
                }
            }
            $sql .= "AND ROWNUM <= 1";

            echo $sql;

        }

        $query= oci_parse($conn, $sql);
        oci_execute($query);
        while($datafunc=oci_fetch_assoc($query)){
            if(in_array($datafunc["MATNR"], $kode_mat_blacklist)){
                    continue;
            } else{
                $matnr = $datafunc["MATNR"];
                $nama_matnr = $datafunc["MAKTX"];
                $uom = $datafunc["MEINS"];

                if(strtoupper($datafunc["GEWEI"])=='KG'){
                    $qtymto=@(number_format($datafunc["NTGEW"]/1000,2));
                }else{
                    $qtymto=number_format($datafunc["NTGEW"]);
                }
                $_POST['nama_produk1'] = $nama_matnr;
                $_POST['uom1'] = $uom;
            }
        }
       //end of get detail material

        $query="SELECT VKBUR,NAME1,STRAS,BZTXT,BEZEB,KVGR1 from RFC_Z_ZCSD_SHIPTO where KUNN2 = '".$_POST['shipto1']."' and BZIRK = '".$_POST['kode_distrik1']."'";
        $sql=oci_parse($conn,$query);
        oci_execute($sql);
        $exec=oci_fetch_assoc($sql);
        if($exec){
            $_POST['kode_prov1'] = $exec['VKBUR'];
            $_POST['nama_shipto1'] = $exec['NAME1'];
            $_POST['alamat1'] = $exec['STRAS'];
            $_POST['nama_distrik1'] = $exec['BZTXT'];
            $_POST['nama_prov1'] = $exec['BEZEB'];
        } else {
            header('Content-Type: application/json');
            echo json_encode(array(
                "status" => 204,
                "message" => "Data shipto tidak ditemukan.",
                "data" => '',
            ));
            return;
        }

        // $_POST['nama_produk1'] = $_POST['nama_produk'];
        // $_POST['uom1'] = $_POST['uom'];
        // $_POST['kode_prov1'] = $_POST['kode_prov'];
        // $_POST['nama_shipto1'] = $_POST['nama_shipto'];
        // $_POST['alamat1'] = $_POST['alamat'];
        // $_POST['nama_distrik1'] = $_POST['nama_distrik'];
        // $_POST['nama_prov1'] = $_POST['nama_prov'];

        $_POST['sold_to'] = $fungsi->sapcode($_POST['sold_to']);
        $_POST['nama_sold_to'] = $fungsi->findOneByOne($conn,"TB_USER_BOOKING","DISTRIBUTOR_ID",$_POST['sold_to'],"NAMA_DISTRIBUTOR");
        if($_POST['so_type'] == 'ZOR') $_POST['nama_so_type'] = 'Standard Sales';
        else if($_POST['so_type'] == 'ZFC') $_POST['nama_so_type'] = 'FOC Sales';
        else if($_POST['so_type'] == 'ZPR') $_POST['nama_so_type'] = 'Project Sales';
        $_POST['kota_tujuan'] = $_POST['kode_distrik1'];
        $_POST['nama_kirim'] = $_POST['jenis_kirim'];
        $_POST['nama_plant'] = $_POST['plant'];
        $_POST['tipeCreatePP'] = 'NEW PP';
        $_POST['jumlah'] = 1;
        switch ($_POST['Pricelist']) {
            case '01': $_POST['nama_pricelist'] = "Project"; break;
            case '02': $_POST['nama_pricelist'] = "Retail"; break;
            case '03': $_POST['nama_pricelist'] = "Sumbangan"; break;
            case '04': $_POST['nama_pricelist'] = "H. Khusus"; break;
            case '05': $_POST['nama_pricelist'] = "Ekspor"; break;
            case '06': $_POST['nama_pricelist'] = "Pabrikan"; break;
            case '07': $_POST['nama_pricelist'] = "Rebate"; break;
            case '08': $_POST['nama_pricelist'] = "Kapal Khusus"; break;
            case '09': $_POST['nama_pricelist'] = "Harga Khusus (SG)"; break;
            case '10': $_POST['nama_pricelist'] = "Harga Subsidi Gudang"; break;
            case '11': $_POST['nama_pricelist'] = "Retail DA"; break;
            case '12': $_POST['nama_pricelist'] = "Harga Khusus CD"; break;
            case '13': $_POST['nama_pricelist'] = "Additional Price"; break;
            case '14': $_POST['nama_pricelist'] = "Harga Franco Toko"; break;
            case '15': $_POST['nama_pricelist'] = "Harga Reward"; break;
            case '16': $_POST['nama_pricelist'] = "Harga Subsidi Khusus"; break;
            case '17': $_POST['nama_pricelist'] = "Harga Direct Selling"; break;
            case '19': $_POST['nama_pricelist'] = "Harga Program Khusus"; break;
            case '60': $_POST['nama_pricelist'] = "Standart TLCC"; break;
            case '61': $_POST['nama_pricelist'] = "Project TLCC"; break;
            case '62': $_POST['nama_pricelist'] = "Export TLCC"; break;
            case '63': $_POST['nama_pricelist'] = "Debt Offset"; break;
            case '64': $_POST['nama_pricelist'] = "Retail"; break;
            case '65': $_POST['nama_pricelist'] = "Donation/ plant use"; break;
            case '66': $_POST['nama_pricelist'] = "Special price"; break;
            case '67': $_POST['nama_pricelist'] = "Manufacture"; break;
            case '68': $_POST['nama_pricelist'] = "Bonus"; break;
            case '6A': $_POST['nama_pricelist'] = "Cash TLCC"; break;
            default: $_POST['nama_pricelist'] = "Unknown Pricelist"; break;
        }

        // var_dump($_POST);return;
        // $action = $_POST['action'];
        // include_once('formulapp_royalty.php');
        // echo $show_ket;

       // create pp function
        $user_id = $_POST["user_id"];
        $validasi_tgl_rdd = trim($_POST["tgl_kirim1"]);
        $validasi_qty = trim($_POST["qty1"]);
        $byg = trim($_POST["plantBayangan"]);

        $validasi_plant = trim($_POST["plant"]);

        if ($validasi_plant == "") {
            $validasi_plant = $byg;
        }
        $validasi_kode_distrik = trim($_POST["kota_tujuan"]);
        $validasi_kode_material = trim($_POST["produk1"]);
        $sub_validasi_kode_material = substr($validasi_kode_material, 0, 7);
        $validasi_incoterm = trim($_POST["jenis_kirim"]);
        $validasi_shipto = trim($_POST["shipto1"]);
        $validasi_org = trim($_POST["org"]);
        $validasi_soldto = trim($_POST["sold_to"]);
        $validasi_qty_front_end = trim($_POST["qty1"]);
        $validasi_tgl_front_end = trim($_POST["tgl_kirim1"]);
        $tglleadtimeppqty = $validasi_tgl_front_end;
        $validasi_route = trim($_POST["route"]);
        $validasi_kode_provinsi = trim($_POST["kode_prov1"]);
        $brand = trim($_POST["brand"]);
        if ($brand == "") {
            $brand = trim($_POST["jenis_kemasan"]);
        }

        $_cek_dist = $validasi_soldto;
        $cek_assoc = "select * from MAPPING_SOLDTO_ASSOC where FLAG_DEL != 'Y' and SOLDTO = '$_cek_dist' ";
        $ascSoldto = oci_parse($conn, $cek_assoc);
        oci_execute($ascSoldto);
        $getDist = oci_fetch_assoc($ascSoldto);
        if (oci_num_rows($ascSoldto) != 0) {
            $_cek_dist = $getDist["SOLDTO_ASSOC"];
        }

        $tgl_harini_quary = date("d-m-Y");
        $getPrd = explode("-", $tgl_harini_quary);
        $getPeriode = $getPrd[1] . "-" . $getPrd[2];

        $cekIsRoyalti = "SELECT case when IS_ROYALTY='Y' then 'X' else '' end as IS_ROYALTY, case when IS_MSA='Y' then 'X' else '' end as IS_MSA  
                FROM MAPPING_BRAND_PLANT 
                WHERE PLANT_MD='$validasi_plant' 
                and BRAND='$brand' AND FLAG_DEL != 'Y' ";
        $exc = oci_parse($conn, $cekIsRoyalti);
        oci_execute($exc);
        $getRoyalti = oci_fetch_assoc($exc);
        $isRoyalti = "";
        $isMsa = "";
        if (oci_num_rows($exc) != 0) {
            $isRoyalti = $getRoyalti["IS_ROYALTY"];
            $isMsa = $getRoyalti["IS_MSA"];

            $mysqlkatr9 = "SELECT KATR9_TEXT FROM RFC_Z_ZCSD_SHIPTO WHERE KUNN2 = '$validasi_shipto' ";
            $mysql_katr9=oci_parse($conn,$mysqlkatr9);
            oci_execute($mysql_katr9);
            $row_katr9 = oci_fetch_assoc($mysql_katr9);
            $katr9=$row_katr9['KATR9_TEXT'];
            if ($katr9 == "NASIONAL") {
                $isMsa = "";
            }
        }

        $getincomterms = "SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '$validasi_plant' AND INCOTERM_MD ='$validasi_incoterm' AND DEL = 0 AND ROWNUM <= 1";

        $exc = oci_parse($conn, $getincomterms);
        oci_execute($exc);
        $incsource = oci_fetch_assoc($exc);

        if (oci_num_rows($exc) > 0) {
            $validasi_incoterm = $incsource["INCOTERM_SOURCE"];
        } else {
            $getincomterms2 = "SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '$validasi_plant' AND DEL = 0 AND ROWNUM <= 1";
            $exc1 = oci_parse($conn, $getincomterms2);
            oci_execute($exc1);
            $incsource2 = oci_fetch_assoc($exc1);

            // if (oci_num_rows($exc1) != 0 || $incsource2["INCOTERM_SOURCE"] != "") {
            if ($incsource2["INCOTERM_SOURCE"] != null) {
                //  $data='Data masuk kesini';
                //  print_r($data);die();
                $validasi_incoterm = $incsource2["INCOTERM_SOURCE"];
            }
            // else{
            //     // $data='Data tidak masuk kesini';
            //     //      print_r($data);die();
            //     $validasi_incoterm = trim($_POST['nama_kirim']);
            // }
        }
        $strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE  MATNR = '{$validasi_kode_material}'  AND WERKS = '{$validasi_plant}' AND ROWNUM <= 1 ";
        $querymat = oci_parse($conn, $strmat);
        oci_execute($querymat);
        $rowmat = oci_fetch_array($querymat, OCI_ASSOC);
        //                            echo json_encode($rowmat);
        if (count($rowmat) > 0 && $rowmat["MEINS"] == "ZAK") {
            $qty = (intval($validasi_qty_front_end) * $rowmat["BERAT"]) / 1000;
        } else {
            $qty = $validasi_qty_front_end;
        }

        // if ($validasi_incoterm == "FRC" || $validasi_incoterm == "CIF") {
        //     // INI UNTUK KONDISI JIKA PILIH INCOTERM FRCO
        //     // di hardcode dulu buta cobak cobak
        //     // get qty harian dulu

        //     // Pengecekan jika shipto yang di pilih adaalah shipto exception , apakah sudah di mappingkan atau belum
        //     if ($rowPlant != "") {
        //         $quary = 0;
        //         $qty_request = $qty;

        //         $sisaQuary = $temp_kuari - $qty;

        //         if ($sisaQuary <= 0) {
        //             $qtyQry = 0;
        //         } else {
        //             $qtyQry = $sisaQuary;
        //         }

        //         $updateQuary = "UPDATE EX_MAPPING_SHIPTO SET TEMP_KUOTA = '$qtyQry' WHERE SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant'";
        //         $Quary = oci_parse($conn, $updateQuary);
        //         $resultQuary = oci_execute($Quary);
        //     } else {
        //         $quary = 0;
        //         $qty_request = $qty;

        //         $sisaQuary = $dataHarian["QUARY"] - $qty;

        //         if ($sisaQuary <= 0) {
        //             $qtyQry = 0;
        //         } else {
        //             $qtyQry = $sisaQuary;
        //         }

        //         // get prioritas plant
        //         $cekPlant = "SELECT DISTINCT
        //             scm.PLANT , 
        //             sys.NAME1,
        //             scm.PRIORITAS,
        //             scm.QUARY
                    
        //         FROM
        //             ZSD_TARGET_HEADER_SCM scm
        //             LEFT JOIN
        //             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.QUARY != '0' AND PLANT = '$validasi_plant' AND scm.FLAG_DEL ='X'  AND ROWNUM <=  1
        //             GROUP BY
        //             scm.PLANT,
        //             sys.NAME1,scm.PRIORITAS,scm.QUARY
        //             ORDER BY 
        //             scm.PRIORITAS
        //             ";

        //         $qplant = @oci_parse($conn, $cekPlant);
        //         @oci_execute($qplant);
        //         // $raw = oci_fetch_assoc($qplant)
        //         while ($raw = oci_fetch_assoc($qplant)) {
        //             // cari prioritas terkecil
        //             // $angkaKecil = array();
        //             $planton = $raw["PLANT"];
        //         }
        //         $updateQuary = "UPDATE ZSD_TARGET_HEADER_SCM SET  QUARY = '$qtyQry' WHERE PLANT = '$planton' AND DISTRIK = '$validasi_kode_distrik' AND MATERIAL = '$validasi_kode_material' AND INCOTERM = '$validasi_incoterm' AND PERIODE = '$getPeriode' AND BRAND = '$brand' AND FLAG_DEL ='X'";
        //         $Quary = oci_parse($conn, $updateQuary);
        //         $resultQuary = oci_execute($Quary);

        //         // cek jika quary sudah habis semua
        //         $cekPlant = "SELECT DISTINCT
        //             scm.ID , 
        //             scm.QTY_HARIAN , 
        //             scm.PLANT , 
        //             sys.NAME1,
        //             scm.PRIORITAS,
        //             scm.QUARY
                    
        //         FROM
        //             ZSD_TARGET_HEADER_SCM scm
        //             LEFT JOIN
        //             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.PERIODE = '$getPeriode' AND scm.FLAG_DEL = 'X'
        //             GROUP BY
        //             scm.ID,
        //             scm.PLANT,
        //             sys.NAME1,scm.PRIORITAS,scm.QUARY, scm.QTY_HARIAN 
        //             ORDER BY 
        //             scm.PRIORITAS
        //             ";

        //         $cekNilaiQuary = @oci_parse($conn, $cekPlant);
        //         @oci_execute($cekNilaiQuary);
        //         while ($rew = oci_fetch_array($cekNilaiQuary)) {
        //             $jmlQuary[] = $rew["QUARY"];
        //             $datas[] = $rew;
        //         }

        //         $totalQuary = array_sum($jmlQuary);

        //         if ($totalQuary == 0) {
        //             foreach ($datas as $key) {
        //                 //    update quary ke qty awal
        //                 $qtyHrn = $key["QTY_HARIAN"];
        //                 $idQuary = $key["ID"];

        //                 $sql = "UPDATE ZSD_TARGET_HEADER_SCM SET QUARY = '$qtyHrn' WHERE ID = '$idQuary' ";
        //                 $query = oci_parse($conn, $sql);
        //                 $result = oci_execute($query);
        //             }
        //         }
        //     }
        // } else {
        //     if ($rowPlant != "") {
        //         // pengecekan mapping shipto exeption -> jika data nya sudah termapping
        //         $quary = 0;
        //         $qty_request = $qty;

        //         $sisaQuary = $temp_kuari - $qty;

        //         if ($sisaQuary <= 0) {
        //             $qtyQry = 0;
        //         } else {
        //             $qtyQry = $sisaQuary;
        //         }

        //         $updateQuary = "UPDATE EX_MAPPING_SHIPTO SET TEMP_KUOTA = '$qtyQry' WHERE SOLDTO = '$validasi_soldto' AND SHIPTO = '$validasi_shipto'  AND  PLANT = '$validasi_plant'";
        //         $Quary = oci_parse($conn, $updateQuary);
        //         $resultQuary = oci_execute($Quary);
        //     } else {
        //         // cek lagi apakah incoterm source nya FOT, FRC , CIF
        //         // jika iya maka cek apakah ada mappingan di plant nya
        //         // kalau ada mengurangi quota nappingan quota

        //         $strkdven = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$validasi_plant}' AND DEL=0";
        //         $query = @oci_parse($conn, $strkdven);
        //         @oci_execute($query);
        //         $rowkdven = oci_fetch_array($query, OCI_ASSOC);
        //         $incoterm_source = $rowkdven["INCOTERM_SOURCE"];

        //         if (
        //             $incoterm_source == "FRC" ||
        //             $incoterm_source == "FOT" ||
        //             $incoterm_source == "CIF"
        //         ) {
        //             // KONDISI UNTUK CEK
        //             $quary = 0;
        //             $qty_request = $qty;

        //             $sisaQuary = $dataHarian["QUARY"] - $qty;

        //             if ($sisaQuary <= 0) {
        //                 $qtyQry = 0;
        //             } else {
        //                 $qtyQry = $sisaQuary;
        //             }

        //             // get prioritas plant
        //             $cekPlant = "SELECT DISTINCT
        //                             scm.PLANT , 
        //                             sys.NAME1,
        //                             scm.PRIORITAS,
        //                             scm.QUARY
                                    
        //                         FROM
        //                             ZSD_TARGET_HEADER_SCM scm
        //                             LEFT JOIN
        //                             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT                                                                                  
        //                             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = 'FRC' AND scm.QUARY != '0' AND PLANT = '$validasi_plant' AND scm.FLAG_DEL ='X'  AND ROWNUM <=  1
        //                             GROUP BY
        //                             scm.PLANT,
        //                             sys.NAME1,scm.PRIORITAS,scm.QUARY
        //                             ORDER BY 
        //                             scm.PRIORITAS
        //                             ";

        //             $qplant = @oci_parse($conn, $cekPlant);
        //             @oci_execute($qplant);
        //             // $raw = oci_fetch_assoc($qplant)
        //             while ($raw = oci_fetch_assoc($qplant)) {
        //                 // cari prioritas terkecil
        //                 // $angkaKecil = array();
        //                 $planton = $raw["PLANT"];
        //             }
        //             $updateQuary = "UPDATE ZSD_TARGET_HEADER_SCM SET  QUARY = '$qtyQry' WHERE PLANT = '$planton' AND DISTRIK = '$validasi_kode_distrik' AND MATERIAL = '$validasi_kode_material' AND INCOTERM = 'FRC' AND PERIODE = '$getPeriode' AND BRAND = '$brand' AND FLAG_DEL ='X'";
        //             $Quary = oci_parse($conn, $updateQuary);
        //             $resultQuary = oci_execute($Quary);

        //             // cek jika quary sudah habis semua
        //             $cekPlant = "SELECT DISTINCT
        //                             scm.ID , 
        //                             scm.QTY_HARIAN , 
        //                             scm.PLANT , 
        //                             sys.NAME1,
        //                             scm.PRIORITAS,
        //                             scm.QUARY
                                    
        //                         FROM
        //                             ZSD_TARGET_HEADER_SCM scm
        //                             LEFT JOIN
        //                             RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //                             WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.PERIODE = '$getPeriode' AND scm.FLAG_DEL ='X'
        //                             GROUP BY
        //                             scm.ID,
        //                             scm.PLANT,
        //                             sys.NAME1,scm.PRIORITAS,scm.QUARY, scm.QTY_HARIAN 
        //                             ORDER BY 
        //                             scm.PRIORITAS
        //                             ";

        //             $cekNilaiQuary = @oci_parse($conn, $cekPlant);
        //             @oci_execute($cekNilaiQuary);
        //             while ($rew = oci_fetch_array($cekNilaiQuary)) {
        //                 $jmlQuary[] = $rew["QUARY"];
        //                 $datas[] = $rew;
        //             }

        //             $totalQuary = array_sum($jmlQuary);

        //             if ($totalQuary == 0) {
        //                 foreach ($datas as $key) {
        //                     //    update quary ke qty awal
        //                     $qtyHrn = $key["QTY_HARIAN"];
        //                     $idQuary = $key["ID"];

        //                     $sql = "UPDATE ZSD_TARGET_HEADER_SCM SET QUARY = '$qtyHrn' WHERE ID = '$idQuary' ";
        //                     $query = oci_parse($conn, $sql);
        //                     $result = oci_execute($query);
        //                 }
        //             }
        //         } else {
        //             // kondisi jika selain FRC dan CIF
        //             // kondisinya ambil plant terserah jika habis maka tidak bisa terpakai kembali
        //             // $qty = $validasi_qty_front_end;
        //             $quary = 0;
        //             $qty_request = $qty;

        //             $sisaQuary = $dataHarian["QTY_BULANAN"] - $qty;

        //             if ($sisaQuary <= 0) {
        //                 $qtyQry = 0;
        //             } else {
        //                 $qtyQry = $sisaQuary;
        //             }

        //             // get prioritas plant
        //             $cekPlant = "SELECT DISTINCT
        //                         scm.PLANT , 
        //                         sys.NAME1,
        //                         scm.PRIORITAS,
        //                         scm.QTY_BULANAN
                                
        //                     FROM
        //                         ZSD_TARGET_HEADER_SCM scm
        //                         LEFT JOIN
        //                         RFC_Z_ZAPP_SELECT_SYSPLAN sys ON sys.WERKS = scm.PLANT
        //                         WHERE scm.BRAND = '$brand' AND scm.DISTRIK = '$validasi_kode_distrik'  AND  scm.MATERIAL = '$validasi_kode_material' AND scm.INCOTERM = '$validasi_incoterm' AND scm.QTY_BULANAN != '0' AND PLANT = '$validasi_plant' AND scm.FLAG_DEL ='X'  AND ROWNUM <=  1
        //                         GROUP BY
        //                         scm.PLANT,
        //                         sys.NAME1,scm.PRIORITAS,scm.QTY_BULANAN
        //                         ORDER BY 
        //                         scm.PRIORITAS
        //                         ";

        //             $qplant = @oci_parse($conn, $cekPlant);
        //             @oci_execute($qplant);
        //             // $raw = oci_fetch_assoc($qplant)
        //             while ($raw = oci_fetch_assoc($qplant)) {
        //                 // cari prioritas terkecil
        //                 // $angkaKecil = array();
        //                 $planton = $raw["PLANT"];
        //             }
        //             $updateQuary = "UPDATE ZSD_TARGET_HEADER_SCM SET  QTY_BULANAN = '$qtyQry' WHERE PLANT = '$planton' AND DISTRIK = '$validasi_kode_distrik' AND MATERIAL = '$validasi_kode_material' AND INCOTERM = '$validasi_incoterm' AND PERIODE = '$getPeriode' AND FLAG_DEL ='X'";
        //             $Quary = oci_parse($conn, $updateQuary);
        //             $resultQuary = oci_execute($Quary);
        //         }
        //     }
        // }

        // tambah new dist lama

        if ($sub_validasi_kode_material == "121-301") {
            $validasi_kode_distrik_getrdd = $validasi_kode_distrik;
        } else {
            $validasi_kode_distrik_getrdd = $validasi_kode_provinsi;
        }

        $mysql = "SELECT
                STANDART_AREA
                FROM
                    (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$validasi_plant' and kota='$validasi_kode_distrik_getrdd' and kd_material='$sub_validasi_kode_material' and delete_mark='0' ORDER BY id desc)
                WHERE
                    rownum BETWEEN 0 AND 1";

        $mysql_set = oci_parse($conn, $mysql);
        oci_execute($mysql_set);
        $row_leadtime = oci_fetch_assoc($mysql_set);
        $leadtimesonya = $row_leadtime[STANDART_AREA];
        // print_r($leadtimeso);

        if ($leadtimesonya != "" or $leadtimesonya != null) {
            $leadtimeso = $leadtimesonya;
        } else {
            $leadtimeso = 0;
        }
        /////////////////////////////////////
        $mysqlperkapgudang =
            "SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='PERSENTASE_KAPASITAS_GUDANG' and delete_mark='0'";
        $mysql_setperkapgudang = oci_parse($conn, $mysqlperkapgudang);
        oci_execute($mysql_setperkapgudang);
        $row_configperkapgudang = oci_fetch_assoc($mysql_setperkapgudang);
        $configperkapgudang = $row_configperkapgudang[CONFIG];
        if ($configperkapgudang != null or $configperkapgudang != "") {
            $configperkapgudangfix = intval($configperkapgudang);
        } else {
            $configperkapgudangfix = 100;
        }
        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        //Perhitungan QTY order PP vs MDXL
        //intransit
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK) {
            $sap->Open();
        }
        if ($sap->GetStatus() != SAPRFC_OK) {
            $show_ket .= $sap->PrintStatus();
            exit();
        }
        $fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL");
        if ($fce == false) {
            $sap->PrintStatus();
            exit();
        }

        $fce->XVKORG = $validasi_org;
        $fce->XKUNNR2 = $validasi_shipto;
        $fce->XBZIRK = $validasi_kode_distrik;

        $validasi_hari_h = date("Ymd", strtotime($tglleadtimeppqty));
        // $vdatuhigh = date('d.m.Y',strtotime());
        $validasi_vdatulow = date(
            "Ymd",
            strtotime($validasi_hari_h . " -" . $leadtimeso . " day")
        );

        $fce->LR_EDATU->row["SIGN"] = "I";
        $fce->LR_EDATU->row["OPTION"] = "BT";
        $fce->LR_EDATU->row["LOW"] = $validasi_vdatulow;
        $fce->LR_EDATU->row["HIGH"] = $validasi_hari_h;
        $fce->LR_EDATU->Append($fce->LR_EDATU->row);

        $fce->Call();
        if ($fce->GetStatus() == SAPRFC_OK) {
            $fce->RETURN_DATA->Reset();
            $s = 0;
            while ($fce->RETURN_DATA->Next()) {
                // $validasi_qty1[$s] = $fce->RETURN_DATA->row["KWMENG"];
                // $validasi_qty1[$s]= $fce->RETURN_DATA->row["KWMENGX"];
                $intransit += $fce->RETURN_DATA->row["RFMNG"];
                $s++;
            }
        } else {
            $fce->PrintStatus();
        }

        $fce->Close();
        $sap->Close();
        // $totallog = count($qty2);

        // $intransit = @array_sum($validasi_qty1);
        //////////////////////////////////////////////////////////////
        //////////////////////////////////////////////////////////////
        if ($validasi_incoterm == "FOT") {
            $mysql2 = "SELECT
                    INCOTERM_SOURCE
                FROM
                    (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$validasi_plant' and INCOTERM_SOURCE='FRC' ORDER BY id_plant desc)
                WHERE
                    rownum BETWEEN 0 AND 1";
            $mysql_set2 = oci_parse($conn, $mysql2);
            oci_execute($mysql_set2);
            $row_config2 = oci_fetch_assoc($mysql_set2);
            $configso2 = $row_config2[INCOTERM_SOURCE];
            if ($configso2 != "" or $configso2 != null) {
                $validasi_incoterm = $configso2;
            }
        }

        if ((trim($_POST["jenis_kirim"]) != 'FOT' || $validasi_incoterm == "FRC") && $validasi_route != "ZR0001") {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$kode_distri_si.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
                // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-distributor?kode_distributor_si='.$validasi_soldto.'&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU',
                CURLOPT_URL =>
                    "http://api-mdxl.aksestoko.com/external/warehouse/validate-distributor?kode_distributor_si=" .
                    $validasi_soldto .
                    "&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $data_output_soldto = json_decode($response, true);
            foreach ($data_output_soldto as $valuesoldto) {
                $kode_soldto_si_mdxl_temp = $valuesoldto["kode_distributor_mdxl"];
                $kode_soldto_si_mdxl = sprintf("%'010s", $kode_soldto_si_mdxl_temp);
            }
            /////////////////////////////////////////////////////////////
            /////////////////////////////////////////////////////////////
            $curl = curl_init();
            curl_setopt_array($curl, array(
                //CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl.'&shipto=1380000004',
                // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/warehouse/validate-shipto?access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor='.$kode_soldto_si_mdxl_temp.'&shipto='.$validasi_shipto,
                CURLOPT_URL =>
                    "http://api-mdxl.aksestoko.com/external/warehouse/validate-shipto?distributor=" .
                    $kode_soldto_si_mdxl_temp .
                    "&shipto=" .
                    $validasi_shipto .
                    "&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU%0A",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $data_output_shipto = json_decode($response, true);
            $adashipto = false;
            foreach ($data_output_shipto as $valueshipto) {
                $kode_gudang_si_mdxl = $valueshipto["kode_gudang_mdxl"];
                $adashipto = true;
            }

            if ($adashipto) {
                $mdxlshipto = $kode_gudang_si_mdxl;
            }

            if ($mdxlshipto == null or $mdxlshipto == "") {
                $mdxlshipto = $validasi_shipto;
            }
            ////////////////////////////////////////////////////////////
            /////////////////////////////////////////////////////////////
            $curl = curl_init();

            curl_setopt_array($curl, array(
                // CURLOPT_URL => 'http://api-mdxl.aksestoko.id/external/report/level-stock?per-page=100000&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&virtual=2&distributor='.$kode_soldto_si_mdxl_temp,
                CURLOPT_URL =>
                    "http://api-mdxl.aksestoko.com/external/report/level-stock?per-page=100&q=&access-token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjA1MzM1MDUsInN1YiI6MTQ0OTgyMn0.PgfuOGJNdv-EgAPcwu1g5-lkGppnD6zaGBqzpKW8zXU&distributor=" .
                    $kode_soldto_si_mdxl_temp,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ));

            $response = curl_exec($curl);

            curl_close($curl);

            $data_output = json_decode($response, true);
            $adadimdxl = false;
        } else {
            $kode_soldto_si_mdxl_temp = $_POST['sold_to'];
        }


        ///////////////////////////
        // $hari_h = date('Ymd');
        // $sap = new SAPConnection();
        // $sap->Connect("../include/sapclasses/logon_data.conf");
        // if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
        // if ($sap->GetStatus() != SAPRFC_OK ) {
        // echo $sap->PrintStatus();
        // exit;
        // }
        // $fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REAL");
        // if ($fce == false ) {
        // $sap->PrintStatus();
        // exit;
        // }

        // //header entri
        // $fce->X_VKORG = '7900';
        // $fce->X_TGL1 = date('Ymd',strtotime($hari_h. ' -'.$leadtimeso.' day'));
        // $fce->X_TGL2 = date('Ymd',strtotime($hari_h.'-1 day'));
        // $fce->X_KUNNR = $kode_soldto_si_mdxl_temp;
        // $fce->X_STATUS = '70';
        // $fce->X_BZIRK = $validasi_kode_distrik;
        // ///////////////////////////
        // $fce->LRI_VKORG->row["SIGN"] = 'I';
        // $fce->LRI_VKORG->row["OPTION"] = 'EQ';
        // $fce->LRI_VKORG->row["LOW"] = '7900';
        // $fce->LRI_VKORG->row["HIGH"] = '';
        // $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
        // $fce->LRI_VKORG->row["SIGN"] = 'I';
        // $fce->LRI_VKORG->row["OPTION"] = 'EQ';
        // $fce->LRI_VKORG->row["LOW"] = '7000';
        // $fce->LRI_VKORG->row["HIGH"] = '';
        // $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
        // ///////////////////////////
        // ///////////////////////////
        // $fce->LR_KODE_DA->row["SIGN"] = 'I';
        // $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
        // $fce->LR_KODE_DA->row["LOW"] = $shipto;
        // $fce->LR_KODE_DA->row["HIGH"] = '';
        // $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
        // foreach ($data_output["items"] as $key => $value) {
        //     if (count($value["shipto_info"]) !== 0) {
        //     //     $adadimdxl = false;
        //     // }else{
        //     //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
        //         $rptrealallshipto = 0;
        //         foreach ($value["shipto_info"] as $key2 => $value2) {
        //             if($value2['kode_shipto']==$mdxlshipto){
        //                 $shiptoinfomdxl=$value["shipto_info"];
        //                 if($shiptoinfomdxl!='' or $shiptoinfomdxl!=null){
        //                     foreach ($value["shipto_info"] as $key3 => $value3) {
        //                         // print_r($value3['kode_shipto']);
        //                         ///////////////////////////
        //                         $fce->LR_KODE_DA->row["SIGN"] = 'I';
        //                         $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
        //                         $fce->LR_KODE_DA->row["LOW"] = $value3['kode_shipto'];
        //                         $fce->LR_KODE_DA->row["HIGH"] = '';
        //                         $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
        //                         // echo "ada";
        //                         ///////////////////////////
        //                         // $rptrealallshipto += $rptrealavgin;

        //                     }
        //                 }
        //             }
        //             // print_r("datanya ".$value2['kode_shipto']);
        //         }
        //     }
        // }

        // // echo "<pre>";
        // // print_r($fce);
        // // echo "</pre>";

        //  $fce->Call();
        //      $fce->ZDATA->Reset();
        //      $rptrealavgin=0;
        //      while ( $fce->ZDATA->Next() ){
        //          $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
        //          // print_r($fce->ZDATA->row["KWANTUM"]);
        //      }

        //  $fce->Close();
        //  $sap->Close();
        //     //  $rptrealavgin = $rptrealavgin/7;
        //     $realisasi_shipment = $rptrealavgin;
        // //  echo "RPT real : ".$rptrealavgin;
        //////////////////////////////////////////////////////////////
        // //////////////////////////////////////////////////////////////
        // WWW gae edit rumus intransit 5665
        $tglleadtimeppqty1 = date("01-m-Y", strtotime($tglleadtimeppqty));
        if (date("d", strtotime($tglleadtimeppqty)) < 15) {
            $tglleadtimeppqty2 = date("t-m-Y", strtotime($tglleadtimeppqty));
        } else {
            $tglleadtimeppqty2 = date(
                "t-m-Y",
                strtotime("$tglleadtimeppqty +1 month")
            );
        }

        $get_harian = "SELECT DISTINCT zthn.PLANT, zls.STANDART_AREA FROM ZSD_TARGET_HARIAN_NEW zthn 
                                                                                    JOIN ZMD_LEADTIME_SO zls ON zthn.PLANT = zls.PLANT AND zthn.DISTRIK = zls.KOTA AND zthn.TIPE = zls.KD_MATERIAL 
                                                                                    WHERE zthn.ORG = '$validasi_org' 
                                                                                    AND zthn.TANGGAL_TARGET BETWEEN TO_DATE('$tglleadtimeppqty1', 'DD-MM-YYYY HH24:MI:SS') AND TO_DATE('$tglleadtimeppqty2', 'DD-MM-YYYY HH24:MI:SS')
                                                                                    AND zthn.DISTRIK = '$validasi_kode_distrik'
                                                                                    AND zthn.TIPE = '$sub_validasi_kode_material'";
        // echo "$get_harian";

        $hasil_harian = oci_parse($conn, $get_harian);
        oci_execute($hasil_harian);
        $planthasil = array();
        $leadtimehasil = array();
        while ($data = oci_fetch_assoc($hasil_harian)) {
            array_push($planthasil, $data[PLANT]);
            array_push($leadtimehasil, $data[STANDART_AREA]);
        }
        $hari_h = date("Ymd");
        $sap = new SAPConnection();
        $sap->Connect("../include/sapclasses/logon_data.conf");
        if ($sap->GetStatus() == SAPRFC_OK) {
            $sap->Open();
        }
        if ($sap->GetStatus() != SAPRFC_OK) {
        $show_ket .= $sap->PrintStatus();
        }
        $rptrealavgin = 0;

        for ($w = 0; $w < count($planthasil); $w++) {
            $fce = $sap->NewFunction("Z_ZAPPSD_RPT_REAL");
            if ($fce == false) {
                $show_ket .= $sap->PrintStatus();
            }

            //header entri
            $fce->X_VKORG = "7900";
            $fce->X_TGL1 = date(
                "Ymd",
                strtotime($hari_h . " -" . $leadtimehasil[$w] . " day")
            );
            $fce->X_TGL2 = date("Ymd", strtotime($hari_h . "-1 day"));
            $fce->X_WERKS = $planthasil[$w];
            $fce->X_KUNNR = $kode_soldto_si_mdxl_temp;
            $fce->X_STATUS = "70";
            $fce->X_BZIRK = $validasi_kode_distrik;
            ///////////////////////////
            $fce->LRI_VKORG->row["SIGN"] = "I";
            $fce->LRI_VKORG->row["OPTION"] = "EQ";
            $fce->LRI_VKORG->row["LOW"] = "7900";
            $fce->LRI_VKORG->row["HIGH"] = "";
            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
            $fce->LRI_VKORG->row["SIGN"] = "I";
            $fce->LRI_VKORG->row["OPTION"] = "EQ";
            $fce->LRI_VKORG->row["LOW"] = "7000";
            $fce->LRI_VKORG->row["HIGH"] = "";
            $fce->LRI_VKORG->Append($fce->LRI_VKORG->row);
            ///////////////////////////
            ///////////////////////////
            $fce->LR_KODE_DA->row["SIGN"] = "I";
            $fce->LR_KODE_DA->row["OPTION"] = "EQ";
            $fce->LR_KODE_DA->row["LOW"] = $shipto;
            $fce->LR_KODE_DA->row["HIGH"] = "";
            $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
            // foreach ($data_output["items"] as $key => $value) {
            //     if (count($value["shipto_info"]) !== 0) {
            //     //     $adadimdxl = false;
            //     // }else{
            //     //         if($value['kode_gudang_si']==$mdxlshipto or $value['kd_gudang_sbi']==$mdxlshipto or $value['kode_gudang_si']==$kode_gudang_si or $value['kd_gudang_sbi']==$kode_gudang_si){
            //         $rptrealallshipto = 0;
            //         foreach ($value["shipto_info"] as $key2 => $value2) {
            //             if($value2['kode_shipto']==$mdxlshipto){
            //                 $shiptoinfomdxl=$value["shipto_info"];
            //                 if($shiptoinfomdxl!='' or $shiptoinfomdxl!=null){
            //                     foreach ($value["shipto_info"] as $key3 => $value3) {
            //                         // print_r($value3['kode_shipto']);
            //                         ///////////////////////////
            //                         $fce->LR_KODE_DA->row["SIGN"] = 'I';
            //                         $fce->LR_KODE_DA->row["OPTION"] = 'EQ';
            //                         $fce->LR_KODE_DA->row["LOW"] = $value3['kode_shipto'];
            //                         $fce->LR_KODE_DA->row["HIGH"] = '';
            //                         $fce->LR_KODE_DA->Append($fce->LR_KODE_DA->row);
            //                         // echo "ada";
            //                         ///////////////////////////
            //                         // $rptrealallshipto += $rptrealavgin;

            //                     }
            //                 }
            //             }
            //             // print_r("datanya ".$value2['kode_shipto']);
            //         }
            //     }
            // }

            // echo "<pre>";
            // print_r($fce);
            // echo "</pre>";

            $fce->Call();
            $fce->ZDATA->Reset();
            if ($fce->GetStatus() == SAPRFC_OK) {
                while ($fce->ZDATA->Next()) {
                    $rptrealavgin += $fce->ZDATA->row["KWANTUM"];
                    // echo " ".$fce->ZDATA->row["KWANTUM"]." "; // gae detailing
                    // echo $fce->ZDATA->row["TGL_SPJ"]."<br>"; // gae detailing
                }
            } else {
                $fce->PrintStatus();
                return;
            }
        }

        ///////////////////////////////////////

        $fce->Close();
        $sap->Close();
        //  $rptrealavgin = $rptrealavgin/7;
        $realisasi_shipment = $rptrealavgin;
        //  echo "RPT real : ".$rptrealavgin;
        //////////////////////////////////////////////////////////////

        if ((trim($_POST["jenis_kirim"]) != 'FOT' || $validasi_incoterm == "FRC") && $validasi_route != "ZR0001") {
            foreach ($data_output["items"] as $key => $value) {
                if (count($value["shipto_info"]) !== 0) {
                    //     $adadimdxl = false;
                    // }else{
                    foreach ($value["shipto_info"] as $key2 => $value2) {
                        if ($value2["kode_shipto"] == $mdxlshipto) {
                            // foreach ($data_output["items"] as $key => $value) {
                            $mdxlvolume_stock_gudang =
                                $value["volume_stock_gudang"];
                            $mdxlpenjualan_avg = $value["penjualan_avg"];
                            $mdxlsell_out = $value["sell_out"];
                            // $mdxlforcase_level_stok = $mdxlforcase_level_stok+$rptrealavgin;
                            ////////////////////////////////////////perubahan rumus 13/06/2023
                            // new
                            $mdxlforcase_level_stok = $mdxlvolume_stock_gudang + $realisasi_shipment - $mdxlpenjualan_avg * $leadtimeso;
                            ///////////////////////////////////////////////////////////////
                            // old

                            // ///////////////////////////////////////////////////////////////////////////////////// perubahan tanggal 08-12-2022
                            //                 /////////////////////////////////////////////
                            //                 $tgl_qtyrddnya = false;
                            //                 $tgl_cek = date('Ymd', strtotime($validasi_tgl_front_end. ' -'.$leadtimeso.' day'));
                            //                 $tgl_cek2 = date('Ymd');

                            //                 if($tgl_cek>$tgl_cek2){
                            //                         $tgl_qtyrddnya=true;
                            //                     }
                            //                 //////////////////////////////////////////////
                            //         if($tgl_qtyrddnya){
                            //             $startTimeStamp1 = date("d-m-Y");
                            //             $startTimeStamp = strtotime($startTimeStamp1);
                            //             $endTimeStamp = strtotime($validasi_tgl_front_end);
                            //             $timeDiff = abs($endTimeStamp - $startTimeStamp);
                            //             $numberDays = $timeDiff/86400;  // 86400 seconds in one day
                            //             $numberDays = intval($numberDays);
                            //             $mdxlforcase_level_stok = $mdxlvolume_stock_gudang-($mdxlpenjualan_avg*$leadtimeso);
                            //             $avg_sell_out_RDD=$numberDays*$mdxlpenjualan_avg;
                            //             $mdxlforcase_level_stok = $mdxlforcase_level_stok-$avg_sell_out_RDD;

                            //             $avg_sell_in_RDD=$numberDays*$rptrealavgin;
                            //             $mdxlforcase_level_stok = $mdxlforcase_level_stok+$avg_sell_in_RDD;
                            //         }else{
                            //             $mdxlforcase_level_stok = $mdxlvolume_stock_gudang-($mdxlpenjualan_avg*$leadtimeso)+$intransit;
                            //         }
                            // ////////////////////////////////////////////////////////////////////////////////////
                            $mdxlkapasitas_gudang = $value["kapasitas_gudang"];
                            $mdxlkapasitas_gudang = ($configperkapgudangfix / 100) * $mdxlkapasitas_gudang;
                            $mdxlorder_qty = $mdxlkapasitas_gudang - $mdxlforcase_level_stok;
                            $mdxlkapasitas_bongkar = $value["kapasitas_bongkar"];
                            $adadimdxl = true;
                        }
                    }
                }
            }
        }
        /////////////////////////////////////////////////
        /////////////////////////////////////////////////
        if ($adadimdxl) {
            $mdxlorder_qty = $mdxlorder_qty;
            $mdxlkapasitas_bongkar = $mdxlkapasitas_bongkar;
        } else {
            /////////////////////////////////////////////
            //mapping gudang dan prov
            $parameterconfiggudang = "GUDANG_" . $validasi_kode_provinsi;
            $mysql_gudang = "SELECT
                                    CONFIG 
                                    FROM (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='$parameterconfiggudang' and DELETE_MARK='0' ORDER BY ID desc)
                                    WHERE
                                    rownum BETWEEN 0 AND 1";
            $mysql_setgudang = oci_parse($conn, $mysql_gudang);
            oci_execute($mysql_setgudang);
            $row_setgudang = oci_fetch_assoc($mysql_setgudang);
            $configbongkar = $row_setgudang[CONFIG];
            /////////////////////////////////////////////
            if ($configbongkar == 0 or $configbongkar == "" or $configbongkar == null
            ) {
                $mysqlconbongkar =
                    "SELECT CONFIG from ZSD_CONFIG where NAMA_CONFIG='KAPASITAS_BONGKAR_DAN_GUDANG' and delete_mark='0'";

                $mysql_configbongkar = oci_parse($conn, $mysqlconbongkar);
                oci_execute($mysql_configbongkar);
                $row_configbongkar = oci_fetch_assoc($mysql_configbongkar);
                $configbongkar = $row_configbongkar[CONFIG];
            }

            if ($configbongkar != 0 or $configbongkar != "" or $configbongkar != null
            ) {
                // $mdxlforcase_level_stok = 0-(0*$leadtimeso)+$intransit;
                $mdxlforcase_level_stok = 0;
                $mdxlkapasitas_gudang = $configbongkar;
                $mdxlorder_qty = $mdxlkapasitas_gudang - $mdxlforcase_level_stok;
                $mdxlkapasitas_bongkar = $configbongkar;
            }
        }
        /////////////////////////////////////////////////
        /////////////////////////////////////////////////
        $sqlgetpp = "SELECT
                            sum(or_trans_dtl.QTY_PP) as TOTPP
                            FROM
                                OR_TRANS_hdr
                            JOIN or_trans_dtl ON
                                or_trans_hdr.NO_PP = or_trans_dtl.NO_PP
                            WHERE
                                (or_trans_dtl.STATUS_LINE = 'OPEN'
                                    OR or_trans_dtl.STATUS_LINE = 'PROCESS'
                                    OR OR_TRANS_dtl.STATUS_LINE = 'APPROVE')
                                AND or_trans_hdr.SOLD_TO = '$validasi_soldto'
                                AND or_trans_hdr.DELETE_MARK = '0'
                                AND or_trans_dtl.DELETE_MARK = '0'
                                AND or_trans_dtl.KODE_TUJUAN = '$validasi_kode_distrik'
                                AND or_trans_dtl.SHIP_TO = '$validasi_shipto'
                                AND to_char(or_trans_dtl.tgl_leadtime, 'DD-MM-YYYY') = '$validasi_tgl_front_end'
                                AND or_trans_dtl.KODE_PRODUK LIKE '121-301%'
                                AND or_trans_hdr.SO_TYPE != 'ZSE'
                                
                                ";

        $mysql_getpp = oci_parse($conn, $sqlgetpp);
        oci_execute($mysql_getpp);
        $row_getpp = oci_fetch_assoc($mysql_getpp);
        $getpp = $row_getpp[TOTPP];

        $final_qty = $mdxlorder_qty - $getpp;
        $final_qty = '0';
        // echo "final_qty $final_qty";
        // $final_qty = '10';
        /////////////////////////////////////////////////
        // echo "final qty : ".$final_qty;
        if ($final_qty > ($mdxlkapasitas_bongkar - $getpp)) {
            $max_order_qty_socc = $mdxlkapasitas_bongkar - $getpp;
            // $max_order_qty_socc = $final_qty;
            // echo "if $max_order_qty_socc";
        } else {
            $max_order_qty_socc = $final_qty;
            // echo "else $max_order_qty_socc";
        }
        $max_order_qty_socc = '0';
        /////////////////////////////////////////////////

        $validasi_qty_final_mdxl = floor($max_order_qty_socc);

        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        //so 3 layer multi ics FOT-FOT-FRC
        if ($validasi_incoterm == "FOT") {
            $mysql2 = "SELECT
                    INCOTERM_SOURCE
                FROM
                    (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$validasi_plant' and INCOTERM_SOURCE='FRC' ORDER BY id_plant desc)
                WHERE
                    rownum BETWEEN 0 AND 1";
            $mysql_set2 = oci_parse($conn, $mysql2);
            oci_execute($mysql_set2);
            $row_config2 = oci_fetch_assoc($mysql_set2);
            $configso2 = $row_config2[INCOTERM_SOURCE];
            if ($configso2 != "" or $configso2 != null) {
                $validasi_incoterm = $configso2;
            }
        }

        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        $validasi_lanjut = false;
        if ($sub_validasi_kode_material == "121-301" or $sub_validasi_kode_material == "121-302") {
            // if ($validasi_incoterm == "FRC" or $validasi_incoterm == "CIF") {
                if ($leadtimeso != "" or $leadtimeso != null) {
                    $validasi_tanggal_rddmin = date(
                        "Ymd",
                        strtotime(" +" . $leadtimeso . " day")
                    );

                    /////////////////////////////////////////
                    //rddmin+1
                    $validasirddminplus1 = $validasi_kode_provinsi . "_" . $validasi_plant;
                    $mysql_tglleadtimeplus = "SELECT
                            CONFIG
                    FROM
                        (select CONFIG from ZSD_CONFIG where NAMA_CONFIG='TANGGAL_$validasirddminplus1' and DELETE_MARK='0' ORDER BY ID desc)
                    WHERE
                        rownum BETWEEN 0 AND 1";
                    // echo $mysql_tglleadtimeplus;
                    $mysql_settglleadtimeplus = oci_parse(
                        $conn,
                        $mysql_tglleadtimeplus
                    );
                    oci_execute($mysql_settglleadtimeplus);
                    $row_leadtimetglleadtimeplus = oci_fetch_assoc(
                        $mysql_settglleadtimeplus
                    );
                    $leadtimesotglleadtimeplus =
                        $row_leadtimetglleadtimeplus[CONFIG];

                    if (
                        $leadtimesotglleadtimeplus != "" or
                        $leadtimesotglleadtimeplus != "0" or
                        $leadtimesotglleadtimeplus != null or
                        $leadtimesotglleadtimeplus != 0
                    ) {
                        $validasi_rddmin_plussatu = date(
                            "Ymd",
                            strtotime(
                                $validasi_tanggal_rddmin .
                                    " +" .
                                    $leadtimesotglleadtimeplus .
                                    " day"
                            )
                        );
                    } else {
                        $validasi_rddmin_plussatu = date("Ymd", strtotime(" +" . $leadtimeso . " day"));
                    }

                    $validasi_tanggal_front_end = date(
                        "Ymd",
                        strtotime($validasi_tgl_rdd)
                    );
                    if ((trim($_POST["jenis_kirim"]) != 'FOT' || $validasi_incoterm == "FRC") && $validasi_route != "ZR0001") {
                        // print_r($validasi_tanggal_front_end.">=".$validasi_rddmin_plussatu);
                        // if ($validasi_tanggal_front_end >= $validasi_rddmin_plussatu) {
                            if ($sub_validasi_kode_material == "121-301") {
                                if (($qty > $validasi_qty_final_mdxl) && ($validasi_qty_final_mdxl > 1) ) {
                                    $show_ket .= "Quantum Melebihi Kemampuan Bongkar : ".  $validasi_qty_final_mdxl;
                                    $validasi_lanjut = false;
                                }elseif($validasi_qty_final_mdxl < 1){
                                    $show_ket .= "Order Quantum Telah Melebihi Stock Gudang dan Intransit : " .$validasi_qty_final_mdxl ;
                                    $validasi_lanjut = false;
                                }else{
                                    // $validasi_lanjut = false;
                                    // $show_ket .= "Gagal Create PP, quantum tidak sesuai validasi. mohon input PP kembali dan pastikan datanya sesuai<br>";
                                    $validasi_lanjut = true;
                                }
                            } else {
                                $validasi_lanjut = true;
                            }
                        // } else {
                        //     $show_ket .=
                        //         "Gagal Create PP, mohon input kembali dan pastikan datanya sesuai. Tanggal minimum ".date("d-m-Y", strtotime($validasi_rddmin_plussatu));
                        // }
                        // }else if(($validasi_incoterm!='FRC' or $validasi_incoterm!='CIF')){
                        //     $validasi_lanjut = true;
                        // }else if($validasi_incoterm=='FOT'){
                        //             if($sub_validasi_kode_material=='121-301'){
                        //                 if($validasi_qty_final_mdxl>=$validasi_qty_front_end){
                        //                     $validasi_lanjut = true;
                        //                 }else{
                        //                     $show_ket .= "Gagal Create PP, quantum tidak sesuai validasi. mohon input PP kembali dan pastikan datanya sesuai<br>";
                        //                 }
                        //             }else{
                        //                 $validasi_lanjut = true;
                        //             }
                        //     // }else if(($validasi_incoterm!='FRC' or $validasi_incoterm!='CIF')){
                        //     //     $validasi_lanjut = true;
                    } else {
                        $validasi_lanjut = true;
                        // $show_ket .= "Gagal Create PP, mohon input kembali dan pastikan datanya sesuai <br>";
                    }
                } else {
                    $show_ket .=
                        "Gagal Create PP, mohon input kembali dan pastikan datanya sesuai. Tanggal RDD belum dimappingkan";
                }
            // } else {
            //     $validasi_lanjut = true;
            // }
        } else {
            $validasi_lanjut = true;
        }
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        if ($validasi_lanjut) {
            // if ($leadtimeso != 0 or $leadtimeso != "" or $leadtimeso != null){
            //     print_r(date('d-m-Y', strtotime(' +'.$leadtimeso.' day'))."/".$leadtimesotglleadtimeplus);
            // }
            /////////////////////////////////////
            $user_org_in = $_POST["user_org"];
            // $user_org_in = trim($_POST['org']);
            $user_name_in = $_POST["user_name"];
            $branch_plant_in = trim($_POST["branch_plant"]);
            $nama_plant_in = trim($_POST["nama_bplant"]);
            $jenis_kirim_in = trim($_POST["jenis_kirim"]);
            $nama_kirim_in = trim($_POST["nama_kirim"]);
            $sold_to_in = trim($_POST["sold_to"]);
            $sold_to_in = $fungsi->sapcode($sold_to_in);
            $nama_sold_to_in = trim($_POST["nama_sold_to"]);
            $route_in = trim($_POST["route"]);
            // if ($plantEx == 1) {

            if ($rowPlant != "") {
                $validasi_plant = $byg;
            } else {
                $validasi_plant = $validasi_plant;
            }
            $plant = $validasi_plant;
            // }else{
            // $plant = trim($_POST['plant']);
            // $nama_plant = trim($_POST['nama_plant']);
            // }
            $cekNamaPlant = "SELECT
                            sys.WERKS,
                            sys.NAME1 
                            FROM
                            RFC_Z_ZAPP_SELECT_SYSPLAN sys 
                            WHERE
                            WERKS = '$plant'
                            GROUP BY
                            sys.WERKS,
                            sys.NAME1 ";

            $qnamaplant = oci_parse($conn, $cekNamaPlant);
            oci_execute($qnamaplant);
            $nmPlant = oci_fetch_assoc($qnamaplant);
            $nama_plant = $nmPlant["NAME1"];

            // pengecekan apakah dia org 7900 atau bukan, di ambil dari plant
            $cekOrg = substr("{$plant}", 0, 2);
            if ($cekOrg == "79" || $cekOrg == 79) {
                $user_org_in = "7900";
            } else {
                $user_org_in = "7000";
            }
            // ========================= //
            $so_type = trim($_POST["so_type"]);
            $j_kemasan = trim($_POST["jenis_kemasan"]);

            if ($so_type == "ZFC") {
                $cara_bayar_in = "CASH";
            } else {
                $cara_bayar_in = trim($_POST["cara_bayar"]);
            }

            $nama_so_type = trim($_POST["nama_so_type"]);
            $ket = trim($_POST["keterangan"]);
            $kd_kapal = trim($_POST["kd_kapal"]);
            $nm_kapal = trim($_POST["nm_kapal"]);
            $nama_kapal = $kd_kapal . "-" . $nm_kapal;
            $posnr = trim($_POST["com_posnr"]);
            $com_sisa = trim($_POST["com_sisa"]);
            $nokontrakhead = trim($_POST["com_kontrak"]);
            $no_pp_in = $fungsi->or_new_pp_number($conn);
            $lelang_isi = trim($_POST["lelangFLAG"]);
            $tipeCreatePP_isi = trim($_POST["tipeCreatePP"]);

            //field bypass
            /* $datatop = cektop();
                    if ($datatop) {
                    $top=$datatop['TOP'];
                    $nama_top=$datatop['NAME_KEY'];
                    }else{
                    $show_ket.= "TOP Tidak ditemukan!!!";
                    break;
                    } */
            $top = $_POST["top"];
            //            $nama_top = $_POST['nama_top'];
            $nama_top = $_POST["top"];

            $pricelist = $_POST["pricelist"];
            $nama_pricelist = $_POST["nama_pricelist"];
            $valperlcnum = trim($_POST["perslcnum"]);
            $lcnum = $_POST["lcnum"];
            $lcnum = $fungsi->sapcode($lcnum);

            $field_names = array(
                "SOLD_TO",
                "NAMA_SOLD_TO",
                "NO_PP",
                "TGL_PP",
                "PLANT_ASAL",
                "NAMA_PLANT",
                "SO_TYPE",
                "NAMA_SO_TYPE",
                "CARA_BAYAR",
                "INCOTERM",
                "NAMA_INCOTERM",
                "BPLANT",
                "STATUS",
                "CREATE_DATE",
                "CREATED_BY",
                "LAST_UPDATE_DATE",
                "LAST_UPDATED_BY",
                "DELETE_MARK",
                "ROUTE",
                "ORG",
                "NOTE",
                "FLAG_LELANG",
                "TIPEPP",
                "TERM_PAYMENT",
                "NAMA_TOP",
                "PRICELIST",
                "NAMA_PRICELIST",
                "IS_ROYALTY",
                "IS_MSA",
            );
            $field_data = array(
                "$sold_to_in",
                "$nama_sold_to_in",
                "$no_pp_in",
                "SYSDATE",
                "$plant",
                "$nama_plant",
                "$so_type",
                "$nama_so_type",
                "$cara_bayar_in",
                "$jenis_kirim_in",
                "$nama_kirim_in",
                "$branch_plant_in",
                "OPEN",
                "SYSDATE",
                "$user_name_in",
                "SYSDATE",
                "$user_name_in",
                "0",
                "$route_in",
                "$user_org_in",
                "$ket",
                "$lelang_isi",
                "$tipeCreatePP_isi",
                "$top",
                "$nama_top",
                "$pricelist",
                "$nama_pricelist",
                "$isRoyalti",
                "$isMsa",
            );
            $tablename = "OR_TRANS_HDR";
            $fungsi->insert($conn, $field_names, $field_data, $tablename);
            $show_ket .= "Order Reservation Success Made with No. $no_pp_in <br>";

            $sampai = $_POST["jumlah"];
            for ($k = 1; $k <= $sampai; $k++) {
                $shipto = "shipto" . $k;
                $nama_shipto = "nama_shipto" . $k;
                $alamat = "alamat" . $k;
                $kode_distrik = "kode_distrik" . $k;
                $nama_distrik = "nama_distrik" . $k;
                $kode_prov = "kode_prov" . $k;
                $nama_prov = "nama_prov" . $k;
                $produk = "produk" . $k;
                $nama_produk = "nama_produk" . $k;
                $qty = "qty" . $k;
                $uom = "uom" . $k;
                $com_kontrak = "com_kontrak" . $k;
                // $com_posnr="com_posnr".$k;
                $noppref_inc = "refpp" . $k;
                ////////////////////////////////////SOCC-v.2
                $tgl_leadtime = "tgl_kirim" . $k;
                // $tgl_leadtime2 = "tgl_kirim" . $k;
                ////////////////////////////////////

                if (isset($_POST[$shipto])) {
                    $shipto_in = trim($_POST[$shipto]);
                    $nama_shipto_in = trim($_POST[$nama_shipto]);
                    $alamat_in = trim($_POST[$alamat]);
                    $produk_in = trim($_POST[$produk]);
                    $kode_distrik_in = trim($_POST[$kode_distrik]);
                    $nama_distrik_in = trim($_POST[$nama_distrik]);
                    $kode_prov_in = trim($_POST[$kode_prov]);
                    $nama_prov_in = trim($_POST[$nama_prov]);
                    $nama_produk_in = trim($_POST[$nama_produk]);
                    /////////////////////////////////SOCC-v.2
                    $leadtime1 = trim($_POST[$tgl_leadtime]);
                    if ($leadtime1 == "" or $leadtime1 == null) {
                        $leadtime1 = date("d-m-Y");
                    }

                    $tgl_leadtimeval = $leadtime1;
                    //////////////////////////////////
                    $qty_in = trim($_POST[$qty]);
                    if ($j_kemasan == "M3 PALLET1") {
                        // konversi per pallet = 1.6 M3
                        $qty_in = $qty_in * 1.6;
                    }

                    $uom_in = trim($_POST[$uom]);
                    $kontrak = $_POST[$com_kontrak];
                    // $posnr = $_POST[$com_posnr];
                    // $tgl_kirim_in = trim($_POST['tgl_kirim' . $k]);
                    // if ($tgl_kirim_in == "")
                    //     $tgl_kirim_in = date('d-m-Y');
                    $noppref_in = trim($_POST[$noppref_inc]);

                    //////
                    $jenis_kirim_in_config = $jenis_kirim_in;
                    //pengecekan incoterm multi ics
                    if ($jenis_kirim_in_config == "FOT") {
                        $mysql2 = "SELECT
                                    INCOTERM_SOURCE
                                FROM
                                    (SELECT INCOTERM_SOURCE FROM ZMD_MAPPING_PLANT WHERE del='0' AND PLANT_MD='$plant' ORDER BY id_plant desc)
                                WHERE
                                    rownum BETWEEN 0 AND 1";
                        $mysql_set2 = oci_parse($conn, $mysql2);
                        oci_execute($mysql_set2);
                        $row_config2 = oci_fetch_assoc($mysql_set2);
                        $configso2 = $row_config2[INCOTERM_SOURCE];
                    }

                    if (
                        $configso2 != 0 or
                        $configso2 != "" or
                        $configso2 != null
                    ) {
                        $jenis_kirim_in_config = $configso2;
                    } else {
                        $jenis_kirim_in_config = $jenis_kirim_in;
                    }
                    /////

                    $produk_in_getleadtime = substr(trim($produk_in), 0, 7);
                    $plant = trim($plant);
                    $kode_distrik_in = trim($kode_distrik_in);
                    if (
                        $produk_in_getleadtime == "121-301" &&
                        ($jenis_kirim_in_config == "FRC" or
                            $jenis_kirim_in_config == "CIF")
                    ) {
                        $mysql = "SELECT
                                STANDART_AREA
                                FROM
                                    (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_distrik_in' and kd_material='$produk_in_getleadtime' and delete_mark='0' ORDER BY id desc)
                                WHERE
                                    rownum BETWEEN 0 AND 1";
                        $mysql_set = oci_parse($conn, $mysql);
                        oci_execute($mysql_set);
                        $row_leadtime = oci_fetch_assoc($mysql_set);
                        $getdatanyaleadtime = $row_leadtime[STANDART_AREA];
                    } elseif (
                        $produk_in_getleadtime == "121-302" &&
                        ($jenis_kirim_in_config == "FRC" or
                            $jenis_kirim_in_config == "CIF")
                    ) {
                        $mysql = "SELECT
                                STANDART_AREA
                                FROM
                                    (select STANDART_AREA from ZMD_LEADTIME_SO where plant='$plant' and kota='$kode_prov_in' and kd_material='$produk_in_getleadtime' and delete_mark='0' ORDER BY id desc)
                                WHERE
                                    rownum BETWEEN 0 AND 1";
                        $mysql_set = oci_parse($conn, $mysql);
                        oci_execute($mysql_set);
                        $row_leadtime = oci_fetch_assoc($mysql_set);
                        $getdatanyaleadtime = $row_leadtime[STANDART_AREA];
                    } else {
                        $getdatanyaleadtime = "0";
                    }

                    if (
                        ($getdatanyaleadtime != "" or
                            $getdatanyaleadtime != null) &&
                        ($jenis_kirim_in_config == "FRC" or
                            $jenis_kirim_in_config == "CIF")
                    ) {
                        $tgl_kirim_in = date(
                            "d-m-Y",
                            strtotime(
                                $tgl_leadtimeval .
                                    " -" .
                                    $getdatanyaleadtime .
                                    " day"
                            )
                        );
                        $tglceknya = date(
                            "Ymd",
                            strtotime(
                                $tgl_leadtimeval .
                                    " -" .
                                    $getdatanyaleadtime .
                                    " day"
                            )
                        );

                        if ($tglceknya < "20240901") {
                            $tgl_kirim_in = "01-09-2024";
                        }
                        $tglceknyaleadtime = date(
                            "Ymd",
                            strtotime($tgl_leadtimeval)
                        );
                        if ($tglceknyaleadtime < "20240902") {
                            $tgl_leadtimeval = "02-09-2024";
                        }
                    } else {
                        $tgl_kirim_in = date("d-m-Y");
                        $tglceknya = date("Ymd");

                        if ($tglceknya < "20240901") {
                            $tgl_kirim_in = "01-09-2024";
                        }
                        $tglceknyaleadtime = date(
                            "Ymd",
                            strtotime($tgl_leadtimeval)
                        );
                        if ($tglceknyaleadtime < "20240901") {
                            $tgl_leadtimeval = "01-09-2024";
                        }
                    }
                    $field_names = array(
                        "NO_PP",
                        "KODE_PRODUK",
                        "NAMA_PRODUK",
                        "QTY_PP",
                        "TGL_KIRIM_PP",
                        "SHIP_TO",
                        "NAMA_SHIP_TO",
                        "ALAMAT_SHIP_TO",
                        "LAST_UPDATE_DATE",
                        "LAST_UPDATED_BY",
                        "DELETE_MARK",
                        "KODE_TUJUAN",
                        "NAMA_TUJUAN",
                        "STATUS_LINE",
                        "ITEM_NUMBER",
                        "NAMA_KAPAL",
                        "KD_PROV",
                        "NM_PROV",
                        "UOM",
                        "NO_PPREF",
                        "NO_KONTRAK",
                        "NO_KONTRAK_POSNR",
                        "PLANT",
                        "NM_PLANT",
                        "TGL_LEADTIME",
                        "IS_ROYALTY",
                        "IS_MSA",
                    );
                    $field_data = array(
                        "$no_pp_in",
                        "$produk_in",
                        "$nama_produk_in",
                        "$qty_in",
                        "instgl_$tgl_kirim_in",
                        "$shipto_in",
                        "$nama_shipto_in",
                        "$alamat_in",
                        "SYSDATE",
                        "$user_name_in",
                        "0",
                        "$kode_distrik_in",
                        "$nama_distrik_in",
                        "OPEN",
                        "$k",
                        "$nama_kapal",
                        "$kode_prov_in",
                        "$nama_prov_in",
                        "$uom_in",
                        "$noppref_in",
                        "$kontrak",
                        "$posnr",
                        "$plant",
                        "$nama_plant",
                        "instgl_$tgl_leadtimeval",
                        "$isRoyalti",
                        "$isMsa",
                    );
                    $tablename = "OR_TRANS_DTL";
                    $fungsi->insert(
                        $conn,
                        $field_names,
                        $field_data,
                        $tablename
                    );
                    $show_ket .= "Item $produk_in with Qty $qty_in Success<br>";
                }
            }
        }
       // create pp function end

        header('Content-Type: application/json');
        echo json_encode(array(
            "status" => 200,
            "message" => 'Data No. '.$_POST['now'],
            "data" => array("catatan" => $show_ket,"nopp" => $no_pp_in,),
            
        ));
    } else if($_POST['action'] == 'check') {
        $temp = array();
       //get detail material
        $kode_mat_blacklist = array();
        $sqlcek = "SELECT KODE_MATERIAL FROM OR_MAP_DISTRIK_MAT WHERE ORG = '".$_POST['org']."' AND DISTRIK = '".$_POST['kode_distrik1']."' AND PLANT = '".$_POST['plant']."' AND DELETE_MARK = '0'";
        $querycek = oci_parse($conn, $sqlcek);
        oci_execute($querycek);
        $kode_mat_blacklist = array();
        while($datacek=oci_fetch_assoc($querycek)){
            array_push($kode_mat_blacklist, $datacek['KODE_MATERIAL']);
        }

        if ($_POST['brand'] == 'CURAH' || $_POST['brand'] == 'MORTAR ZAK' || $_POST['brand'] == 'MORTAR CURAH' ) {
            $sql = "SELECT DISTINCT
            scm.BRAND,
            scm.MATERIAL AS MATNR,
            sal.MAKTX AS MAKTX,
            UPPER( sal.MEINS ) AS MEINS 
            FROM
                ZSD_TARGET_HEADER_SCM scm 
                LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = scm.MATERIAL
            WHERE
            sal.VKORG IN (".$_POST['org'].") AND scm.BRAND = '".$_POST['brand']."' AND  scm.FLAG_DEL != 'Y' AND  ROWNUM <= 1";
            if ($_POST['produk1']) {
                $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                $sql .= "sal.MATNR = '".$_POST['produk1']."'";
            }
            $sql .= "AND ROWNUM <= 1";
        }else{
            $sql = "SELECT DISTINCT
            mm.BRAND,
            mm.KODE_MATERIAL AS MATNR,
            sal.MAKTX as MAKTX,
            UPPER(sal.MEINS) as MEINS
        
            FROM
            MAPPING_MATERIAL_BRAND mm
            LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 sal ON sal.MATNR = mm.KODE_MATERIAL 
            LEFT JOIN ZSD_TARGET_HEADER_SCM scm ON scm.MATERIAL = sal.MATNR
            WHERE
            sal.VKORG IN (".$_POST['org'].") AND mm.BRAND = '".$_POST['brand']."' AND  mm.FLAG_DEL != 'Y'";
            if ($_POST['produk1']) {
                $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                $sql .= "sal.MATNR = '".$_POST['produk1']."'";
            }
            if ($_POST['jenis_kemasan']) {
                if($_POST['jenis_kemasan'] == 'ZAK'){
                    $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                    $sql .= "sal.MEINS IN ('".$_POST['jenis_kemasan']."','BAG')";
                } else {
                    $sql .=preg_match("/WHERE/i",$sql)? " AND ":" WHERE ";
                    $sql .= "sal.MEINS = '".$_POST['jenis_kemasan']."'";
                }
            }
            $sql .= "AND ROWNUM <= 1";

        }

        $query= oci_parse($conn, $sql);
        oci_execute($query);
        while($datafunc=oci_fetch_assoc($query)){
            if(in_array($datafunc["MATNR"], $kode_mat_blacklist)){
                    continue;
            } else{
                $matnr = $datafunc["MATNR"];
                $nama_matnr = $datafunc["MAKTX"];
                $uom = $datafunc["MEINS"];

                if(strtoupper($datafunc["GEWEI"])=='KG'){
                    $qtymto=@(number_format($datafunc["NTGEW"]/1000,2));
                }else{
                    $qtymto=number_format($datafunc["NTGEW"]);
                }
                $temp['nama_produk'] = $nama_matnr;
                $temp['uom'] = $uom;
                $temp['qto'] = $qtymto;
            }
        }
       //end of get detail material

        $query="SELECT VKBUR,NAME1,STRAS,BZTXT,BEZEB,KVGR1 from RFC_Z_ZCSD_SHIPTO where KUNN2 = '".$_POST['shipto1']."' and BZIRK = '".$_POST['kode_distrik1']."'";
        $sql=oci_parse($conn,$query);
        oci_execute($sql);
        $exec=oci_fetch_assoc($sql);
        if($exec){
            $temp['kode_prov'] = $exec['VKBUR'];
            $temp['nama_shipto'] = $exec['NAME1'];
            $temp['alamat'] = $exec['STRAS'];
            $temp['nama_distrik'] = $exec['BZTXT'];
            $temp['nama_prov'] = $exec['BEZEB'];
            $temp['typetruck'] = $exec['KVGR1'];
        } else {
            header('Content-Type: application/json');
            echo json_encode(array(
                "status" => 204,
                "message" => "Data shipto tidak ditemukan.",
                "data" => '',
            ));
        }

        header('Content-Type: application/json');
        echo json_encode(array(
            "status" => 200,
            "message" => "Success get detail data.",
            "data" => $temp,
        ));
    }
}else{
    header('Content-Type: application/json');
    echo json_encode(
        array(
            "status" => 405,
            "message"=> "Access Denied",
        )
    );
    exit();
}

function readExcel($excelFile) {
    try {
        $cell   = new Spreadsheet_Excel_Reader($excelFile);
        
        $totalRow = $cell->rowcount($sheet_index=0);
        
        $data = array();
        $lengthPopulatedColumn = 16;
        for ($row = 2; $row <= $totalRow; $row++) {
            for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                $data[$row][$column] = $cell->val($row, $column);
            }
        }

        return $data;
    } catch (Exception $e) {
        throw new Exception($e->getMessage());
    }
}

?>
