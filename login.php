<?php




require 'cross_validation.php';
session_start();
//session start untuk meregister sesion user
//fungsi untuk membangun koneksi
include ('include/fungsi.php');
$fungsi=new fungsi();
$conn=$fungsi->koneksi();
$acak=$fungsi->acaklogin();

$act=$_GET['act'];
if($act=="logout"){
	$fungsi->del_session_user();
	if(isset($_GET['act'])){
		header("Location: login.php");
		exit;
	}
}
if(isset($_POST['login'])){
	validateCsrfToken();
	$pass=strip_tags($_POST['pass']);
	$pass=md5($pass);
	$capt=strtolower($_POST['valid']);
	$code=strtolower($_SESSION['captcha_code']);
	$nama=strip_tags(trim(strtoupper($_POST['nama'])));
	
	$awo="SELECT * FROM TB_USER_BOOKING WHERE NAMA='$nama' AND DELETE_MARK <> '1' ";
	$query= @oci_parse($conn,$awo);
	@oci_execute($query);
	$dataawo=@oci_fetch_assoc($query);
	$status=$dataawo['ACTIVE_MARK'];
	if($status==0){
		if($fungsi->login($conn,$nama,$pass,$capt,$code)){
			$yohou="home.php";
			header("Location: home.php");
			exit;
		}else{
		$komen="Login try again, Check your Name, Your Pasword and Captcha !!";
		}
	}else{
	$komen="Your Account In Block.. \n Please contact administrator...!!";
	}
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
	<title>Order reservation application : Login :)</title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
<!--===============================================================================================-->	
	<link rel="icon" type="image/png" href="images/SI.png"/>
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/bootstrap.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/fonts/Linearicons-Free-v1.0.0/icon-font.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/animate.css">
<!--===============================================================================================-->	
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/hamburgers.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/animsition.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/select2.min.css">
<!--===============================================================================================-->	
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/daterangepicker.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/util.css">
	<link rel="stylesheet" type="text/css" href="include/bootstrap/css/main.css">
<!--===============================================================================================-->
<link rel="stylesheet" href="include/bootstrap/css/login_css.css"/>
</head>
<body>
	
	<div class="limiter">
		<div class="container-login100">
			<div class="wrap-login100">
				<div class="login100-form-title" style="background-image: url(images/bg-06.jpg);">
					<span class="login100-form-title-1">
						CSMS <br>Semen Indonesia Group
					</span>
				</div>

				<form class="login100-form validate-form" id="form1" name="form1" method="post" action="login.php">
					<div class="wrap-input100 validate-input m-b-18" data-validate="Username is required">
						<span class="label-input100">Username</span>
						<input class="input100" type="text" name="nama" placeholder="Enter username">
						<span class="focus-input100"></span>
					</div>

					<div class="wrap-input100 validate-input m-b-18" data-validate = "Password is required">
						<span class="label-input100">Password</span>
						<input class="input100" type="password" name="pass" placeholder="Enter password">
						<span class="focus-input100"></span>
					</div>
                                    
                                        <div class="wrap-input100 validate-input m-b-18" data-validate="Username is required">
						<span class="h5">(rewrite the code below)</span>
						<input class="input100" type="text" name="valid" placeholder="Enter Captcha" id="valid" size="6">
						<span class="focus-input100">
                                                   
                                                </span>
					</div>

					<div class="flex-sb-m w-full p-b-20">
						<div class="contact100-form-checkbox">
							 <img src="getimage.php" alt="" name="captcha" width="100" height="45" id="captcha" />
                                                    <a href="javascript:void(0);" onclick="document.images['captcha'].src ='getimage.php'+ '?' + (new Date()).getTime();">
                                                        <img src='images/refresh.png' title='Reload Captcha' border='0' width='20' height='20'>
                                                    </a>
						</div>
					</div>

					<div class="container-login100-form-btn">
                                            <input name="login" type="submit" class="login100-form-btn" id="login" value="Login" />
					</div>
				</form>
                                <p align="center" class="style1"><? echo $komen; ?></p>
			</div>
		</div>
            
    <ul class="bg-bubbles">
      <li></li>
      <li></li>
      <li></li>
      <li></li>
      <li></li>
      <li></li>
      <li></li>
      <li></li>
      <li></li>
      <li></li>
    </ul>
    
            
            
	</div>
    
	<!-- <? include('privacy_policy.php'); ?> -->
    
    


	
<!--===============================================================================================-->
	<script src="include/bootstrap/js/jquery-3.2.1.min.js"></script>
<!--===============================================================================================-->
	<script src="include/bootstrap/js/animsition.min.js"></script>
<!--===============================================================================================-->
	<script src="include/bootstrap/js/popper.js"></script>
	<script src="include/bootstrap/js/bootstrap.min.js"></script>
<!--===============================================================================================-->
	<script src="include/bootstrap/js/select2.min.js"></script>
<!--===============================================================================================-->
	<script src="include/bootstrap/js/moment.min.js"></script>
	<script src="include/bootstrap/js/daterangepicker.js"></script>
<!--===============================================================================================-->
	<script src="include/bootstrap/js/countdowntime.js"></script>
<!--===============================================================================================-->
	<script src="include/bootstrap/js/main.js"></script>

</body>
</html>
