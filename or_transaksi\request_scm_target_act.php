<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';
require_once('phpmailer/phpmailer.php');
require_once('phpmailer/class.smtp.php');

$mail = new PHPMailer();

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'kode_region';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$periode = date('Y-m', strtotime(htmlspecialchars($_REQUEST['periode'])));
$note = htmlspecialchars($_REQUEST['note']);
$approval_name = htmlspecialchars($_REQUEST['approval_name']);
$username = htmlspecialchars($_REQUEST['username']);
$email = htmlspecialchars($_REQUEST['email']);
$level = htmlspecialchars($_REQUEST['level']);

$delete = htmlspecialchars($_REQUEST['delete']);
$id = htmlspecialchars($_REQUEST['id']);
$request_id = htmlspecialchars($_REQUEST['id_event']);
$created_by = htmlspecialchars($user_name);
$UPDATE_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';

$brand     = htmlspecialchars($_REQUEST['brand']);
$plant = htmlspecialchars($_REQUEST['plant']);
$tipe_order = htmlspecialchars($_REQUEST['tipe_order']);
$distrik   = htmlspecialchars($_REQUEST['distrik']);
$material   = htmlspecialchars($_REQUEST['material']);
$incoterm   = htmlspecialchars($_REQUEST['incoterm']);
$qty_bulanan   = htmlspecialchars($_REQUEST['qty_bulanan']);
$periode   = htmlspecialchars($_REQUEST['periode']);
$prioritas   = htmlspecialchars($_REQUEST['prioritas']);
$url_approve = $_SERVER['HTTP_ORIGIN'].'/dev/sd/sdonline/or_transaksi/approval_scm_target.php'; // DEV
// $url_approve = $_SERVER['HTTP_ORIGIN'].'/sdonline/or_transaksi/approval_scm_target.php'; // PROD


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 10;
                    for ($row = 5; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                if($_FILES['attachment']){
                    $attachmentFile  = $_FILES['attachment'];
                    $attachment = $attachmentFile['name'];
                }else {
                    $attachment = "";
                }

                $return_id_head = 0;

                $request_number = generateNumber($conn);
                $sqlHeadIn= "INSERT INTO REQUEST_TARGET_SCM (REQUEST_NUMBER, ATTACHMENT, NOTE, STATUS, created_at, created_by, DEL_MARK) values ('$request_number','".$attachment."','".$note."','1',  SYSDATE, '".$created_by."', '0') RETURNING ID INTO :ID";
                $queryHeadIn = oci_parse($conn, $sqlHeadIn);

                oci_bind_by_name($queryHeadIn, ":ID", $return_id_head, -1, SQLT_INT);

                $resultHeadIn = oci_execute($queryHeadIn);

                $getDataApproval = array();

                if ($resultHeadIn) {
                    $getDataApproval = getDataApproval($conn, $return_id_head);
                    $getDataApproval['URL'] = $url_approve;
                }

                $count_success = 0;
                foreach ($data as $rowNumber => $row) {
                    // Skip baris yang kosong
                    if (empty($row[2]) && empty($row[3]) && $row[4] === '' && empty($row[5]) && empty($row[6]) && empty($row[7]) && empty($row[8]) && empty($row[9]) && empty($row[10])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[2]) || empty($row[3]) || $row[4] === '' || empty($row[5]) || empty($row[6]) || empty($row[7]) || empty($row[8]) || empty($row[9]) || empty($row[10])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        continue;
                    }

                    $data_cek = checkDuplicateDataDtl($conn, $row[2], $row[3], $row[4],$row[5], $row[6], $row[7], $row[9]);
                    // Cek duplikasi di database
                    if ($data_cek['JUMLAH'] > 0) {
                        $messageRows['database'][] = $rowNumber;
                        continue;
                    }

                    $cek_matsales = CekMatSales($conn, $row[3], $row[6]);
                    if ($cek_matsales['JUMLAH'] < 1) {
                        $messageRows['runcost'][] = $rowNumber;
                        continue;
                    }

                    // Jika tidak ada masalah, lakukan upload
                    if (insert_detail($conn, $row[2], $row[3], $row[4],$row[5], $row[6], $row[7], $row[8], $row[9], $row[10], $return_id_head, $created_by)) {
                        $messageRows['success'][] = $rowNumber;
                        $count_success++;
                    } else {
                        $messageRows['system'][] = $rowNumber;
                    }
                }

                if ($count_success < 1) {
                    $sqlcek= "UPDATE REQUEST_TARGET_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $return_id_head";
                    $querycek= oci_parse($conn, $sqlcek);
                    $return=oci_execute($querycek);
                }

                if ($count_success > 0 && !empty($getDataApproval)) {
                    $send_email = sendEmail($mail, $getDataApproval);
                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }
                
                // Notifikasi untuk baris pengecekan runcost di database
                if (!empty($messageRows['runcost'])) {
                    $adjustedRuncost = array_map('adjustRowNumber', $messageRows['runcost']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedRuncost) . " Plant Material belum dilakukan runcost. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage));
            }
        }
        break;        
        case 'show' :
        {
            displayData($conn);
        }
        break;
        case 'show_detail' :
        {
            displayDataDtl($conn, $request_id);
        }
        break;
        case 'add':
        {
            if($_FILES['attachment']){
                $attachmentFile  = $_FILES['attachment'];
                $attachment = $attachmentFile['name'];
            }else {
                $attachment = "";
            }
            if (insert($conn,$note,$attachment,$created_by, $url_approve, $mail)) {
                echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
            } else {
                echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
            }
        }
        break;
        case 'add_detail':
        {
            $cek_matsales = CekMatSales($conn, $plant, $material);
            if ($cek_matsales['JUMLAH'] > 0) {
                $data_cek = checkDuplicateDataDtl($conn,$brand,$plant,$tipe_order, $distrik,$material,$incoterm,$periode);
                if ($data_cek['JUMLAH'] > 0) {
                    echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
                } else {
                    if (insert_detail($conn,$brand,$plant,$tipe_order, $distrik,$material,$incoterm,$qty_bulanan,$periode,$prioritas,$request_id,$created_by)) {
                        echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                    } else {
                        echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                    }
                }
            }else {
                echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Plant Material belum dimapping!'));
            }
        }
        break;
        case 'edit' :
        {
            $sqlcek= "UPDATE REQUEST_TARGET_SCM set NOTE = '$note', ATTACHMENT = '$attachment', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Edit data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'edit_dtl' :
        {
            $sqlcek= "UPDATE REQUEST_TARGET_HEADER_SCM set PERIODE = '$periode', BRAND = '$brand', PLANT = '$plant', DISTRIK = '$distrik', MATERIAL = '$material', INCOTERM = '$incoterm', QTY_BULANAN = '$qty_bulanan', PRIORITAS = '$prioritas', TIPE_ORDER = '$tipe_order', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Edit data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "UPDATE REQUEST_TARGET_HEADER_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where REQUEST_ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $result=oci_execute($querycek);

            $sqlcek= "UPDATE REQUEST_TARGET_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'delete_dtl' :
        {
            $sqlcek= "UPDATE REQUEST_TARGET_HEADER_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'multipleDel' :
        {

            $value = ($_POST['data']);
            $list = array();
            $gagal = 0;
            $sukses= 0;
            $i=0; 
            
            while($i < count($value)){
                $idDlt = $value[$i]['ID']; 
                $sqlcek= "UPDATE REQUEST_TARGET_HEADER_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where REQUEST_ID = $idDlt";
                $querycek= oci_parse($conn, $sqlcek);
                $return=oci_execute($querycek);

                $sql = "UPDATE REQUEST_TARGET_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $idDlt ";
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
    
                if($result){ 
                    $sukses=$sukses+1; 
                }else{ 
                    $gagal=$gagal+1; 
                }
    
                array_push($list, $ID);

                $i++;
            }  
            
            if ($result){
                $keterangan = array('success'=>"Data Berhasil Di Delete : ".$sukses.", gagal : ".$gagal." ! ");
            } else {
                $keterangan = array('errorMsg'=>"Data Gagal Di Delete : ".$gagal." ! ");
            }
            // }
            echo json_encode($keterangan);

        }
        break;
        case 'multipleDelDtl' :
        {

            $value = ($_POST['data']);
            $list = array();
            $gagal = 0;
            $sukses= 0;
            $i=0; 
            
            while($i < count($value)){
                $idDlt = $value[$i]['ID'];          
                $sql = "UPDATE REQUEST_TARGET_HEADER_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $idDlt ";
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
    
                if($result){ 
                    $sukses=$sukses+1; 
                }else{ 
                    $gagal=$gagal+1; 
                }
    
                array_push($list, $ID);

                $i++;
            }  
            
            if ($result){
                $keterangan = array('success'=>"Data Berhasil Di Delete : ".$sukses.", gagal : ".$gagal." ! ");
            } else {
                $keterangan = array('errorMsg'=>"Data Gagal Di Delete : ".$gagal." ! ");
            }
            // }
            echo json_encode($keterangan);

        }
        break;
    }
}

function displayData($conn){
    $org = $_SESSION['user_org'];
    if($conn){
        $sql_app = "SELECT
                        APPROVAL_LEVEL,
                        APPROVAL_NAME
                    FROM
                        MAPP_APPR_TARGET_SCM
                    WHERE
                        DEL_MARK = '0'
                    ORDER BY
                        APPROVAL_LEVEL";
            // echo $sql1;
        $query_app= oci_parse($conn, $sql_app);
        oci_execute($query_app);
        $map_app=array();

        while($row=oci_fetch_array($query_app)){
            $map_app[$row['APPROVAL_LEVEL']] = $row['APPROVAL_NAME'];
        }

        $sql1 = "SELECT
                    rts.*,
                    TO_CHAR(rts.CREATED_AT , 'DD-MON-YYYY HH24:MI:SS') AS REQUESTED_AT,
                    CASE
                        WHEN TO_NUMBER(rts.status) > (
                        SELECT
                            MAX(APPROVAL_LEVEL)
                        FROM
                            MAPP_APPR_TARGET_SCM mats
                        WHERE
                            DEL_MARK = '0'
                        ) THEN 1
                        ELSE 0
                    END AS APPROVED
                FROM
                    REQUEST_TARGET_SCM rts
                WHERE
                    rts.DEL_MARK = '0' 
                    AND rts.CREATED_BY = '".$_SESSION['user_name']."'";
            // echo $sql1;
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $status_f = "";
            if ((int) $row['STATUS'] != 0 && (int) $row['status'] <= count($map_app) && isset($map_app[$row['STATUS']])) {
                $status_f = "Waiting Approve ".$map_app[$row['STATUS']];
            } else if ((int) $row['STATUS'] != 0 && (int) $row['STATUS'] > count($map_app)) {
                $status_f = "Approved";
            } else {
                $status_f = "Rejected by ".$map_app[$row['REJECTED_APP']];
            }


            $result[$i]['ID'] = $row['ID'];
            $result[$i]['REQUEST_NUMBER'] = "REQ".sprintf("%06s", $row['REQUEST_NUMBER']);
            $result[$i]['ATTACHMENT'] = $row['ATTACHMENT'];
            $result[$i]['NOTE'] = $row['NOTE'];
            $result[$i]['STATUS'] = $row['STATUS'];
            $result[$i]['STATUS_F'] = $status_f;
            $result[$i]['REQUESTED_BY'] = $row['CREATED_BY'];
            $result[$i]['REQUESTED_AT'] = $row['REQUESTED_AT'];
            $result[$i]['CREATED_AT'] = $row['CREATED_AT'];
            $result[$i]['REJECTED_APP'] = $row['REJECTED_APP'];
            $result[$i]['REJECTED_BY'] = $row['REJECTED_BY'];
            $result[$i]['REJECTED_AT'] = $row['REJECTED_AT'];
            $result[$i]['APPROVED'] = $row['APPROVED'];
            // $result[$i]['PERIODE'] = $row['PERIODE'];
            // $result[$i]['BRAND'] = $row['BRAND'];
            // $result[$i]['PLANT'] = $row['PLANT'];
            // $result[$i]['DISTRIK'] = $row['DISTRIK'];
            // $result[$i]['MATERIAL'] = $row['MATERIAL'];
            // $result[$i]['INCOTERM'] = $row['INCOTERM'];
            // $result[$i]['QTY_BULANAN'] = $row['QTY_BULANAN'];
            // $result[$i]['PRIORITAS'] = $row['PRIORITAS'];
            // $result[$i]['TIPE_ORDER'] = $row['TIPE_ORDER'];
            // $result[$i]['CREATED_AT'] = $row['CREATED_AT_F'] == null ? '-' : $row['CREATED_AT_F'];
            // $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? '-' : $row['CREATED_BY'];
            // $result[$i]['UPDATED_AT'] = $row['UPDATED_AT_F'] == null ? '-' : $row['UPDATED_AT_F'];
            // $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? '-' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function displayDataDtl($conn, $request_id){
    $org = $_SESSION['user_org'];
    if($conn){
        $sql1 = "SELECT
                    a.*,
                    CASE
                        WHEN TO_NUMBER(a.status) > (
                        SELECT
                            MAX(APPROVAL_LEVEL)
                        FROM
                            MAPP_APPR_TARGET_SCM mats
                        WHERE
                            DEL_MARK = '0'
                        ) THEN 1
                        ELSE 0
                    END AS APPROVED
                FROM
                    (
                    SELECT
                        rths.ID,
                        rths.PERIODE,
                        rths.BRAND,
                        rths.PRIORITAS,
                        rths.PLANT,
                        rths.TIPE_ORDER,
                        rths.DISTRIK,
                        rths.MATERIAL,
                        rths.INCOTERM,
                        rths.QTY_BULANAN,
                        rths.CREATED_BY,
                        rths.UPDATED_BY,
                        TO_CHAR(rths.CREATED_AT , 'DD-MON-YYYY HH24:MI:SS') AS CREATED_AT_F,
                        TO_CHAR(rths.UPDATED_AT , 'DD-MON-YYYY HH24:MI:SS') AS UPDATED_AT_F,
                        MAX(pl.NAME1) AS PLANT_NAME,
                        MAX(ma.MAKTX) AS MATERIAL_NAME,
                        MAX(kt.NM_KOTA) AS CITY_NAME,
                        pl.WERKS,
                        ma.MATNR,
                        rts.STATUS
                    FROM
                        REQUEST_TARGET_HEADER_SCM rths
                    LEFT JOIN ZREPORT_M_KOTA kt 
                    ON
                        rths.DISTRIK = kt.KD_KOTA
                    LEFT JOIN RFC_Z_ZAPP_SELECT_SYSPLAN pl 
                    ON
                        rths.PLANT = pl.WERKS
                    LEFT JOIN RFC_Z_ZCSD_LIST_MAT_SALES_2 ma 
                    ON
                        rths.MATERIAL = ma.MATNR
                    LEFT JOIN REQUEST_TARGET_SCM rts 
                    ON
                        rths.REQUEST_ID = rts.ID
                    WHERE
                        rths.DEL_MARK = '0'
                        AND rths.REQUEST_ID = '".$request_id."'
                    GROUP BY
                        rths.ID,
                        rths.PERIODE,
                        rths.BRAND,
                        rths.PRIORITAS,
                        rths.PLANT,
                        rths.TIPE_ORDER,
                        rths.DISTRIK,
                        rths.MATERIAL,
                        rths.INCOTERM,
                        rths.QTY_BULANAN,
                        rths.REQUEST_ID,
                        rths.CREATED_AT,
                        rths.CREATED_BY,
                        rths.UPDATED_AT,
                        rths.UPDATED_BY,
                        pl.WERKS,
                        ma.MATNR,
                        rts.STATUS) a";
            // echo $sql1;
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            if ($row['TIPE_ORDER'] === '0') {
                $row['TIPE_ORDER'] = 'Standart';
            } elseif ($row['TIPE_ORDER'] === '1') {
                $row['TIPE_ORDER'] = 'Project';
            }
            $result[$i]['ID'] = $row['ID'];
            $result[$i]['PERIODE'] = $row['PERIODE'];
            $result[$i]['BRAND'] = $row['BRAND'];
            $result[$i]['PLANT'] = $row['PLANT'];
            $result[$i]['PLANT_NAME'] = $row['PLANT_NAME'];
            $result[$i]['DISTRIK'] = $row['DISTRIK'];
            $result[$i]['CITY_NAME'] = $row['CITY_NAME'];
            $result[$i]['MATERIAL'] = $row['MATERIAL'];
            $result[$i]['MATERIAL_NAME'] = $row['MATERIAL_NAME'];
            $result[$i]['INCOTERM'] = $row['INCOTERM'];
            $result[$i]['QTY_BULANAN'] = $row['QTY_BULANAN'];
            $result[$i]['PRIORITAS'] = $row['PRIORITAS'];
            $result[$i]['TIPE_ORDER'] = $row['TIPE_ORDER'];
            $result[$i]['CREATED_AT'] = $row['CREATED_AT_F'] == null ? '-' : $row['CREATED_AT_F'];
            $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? '-' : $row['CREATED_BY'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT_F'] == null ? '-' : $row['UPDATED_AT_F'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? '-' : $row['UPDATED_BY'];
            $result[$i]['APPROVED'] = $row['APPROVED'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn,$note,$attachment,$created_by,$url_approve, $mail){
    $return_id_head = 0;
    $request_number = generateNumber($conn);
    $sqlcek= "INSERT INTO REQUEST_TARGET_SCM (REQUEST_NUMBER, ATTACHMENT, NOTE, STATUS, created_at, created_by, DEL_MARK) values ('$request_number','".$attachment."','".$note."','1',  SYSDATE, '".$created_by."', '0') RETURNING ID INTO :ID";
    $query = oci_parse($conn, $sqlcek);
    oci_bind_by_name($query, ":ID", $return_id_head, -1, SQLT_INT);
    $result = oci_execute($query);
    if ($result){
        $getDataApproval = getDataApproval($conn, $return_id_head);
        if (!empty($getDataApproval)) {
            $getDataApproval['URL'] = $url_approve;
            $send_email = sendEmail($mail, $getDataApproval);
        }
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

function insert_detail($conn,$brand,$plant,$tipe_order, $distrik,$material,$incoterm,$qty_bulanan,$periode,$prioritas,$request_id,$created_by) {

    $sqlcek= "INSERT INTO REQUEST_TARGET_HEADER_SCM (PERIODE, BRAND, PLANT, DISTRIK, MATERIAL, INCOTERM, QTY_BULANAN, PRIORITAS, TIPE_ORDER, REQUEST_ID, created_at, created_by, DEL_MARK) values ('$periode','$brand','".$plant."','".$distrik."', '".$material."','".$incoterm."','".$qty_bulanan."','".$prioritas."','".$tipe_order."','".$request_id."',  SYSDATE, '".$created_by."', '0')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn,$approval_group,$approval_name,$username, $email, $level) {
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPP_APPR_TARGET_SCM
                WHERE 
                    APPROVAL_GROUP = '$approval_group'
                    AND APPROVAL_NAME = '$approval_name'
                    AND USERNAME = '$username'
                    AND EMAIL = '$email'
                    AND APPROVAL_LEVEL = '$level'
                    AND DEL_MARK = '0'
                ";
    
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    
    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function checkDuplicateDataDtl($conn,$brand,$plant,$tipe_order, $distrik,$material,$incoterm,$periode)
{
    $sql_select =  "SELECT
                        COUNT(PLANT) AS JUMLAH
                    FROM
                       REQUEST_TARGET_HEADER_SCM
                    WHERE
                        BRAND = '" . $brand . "' 
                        AND PLANT = '" . $plant . "' 
                        AND TIPE_ORDER = '" . $tipe_order . "' 
                        AND PERIODE = '" . $periode . "' 
                        AND DISTRIK = '" . $distrik . "' 
                        AND MATERIAL = '" . $material . "' 
                        AND INCOTERM = '" . $incoterm . "' 
                        AND DEL_MARK = '0' ";

    // echo $sql_select;
    $query = oci_parse($conn, $sql_select);
    oci_execute($query);
    while ($row = oci_fetch_array($query)) {
        $arData['JUMLAH'] = $row['JUMLAH'];
    }
    return $arData;
}

function CekMatSales($conn, $plant, $material)
{   
    $sql_select =  "SELECT
                        COUNT(*) AS JUMLAH
                    FROM
                        RFC_Z_ZCSD_LIST_MAT_SALES_2
                    WHERE 
                        WERKS = '" . $plant . "'
                        AND MATNR = '" . $material . "' 
                        AND RUNCOST = '1' ";

    $query = oci_parse($conn, $sql_select);
    oci_execute($query);

    while ($row = oci_fetch_array($query)) {
        $arData['JUMLAH'] = $row['JUMLAH'];
    }
    return $arData;
}

function generateNumber($conn) {
    $sql_count = "SELECT
                    COALESCE(MAX(REQUEST_NUMBER), 0) + 1 AS NEXT_NUMBER
                FROM
                    REQUEST_TARGET_SCM
                ";
    
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['NEXT_NUMBER'];
    
    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function getDataApproval($conn,$id) {
    $sql_count = "SELECT rts.*, maps.APPROVAL_NAME, maps.EMAIL 
                FROM REQUEST_TARGET_SCM rts
                LEFT JOIN MAPP_APPR_TARGET_SCM maps ON
                    rts.STATUS = maps.APPROVAL_LEVEL
                    AND maps.DEL_MARK = '0'
                WHERE 
                    rts.ID = '$id'
                    AND rts.DEL_MARK = '0'
                ";
    
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count;
    $result['REQUEST_NUMBER'] = "REQ".sprintf("%06s", $row_count['REQUEST_NUMBER']);

    return $result;
}

function sendEmail($mail, $data) {
    $mail_to = $data['EMAIL'];
    // $emailcc = "<EMAIL>";
    
    $mail->IsSMTP();
    $mail->SMTPDebug  = 1;
    $mail->Host       = "relay.sig.id";
    $mail->Port       = 25;
    $mail->SetFrom('<EMAIL>', 'Approval Pengajuan Target Source Plant'); // masukkan alamat pengririm dan nama pengirim jika alamat email tidak sama, maka yang digunakan alamat email untuk username
    $mail->Subject   = "Target Source Plant [" . $data['REQUEST_NUMBER'] . "]";

    $body  = "Dengan Hormat, <br><br>";
    $body .= "Berikut kami sampaikan pengajuan Target Source Plant dengan nomor  " . $data['REQUEST_NUMBER'] . " yang telah diajukan oleh ".$data['CREATED_BY'].". <br><br>";
    $body .= "silahkan klik link berikut untuk melakukan approve :  " . $data['URL'] ." . <br><br>";
    $body .= "Terima Kasih. <br><br>";
    $mail->MsgHTML($body); //masukkan isi dari email

    $mail->AddAddress($mail_to);
    // $mail->AddCC($emailcc);

    if (!$mail->Send()) {
        $response = array("status" => false, "message" => $mail->ErrorInfo);
    } else {
        $response = array("status" => true, "message" => 'Sukses send email');
    }

    return $response;
}

function adjustRowNumber($num) {
    return $num - 4;
}



?>
