@echo off
setlocal

:: Input nama penulis
set /p author=Masukkan nama penulis (default: <PERSON><PERSON><PERSON>): 
if "%author%"=="" set author=<PERSON><PERSON><PERSON>

:: Input tanggal mulai
set /p since=Masukkan tanggal mulai (YYYY-MM-DD, default: 2023-01-01): 
if "%since%"=="" set since=2023-01-01

:: Input tanggal akhir
set /p until=Masukkan tanggal akhir (YYYY-MM-DD, default: 2023-01-31): 
if "%until%"=="" set until=2023-01-31

echo.
echo Menampilkan commit untuk %author% dari %since% hingga %until%...
echo ========================================================
git log --author="%author%" --since="%since%" --until="%until%" --name-status --pretty=format:"%%h | %%ad | %%s" --date=short
echo.
echo Selesai.
pause
