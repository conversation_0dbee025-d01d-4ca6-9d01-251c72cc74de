<?php

/**
 * Description of mEntryBayarVa
 *
 * <AUTHOR> D Munir <<EMAIL>>
 * @since 1.0
 */
class mEntryBayarVa
{
    /**
     *
     * @var dHttpClient
     */
    public $client;
    public $bosUrl = 'https://***********/dev/bni_va_php7';

    public function __construct()
    {
        if (dee::$app->request->isLocal) {
            $this->bosUrl = 'http://sisi.test/dev-bos/bni_va';
        }
        $this->client = new dHttpClient();
        $this->client->baseUrl = $this->bosUrl;
    }

    public function getInvoices($dist, $org = null)
    {
        $sapFunction = dee::$app->sapFunction;
        $params = array(
            'PI_BUKRS' => $org,
            'PI_PYMNT'  => 'X'
        );
        
        $params['PT_KUNNR'] = array(
            array(
                'SIGN' => 'I',
                'OPTION' => 'EQ',
                'LOW' => $dist,
            )
        );

        $result = $sapFunction->call('Z_ZCFI_CMS_RN_DISPLAY_VO', $params);

        return $result['PT_RN_HDX'];
    }

    public function getDist($kode)
    {
        $sql = "SELECT * FROM TB_USER_BOOKING WHERE DISTRIBUTOR_ID=" . $kode;
        $db = dee::$app->db;
        return $db->queryOne($sql);
    }

    public function createVa($invoices)
    {
        $first = $invoices[0];
        $dist = $this->getDist($first['KONTO']);
        $total = 0;
        foreach ($invoices as $row) {
            $amount = strtr($row['WRBTR'], array('.' => '', '-' => ''));
            $total += $amount;
        }
        $header = array(
            'customer_no' => $first['KONTO'],
            'customer_name' => isset($dist['NAMA_DISTRIBUTOR']) && $dist['NAMA_DISTRIBUTOR'] !== null
                ? $dist['NAMA_DISTRIBUTOR']
                : dee::$app->session->user_name,
            'amount' => $total,
            'username' => dee::$app->session->user_name,
            'BELNR' => $first['BELNR'],
        );

        echo json_encode(array(
            'header' => $header,
            'details' => $invoices,
        ));

        exit;

        $response = $this->client->post('dee.php/va/create', array(
            'header' => $header,
            'details' => $invoices,
        ));
        if ($response['isOk']) {
            $body = $response['body'];
            //            $db = dee::$app->db;
            //            $va = $body['virtual_account'];
            //            $header = array(
            //                'VA_NUMBER' => $va,
            //                'COMPANY' => '3000',
            //                'VA_DATE' => date('Y-m-d'),
            //                'VA_STATUS' => '1', // open, paid, expired
            //                'VA_BANK' => 'BNI',
            ////                'REFERENSE' => '',
            ////                'CREATED_BY' => '',
            ////                'CREATED_AT' => '',
            ////                'UPDATE_BY' => '',
            ////                'UPDATE_AT' => '',
            //            );
            //            $db->insert('Z_VA_HEADER', $header);
            //            $c = 10;
            //            foreach ($invoices as $line) {
            //                $row = array(
            //                    'VA_NUMBER' => $va,
            //                    'FISCAL_YEAR' => $line['GJAHR'],
            //                    'COMPANY_CODE' => $line['BUKRS'],
            //                    'DOCUMENT_NUMBER' => $line['BELNR'],
            //                    'LINE_ITEM' => $c,
            //                    //'DOCUMENT_TYPE' => '',
            //                    'TERM_OF_PAYMENT' => $line['ZTERM'],
            //                    //'BASELINE_PAYMENT_DATE',
            //                    //'NET_DUE_DATE' => $line['BELNR'],
            //                    'CURRENCY' => $line['WAERS'],
            //                    'AMOUNT' => $line['WRBTR'],
            //                    //'CLEARING_DOC' => $line['BELNR'],
            //                    //'CLEARING_FISCAL_YEAR' => $line['BELNR'],
            //                );
            //                $db->insert('Z_VA_LINE', $row);
            //                $c += 10;
            //            }
            return $response['body'];
        } else {
            dee::$controller->httpStatus('500');
            return $response['body'];
        }
    }

    public function detailVa($va_number)
    {
        $response = $this->client->post('dee.php/va/inquiry', array(
            'va_number' => $va_number,
        ));
        if ($response['isOk']) {
            $body = $response['body'];
            return $body;
        }
        if (isset($response['status']['code'])) {
            dee::$controller->httpStatus($response['status']['code']);
        } else {
            dee::$controller->httpStatus('500');
        }
        return isset($response['body']) ? $response['body'] : 'Error system';
    }
}
