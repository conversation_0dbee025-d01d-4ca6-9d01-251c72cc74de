<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'kode_region';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$approval_name = htmlspecialchars($_REQUEST['approval_name']);
$username = htmlspecialchars($_REQUEST['username']);
$email = htmlspecialchars($_REQUEST['email']);
$level = htmlspecialchars($_REQUEST['level']);
$active = htmlspecialchars($_REQUEST['active']);

$delete = htmlspecialchars($_REQUEST['delete']);
$id = htmlspecialchars($_REQUEST['id']);
$created_by = htmlspecialchars($user_name);
$UPDATE_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 4;
                    for ($row = 3; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    // Skip baris yang kosong
                    if (empty($row[1]) && empty($row[3]) && empty($row[4])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[1]) || empty($row[3]) || empty($row[4])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkDuplicateData($conn, $row[1], $row[2], $row[3],$row[4])) {
                        $messageRows['database'][] = $rowNumber;
                        continue;
                    }

                    // Jika tidak ada masalah, lakukan upload
                    if (insert($conn, $row[1], $row[2], $row[3], $row[4], $created_by)) {
                        $messageRows['success'][] = $rowNumber;
                    } else {
                        $messageRows['system'][] = $rowNumber;
                    }
                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage));
            }
        }
        break;        
        case 'show' :
        {
            displayData($conn);
        }
        break;
        case 'add':
        {
            if (checkDuplicateData($conn,$approval_name,$username, $email, $level)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            } else {
                if (insert($conn,$approval_name,$user_name, $email, $level, $created_by)) {
                    echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                } else {
                    echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                }
            }
        }
        break;
        case 'edit' :
        {
            if (checkDuplicateData($conn,$approval_name,$username, $email, $level)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            }else {
                $sqlcek= "UPDATE MAPP_APPR_TARGET_SCM set APPROVAL_NAME = '$approval_name', USERNAME = '$username', EMAIL = '$email', APPROVAL_LEVEL = '$level', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name', DEL_MARK = '$active' where ID = $id";
                $querycek= oci_parse($conn, $sqlcek);
                $return=oci_execute($querycek);
                if ($return){
                    echo json_encode(array('success'=>true,'info'=>"Edit data success"));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "UPDATE MAPP_APPR_TARGET_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'multipleDel' :
        {

            $value = ($_POST['data']);
            $list = array();
            $gagal = 0;
            $sukses= 0;
            $i=0; 
            
            while($i < count($value)){
                $idDlt = $value[$i]['ID'];          
                $sql = "UPDATE MAPP_APPR_TARGET_SCM set DEL_MARK = '1', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $idDlt ";
                $query= oci_parse($conn, $sql);
                $result=oci_execute($query);
    
                if($result){ 
                    $sukses=$sukses+1; 
                }else{ 
                    $gagal=$gagal+1; 
                }
    
                array_push($list, $ID);

                $i++;
            }  
            
            if ($result){
                $keterangan = array('success'=>"Data Berhasil Di Delete : ".$sukses.", gagal : ".$gagal." ! ");
            } else {
                $keterangan = array('errorMsg'=>"Data Gagal Di Delete : ".$gagal." ! ");
            }
            // }
            echo json_encode($keterangan);

        }
        break;
    }
}

function displayData($conn){
    $org = $_SESSION['user_org'];
    if($conn){
        $sql1 = "SELECT
        mats.*,
        TO_CHAR(mats.CREATED_AT , 'DD-MON-YYYY HH24:MI:SS') AS CREATED_AT_F,
	    TO_CHAR(mats.UPDATED_AT , 'DD-MON-YYYY HH24:MI:SS') AS UPDATED_AT_F
        FROM
            MAPP_APPR_TARGET_SCM mats
        ORDER BY
            mats.DEL_MARK,
            mats.APPROVAL_LEVEL";
            // echo $sql1;
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID'] = $row['ID'];
            $result[$i]['APPROVAL_NAME'] = $row['APPROVAL_NAME'];
            $result[$i]['USERNAME'] = $row['USERNAME'];
            $result[$i]['EMAIL'] = $row['EMAIL'];
            $result[$i]['APPROVAL_LEVEL'] = $row['APPROVAL_LEVEL'];
            $result[$i]['STATUS'] = $row['DEL_MARK'] == '0' ? "ACTIVE" : "INACTIVE";
            $result[$i]['DEL_MARK'] = $row['DEL_MARK'];
            $result[$i]['CREATED_AT'] = $row['CREATED_AT_F'] == null ? '-' : $row['CREATED_AT_F'];
            $result[$i]['CREATED_BY'] = $row['CREATED_BY'] == null ? '-' : $row['CREATED_BY'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT_F'] == null ? '-' : $row['UPDATED_AT_F'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? '-' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn,$approval_name,$username, $email, $level, $created_by){
    $sqlcek= "INSERT INTO MAPP_APPR_TARGET_SCM (APPROVAL_NAME, USERNAME, EMAIL, APPROVAL_LEVEL, created_at, created_by, DEL_MARK) values ('".$approval_name."','".$username."','".$email."', '".$level."',  SYSDATE, '".$created_by."', '0')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn,$approval_name,$username, $email, $level) {
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPP_APPR_TARGET_SCM
                WHERE 
                    APPROVAL_NAME = '$approval_name'
                    AND USERNAME = '$username'
                    AND EMAIL = '$email'
                    AND APPROVAL_LEVEL = '$level'
                    AND DEL_MARK = '0'
                ";
    $query_count = oci_parse($conn, $sql_count);
    
    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;
    
    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function adjustRowNumber($num) {
    return $num - 2;
}



?>
