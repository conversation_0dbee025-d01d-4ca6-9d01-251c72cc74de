<?php
include('../include/ex_fungsi.php');
require 'library/phpqrcode/qrlib.php';
require 'library/tcpdf/tcpdf.php';
require 'library/fpdi/fpdi.php';
require_once 'helper.php';

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$no_ba = $data->no_ba;
$org = $data->org;
$username_approver = $data->username_approver;
$id_approver = $id_approver;

$query_ba = "SELECT
EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL,
SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
FROM
EX_BA
JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
WHERE EX_BA.DELETE_MARK = '0' 
AND EX_BA.NO_BA = :no_ba
GROUP BY EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL
ORDER BY
EX_BA.ID DESC";
$sql_ba = oci_parse($conn, $query_ba);
oci_bind_by_name($sql_ba, ":no_ba", $no_ba);
oci_execute($sql_ba);

$data_ba = oci_fetch_array($sql_ba);
$status_ba = $data_ba['STATUS_BA'];
$no_ba = $data_ba['NO_BA'];
$filename = $data_ba['FILENAME'];
$user = new User_SP($org);

if($status_ba == 40){
    // URL to be encoded in QR Code
    $param = array(
        "no_ba" => $no_ba,
        "level" => "kabiro"
    );
    $param = base64_encode(json_encode($param));
    $signUrl = get_base_url() . "ex_ba_sp/api/verify_sign_ba.php?kode=" . $param;
    $qrFile = dirname(dirname(__FILE__)) . "/upload/qr_code.png";
    // Generate QR Code
    QRcode::png($signUrl, $qrFile, QR_ECLEVEL_L, 5);

    // Open existing pdf
    $pdf = new FPDI();
    $pdf->AddPage();
    $pdf->setSourceFile( dirname(dirname(__FILE__)) . '/upload/' . $filename); // Load existing PDF
    $tplIdx = $pdf->importPage(1);
    $pdf->useTemplate($tplIdx, 0, 0, 210);

    // Embed QR Code
    $pdf->Image('upload/qr_code.png', 135, 95, 20, 20, 'PNG');
    $pdf->Output(dirname(dirname(__FILE__)) . '/upload/' . $filename, 'F');

    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
    $field_data = array("APPROVE KABIRO", "SYSDATE", "$username_approver", "APPROVE KABIRO");
    $tablename = "EX_TRANS_HDR";
    $field_id = array('NO_BA');
    $value_id = array("$no_ba");
    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
    $field_data = array("50", "SYSDATE", "$username_approver");
    $tablename = "EX_BA";
    $field_id = array('NO_BA');
    $value_id = array("$no_ba");
    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    // track
    $id_approver = '0' . $id_approver;
    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
    $field_data = array("$no_ba","50","Completed","$id_approver","SYSDATE");
    $tablename = "EX_BA_TRACK";
    $fungsi->insert($conn, $field_names, $field_data, $tablename);

    //sendEMail
    $email_content_table = "";
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];
    $mailCc = '';
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Notifikasi Approve Berita Acara Rekapitulasi', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table);
    }

    $show_ket = 'Dokumen BASTP berhasil disetujui';
}else if($status_ba >= 50){
    $show_ket = 'Dokumen BASTP sudah disetujui';
}else{
    $show_ket = 'Dokumen BASTP sedang tidak dalam status perlu persetujuan kabiro';
}
?>