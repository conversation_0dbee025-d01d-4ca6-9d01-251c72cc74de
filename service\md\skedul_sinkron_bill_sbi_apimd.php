<?php 
// session_start();
require_once("saprfc.php");
include("koneksi.php");
require_once("SAPDataModule_Connection.php");

require_once("../autorisasi.php");
$fautoris = new autorisasi();

$page="skedul_sinkron_bill_sbi_apimd.php";
$currentPage="skedul_sinkron_bill_sbi_apimd.php";
$dtBill = array();
try {
    $dtBill = array();

    $sap_con = new SAPDataModule_Connection();

    $sap = $sap_con->getConnSAP(); //getConnSAP_Dev(); //
    
    #/SAP Connection #

    if (!$sap_con) {echo "Koneksi oracle gagal";exit;}
    $fce = &$sap->NewFunction("ZCSD_GET_DO_AUTOGR");
    if ($fce == false) {$sap->PrintStatus();exit;}

    // $date1 = str_replace("-","", $_POST['startdate']);
    // $date2 = str_replace("-","", $_POST['enddate']);

    // $date1 = '20230619';//date('Ymd', strtotime(' -3 days'));//date("Ym01");
    // $date2 = '20230619';//date("Ymd");

    $date1 = date('Ymd', strtotime(' -7 days'));//date("Ym01");
    $date2 = date("Ymd");
    // $date2 = date("Ymd",strtotime("-1 day"));

    // $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => $date);
    $tmp = array("SIGN" => 'I', "OPTION" => 'BT', "LOW" => "$date1", "HIGH" => "$date2");
    $fce->LR_CREATE_DATE->Append($tmp);

    $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "");
    $fce->LR_BILLING->Append($tmp);

    $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "PTSC");
    $fce->LR_VKORG->Append($tmp);
    $tmp = array("SIGN" => 'I', "OPTION" => 'EQ', "LOW" => "ID50");
    $fce->LR_VKORG->Append($tmp);

    $fce->Call();
    // echo "<pre>";
    // print_r($fce->LR_CREATE_DATE);
    $data = array();
    if ($fce->GetStatus() == SAPRFC_OK) {
        
        $fce->T_DATA_AUTOGR->Reset();
        $i = 0;
        $insert_sukses = 0;
        $insert_gagal = 0;
        $nomordopertama = "5207535404";
           
        $nomorarray = 1;

    

        // $conn = $fautoris->koneksi();
        // $sql = "select * from apimd_url_config where nama = 'lookupbillingstatus' and delete_mark = 0";
        // $query = @oci_parse($conn, $sql);
        // @oci_execute($query);
        // $results = oci_fetch_array($query, OCI_ASSOC);
        
        
        
        while ($fce->T_DATA_AUTOGR->Next()) {
            
            $expo = $fce->T_DATA_AUTOGR->row;

            if ($expo['NO_DO']) {
                //echo $expo['NO_DO']." no do $i<br>";
                // $url = 'https://sip.solusibangunindonesia.com/APIMD/LookupBillingStatus';
                // $data = array(
                //    "Token"=> "aSsMx7GV0HFGzlufM4DH", 
                //    "SystemID"=> "QASSO", 
                //    "Sign"=> "I", 
                //    "High"=> "{$expo['NO_DO']}", 
                //    "Low"=> "{$expo['NO_DO']}", 
                //    "Option"=> "EQ"
                // $url = 'https://sip.solusibangunindonesia.com/APIMD/LookupBillingStatus';
                // $data = array(
                //     "Token"=> "aSsMx7GV0HFGzlufM4DH", 
                //    "SystemID"=> "PASSO", 
                //    "Sign"=> "I", 
                //    "High"=> "{$expo['NO_DO']}", 
                //    "Low"=> "{$expo['NO_DO']}", 
                //    "Option"=> "EQ"
                // );
                // $options = array(
                //     'http' => array(
                //     'header' => "Content-type: application/json\r\n",
                //     'method' => 'POST',
                //     'content' => json_encode($data),
                //     )
                // );
                // $context = stream_context_create($options);
                // $result = file_get_contents($url, false, $context);
                // $response = json_decode($result, true);
                // print_r($expo);
                // echo "<br/>";
                // print_r($response->Data[0]);

                // $url = $results['URL'];
                // $data = array(
                //     "token" => $results['TOKEN'],
                //     "systemid" => "QASSO",
                //     "no_do" => $expo['NO_DO'],
                // );
                $url = 'https://dev-skedul.sig.id/md/api_sbi/LookupBillingStatus.php';//$results['URL'];
                // $url = 'https://10.4.194.97/md/api_sbi/LookupBillingStatus.php';//$results['URL'];
                $data = array(
                    "token" =>"9999999999",// $results['TOKEN'],
                    "systemid" => "QASSO",
                    "no_do" => $expo['NO_DO'],
                );

                $ch = curl_init();

                // Set cURL options
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                // Disable SSL verification
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

                $result = curl_exec($ch);

                if (curl_errno($ch)) {
                    // Handle cURL error
                    $result = "<br> cURL Error: " . curl_error($ch);
                } else {
                    // No cURL error, process the result
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                    if ($httpCode == 200) {
                        // Successful request, handle $result data
                        // echo "<br> Successful result: " . $result;
                    } else {
                        // Handle HTTP error
                        $result = "HTTP Error: " . $httpCode;
                    }
                }

                $response = json_decode($result, true);
                 echo  'INFO DETAIL ===> <pre>';
                     print_r(curl_getinfo($ch));
                     echo '</pre>';
                 echo  'RESPONSE ===> <pre>';
                     print_r($result);
                     echo '</pre>';
                 echo '<pre>';
                     print_r($expo);
                     echo '</pre>';
                
                
                //var_dump ($response['Data']);echo " respon lookupbillingstatus<br><br>";

                if (!empty($response['Data'])) {
                    //print_r($param);
                    //echo "DISINI";
                    
                    
                    foreach ($response['Data'] as $key =>$value){ 
                        print_r($value);
                        $hasil = array(array("NO_DO"=>$value['NO_DO'], "BILLING"=>$value['NO_BILLING'], "PPN"=>$value['PPN'], "PPH"=>$value->PPH, "FKIMG"=>$value['FKIMG'], "NETPR"=>$value['NETPR'], "COUNT_BILL_SBI"=>$value['COUNT_ITEM_BILL'], "FKDAT"=>str_replace('-','',$value['FKDAT']), "VRKME"=>$value['VRKME']));

                        // =================================== pass to rfc SI =================================
                        $saprfc_id = "ZCSD_UPDATE_BILL_AUTO_GR";
                        $sap_con2 = new SAPDataModule_Connection();
                        $sap2 = $sap_con2->getConnSAP(); //getConnSAP_Dev(); //
                        if (!$sap_con) {echo "Koneksi oracle gagal";exit;}
                        $fce2 = &$sap2->NewFunction("ZCSD_UPDATE_BILL_AUTO_GR");
                        if ($fce2 == false) {$fce2->PrintStatus();exit;}
                        
                        $tmp = array($hasil);
                        $tmp = $tmp[0];
//                     
                        $fce2->T_UPD->row["NO_DO"] =$tmp[0]["NO_DO"];
                        $fce2->T_UPD->row["BILLING"] =$tmp[0]["BILLING"];
                        $fce2->T_UPD->row["PPN"] =$tmp[0]["PPN"];
                        $fce2->T_UPD->row["PPH"] =$tmp[0]["PPH"];
                        $fce2->T_UPD->row["FKIMG"] =$tmp[0]["FKIMG"];
                        $fce2->T_UPD->row["NETPR"] =$tmp[0]["NETPR"];
                        $fce2->T_UPD->row["FKDAT"] =$tmp[0]["FKDAT"];
                        $fce2->T_UPD->row["COUNT_BILL_SBI"] =$tmp[0]["COUNT_BILL_SBI"];
                        $fce2->T_UPD->row["VRKME"] =$tmp[0]["VRKME"];
                        $fce2->T_UPD->Append($fce2->T_UPD->row);
                        
                        
                        
                        $fce2->Call();
                        // print_r($fce2->T_UPD->row);
                        // print_r($hasil);
                        
                    
                        // Call successfull?
                        $status = "Gagal";
                        if ($fce2->getStatus() == SAPRFC_OK) {
                            $status = "Sukses";
                            echo $sap2->getStatusText()."</p>";
                        }else { 
                            echo $sap2->getStatusText()."</p>";
                        }
                        //print_r($sap2);
                        
                        // Logoff/Close saprfc-connection 
//                        $fce2->logoff();
                        $fce2->Close();
                        $sap2->Close();
    
                        $dataarray5[$nomorarray] = array(
                        "NMORG" => $expo['NMORG'],
                        "NMPLAN" => $expo['NMPLAN'],
                        "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
                        "NO_DO" => $expo['NO_DO'],
                        "NO_SO" => $expo['NO_SO'],
                        "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
                        "NO_DO_REFF" => $expo['NO_DO_REFF'],
                        "CREATE_DATE" => $expo['CREATE_DATE'],
                        "NO_BILLING" => $value['NO_BILLING'],
                        "FKIMG" => $value['FKIMG'],
                        "NETPR" => $value['NETPR'],
                        "SYNC" => $status,
                        );

                        
                        //echo "<p>finished @ ".date(DATE_RFC822)."</p>";
                        //echo "<p>Jumlah Record : $no</p>";
                        // =================================== pass to rfc SI =================================
                    }
                }else {
                    
                    $dataarray5[$nomorarray] = array(
                        "NMORG" => $expo['NMORG'],
                        "NMPLAN" => $expo['NMPLAN'],
                        "NO_TRANSAKSI" => $expo['NO_TRANSAKSI'],
                        "NO_DO" => $expo['NO_DO'],
                        "NO_SO" => $expo['NO_SO'],
                        "NO_PO_REFICS" => $expo['NO_PO_REFICS'],
                        "NO_DO_REFF" => $expo['NO_DO_REFF'],
                        "CREATE_DATE" => $expo['CREATE_DATE'],
                        "NO_BILLING" => "Belum Ada Billing",
                        "FKIMG" => "Belum Ada Billing",
                        "NETPR" => "Belum Ada Billing",
                        "SYNC" => "Gagal",
                    );
                    
                }

                // echo '<pre>';
                //         print_r($response);
                //         echo '</pre>';
                //         exit();

                $nomorarray++;
                // echo '<pre>';
                // print_r($sap2);
                // echo '</pre>';
            }
            
            // echo "$i, ";

            

            $i++;
            // if ($i==10) exit;
        }

        //echo "<p>finished @ZCSD_GET_DO_AUTOGR</p>";
        //echo "<p>Jumlah Record : $i</p>";
    } else {
        $fce->PrintStatus();
    }
    // echo "<pre>";
    // print_r($dtBill);
    // echo json_encode($data);
    // print_r($data);

    $fce->Close();
    $sap->Close();
    // ================================ get list do rfc =================================

 } catch (Throwable $th) {
     //throw $th;
     //print_r($th);
     var_dump($th);
 }
 
 $data2 = 0;
 
