<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();

$org=$_SESSION['user_org'];

if($org == '1000'){
	$display_upload = "inline";
}else {
	$display_upload = "none";
}

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=438;
$user_id=$_SESSION['user_id'];
$page="generate_ppl_park.php";
$no_invoice= $_REQUEST['no_invoice'];
$nod= '';
$sql= "SELECT NO_BA,NO_INVOICE,NO_INV_VENDOR,NO_SHP_TRN,NO_SHP_TRN2,KODE_PRODUK,NAMA_PRODUK,PLANT,NAMA_PLANT,WARNA_PLAT,VEHICLE_TYPE,NAM<PERSON>_VENDOR,VENDOR,SAL_DISTRIK,NAMA_SAL_DIS,SOLD_TO,NAMA_SOLD_TO,SHIP_TO,QTY_SHP,QTY_KTG_RUSAK,QTY_SEMEN_RUSAK,ID,NO_POL,SHP_COST,TOTAL_KLAIM_ALL,NO_PAJAK_EX,KOSTL,PRCTR,KELOMPOK_TRANSAKSI,INCO,ORG, to_char(TANGGAL_INVOICE,'DD-MM-YYYY') as TANGGAL_INVOICE1, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' ORDER BY SAL_DISTRIK, NAMA_VENDOR,NO_SHP_TRN ASC";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query= oci_parse($conn, $sql);
	oci_execute($query);
	// echo $sql;
	
	while($row=oci_fetch_array($query)){
		$no_ba_v=$row[NO_BA];
		$no_invoice_v=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$spj_v[]=$row[NO_SHP_TRN];
		$spjmd[]=$row[NO_SHP_TRN2];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM1];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$plant_v=$row[PLANT]; 
		$nama_plant_v=$row[NAMA_PLANT]; 
                $warna_plat_v=$row[WARNA_PLAT]; 
                $type_plat_ingv=trim($row[VEHICLE_TYPE]);                
                if($type_plat_ingv=='205'){
                    $warna_plat_v='HITAM';
                }		
		$nama_vendor_v=$row[NAMA_VENDOR]; 
		$vendor_v=$row[VENDOR]; 
		
		$tanggal_invoice_v=$row[TANGGAL_INVOICE1];
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];  
		$shp_cost_v[]=$row[SHP_COST];  
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  
		$cost_center_v=$row[KOSTL];  
		$profit_center_v=$row[PRCTR];  
		$prctr_v[]=$row[PRCTR];  
		$kel=$row[KELOMPOK_TRANSAKSI];  		
		$inco=$row[INCO];  	
                $orgin_v=$row[ORG];
		
	}
	if ($warna_plat_v != "KUNING" or ( $kel == "LAUT" and $inco != "FOB" )) 
	$jenis_pajak = "VN";
	else //($warna_plat_v=="HITAM")
	$jenis_pajak = "VZ";

	$total=count($shp_trn_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";
        
     $sql1= "SELECT NO_REKENING, BANK, BANK_CABANG, BVTYP FROM EX_INVOICE WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice'";
// STATUS = 'PROGRESS' AND STATUS2 = 'INVOICED' AND KELOMPOK_TRANSAKSI = 'DARAT'

	$query1 = oci_parse($conn, $sql1);
	oci_execute($query1);

	while($row1=oci_fetch_array($query1)){
            if($row1[NO_REKENING]!=''){
                $no_rek = $row1[NO_REKENING];
                $nama_bank = $row1[BANK];
                $cabang_bank = $row1[BANK_CABANG];
                $bvtyp = $row1[BVTYP];	
            }
	}

	///DENDA K3
	$sql_ = "SELECT NO_DOC_DENDA,KREDIT,SALDO FROM EX_DENDAK3_SALDO WHERE NO_INVOICE LIKE '$no_invoice'";

	$query_= oci_parse($conn, $sql_);
	oci_execute($query_);
	$dendak3 = array();
	while($row=oci_fetch_assoc($query_)){
	$dendak3[] = $row;
	}
        
        //start POEX
        $sqlPOEX = "SELECT MPOAT.BA_NUMBER,MPOAT.NUM,MPOAT.NILAI_TRANSAKSI,MPOA.JUMLAH FROM M_POTONGAN_OA MPOA
                    JOIN M_POTONGAN_OA_TRANS MPOAT ON MPOAT.NUM = MPOA.NUM
                    WHERE MPOAT.NO_INVOICE = '$no_invoice' AND MPOAT.IS_DELETE = '0'";
	$queryPOEX= oci_parse($conn, $sqlPOEX);
	oci_execute($queryPOEX);
	$poex = array();
	while($rowpoex=oci_fetch_assoc($queryPOEX)){
            $poex[] = $rowpoex;
	}
        
        //end POEX
        
        $today = date('Ymd');
        
        $sqlskbd = "SELECT NO_SKBP FROM EX_SKBP WHERE ORG = '$orgin_v' AND VENDOR = '$vendor_v' AND STATUS = 'APPROVE'
        AND TO_CHAR(DATE_FROM, 'YYYYMMDD') <= $today AND TO_CHAR(DATE_TO, 'YYYYMMDD') >= $today AND DELETE_MARK = '0'";
//        echo $sqlskbd;
        $queryskbd = oci_parse($conn, $sqlskbd);
	oci_execute($queryskbd);
        while($row=oci_fetch_assoc($queryskbd)){
            $skbd[] = $row[NO_SKBP];
	}

//	include ('../denda_k3/dendak3.php');
//	$fung = new dendak3();
//	$fung->test();
	// $dendak3 = $dendak3[0];
	// print_r($dendak3);
	if(isset($_POST["reject"]) && $_POST["reject"] == '') {
	?>
	<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
	<script src="../include/jquery.min.js"></script>
	<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
		<div class="alert alert-info" role="alert">
			<form method="post" action="">
				<div class="alert alert-warning" role="alert">
					Reject invoice?
					<br/>
					<strong>Alasan:</strong>
					<textarea style="width: 100%; height: 100px;" name="komentar" maxlength="255" required="required"></textarea>
				</div>
				<button type="submit" name="reject" value="reject" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Reject&nbsp;&nbsp;&gt;&gt;</button>
				<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
			</form>
		</div>
	</div>
	<?
		exit;
	}
	if((isset($_POST["reject"]) && $_POST["reject"] == 'reject')) {
	$no_ba = $_GET["no_ba"];
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.NO_INVOICE,EX_BA_INVOICE.NO_BA,EX_BA_INVOICE.NO_FAKTUR_PAJAK,EX_BA_INVOICE.STATUS_BA_INVOICE,EX_BA_INVOICE.LAMPIRAN,EX_BA_INVOICE.DIPAKAI,EX_BA_INVOICE.KOMENTAR_REJECT, EX_BA_INVOICE.CREATED_BY,EX_BA_INVOICE.CREATED_AT, to_char(TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1 FROM EX_BA_INVOICE WHERE NO_BA = '$no_ba' AND DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	if(isset($data_invoice_ba["ID"])) {
		if(isset($_POST["approve"])){ 
			$status_ba = 90;
			$keter = "Aprove Manager ";
			 
		}
		if(isset($_POST["reject"])){ 
			$status_ba = 80;
			$keter = "Reject Manager "; 
		}
		if(isset($_POST["cancelInv"])){
			$status_ba = 2;
			$keter = "Cancel";
		}
		// $status_ba = isset($_POST["approve"]) ? 50 : 40; 
		$action = "approval_verifikasi_invoice";
		include ('formula_prod.php');
		// header('Location: list_verifikasi_invoice.php');
		// $sql_ba_invoice = "
			// UPDATE
				// EX_BA_INVOICE
			// SET
				// STATUS_BA_INVOICE = $action,
				// UPDATED_BY = $user_id,
				// UPDATED_AT = TO_DATE('" . date("Y-m-d") . "','YYYY-MM-DD'),
				// ".($action == 40 ? "KOMENTAR_REJECT = '".$_POST['komentar']."'," : "")."
				// TGL_APPROVE_REJECT = TO_DATE('" . date("Y-m-d") . "','YYYY-MM-DD'),
				// APPROVE_REJECT_BY = $user_id
			// WHERE
				// ID = ".$data_invoice_ba["ID"]."
		// ";
		// $query_ba_invoice = oci_parse($conn, $sql_ba_invoice);
		// oci_execute($query_ba_invoice);
	}
}
	
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

<style>
.upload-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.upload-section .adminlist {
    margin: 10px 0;
}

.upload-section input[type="file"] {
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: white;
}

.row-file {
    background-color: white;
}

.row-file:nth-child(even) {
    background-color: #f8f8f8;
}

.row-file td {
    padding: 5px;
    border: 1px solid #ddd;
    vertical-align: middle;
}

.row-file input[type="text"] {
    padding: 3px;
    border: 1px solid #ccc;
    border-radius: 2px;
    width: 95%;
}

.delete-file-btn {
    color: #ff0000;
    font-weight: bold;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
}

.delete-file-btn:hover {
    background-color: #ffcccc;
    border-radius: 3px;
    padding: 2px 5px;
}

.preview-link {
    color: #0066cc;
    text-decoration: underline;
    cursor: pointer;
}

.preview-link:hover {
    color: #003366;
}
</style>

<script> 

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }


</script>
<script language="javascript">
<!--
var j=1;
    function start_addd() {
        j++;
	var cek=j-1;
	if(validasi('komponen_biaya_d'+cek+'','','R','nama_komponen_d'+cek+'','','R','nilaid'+cek+'','','RisNum','keterangan_d'+cek+'','','R')){
		if(cek>1){
			for (var i = 1; i < cek; i++){
				var obj_acc_nod = document.getElementById('komponen_biaya_d'+i+'');
				var nilai_acc_nod = obj_acc_nod.value;	
	
				var obj_acc_nod_cek = document.getElementById('komponen_biaya_d'+cek+'');
				var nilai_acc_nod_cek = obj_acc_nod_cek.value;	

				if (nilai_acc_nod == nilai_acc_nod_cek ){
					alert('Data Komponen Biaya Debet Telah Diinputkan \n Silahkan Input Ulang...');
					j--;
					return false;	
				}
			} 
		}
		var body1 = document.getElementById("cobad");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "dd"+j); 
		newdiv.innerHTML='<table width="95%" align="center" class="adminlist"><tr><td align="left" width="45%"><div id="voucherd'+j+'"><input name="komponen_biaya_d'+j+'" type="text" class="inputlabel" id="komponen_biaya_d'+j+'" value="" onChange="ketik_acc_nod(this)" maxlength="12" size="10"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="nama_komponen_d'+j+'" type="text" class="inputlabel" id="nama_komponen_d'+j+'" value="" readonly="true"  size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="pajak_d'+j+'" type="text" class="inputlabel" id="pajak_d'+j+'" value="" readonly="true"  size="5"/><input name="no_gl_d'+j+'" type="hidden" id="no_gl_d'+j+'" value=""/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="cari_btnd" type="button" class="button" id="cari_btnd" value="..." onClick="find_acc()"/></div></td><td align="center" width="20%"><input type="text" value="" id="nilaid'+j+'" name="nilaid'+j+'" size="20" onBlur="javascript:IsNumeric(this)"/></td><td align="center" width="25%"><input name="keterangan_d'+j+'" type="text" id="no_fakturd'+j+'" size="50" value=""/></td><td width="10%"></td></tr></table>';
		body1.appendChild(newdiv);
		
		document.getElementById("jumlahd").value=j;
		}else{
		j--;
		}
    }

var k=1;
    function start_addk() {
        k++;
	var cek=k-1;
	if(validasi('komponen_biaya_k'+cek+'','','R','nama_komponen_k'+cek+'','','R','nilaik'+cek+'','','RisNum','keterangan_k'+cek+'','','R')){

		if(cek>1){
			for (var i = 1; i < cek; i++){
				var obj_acc_nok = document.getElementById('komponen_biaya_k'+i+'');
				var nilai_acc_nok = obj_acc_nok.value;	
	
				var obj_acc_nok_cek = document.getElementById('komponen_biaya_k'+cek+'');
				var nilai_acc_nok_cek = obj_acc_nok_cek.value;	

				if (nilai_acc_nok == nilai_acc_nok_cek ){
					alert('Data Komponen Biaya Kredit Telah Diinputkan \n Silahkan Input Ulang...');
					k--;
					return false;	
				}
			} 
		}

		var body1 = document.getElementById("cobak");
 		var newdiv=document.createElement("div");
		newdiv.setAttribute("id", "kk"+k); 
		newdiv.innerHTML='<table width="95%" align="center" class="adminlist"><tr><td align="left" width="45%"><div id="voucherk'+k+'"><input name="komponen_biaya_k'+k+'" type="text" class="inputlabel" id="komponen_biaya_k'+k+'" value="" onChange="ketik_acc_nok(this)" maxlength="12" size="10"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="nama_komponen_k'+k+'" type="text" class="inputlabel" id="nama_komponen_k'+k+'" value="" readonly="true"  size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="pajak_k'+k+'" type="text" class="inputlabel" id="pajak_k'+k+'" value="" readonly="true"  size="5"/><input name="no_gl_k'+k+'" type="hidden" id="no_gl_k'+k+'" value=""/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input name="cari_btnk" type="button" class="button" id="cari_btnk" value="..." onClick="find_acck()"/></div></td><td align="center" width="20%"><input type="text" value="" id="nilaik'+k+'" name="nilaik'+k+'" size="20" onBlur="javascript:IsNumeric(this)"/></td><td align="center" width="25%"><input name="keterangan_k'+k+'" type="text" id="keterangan_k'+k+'" size="50" value=""/></td><td width="10%"></td></tr></table>';
		body1.appendChild(newdiv);
		
		document.getElementById("jumlahk").value=k;
		}else{
		k--;
		}
    }

    
	function cek_lastd() {
		var obj = document.getElementById("jumlahd");
		var cek = obj.value;	
		if (document.hasil == true){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_acc_nod = document.getElementById('komponen_biaya_d'+i+'');
					var nilai_acc_nod = obj_acc_nod.value;	
		
					var obj_acc_nod_cek = document.getElementById('komponen_biaya_d'+cek+'');
					var nilai_acc_nod_cek = obj_acc_nod_cek.value;	
	
					if (nilai_acc_nod == nilai_acc_nod_cek ){
						alert('Data Komponen Debet Telah Diinputkan \n Silahkan Input Ulang...');
						j--;
						return false;	
						document.hasil = false;
					}
				} 
			}
			return true;
		}else{
			return false;	
			document.hasil = false;
		}	
    }

	function cek_lastk() {
		var obj = document.getElementById("jumlahk");
		var cek = obj.value;	
		if (document.hasil == true){
			if(cek>1){
				for (var i = 1; i < cek; i++){
					var obj_acc_nod = document.getElementById('komponen_biaya_k'+i+'');
					var nilai_acc_nod = obj_acc_nod.value;	
		
					var obj_acc_nod_cek = document.getElementById('komponen_biaya_k'+cek+'');
					var nilai_acc_nod_cek = obj_acc_nod_cek.value;	
	
					if (nilai_acc_nod == nilai_acc_nod_cek ){
						alert('Data Komponen Kredit Telah Diinputkan \n Silahkan Input Ulang...');
						k--;
						return false;	
						document.hasil = false;
					}
				} 
			}
			return true;	
		}else{
			return false;	
			document.hasil = false;
		}	
    }

	function stop_addd()
	{
	if (j==1){alert('Maaf Minimal 1 Detail Komponen Biaya..');return false;}
	k=j;
	k=k.toString();
    var body1 = document.getElementById("cobad");
	var buang = document.getElementById("dd"+k);
    body1.removeChild(buang);
	j=j-1;
	document.getElementById("jumlahd").value=j;
	}
	function stop_addk()
	{
	if (k==1){alert('Maaf Minimal 1 Detail Komponen Biaya..');return false;}
	l=k;
	l=l.toString();
    var body1 = document.getElementById("cobak");
	var buang = document.getElementById("kk"+l);
	alert(l);
    body1.removeChild(buang);
	k=k-1;
	document.getElementById("jumlahk").value=k;
	}
	
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
		 return false;
		 }	  
	 } 
   }

function ketik_acc_nod(obj) {
        var comorg = document.getElementById('org');
	var strURL="ketik_acc.php?komponen_biaya="+obj.value+"&org="+comorg.value+"&nourut="+j;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("voucherd"+j).innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function ketik_acc_nok(obj) {
        var comorg = document.getElementById('org');
	var strURL="ketik_acc.php?komponen_biaya="+obj.value+"&org="+comorg.value+"&nourut="+k;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("voucherk"+k).innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function find_acc() {
            var comorg = document.getElementById('org');
	    var jenis = document.getElementById("jenis_pajaknya");
		var strURL="cari_accd.php?pajak="+jenis.value+"&org="+comorg.value+"&nourut="+j;
		popUp(strURL);
}
function find_acck() {	
            var comorg = document.getElementById('org');
	    var jenis = document.getElementById("jenis_pajaknya");
		var strURL="cari_acck.php?pajak="+jenis.value+"&org="+comorg.value+"&nourut="+k;
		popUp(strURL);
}
function find_rek() {	
	    var no_vendor = document.getElementById("no_vendor");
		var strURL="cari_rek.php?no_vendor="+no_vendor.value;
		popUp(strURL);
}

function testlink(a){
	var noinvc = '0000000000'+a;
	var panjang = noinvc.length;
	if (panjang>10){
		ekor = panjang -10;
		noinvc = noinvc.substring(ekor, panjang);
	}
	k3_nilai=[];
	// alert(noinvc);
	// alert(k3_temp);
	newWindow = window.open("../denda_k3/preview_denda.php?invoice="+noinvc+"&nod="+k3_temp, null, "height=650,width=700,status=yes,toolbar=no,menubar=no,location=no");
	// newWindow = window.open("../denda_k3/preview_denda.php?invoice="noinvc"&noc="k3_temp"", null, "height=1000,width=700,status=yes,toolbar=no,menubar=no,location=no");
}
</script>
</head>

<body>


<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Generate PPL </th>
</tr></table></div>
<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Data Invoice </th>
</tr>
</table>
</div>

<form id="data_claim" name="data_claim" method="post" action="generate_ppl_verif.php" onSubmit="validasi('no_invoice','','R','no_invoice_expeditur','','R','no_vendor','','R','tanggal_invoice','','R','no_rek','','R','nama_bank','','R','bvtyp','','R');cek_lastd();cek_lastk();return document.hasil" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No BA</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_ba" name="no_ba" value="<?=$no_ba_v?>" readonly="true"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td>
            <input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice_v?>" readonly="true"/>
            <input type="hidden" id="org" name="org" value="<?=$orgin_v?>" readonly="true"/>
      </td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice Expeditur </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice_expeditur" name="no_invoice_expeditur" value="<?=$no_invoice_ex_v?>" readonly="true"/></td>
    </tr>
    <tr>
      <td  class="puso">Vendor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="no_vendor" name="no_vendor"  value="<?=$vendor_v?>" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_vendor" name="nama_vendor"  value="<?=$nama_vendor_v?>" readonly="true"/></td>
    </tr>
    <tr>
      <td  class="puso">Tanggal  Invoice</td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_invoice" type="text" id="tanggal_invoice" readonly="true" value="<?=$tanggal_invoice_v?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Cost Center </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="cost_center" name="cost_center"  value="<?=$cost_center_v?>" readonly="true" size="50"/>
        &nbsp;&nbsp;&nbsp;&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Profit Center </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="profit_center" name="profit_center"  value="<?=$profit_center_v?>" readonly="true"/>
        &nbsp;&nbsp;&nbsp;&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Warna Plat </td>
      <td  class="puso">:</td>
      <td ><input name="warna_plat" type="text" id="warna_plat" value="<?=$warna_plat_v?>" readonly="true" /></td>
    </tr>
    <tr>
      <td  class="puso">No Rekening </td>
      <td  class="puso">:</td>
      <td > <input type="text" id="bvtyp" name="bvtyp"  value="<?=$bvtyp?>" readonly="true" size="8"/> &nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="nama_bank" name="nama_bank"  value="<?=$nama_bank?>" readonly="true"/>&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="no_rek" name="no_rek"  value="<?=$no_rek?>" readonly="true"/>
      <input name="cari_rek" type="button" class="button" id="cari_rek" value="..." onClick="find_rek()"/></td>
    </tr>
    <tr>
      <td  class="puso">&nbsp;</td>
      <td  class="puso">&nbsp;</td>
      <td >
      <input type="text" id="cabang_bank" name="cabang_bank"  value="<?=$cabang_bank?>" readonly="true" size="50"/>
	   <input type="hidden" name="jenis_pajaknya" id="jenis_pajaknya" value="<?=$jenis_pajak?>"/>
	  </td>
    </tr>
    <tr>
      <td  class="puso">Keterangan</td>
      <td  class="puso">:</td>
      <td> <input type="text" id="keterangan_miro" name="keterangan_miro"  value="" size="60"/> 
      </td>
    </tr>
    <tr>
      <td  class="puso">SKBP</td>
      <td  class="puso">:</td>
      <? if(count($skbd)>0){ ?>
      <td> <input type="Checkbox" name="skbp" id="skbp" value="YES" checked> Surat Keterangan Bebas Pemotongan Dana/Pemungutan PPh23
      <? } else{ ?>
      <td> <input type="Checkbox" name="skbp" id="skbp" value="YES"> Surat Keterangan Bebas Pemotongan Dana/Pemungutan PPh23
      <? } ?>
      </td>
    </tr>  
  </table>
<br />
<br />
<?
	if($total>0){

?>

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Invoice </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" >
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;NO.</strong></td>
		<td align="center"><strong >TGL SPJ </strong></td>
		 <td align="center"><strong>AREA LT </strong></td>
		 <td align="center"><strong>PRCTR </strong></td>
		 <td align="center"><strong>NO SPJ </strong></td>
		 <td align="center"><strong>SPJ MD </strong></td>
		 <td align="center"><strong>NO POL </strong></td>
		 <td align="center"><strong>PRODUK </strong></td>
		 <td align="center"><strong>DISTRIBUTOR</strong></td>
		 <td align="center"><strong>K.KTG</strong></td>
		 <td align="center"><strong>K.SMN</strong></td>
		 <td align="center"><strong>KWANTUM</strong></td>
		 <td align="center"><strong>JUMLAH</strong></td>
		 <td align="center"><strong>KLAIM</strong></td>
      </tr >
	  </thead>
	  <tbody>
<?
		$total_qty_kantong_rusak_v=0;
		$total_qty_semen_rusak_v=0;
		$total_qty_v=0;
		$total_shp_cost_v=0;
		$total_total_klaim_all_v=0;

?>
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     

<?
		$total_qty_kantong_rusak_v+=$qty_kantong_rusak_v[$i];
		$total_qty_semen_rusak_v+=$qty_semen_rusak_v[$i];
		$total_qty_v+=$qty_v[$i];
		$total_shp_cost_v+=$shp_cost_v[$i];
		$total_total_klaim_all_v+=$total_klaim_all_v[$i];

?>
	  <td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $sal_dis_v[$i]; ?></td>
		<td align="center"><? echo $prctr_v[$i]; ?></td>
		<td align="center"><? echo $spj_v[$i]; ?></td>
		<td align="center"><? echo $spjmd[$i]; ?></td>
		<td align="center"><? echo $no_pol_v[$i]; ?></td>
		<td align="center"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? echo $nama_sold_to_v[$i]; ?></td>
		<td align="center"><? echo number_format($qty_kantong_rusak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($qty_semen_rusak_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($qty_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($shp_cost_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($total_klaim_all_v[$i],0,",","."); ?></td>
		</tr>
	  <? } ?>
		</tbody>
		<tfoot>
		<tr class="quote">
		<td colspan="9" align="center"><strong>Total</strong></td>
		<td align="center"><strong><? echo number_format($total_qty_kantong_rusak_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_qty_semen_rusak_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_qty_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_shp_cost_v,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_total_klaim_all_v,0,",","."); ?></strong></td>
		</tr>
		</tfoot>
	</table>

<br />
<br />

<? 
if (count($dendak3)>0) {
?>

	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Pelanggaran K3 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
		  <tr class="quote">
			<td ><strong>&nbsp;&nbsp;NO.</strong></td>
			<td align="center"><strong >NO DOKUMEN</strong></td>
			<td align="center"><strong>DIPOTONGKAN</strong></td>
			<td align="center"><strong>JUMLAH DENDA</strong></td>
	      </tr >
		  </thead>
		  <tbody>
		<?
			$total_potongan=0;
			$total_denda=0;
			$total_sisa=0;

		?>
	  	<?  $i=0;
  	// for($i=0; $i<$total;$i++) {
  		foreach ($dendak3 as $key => $value) {

  			$total_potongan += floatval($value['KREDIT']);
  			$total_denda += floatval($value['SALDO']);

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     
		  	<td align="center"><? echo $b; ?></td>
			<td align="center"><?= $value['NO_DOC_DENDA']  ?></td>
			<td align="center"><?= number_format($value['KREDIT'],0,",",".") ?></td>
			<td align="center"><?= number_format($value['SALDO'],0,",",".") ?></td>
		</tr>
		<? 
		  $i++;
		} 
		?>
		</tbody>
		<tfoot>
		<tr class="quote">
		<td colspan="2" align="center"><strong>Total</strong></td>
		<td align="center"><strong><? echo number_format($total_potongan,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_denda,0,",","."); ?></strong></td>
		</tr>
		</tfoot>
	</table>

<br />
<br />
<? }?>
<!-- //Start POEX-->
<?php if (count($poex)>0) {?>
    	<div align="center">
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Potongan OA</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
		  <tr class="quote">
			<td ><strong>&nbsp;&nbsp;NO.</strong></td>
			<td align="center"><strong >NO DOKUMEN</strong></td>
                        <td align="center"><strong >NUM</strong></td>
			<td align="center"><strong>DIPOTONGKAN</strong></td>
			<td align="center"><strong>NILAI MASTER POTONGAN</strong></td>
                 </tr >
		</thead>
		<tbody>
		<?
			$total_potonganpoex=0;
			$total_dendapoex=0;
			$total_sisa=0;

		?>
	  	<?  $i=0;
  		foreach ($poex as $key => $value) {

  			$total_potonganpoex += floatval($value['NILAI_TRANSAKSI']);
  			$total_dendapoex += floatval($value['JUMLAH']);

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke' >";
			}	

		?>     
		  	<td align="center"><? echo $b; ?></td>
			<td align="center"><?= $value['BA_NUMBER']  ?></td>
                        <td align="center"><?= $value['NUM']  ?></td>
			<td align="center"><?= number_format($value['NILAI_TRANSAKSI'],0,",",".") ?></td>
			<td align="center"><?= number_format($value['JUMLAH'],0,",",".") ?></td>
		</tr>
		<? 
		  $i++;
		} 
		?>
                </tbody>
		<tfoot>
		<tr class="quote">
		<td colspan="3" align="center"><strong>Total</strong></td>
		<td align="center"><strong><? echo number_format($total_potonganpoex,0,",","."); ?></strong></td>
		<td align="center"><strong><? echo number_format($total_dendapoex,0,",","."); ?></strong></td>
		</tr>
		</tfoot>
	</table>
    <br />
    <br />
<?php } ?> 
    <!-- //End POEX-->
	<!-- <a onclick="testlink(<?= $no_invoice;?>)" class="button">Find Dokumen Denda k3</a> -->
	<!-- <a href="javascript:popUp('../denda_k3/preview_denda.php?invoice=<?=$no_invoice;?>&noc=k3_temp')" onclick="k3_nilai=[]" class="button">Find Dokumen Denda k3</a> -->
	<table width="95%" align="center" class="adminlist">
	<tr class="quote">
		<td align="left" width="45%"><strong>Komponen Surcharge</strong></td>
		<td align="center" width="20%"><strong>Nilai Debet</strong></td>
		<td align="center" width="25%"><strong>Keterangan</strong></td>
		<td width="10%"></td>
	</tr>
	<tr>
		<?php
			$sql_surcace = "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN='SURCHARGE'";

			$sql_total_price = "SELECT
									FORMULA.PK,
									FORMULA.NO_SPJ,
									FORMULA.SISA_WAKTU1-FORMULA.LEADTIME AS HARI,
									CASE 
										WHEN (FORMULA.SISA_WAKTU1-FORMULA.LEADTIME) < 1 THEN 24
										WHEN  (FORMULA.SISA_WAKTU1-FORMULA.LEADTIME) IS NULL THEN 24
										ELSE 48
									END WAKTU_SHURCHARGE,
									(FORMULA.SELISIH_WAKTU) SELISIH_WAKTU,
									HARGA.HARGA
									FROM (
									SELECT
									N1.*,
									L.STANDART_AREA AS LEADTIME
									FROM (
									SELECT
									'X' AS PK,
									A.NO_SO,
									A.NO_SHP_TRN AS NO_SPJ,
									A.ORG,
									A.PLANT,
									A.SAL_DISTRIK,
									SUBSTR(A.KODE_PRODUK,0, 7) AS MATERIAL,
									A.TANGGAL_KIRIM AS TANGGAL_SPJ,
									TO_CHAR(A.TANGGAL_DATANG, 'YYYYMMDDHHMI') AS TGL_DATANG_CSMS,
									TO_CHAR(A.TANGGAL_BONGKAR, 'YYYYMMDDHHMI') AS TGL_BONGKAR_CSMS,
									A.TANGGAL_DATANG,
									(TO_CHAR(C.TANGGAL_DATANG, 'YYYYMMDD')-TO_CHAR(A.TANGGAL_KIRIM, 'YYYYMMDD')) AS SISA_WAKTU1,
									(TO_CHAR(C.TANGGAL_BONGKAR, 'YYYYMMDDHHMI')-TO_CHAR(C.TANGGAL_DATANG, 'YYYYMMDDHHMI')) AS SELISIH_CSMS,
									A.TANGGAL_BONGKAR,
									B.TGL_KIRIM_PP,
									B.TGL_TERIMA,
									ROUND((C.TANGGAL_BONGKAR-C.TANGGAL_DATANG)*24, 0) AS SELISIH_WAKTU,
									TO_CHAR(C.TANGGAL_DATANG, 'YYYYMMDDHHMI') AS TANGGAL_DATANG_EPOD,
									TO_CHAR(C.TANGGAL_BONGKAR, 'YYYYMMDDHHMI') AS TANGGAL_BONGKAR_EPOD
									FROM EX_TRANS_HDR A 
									LEFT JOIN OR_TRANS_DTL B ON A.NO_SO=B.NO_SO
									LEFT JOIN LOG_SEMEN_POD C ON A .NO_SHP_TRN=C.NO_SPJ
									WHERE A.NO_INVOICE = '$no_invoice_v'
									) N1 
									LEFT JOIN ZMD_LEADTIME_SO L ON N1.PLANT=L.PLANT AND N1.SAL_DISTRIK=L.KOTA AND N1.MATERIAL=L.KD_MATERIAL
									WHERE L.STANDART_AREA IS NOT NULL
									) FORMULA 
									LEFT JOIN (SELECT 
													'X' AS PK,
													A.HARGA
												FROM M_SETTING_HARGASURCACE A 
												WHERE NAMA_SURCACE='Jam') HARGA ON FORMULA.PK=HARGA.PK";


			$query_surcace= oci_parse($conn, $sql_surcace);
			oci_execute($query_surcace);

			$query_price= oci_parse($conn, $sql_total_price);
			oci_execute($query_price);

			$row = oci_fetch_assoc($query_surcace);
			$row_price = oci_fetch_assoc($query_price);
			$price_condition = $row_price['SELISIH_WAKTU'] - $row_price['WAKTU_SHURCHARGE'];
			if (count($row_price) > 0) {
				$price_condition = $price_condition * $row_price['HARGA'];
			} else {
				$price_condition = 0;
			}
		?>
		<td align='left'>
			<div id='voucherd1'>
				<input type='text' class="inputlabel" value="<?=$row['KODE_KOMPONEN'];?>" id='komponen_biaya_ds1' name='komponen_biaya_ds1' size='10' readonly/>
				&nbsp;&nbsp;&nbsp;&nbsp;
				<input type='text' class="inputlabel" value="<?=$row['NAMA_KOMPONEN'];?>" id='nama_komponen_ds1' name='nama_komponen_ds1' size='20' readonly/>
				&nbsp;&nbsp;&nbsp;&nbsp;
				<input type='text' class="inputlabel" value="<?=$row['TAX_CODE'];?>" id='pajak_ds1' name='pajak_ds1' size='5' readonly/>
			</div>
		</td>
		<td align="center">
			<input type='text' id='nilaids1' name='nilaids1' value="<?=$price_condition < 0 ? 0 : $price_condition;?>" size='20' onBlur='javascript:IsNumeric(this)'/>
			<input name="no_gl_ds1" type="hidden" id="no_gl_ds1" value="<?=$no_gld?>"/>
		</td>
		<td align="left">
			<input name="keterangan_ds1" type="text" id="keterangan_ds1" size="50" value=""/>
		</td>
		<td></td>
	</tr>
</table>
<br /><br />
<table width="95%" align="center" class="adminlist">
<tr class="quote">
	<td align="left" width="45%"><strong>Komponen Biaya</strong></td>
	<td align="center" width="20%"><strong>Nilai Debet</strong></td>
	<td align="center" width="25%"><strong>Keterangan</strong></td>
	<td width="10%"></td>
</tr>
<tr>
<td align="left">
	<div id="voucherd1">
      <input name="komponen_biaya_d1" type="text" class="inputlabel" id="komponen_biaya_d1" value="<?=$acc_nod?>" onChange="ketik_acc_nod(this)" maxlength="12" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_komponen_d1" type="text" class="inputlabel" id="nama_komponen_d1" value="<?=$nama_acc_nod?>" readonly="true"  size="20"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="pajak_d1" type="text" class="inputlabel" id="pajak_d1" value="<?=$pajakd?>" readonly="true"  size="5"/>
      <input name="no_gl_d1" type="hidden" id="no_gl_d1" value="<?=$no_gld?>"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
        <input name="cari_btnd" type="button" class="button" id="cari_btnd" value="..." onClick="find_acc()"/>
	</div>
    </td>
<td align="center">
	<input type="text" value="" id="nilaid1" name="nilaid1" size="20" onBlur="javascript:IsNumeric(this)"/></td>
<td align="center">
	<input name="keterangan_d1" type="text" id="keterangan_d1" size="50" value=""/></td>
<td width="100">
	<input type="button" value=" + " name="tambah" onClick="return start_addd();" class="button"/>
	<input type="button" value="  -  " name="kurang" onClick="return stop_addd();" class="button" />
</td>
</tr>
</table>

<div id="cobad"></div>

<input type="hidden" value="1" name="jumlahd" id="jumlahd" />
	
<br />
<br />
<table width="95%" align="center" class="adminlist">
<tr class="quote">
	<td align="left" width="45%"><strong>Nama Komponen</strong></td>
	<td align="center" width="20%"><strong>Nilai Kredit</strong></td>
	<td align="center" width="25%"><strong>Keterangan</strong></td>
	<td width="10%"></td>
</tr>
<tr>
<td align="left">
	<div id="voucherk1">
      <input name="komponen_biaya_k1" type="text" class="inputlabel" id="komponen_biaya_k1" value="<?=$acc_nok?>" maxlength="12" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_komponen_k1" type="text" class="inputlabel" id="nama_komponen_k1" value="<?=$nama_acc_nok?>" readonly="true"  size="20"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="pajak_k1" type="text" class="inputlabel" id="pajak_k1" value="<?=$pajakk?>" readonly="true"  size="5"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="no_gl_k1" type="hidden" id="no_gl_k1" value="<?=$no_gl_k?>"/>
        <input name="cari_btnk" type="button" class="button" id="cari_btnk" value="..." onClick="find_acck()"/>
    </div></td>
<td align="center">
	<input type="text" value="" id="nilaik1" name="nilaik1" size="20" onBlur="javascript:IsNumeric(this)"/></td>
<td align="center">
	<input name="keterangan_k1" type="text" id="keterangan_k1" size="50" value=""/><input name="doctemp" type="text" id="doctemp" hidden /></td>
<td width="100">
	<input type="button" value=" + " name="tambah" onClick="return start_addk();" class="button"/>
	<input type="button" value="  -  " name="kurang" onClick="return stop_addk();" class="button" /></td>
</tr>
</table>
<div id="cobak"></div>
<input type="hidden" value="1" name="jumlahk" id="jumlahk" />

<!-- File Upload Section -->
<br />
<br />
<table width="95%" align="center" class="adminlist">
  <tr class="quote">
    <td align="left" width="45%">Upload Lampiran</td>
    <td width="20%">&nbsp;</td>
    <td width="25%">&nbsp;</td>
    <td width="10%">&nbsp;</td>
  </tr>
  <tr>
    <td align="left" colspan="4">
      <input type="file" style="width: 400px;" onchange="tambahFile(this);" accept="image/gif, image/jpeg, image/png, application/pdf" multiple>
      <br/>
      <br/>
      <i style="color:red;">Note: Ukuran File Lampiran Max. 2 MB (Format: PDF, JPG, PNG, GIF)</i>
    </td>
  </tr>
</table>
<br />
<table width="95%" align="center" class="adminlist">
  <tr class="quote">
    <td align="center" style="width: 40px;"><strong>NO</strong></td>
    <td align="center" style="width: 150px;"><strong>Nama Lampiran</strong></td>
    <td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
    <td align="center" style="width: 200px;"><strong>Lampiran</strong></td>
    <td align="center" style="width: 120px;"><strong>Submit By</strong></td>
    <td align="center" style="width: 60px;"><strong>Aksi</strong></td>
  </tr>
<tbody id="isiLampiran">
  <!-- File uploads will be added here dynamically -->
</tbody>
</table>
<br />
<br />

<table>
<tr>
<td>
	<?php 
	$keterangan_miro = $_POST['keterangan_miro'];
	$skbp = $_POST['skbp'];
	?>
	<input type="submit" value=" APPROVE " name="simpan" class="button"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<!-- <button type="submit" name=" APPROVE " style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Approve</button> -->
	<a href="generate_ppl_reject.php?no_ba=<?=$no_ba_v?>&no_invoice=<?=$no_invoice_v?>&keterangan_miro=<?=$keterangan_miro?>&skbp=<?=$skbp?>" class="button">REJECT</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	<!-- <button type="submit" name="reject" style="margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 4px; background-color: #ff0000; color: #fff; border: 1px solid #000; border-radius: 4px;">Reject</button> -->
	<a href="list_verifikasi_invoice.php" class="button"><? echo "CANCEL"; ?></a>

	<input name="btn-upload" type="button" class="button" id="btn-upload" value="UPLOAD" style="margin-left:30px;display:<?= $display_upload ?>;" onClick="upload('<?= $no_invoice_v ?>')">
</td>
</tr>
</table>
</form>
	</div>
	
	
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>

<script src="../include/jquery.min.js"></script>
<script type="text/javascript">
<? if ($total> 11){ ?>
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
<? } ?>

var monthNames = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
var hari_ini = new Date();
var tgl = String(hari_ini.getDate()).padStart(2, '0');
var bln = hari_ini.getMonth();
var thn = hari_ini.getFullYear();

var jumlahFile = 0;
var indexFile = 0;

function tambahFile(file) {
	if((file.value).length > 0) {
		
		const fileSize = file.files[0].size / 1024 / 1024; // in MiB
		  if (fileSize > 2) {
			alert('File size max 2 MB');
			file.value="";
			 return false;
		  } 
		var reader = new FileReader();
		var user = "<?php echo isset($_SESSION['user_name']) ? $_SESSION['user_name'] : 'User'; ?>";
		reader.readAsDataURL(file.files[0]);
		reader.onload = function () {
			$("#isiLampiran").append(
				"<tr class='row-file'>" +
					"<td align='center' class='nomor-file'></td>" +
					"<td align='center'>" +
						"<input type='text' name='file["+indexFile+"][lampiran]' required='required' style='width: 100%;' maxlength='40' placeholder='Nama lampiran'>" +
						"<input type='hidden' name='file["+indexFile+"][nama]' style='display: none;' value=\""+file.files[0].name+"\">" +
						"<input type='hidden' name='file["+indexFile+"][file]' style='display: none;' value=\""+reader.result+"\">" +
					"</td>" +
					"<td align='center'><input type='hidden' name='file["+indexFile+"][tanggal]' style='display: none;' value='"+thn+'-'+String(bln+1).padStart(2, '0')+'-'+tgl+"'>"+tgl+"-"+monthNames[bln]+"-"+thn+"</td>" +
					"<td align='center'><a href='javascript:void(0);' class='preview-link' data-base64=\""+reader.result+"\" data-name=\""+file.files[0].name+"\" onclick='previewFile(this);'>"+file.files[0].name+"</a></td>" +
					"<td align='center'><input type='text' name='file["+indexFile+"][user]' value=\""+user+"\" style='width: 100%;' readonly='readonly'>" +
					"<td align='center'><a href='javascript:void(0);' onclick='kurangiFile(this);' class='delete-file-btn' title='Hapus file'>&times;</a></td>" +
				"</tr>"
			);
			file.value = "";
			jumlahFile++;
			indexFile++;
			refreshNomor();
		}
		reader.onerror = function (error) {
			alert('Error: ', error);
		}
	}
}

function kurangiFile(elm) {
	if(confirm("Hapus file?")) {
		elm.parentNode.closest('.row-file').remove();
		jumlahFile--;
		refreshNomor();
	}
}

function refreshNomor() {
	var elmNomor = document.getElementsByClassName('nomor-file');
	for (var i=0; i < elmNomor.length; i++) {
		elmNomor[i].innerHTML = i + 1;
	}
}

function previewFile(elm) {
	var w = window.open('about:blank');

	// FireFox seems to require a setTimeout for this to work.
	setTimeout(() => {
	  w.document.body.appendChild(w.document.createElement('iframe')).src = $(elm).data("base64");
	  w.document.body.style.margin = 0;
	  w.document.getElementsByTagName("iframe")[0].style.width = '100%';
	  w.document.getElementsByTagName("iframe")[0].style.height = '100%';
	  w.document.getElementsByTagName("iframe")[0].style.border = 0;
	}, 10);
}

function upload(no_inv) {
	var strURL="run_ppl_upload.php?no_inv="+no_inv;
	popUp(strURL);
}

</script>
</body>
</html>
