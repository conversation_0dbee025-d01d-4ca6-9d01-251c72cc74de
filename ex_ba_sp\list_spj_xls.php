<?php

session_start();
include_once('../include/ex_fungsi.php');
// include('../include/or_fungsi.php');
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$user_org = $_SESSION['user_org'];

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
    header("Pragma: public");
}

$orgIn = $_POST['eks_org_in'];     
$no_shipment = $_POST['eks_no_shipment'];     
$distributor = $_POST['eks_distributor'];     
$vendor = $_POST['eks_vendor'];     
$plant = $_POST['eks_plant'];     
$tipe_transaksi = $_POST['eks_tipe_transaksi'];     
$tanggal_mulai_sql = $_POST['eks_tanggal_mulai'];     
$tanggal_selesai_sql = $_POST['eks_tanggal_selesai'];     
$warna_plat = $_POST['eks_warna_plat'];     
$jeniSpj = $_POST['eks_jenis_spj'];     

// START prepared statement
    $sql = "SELECT DISTINCT 
                EX_PAJAK_HDR_V4.ID,
                EX_PAJAK_HDR_V4.SHIP_TO,
                EX_PAJAK_HDR_V4.ORG,
                EX_PAJAK_HDR_V4.NO_SHP_TRN,
                EX_PAJAK_HDR_V4.TANGGAL_KIRIM,
                EX_PAJAK_HDR_V4.TANGGAL_BONGKAR,
                EX_PAJAK_HDR_V4.TANGGAL_DATANG,
                EX_PAJAK_HDR_V4.NAMA_PRODUK,
                EX_PAJAK_HDR_V4.PLANT,
                EX_PAJAK_HDR_V4.NO_POL,
                EX_PAJAK_HDR_V4.WARNA_PLAT,
                EX_PAJAK_HDR_V4.SAL_DISTRIK,
                EX_PAJAK_HDR_V4.SOLD_TO,
                EX_PAJAK_HDR_V4.NAMA_SOLD_TO,
                EX_PAJAK_HDR_V4.QTY_SHP,
                EX_PAJAK_HDR_V4.QTY_KTG_RUSAK,
                EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK,
                EX_PAJAK_HDR_V4.TARIF_COST,
                EX_PAJAK_HDR_V4.SHP_COST,
                EX_PAJAK_HDR_V4.VENDOR,
                EX_PAJAK_HDR_V4.NAMA_VENDOR,
                EX_PAJAK_HDR_V4.SPT_PAJAK,
                EX_PAJAK_HDR_V4.EVIDENCE_POD1,
                EX_PAJAK_HDR_V4.EVIDENCE_POD2,
                EX_PAJAK_HDR_V4.GEOFENCE_POD,
                EX_PAJAK_HDR_V4.READY_TO_INV,
                EX_PAJAK_HDR_V4.KETERANGAN_POD,
                EX_PAJAK_HDR_V4.STATUS2,
                EX_PAJAK_HDR_V4.STATUS,
                EX_PAJAK_HDR_V4.FLAG_POD,
                EX_PAJAK_HDR_V4.REJECT_STATUS,
                EX_PAJAK_HDR_V4.NOTE,
                EX_TRANS_HDR.KET_ERROR,
                EX_TRANS_HDR.NO_BA
    FROM EX_PAJAK_HDR_V4
    LEFT JOIN EX_TRANS_HDR ON EX_PAJAK_HDR_V4.NO_SHP_TRN = EX_TRANS_HDR.NO_SHP_TRN";

    if ($status_asal == "E-LOG") {
        $sql .= " JOIN EX_INPUTCLAIM_SEMEN ON EX_PAJAK_HDR_V4.NO_SHP_TRN = EX_INPUTCLAIM_SEMEN.NO_SPJ";
    }

    $sql .= " WHERE 1=1";

    $params = array();

    if ($orgIn) {
      $sql .= " AND EX_PAJAK_HDR_V4.ORG = :org_in";
      $params[':org_in'] = $orgIn;
    } else {
        // paksa query tidak hasilkan data jika org kosong
        $sql .= " AND 1=0";
    }

    if (!empty($no_shipment)) {
        $sql .= " AND EX_PAJAK_HDR_V4.NO_SHP_TRN LIKE :no_shipment";
        $params[":no_shipment"] = "%$no_shipment%";
    }

    if (!empty($distributor)) {
        $sql .= " AND (EX_PAJAK_HDR_V4.NAMA_SOLD_TO LIKE :distributor OR EX_PAJAK_HDR_V4.SOLD_TO LIKE :distributor)";
        $params[":distributor"] = "%$distributor%";
    }

    if (!empty($vendor)) {
        $sql .= " AND (EX_PAJAK_HDR_V4.NAMA_VENDOR LIKE :vendor OR EX_PAJAK_HDR_V4.VENDOR LIKE :vendor)";
        $params[":vendor"] = "%$vendor%";
    }

    if (!empty($plant)) {
        $sql .= " AND EX_PAJAK_HDR_V4.PLANT LIKE :plant";
        $params[":plant"] = "%$plant%";
    }

    if (!empty($tipe_transaksi)) {
        $sql .= " AND EX_PAJAK_HDR_V4.TIPE_TRANSAKSI LIKE :tipe_transaksi";
        $params[":tipe_transaksi"] = "%$tipe_transaksi%";
    }

    if (!empty($tanggal_mulai_sql) && !empty($tanggal_selesai_sql)) {
        $sql .= " AND EX_PAJAK_HDR_V4.TANGGAL_KIRIM BETWEEN TO_DATE(:tgl_awal, 'DD-MM-YYYY') AND TO_DATE(:tgl_akhir, 'DD-MM-YYYY')";
        $params[":tgl_awal"] = $tanggal_mulai_sql;
        $params[":tgl_akhir"] = $tanggal_selesai_sql;
    }

    if (!empty($warna_plat)) {
        $sql .= " AND LOWER(EX_PAJAK_HDR_V4.WARNA_PLAT) LIKE LOWER(:warna_plat)";
        $params[":warna_plat"] = "$warna_plat%";
    }

    if (!empty($jeniSpj)) {
        if ($jeniSpj == "klaim") {
            $sql .= " AND (EX_PAJAK_HDR_V4.QTY_KTG_RUSAK > 0 OR EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK > 0)";
        } else if ($jeniSpj == "ds") {
            $sql .= " AND EX_PAJAK_HDR_V4.SOLD_TO LIKE '00000003%'";
        } else {
            $sql .= " AND (EX_PAJAK_HDR_V4.QTY_KTG_RUSAK <= 0 AND EX_PAJAK_HDR_V4.QTY_SEMEN_RUSAK <= 0)";
        }
    }

    $sql .= " AND EX_PAJAK_HDR_V4.DELETE_MARK = '0'";
    $sql .= " AND EX_PAJAK_HDR_V4.VEHICLE_TYPE <> '205'";
    $sql .= " AND EX_PAJAK_HDR_V4.KELOMPOK_TRANSAKSI = 'DARAT'";
    $sql .= " AND EX_PAJAK_HDR_V4.STATUS_PAJAK = 'OK'";
    $sql .= " AND EX_PAJAK_HDR_V4.TIPE_TRANSAKSI = 'BAG'";
    $sql .= " ORDER BY EX_PAJAK_HDR_V4.ORG, VENDOR, EX_PAJAK_HDR_V4.SAL_DISTRIK, EX_PAJAK_HDR_V4.NO_SHP_TRN ASC";

    $query = oci_parse($conn, $sql);
    foreach ($params as $key => $val) {
        oci_bind_by_name($query, $key, $params[$key]);
    }
// END prepared statement


oci_execute($query);

$sqlcek = "SELECT SHIPTO FROM OR_MAP_SHIPTO_3PL WHERE DELETE_MARK = '0'";
$params = array();
if ($orgIn) {
$sqlcek .= " AND ORG = :org_in";
$params[':org_in'] = $orgIn;
} else {
    // paksa query tidak hasilkan data jika org kosong
    $sqlcek .= " AND 1=0";
}
$querycek = oci_parse($conn, $sqlcek);
foreach ($params as $key => $val) {
    oci_bind_by_name($querycek, $key, $params[$key]);
}
oci_execute($querycek);
$shiptoblack = array();
while($datacek=oci_fetch_assoc($querycek)){
    array_push($shiptoblack, $datacek[SHIPTO]);
}

while($row=oci_fetch_array($query)){
    if((in_array($row[SHIP_TO], $shiptoblack)) && ($status_asal=="NON-ELOG")){
        continue;
    } else if((!in_array($row[SHIP_TO], $shiptoblack)) && ($status_asal=="E-LOG")){
        continue;
    }else{
    $com[]=$row[ORG];
    $no_shipment_v[]=$row[NO_SHP_TRN];
    $tgl_kirim_v[]=$row[TANGGAL_KIRIM];
    $tgl_bongkar_v[]=$row[TANGGAL_BONGKAR];
    $tgl_datang_v[]=$row[TANGGAL_DATANG];
    $produk_v[]=$row[NAMA_PRODUK];
    $plant_v[]=$row[PLANT];
    $no_pol_v[]=$row[NO_POL];
    $warna_plat_v[]=$row[WARNA_PLAT];
    $sal_distrik_v[]=$row[SAL_DISTRIK];
    $sold_to_v[]=$row[SOLD_TO];
    $ship_to_v[]=$row[SHIP_TO];
    $nama_sold_to_v[]=$row[NAMA_SOLD_TO];
    $qty_v[]=$row[QTY_SHP];
    $qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
    $qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
    $id_v[]=$row[ID];
    $tarif_cost_v[]=$row[TARIF_COST];
    $shp_cost_v[]=$row[SHP_COST];
    $no_vendor_v=$row[VENDOR];
    $nama_vendor_v=$row[NAMA_VENDOR];
    $spt_cek=$row[SPT_PAJAK];
    $lampiran[]=$row[EVIDENCE_POD1];
    $lampiran2[]=$row[EVIDENCE_POD2];
    $GEOFENCE_POD[]=$row[GEOFENCE_POD];
    $READY_TO_INV[]=$row[READY_TO_INV];
    $KETERANGAN_POD[] = $row[KETERANGAN_POD];
    $alasan_reject = '';

    // status recalculate
    if($row[STATUS2] == 'DRAFT'){
        if($row[KET_ERROR] == ""){
        $recalculated_by[] = 'Belum di Proses';
        }else{
        $recalculated_by[] = 'Gagal';
        $alasan_reject = $row[KET_ERROR];
        }
    }else{
        $recalculated_by[] = 'Sukses';
    }

    // status pod
    if($row[STATUS] == 'DRAFT'){
        $flag_POD[] = 'Belum di Proses';
    }else{
        $flag_POD[]=$row[FLAG_POD];
    }

    // status bastp
    if($row[NO_BA]){
        if($row[REJECT_STATUS] == "1"){
        $bastp_created_by[] = 'Manual - Sukses';
        }else{
        $bastp_created_by[] = 'Otomatis - Sukses';
        }
    }else{
        if($row[REJECT_STATUS] == "1"){
        $bastp_created_by[] = 'Otomatis - Gagal';
        $alasan_reject = $row[NOTE];
        }else{
        $bastp_created_by[] = 'Belum di Proses';
        }
    }

    $KETERANGAN[] = $alasan_reject;
    }
}

$total=count($no_shipment_v);
if ($total > 0) {
    HeaderingExcel('List SPJ periode '.$tanggal_mulai_sql.' hingga '.$tanggal_selesai_sql.'.xls');

    // Creating a workbook
    $workbook = new Workbook("-");
    // Adding format
    $format_bold = &$workbook->add_format();
    $format_bold->set_bold();
    // Creating the first worksheet
    $worksheet1 = &$workbook->add_worksheet('List SPJ');
    //$worksheet1->set_column(1, 1, 40);
    //$worksheet1->set_row(1, 20);

    $worksheet1->write(0, 0, 'No', $format_bold);
    $worksheet1->write(0, 1, 'Org', $format_bold);
    $worksheet1->write(0, 2, 'TGL. SPJ', $format_bold);
    $worksheet1->write(0, 3, 'TGL. DATANG', $format_bold);
    $worksheet1->write(0, 4, 'TGL. BONGKAR', $format_bold);
    $worksheet1->write(0, 5, 'AREA. LT', $format_bold);
    $worksheet1->write(0, 6, 'NO. SPJ', $format_bold);
    $worksheet1->write(0, 7, 'NO. POL', $format_bold);
    $worksheet1->write(0, 8, 'PLAT', $format_bold);
    $worksheet1->write(0, 9, 'PRODUK', $format_bold);
    $worksheet1->write(0, 10, 'DISTRIBUTOR', $format_bold);
    $worksheet1->write(0, 11, 'SHIP TO', $format_bold);
    $worksheet1->write(0, 12, 'K.KTG', $format_bold);
    $worksheet1->write(0, 13, 'K.SMN', $format_bold);
    $worksheet1->write(0, 14, 'KWANTUM', $format_bold);
    $worksheet1->write(0, 15, 'TARIF', $format_bold);
    $worksheet1->write(0, 16, 'JUMLAH', $format_bold);
    $worksheet1->write(0, 17, 'GEOFENCE POD', $format_bold);
    $worksheet1->write(0, 18, 'Recalculate', $format_bold);
    $worksheet1->write(0, 19, 'POD', $format_bold);
    $worksheet1->write(0, 20, 'Create BASTP', $format_bold);
    $worksheet1->write(0, 21, 'KETERANGAN', $format_bold);

    for($i=1; $i<=$total;$i++) {

        $b=$i;
        $worksheet1->write($i, 0, $b);
        $worksheet1->write_string($i, 1, $com[$i - 1]);
        $worksheet1->write_string($i, 2, $tgl_kirim_v[$i - 1]);
        $worksheet1->write_string($i, 3, $tgl_datang_v[$i - 1]);
        $worksheet1->write_string($i, 4, $tgl_bongkar_v[$i - 1]);
        $worksheet1->write_string($i, 5, $sal_distrik_v[$i - 1]);
        $worksheet1->write_string($i, 6, $no_shipment_v[$i - 1]);
        $worksheet1->write_string($i, 7, $no_pol_v[$i - 1]);
        $worksheet1->write_string($i, 8, $warna_plat_v[$i - 1]);
        $worksheet1->write_string($i, 9, $produk_v[$i - 1]);
        $worksheet1->write_string($i, 10, $sold_to_v[$i-1]." / ".$nama_sold_to_v[$i-1]);
        $worksheet1->write_string($i, 11, $ship_to_v[$i - 1]);
        $worksheet1->write($i, 12, number_format($qty_kantong_rusak_v[$i - 1],0,",","."));
        $worksheet1->write($i, 13, number_format($qty_semen_rusak_v[$i - 1],0,",","."));
        $worksheet1->write($i, 14, number_format($qty_v[$i - 1] ,0,",","."));
        $worksheet1->write($i, 15, number_format($tarif_cost_v[$i - 1] ,0,",","."));
        $worksheet1->write($i, 16, number_format($shp_cost_v[$i - 1] ,0,",","."));
        $worksheet1->write_string($i, 17, $GEOFENCE_POD[$i - 1]);
        $worksheet1->write_string($i, 18, $recalculated_by[$i - 1]);
        $worksheet1->write_string($i, 19, $flag_POD[$i - 1]);
        $worksheet1->write_string($i, 20, $bastp_created_by[$i - 1]);
        $worksheet1->write_string($i, 21, $KETERANGAN[$i - 1]);
    }

    $workbook->close();
}