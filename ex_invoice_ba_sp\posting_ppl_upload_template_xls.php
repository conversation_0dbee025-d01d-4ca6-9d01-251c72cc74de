<?php

session_start();
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

if (isset($_GET['no_inv'])) {
    $no_inv = $_GET['no_inv'];
} else {
    $no_inv = "";
}

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
    header("Pragma: public");
}

HeaderingExcel('template_upload_posting_ppl.xls');

// Creating a workbook
$workbook = new Workbook("-");
// Adding format
$format_bold = &$workbook->add_format();
$format_bold->set_bold();
$worksheet1 = &$workbook->add_worksheet('Data Upload');
$worksheet2 = &$workbook->add_worksheet('Contoh Data');

// Header Sheet 1: Upload Data
$worksheet1->write_string(0, 0, 'NO_INV', $format_bold);
$worksheet1->write_string(0, 1, 'E_BELNR', $format_bold);

// Data Sheet 1
$worksheet1->write_string(1, 0, $no_inv);

// Header Sheet 2: Contoh Data
$worksheet2->write_string(0, 0, 'NO_INV', $format_bold);
$worksheet2->write_string(0, 1, 'E_BELNR', $format_bold);

// Data Sheet 2
$worksheet2->write_string(1, 0, "0000000204");
$worksheet2->write_string(1, 1, "5100000069");


$workbook->close();