<?php
    session_start();
    $user = $_SESSION['user_name'];
    include ('../include/or_fungsi.php');
    require_once ('../MainPHPExcel/MainPHPExcel.php'); 
    $fungsi=new or_fungsi();
    $conn=$fungsi->or_koneksi();

    ///hello
    if(isset($_GET['act']) && $_GET['act'] == 'show'){
        if ($filter_status) {
            if ($filter_status != '' && $filter_status != '5') {
                $statusCondition = "AND (
                    (STATUS = '$filter_status' AND APP1_ID IN ('$user'))
                    OR (STATUS = '$filter_status' AND APP2_ID IN ('$user'))
                )";
            } elseif ($filter_status != '' && $filter_status == '5') {
                $statusCondition = "AND (APP1_ID IN ('$user') OR APP2_ID IN ('$user'))";
            } else {
                $statusCondition = "AND (
                    (STATUS = '1' AND APP1_ID IN ('$user'))
                    OR (STATUS = '2' AND APP2_ID IN ('$user'))
                )";
            }
        
            // Tambahkan filter untuk tanggal jika start_date dan end_date diset
            if ($start_date && $end_date) {
                $dateCondition = "AND (FIRST_DATE_BEFORE BETWEEN TO_DATE('$start_date', 'YYYY-MM-DD') AND TO_DATE('$end_date', 'YYYY-MM-DD'))";
            } else {
                $dateCondition = "";
            }
        
            $sql = "SELECT * FROM SUBM_SO_RESCHEDULE WHERE DELETE_MARK = '0' $statusCondition $dateCondition ORDER BY ID_SUB DESC";
        } else {
            $tglAwal = date('Y-m-d', strtotime($start_date));
            $tglAkhir = date('Y-m-d', strtotime($end_date));
            // Tambahkan filter untuk tanggal jika start_date dan end_date diset
            if ($start_date && $end_date) {
                $dateCondition = "AND (FIRST_DATE_BEFORE BETWEEN TO_DATE('$tglAwal', 'YYYY-MM-DD') AND TO_DATE('$tglAkhir', 'YYYY-MM-DD'))";
            } else {
                $dateCondition = "";
            }
        
            $sql = "SELECT * FROM SUBM_SO_RESCHEDULE WHERE (APP1_ID = '$user' AND STATUS = '1' $dateCondition) OR (APP2_ID = '$user' AND STATUS = '2' $dateCondition)";
        }
      //  echo $sql;

        $query_get = oci_parse($conn, $sql);
        oci_execute($query_get);
        $result = array();
        while($row=oci_fetch_array($query_get)){
            array_push($result, $row);
        }
        echo json_encode($result);
        return;
    }

    function sendMail($data){
        $fungsi=new or_fungsi();
        $conn=$fungsi->or_koneksi();
        if ($data['status'] == '1') {
            $nmStatus = 'Waiting Approve 2';
            $mail = $data['email2'];
            $mail_cc = null;
            $mail2 = $data['emaildist'];
            $sbj = "Reschedule, Reopen, Reject SO Approval";
            $msgs = "<thead>
                <tr>
                    <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
                        <h2><b>Reschedule SO</b></h2>
                        <br/>
                        <p>Mohon untuk ditindaklanjuti pengajuan pengajuan reschedule SO dengan detail di bawah ini :</p>
                    </td>
                </tr>
            </thead>";

            $sbj2 = "your request has been approved";
            $msgs2 = "<thead>
                <tr>
                    <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
                        <h2><b>Reschedule SO</b></h2>
                        <br/>
                        <p>Permintaan reschedule SO dengan no SO <b>".$data['no_so']."</b> Telah di setujui :</p>
                    </td>
                </tr>
            </thead>";
            // $part="http://10.15.5.150/dev/sd/sdonline/or_transaksi/vApproval_reschedule_so.php";
        }else{
            $nmStatus = 'Approved';
            $kota = $data['kd_prop'];
            if ($data['submitter'] == 'DISTRIBUTION') {
                // Buat query SQL untuk mengambil email cc dari database
                $sql = "SELECT MAIL_TO, MAIL_CC FROM MAPPING_CC_APPROVAL_SO WHERE SUBMITTER = 'DISTRIBUTION' AND KD_KOTA = '$kota'";
                $query_get = oci_parse($conn, $sql);
                oci_execute($query_get);
                $result = oci_fetch_array($query_get);
            }else if ($data['submitter'] == 'SCM') {
                // Buat query SQL untuk mengambil email cc dari database
                $sql = "SELECT MAIL_TO, MAIL_CC FROM MAPPING_CC_APPROVAL_SO WHERE SUBMITTER = 'SCM' AND KD_KOTA = '$kota'";
                $query_get = oci_parse($conn, $sql);
                oci_execute($query_get);
                $result = oci_fetch_array($query_get);
            }else if ($data['submitter'] == 'SALESAREA') {
                // Buat query SQL untuk mengambil email cc dari database
                $sql = "SELECT MAIL_TO, MAIL_CC FROM MAPPING_CC_APPROVAL_SO WHERE SUBMITTER = 'SALESAREA' AND KD_KOTA = '$kota'";
                $query_get = oci_parse($conn, $sql);
                oci_execute($query_get);
                $result = oci_fetch_array($query_get);
            }

            $mail_to = array();
            array_push($mail_to, $result['MAIL_TO'], $data['emaildist']);
            $mail = implode(", ", $mail_to);
            $mail_cc = $result['MAIL_CC'];
            $sbj = "your request has been approved";
            $msgs ="<thead>
                <tr>
                    <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
                        <h2><b>Reschedule SO</b></h2>
                        <br/>
                        <p>Permintaan reschedule SO dengan no SO <b>".$data['no_so']."</b> Telah di setujui :</p>
                        <br/>
                    </td>
                </tr>
            </thead>";
            // $part="http://10.15.5.150/dev/sd/sdonline/or_transaksi/vReqAppMHoliday.php";
        }
       //dev
        //$part="https://csms.semenindonesia.com/sparebag/ListApproval.php";//prod
        
        $tgl       = date('d-m-Y');
            
        // $to        = $data[0]['EMAIL_KABIRO'];  
        // if (!$to) {
        //     return false;
        // } 
        // var_dump($mail);
        // die;
        $to       = $mail;
        $cc       = $mail_cc;
        $subject   = $sbj;
        
        $message1  = "<html><head></head>";
        
        $message1 .= $msgs;



        $message1 .= "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
        $message1 .= "
            <div align=\"center\">
            <table width=\"95%\" align=\"center\" class=\"adminlist\" id=\"myScrollTable\">
            <thead>
              <tr class=\"quote\">
                <td ><strong>&nbsp;&nbsp;No.</strong></td>
                <td align=\"center\"><strong >Soldto</strong></td>
                <td align=\"center\"><strong >shipto</strong></td>
                <td align=\"center\"><strong >No PP</strong></td>
                <td align=\"center\"><strong >No SO</strong></td>
                <td align=\"center\"><strong >Request Date</strong></td>
                <td align=\"center\"><strong >Status</strong></td>
                 <td align=\"center\"><strong>Request By</strong></td>
              </tr>
              </thead>
              <tbody>
                     ";
    
                $b=1;
        
                $message1.= " 
                <td align=\"center\">".$b."</td>
                <td align=\"center\">".$data['soldto']."</td>       
                <td align=\"center\">".$data['shipto']."</td>
                <td align=\"center\">".$data['no_pp']."</td>
                <td align=\"center\">".$data['no_so']."</td>
                <td align=\"center\">".$data['req_date']."</td>
                <td align=\"center\"><button style=\"background-color:#4CAF50\">".$nmStatus."</button></td>
                <td align=\"center\">".$data['req_by']."</td>
                </tr>";
        
        //         }
        // }
        
        $message1 .= "";
        $message1 .= "</html>";

        // Email kedua
        $to2 = $mail2;
        $subject2 = $sbj2;
        $message2 = "<html><head></head>";
        $message2 .= $msgs2;

        $message2 .= "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
        $message2 .= "
            <div align=\"center\">
            <table width=\"95%\" align=\"center\" class=\"adminlist\" id=\"myScrollTable\">
            <thead>
            <tr class=\"quote\">
                <td ><strong>&nbsp;&nbsp;No.</strong></td>
                <td align=\"center\"><strong >Soldto</strong></td>
                <td align=\"center\"><strong >shipto</strong></td>
                <td align=\"center\"><strong >No PP</strong></td>
                <td align=\"center\"><strong >No SO</strong></td>
                <td align=\"center\"><strong >Request Date</strong></td>
                <td align=\"center\"><strong >Status</strong></td>
                <td align=\"center\"><strong>Request By</strong></td>
            </tr>
            </thead>
            <tbody>
                    ";
    
                $b=1;
        
                $message2.= " 
                <td align=\"center\">".$b."</td>
                <td align=\"center\">".$data['soldto']."</td>       
                <td align=\"center\">".$data['shipto']."</td>
                <td align=\"center\">".$data['no_pp']."</td>
                <td align=\"center\">".$data['no_so']."</td>
                <td align=\"center\">".$data['req_date']."</td>
                <td align=\"center\"><button style=\"background-color:#4CAF50\">".$nmStatus."</button></td>
                <td align=\"center\">".$data['req_by']."</td>
                </tr>";
        
        //         }
        // }
        
        $message2 .= "";
        $message2 .= "</html>";
        
        $headers  = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <EMAIL>' . "\r\n";
        $headers .= 'Cc: ' . $cc . "\r\n";

        // if ($data['status'] != '2') {
        //     $fungsi=new or_fungsi();
        //     $conn=$fungsi->or_koneksi();
        //     // get cc email berdasarkan distrik
        //     $getData = "SELECT DISTINCT BZIRK FROM RFC_Z_ZCSD_SHIPTO WHERE KUNN2 = ".$data['shipto']." AND ROWNUM = 1";
        //                     $cek = oci_parse($conn, $getData);
        //                     oci_execute($cek);
        //                     $hasilCek = oci_fetch_array($cek);
        //     $getKT = $hasilCek[0];
        //     if ($getKT != '') {
               	
	
        //     $getData2 = "SELECT CC_EMAIL FROM MAINTAIN_CC_EMAIL WHERE KD_KOTA = '$getKT'";
        //     $cek2 = oci_parse($conn, $getData2);
        //     oci_execute($cek2);
        //     $hasilCek2 = oci_fetch_array($cek2);
        //     $getCC = $hasilCek2[0];
    
        //     }

        // $headers .= 'Cc:' .$getCC. "\r\n";


        // }
       
            


        // $headers .= 'Cc: <EMAIL>' . "\r\n";
        // echo $message1;
        $send1=mail($to,$subject,$message1,$headers);
        $send2=mail($to2,$subject2,$message2,$headers);
        
            if (($send1 )){
                // echo "Sending Mail Success";
            } else {
                // echo "Sending Mail Failed";
            }
         
         $response['jml_sukses'] = $no;
         $response['jml_fail']   = $no1;  
         return $response;
    }

    if(isset($_GET['act']) && $_GET['act'] == 'approve'){
        // Ambil data dari permintaan JavaScript
        $datas = $_POST['data'];
        // Hitung data untuk validasi
        $totalData = count($datas);
        if($totalData <= 5){
            foreach($datas as $data){
                if ($data['STATUS'] == 1){
                    $user = $_SESSION['user_name'];
                    // Update basis data
                    $sql = "UPDATE SUBM_SO_RESCHEDULE SET STATUS = '2', APP1_DATE = SYSDATE, LAST_UPDATED_BY = '$user', LAST_UPDATED_DATE = SYSDATE  WHERE ID_SUB = '".$data['ID_SUB']."'";
    
                    $query_update = oci_parse($conn, $sql);
                    if(oci_execute($query_update)){
                        $keterangan = array('success' => 'Data has been approved successfully.');
                    }else{
                        $keterangan = array('errorMsg' => 'Failed to approve the data.');
                    }

                    // Mailer
                    $app2 = $data['APP2_ID'];
                    $dist = $data['INSERT_BY'];
                    
                    // get email approval 2
                    $getMail2 = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE NAMA = '{$app2}'";
                    $cek = oci_parse($conn, $getMail2);
                    oci_execute($cek);
                    $email2App = oci_fetch_array($cek);

                    //get email distributor
                    $getMailDist = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE NAMA = '{$dist}'";
                    $cekDist = oci_parse($conn, $getMailDist);
                    oci_execute($cekDist);
                    $emailDistr = oci_fetch_array($cekDist);

                    $dataLempar = array(
                        'email2' => $email2App[0],
                        'emaildist' =>  $emailDistr[0],
                        'soldto' => $data['ID_SOLD_TO'],
                        'shipto' => $data['ID_SHIPTO'],
                        'no_pp'=> $data['NO_PP'],//$shiptoname, 
                        'req_date'=> $data['TGL_SO_SUBMISSION'],
                        'req_by' => $_SESSION['user_name'],
                        'status' => "1",
                        'no_so' => $data['NO_SO'],
                        'insert_by' => $data['NO_SO'],
                    );
                }else{
                    $user = $_SESSION['user_name'];

                    // Update to SAP
                    $tanggalRequest = date('Ymd', strtotime($data['RDD']));
                    $tanggalKirim = date('Ymd', strtotime($data['FIRST_DATE']));

                    // get plant
                    $getPlant = "SELECT PLANT_ASAL,ORG FROM OR_TRANS_HDR WHERE NO_PP = '{$data['NO_PP']}'";
                    $cekPlant = oci_parse($conn, $getPlant);
                    oci_execute($cekPlant);
                    $plantnya = oci_fetch_array($cekPlant,OCI_ASSOC);
                    $xplant = $plantnya["PLANT_ASAL"]; //plant MD
                    $xorg = $plantnya["ORG"];

                    // Get no SO
                    $nso = $data['NO_SO'];
                    $no_so =  sprintf("%010s",$nso);

                    if ($xorg == '7000' || $xorg == 7000) {
                        $sap = new SAPConnection();
                        $sap->Connect("../include/sapclasses/logon_data.conf");
                        if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open();
                        $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                        /////////////////////
                        if($data['REQ_STATUS_SO'] == "null"){
                            $reqStatusSo = "";
                            $delivblock = "";
                        }else{
                            $reqStatusSo = $data['REQ_STATUS_SO'];
                            $delivblock = "Z1";
                            //change
                            $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                            $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';
                            $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                            $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X'; 
                        }
                        /////////////////////
                        $fce->SALESDOCUMENT = $no_so;//"ZOR"; //pastikan 10 angka
                        $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                        // $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                        //memastikan UPDATEFLAG dengan param U, untuk kemudian di sinkron ulang ke semua server CSMS
                        $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                        $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                        // $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';
                    
                        $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                        // $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                        $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);
                        
                        $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                        $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                        // $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                        $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
                        
                        $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                        $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                        // $fce->SCHEDULE_LINES->row["REQ_QTY"] = $qty_approve;x`
                        $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                        $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                        $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
                    
                        $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                        $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                        $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                        // $fce->SCHEDULE_LINESX->row["REQ_QTY"] = 'X';
                        $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                        $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                        $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
                        
                        $fce->Call();
                    
                        if ($fce->GetStatus() == SAPRFC_OK) {
                            $fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                            $fce->Call();
                        }else{
                            $fce->PrintStatus();
                        }
                    
                        $fce->Close();
                        $sap->Close();                    
                    }else{
                        $strkdven = "SELECT * FROM ZMD_MAPPING_PLANT WHERE PLANT_MD = '{$xplant}' AND DEL=0";
                        $query = @oci_parse($conn, $strkdven);
                        @oci_execute($query);
                        $rowkdven = oci_fetch_array($query, OCI_ASSOC);
                        $plant_opco = $rowkdven["PLANT_OPCO"];
                        $org_opco   = $rowkdven["COM_OPCO"];
                        $plant_opco2 = $rowkdven["PLANT_OPCO_2"];
                        $org_opco2   = $rowkdven["COM_OPCO_2"];
                        $sap = new SAPConnection();
                        $sap->Connect("../include/sapclasses/logon_data.conf");
                        if ($sap->GetStatus() == SAPRFC_OK)
                            $sap->Open();
                        if ($sap->GetStatus() != SAPRFC_OK) {
                            echo $sap->PrintStatus();
                            exit;
                        }
        
                        if ($xplant != '' && $plant_opco == '' ) {  // 
                            # code...
                            // update data tanggal SO atau reschedule SO
                            // ==================================================== layer 1
                                $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                                ///////////////
                                if($data['REQ_STATUS_SO'] == "null"){
                                    $reqStatusSo = "";
                                    $delivblock = "";
                                }else{
                                    $reqStatusSo = $data['REQ_STATUS_SO'];
                                    $delivblock = "Z1";
                                    //x
                                    $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                                    $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';
                                    $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                    $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';                                    
                                }
                                ///////////////
                                $fce->SALESDOCUMENT = $no_so;//"ZOR"; //pastikan 10 angka
                                $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                                // $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
    
                                $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                                $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                                // $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';

                                $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                                // $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);

                                $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                                $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                                // $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                                $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
    
                                $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                                $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                                $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                                $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                                $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
    
    
                                $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                                $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                                $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                                $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                                $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                                $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
                                
                                $fce->Call();
    
                                if ($fce->GetStatus() == SAPRFC_OK) {
                                    $fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                                    $fce->Call();
                                }else{
                                    $fce->PrintStatus();
                                }
    
                                $fce->Close();
                                $sap->Close();
                        }elseif ($plant_opco != '' && $plant_opco2 == '' && ($org_opco != 'PTSC' && $org_opco != '1000' && $org_opco != 'ID50')) { // layer 2
                            if($data['REQ_STATUS_SO'] == "null"){
                                $reqStatusSo = "";
                                $delivblock = "";
                            }else{
                                $reqStatusSo = $data['REQ_STATUS_SO'];
                                $delivblock = "Z1";
                            }
                                
                            $fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL2");
                            if ($fce == false) {
                                $sap->PrintStatus();
                                exit;
                            }
                            // update data sesuao layer 2 atau 3 
                            $fce->XVKORG = $xorg;//"7900";
                            $fce->XVBELN =sprintf("%010s", $no_so);//"0011012203";
                            // $fce->XFLAG = "O";
                            
                            //$now = date ('Ymd');
                            //$tgl_fr = $now->modify('-30 day');
                            $tgl_to = date('Ymd');
                            $tgl_fr = date('Ymd', strtotime("-30 days"));
                            
                            $fce->LR_EDATU->row['SIGN'] = 'I';
                            $fce->LR_EDATU->row['OPTION'] = 'BT';
                            $fce->LR_EDATU->row['LOW'] = $tgl_fr;
                            $fce->LR_EDATU->row['HIGH'] = $tgl_to;
                            $fce->LR_EDATU->Append($fce->LR_EDATU->row);
                            
                            $fce->Call();
                            
                            // echo "<pre>";
                            // var_dump($fce->RETURN_DATA->row);exit;
                            //        var_dump($fce,$tgl_fr,$tgl_to,$xorg,sprintf("%010s", $no_so)); 
                            // echo "</pre>";
                            // die();
                            
                            if ($fce->GetStatus() == SAPRFC_OK) {
                                $fce->RETURN_DATA->Reset();
                                $s = 0;
                                while ($fce->RETURN_DATA->Next()) { 
                                    
                                    if($fce->RETURN_DATA->row["SO_REFF"]!=''){
                                        $data[$s] = $fce->RETURN_DATA->row["SO_REFF"];
                                        $data_so2 = sprintf("%010s", $data[$s]);
    
                                    } else {
                                        $data[$s] = '';
                                    }
                                    $s++;
                                }
    
                                    // =============================================== layer 1
                                    $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE"); 
                                    $fce->SALESDOCUMENT = $no_so;//"ZOR"; //pastikan 10 angka
                                    $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                                    $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
    
                                    $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                                    $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                                    $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';

                                    $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                                    $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                    $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);

                                    $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                                    $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                                    $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                                    $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
    
                                    $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                                    $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                                    $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                                    $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                                    $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
    
    
                                    $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                                    $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                                    $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                                    $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                                    $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                                    $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
    
                                    $fce->Call();
    
    
    
                                    if ($data != '') {
                                    // ===================================================== update data tanggal SO atau reschedule SO layer 2
                                        foreach ($data as $key ) {
                                        # code...
                                            $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                                            $fce->SALESDOCUMENT = sprintf("%010s", $key);//"ZOR"; //pastikan 10 angka
                                            $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                                            $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
    
                                            $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                                            $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                                            $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';

                                            $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                                            $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                            $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);

                                            $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                                            $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                                            $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                                            $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
    
                                            $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                                            $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                                            $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                                            $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                                            $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
    
    
                                            $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                                            $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                                            $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                                            $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                                            $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                                            $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
    
    
                                            $fce->Call();
                                        }
                                    }
    
                                    if ($fce->GetStatus() == SAPRFC_OK) {
                                        $fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                                        $fce->Call();
                                    }else{
                                        $fce->PrintStatus();
                                    }
                                
    
                                $fce->Close();
                                $sap->Close();
    
                            } else {
                                $show_ket = "Update Data SO Layer 2 gagal <br>";
                                $keterangan = array('errorMsg'=>$show_ket);
                            }
                            // $plant_opco2 != '' && ($org_opco2 != 'PTSC' || $org_opco2 != '1000' || $org_opco2 != 'ID50')
                        }elseif ($plant_opco2 != '' && ($org_opco2 != 'PTSC' && $org_opco2 != '1000' && $org_opco2 != 'ID50')) { // layer3
                            // update data sesuao layer 2 dan 3 
                            if($data['REQ_STATUS_SO'] == "null"){
                                $reqStatusSo = "";
                                $delivblock = "";
                            }else{
                                $reqStatusSo = $data['REQ_STATUS_SO'];
                                $delivblock = "Z1";
                            }
                            
                            $fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL2");
                            if ($fce == false) {
                                $sap->PrintStatus();
                                exit;
                            }
                            $fce->XVKORG = $xorg;//"7900";
                            $fce->XVBELN = $no_so;//"0011012203";
                            // $fce->XFLAG = "O";
                                    
                            //$now = date ('Ymd');
                            //$tgl_fr = $now->modify('-30 day');
                            $tgl_to = date('Ymd');
                            $tgl_fr = date('Ymd', strtotime("-30 days"));
                            
                            $fce->LR_EDATU->row['SIGN'] = 'I';
                            $fce->LR_EDATU->row['OPTION'] = 'BT';
                            $fce->LR_EDATU->row['LOW'] = $tgl_fr;
                            $fce->LR_EDATU->row['HIGH'] = $tgl_to;
                            $fce->LR_EDATU->Append($fce->LR_EDATU->row);		
                            $fce->Call();
                            
                            
                            
                            if ($fce->GetStatus() == SAPRFC_OK) {
                                $fce->RETURN_DATA->Reset();
                                $s = 0;
                                while ($fce->RETURN_DATA->Next()) {                
                                    if($fce->RETURN_DATA->row["IHREZ_E"]!=''){
                                        
                                        $data3 = $fce->RETURN_DATA->row["IHREZ_E"];
                                        $data2= $fce->RETURN_DATA->row["SO_REFF"];
                                        // $data[$s] = $fce->RETURN_DATA->row;
    
                                    } else {
                                        // $data[$s] = '';
                                        $data3= '';
                                        $data2 = '';
                                    }
                                    $s++;
                                }
                                    // ==================================================== layer 1
                                    
                                        $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE"); 
                                        $fce->SALESDOCUMENT = $no_so;//"ZOR"; //pastikan 10 angka
                                        $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                                        $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
    
                                        $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                                        $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                                        $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';

                                        $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                                        $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                        $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);

                                        $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                                        $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                                        $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                                        $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
    
                                        $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                                        $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                                        $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                                        $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                                        $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
        
        
                                        $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                                        $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                                        $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                                        $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                                        $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                                        $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
                                        $fce->Call();
                                        if (($data3 != '')&&($data2 != '')) {
                                     
                                            
                                            // foreach ($data2 as $key) {
                                                // ===================================================== update data tanggal SO atau reschedule SO layer 2                             
                                                if($plant_opco != '' && $plant_opco2 == '' && ($org_opco != 'PTSC' && $org_opco != '1000' && $org_opco != 'ID50')){
                                                    $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                                                    // $fce->SALESDOCUMENT = sprintf("%010s", $key[$hit]['SO_REFF']);//"ZOR"; //pastikan 10 angka
                                                    $fce->SALESDOCUMENT = sprintf("%010s", $data2);
                                                    $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                                                    $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                
                                                    $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                                                    $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                                                    $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';

                                                    $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                                                    $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                                    $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);

                                                    $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                                                    $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                                                    $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                                                    $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
        
                                                    $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                                                    $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                                                    $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                                                    $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                                                    $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
                    
                    
                                                    $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                                                    $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                                                    $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                                                    $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                                                    $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                                                    $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
        
                                                    $fce->Call();
                                                }
    
                                                // =================================================== update data tanggal SO atau reschedule SO layer 3
                                                if($plant_opco2 != '' && ($org_opco2 != 'PTSC' && $org_opco2 != '1000' && $org_opco2 != 'ID50')){
                                                    $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                                                    // $fce->SALESDOCUMENT = sprintf("%010s", $key[$hit]['IHREZ_E']);//"ZOR"; //pastikan 10 angka
                                                    $fce->SALESDOCUMENT = sprintf("%010s", $data3);
                                                    $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                                                    $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                
                                                    $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                                                    $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                                                    $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';

                                                    $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                                                    $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                                                    $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);

                                                    $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                                                    $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                                                    $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                                                    $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
        
                                                    $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                                                    $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                                                    $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                                                    $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                                                    $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
                    
                    
                                                    $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                                                    $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                                                    $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                                                    $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                                                    $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                                                    $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
                                                    
                                                        $fce->Call();
                                                }
                                            // }
                                        }
    
    
                                        if ($fce->GetStatus() == SAPRFC_OK) {
                                            $fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                                            $fce->Call();
                                            
                                        }else{
                                            $fce->PrintStatus();
                                        }
    
        
                                    
                                    $fce->Close();
                                    $sap->Close();
                                // }
                            } else {
                                $show_ket = "Update Data SO Layer 3 gagal <br>";
                                $keterangan = array('errorMsg'=>$show_ket);
                            }
                        }elseif ($org_opco == 'PTSC' || $org_opco == '1000' || $org_opco == 'ID50' || $org_opco2 == 'PTSC' || $org_opco2 == '1000' || $org_opco2 == 'ID50') { // update layer pertama saja karna layer lain ada non SI
                            // $fce = $sap->NewFunction("BAPI_SALESORDER_CHANGE");
                            // if($data['REQ_STATUS_SO'] == "null"){
                            //     $reqStatusSo = "";
                            //     $delivblock = "";
                            // }else{
                            //     $reqStatusSo = $data['REQ_STATUS_SO'];
                            //     $delivblock = "Z1";
                            //     //x
                            //     $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                            //     $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';
                            //     $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                            //     $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';                                 
                            // }
                            // $fce->SALESDOCUMENT = $no_so;//"ZOR"; //pastikan 10 angka
                            // $fce->ORDER_HEADER_IN["REQ_DATE_H"] = $tanggalRequest;
                            // // $fce->ORDER_HEADER_IN["DLV_BLOCK"] = $delivblock;
                          
                            // $fce->ORDER_HEADER_INX["UPDATEFLAG"] = 'U';//"Z1";
                            // $fce->ORDER_HEADER_INX["REQ_DATE_H"] = 'X';
                            // // $fce->ORDER_HEADER_INX["DLV_BLOCK"] = 'X';
                          
                            // $fce->ORDER_ITEM_IN->row["ITM_NUMBER"] = '000010';
                            // // $fce->ORDER_ITEM_IN->row["REASON_REJ"] = $reqStatusSo;
                            // $fce->ORDER_ITEM_IN->Append($fce->ORDER_ITEM_IN->row);
                            
                            // $fce->ORDER_ITEM_INX->row["ITM_NUMBER"] = '000010';
                            // $fce->ORDER_ITEM_INX->row["UPDATEFLAG"] = 'U';
                            // // $fce->ORDER_ITEM_INX->row["REASON_REJ"] = 'X';
                            // $fce->ORDER_ITEM_INX->Append($fce->ORDER_ITEM_INX->row);
                            
                            // $fce->SCHEDULE_LINES->row["ITM_NUMBER"] = '000010'; 
                            // $fce->SCHEDULE_LINES->row["SCHED_LINE"] = '0001';
                            // $fce->SCHEDULE_LINES->row["REQ_DATE"] = $tanggalKirim;
                            // $fce->SCHEDULE_LINES->row["DLV_DATE"] = $tanggalKirim;
                            // $fce->SCHEDULE_LINES->Append($fce->SCHEDULE_LINES->row);
                          
                            // $fce->SCHEDULE_LINESX->row["ITM_NUMBER"] = '000010';
                            // $fce->SCHEDULE_LINESX->row["SCHED_LINE"] = '0001';
                            // $fce->SCHEDULE_LINESX->row["UPDATEFLAG"] = 'U';
                            // // $fce->SCHEDULE_LINESX->row["REQ_QTY"] = 'X';
                            // $fce->SCHEDULE_LINESX->row["REQ_DATE"] = 'X';
                            // $fce->SCHEDULE_LINESX->row["DLV_DATE"] = 'X';
                            // $fce->SCHEDULE_LINESX->Append($fce->SCHEDULE_LINESX->row);
                            
                            // $fce->Call();
                          
                            // if ($fce->GetStatus() == SAPRFC_OK) {
                            //     $fce = $sap->NewFunction ("BAPI_TRANSACTION_COMMIT");
                            //     $fce->Call();
                                
                            // }else{
                            //     $fce->PrintStatus();
                            // }


                        
                            // $fce->Close();
                            // $sap->Close();
                        }else{
                            $show_ket = "Layer Plant tidak di temukan <br>";
                            $keterangan = array('errorMsg'=>$show_ket);
                        }

                        //ini untuk update so SBI
                        if($org_opco == 'PTSC' || $org_opco == PTSC ){
                            //$data_so_ke2 = '3512124551';
                          $sap = new SAPConnection();
                           $sap->Connect("../include/sapclasses/logon_data.conf");
                            if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open();
                              echo 'Indonesia';
                            if($data['REQ_STATUS_SO'] == "null"){
                                $reqStatusSo = "";
                                $delivblock = "";
                            }else{
                                $reqStatusSo = $data['REQ_STATUS_SO'];
                                $delivblock = "Z1";
                            }
                                
                            $fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL2");
                            // Belum call sudah di exit
                            if ($fce == false) {
                                $sap->PrintStatus();
                                  echo 'Indonesia2';
                                exit;
                            }
                            // update data sesuao layer 2 atau 3 
                            $fce->XVKORG = $xorg;//"7900";
                            $fce->XVBELN =sprintf("%010s", $no_so);//"0011012203";
                            // $fce->XFLAG = "O";
                            
                            //$now = date ('Ymd');
                            //$tgl_fr = $now->modify('-30 day');
                            $tgl_to = date('Ymd');
                            $tgl_fr = date('Ymd', strtotime("-30 days"));
                            
                            $fce->LR_EDATU->row['SIGN'] = 'I';
                            $fce->LR_EDATU->row['OPTION'] = 'BT';
                            $fce->LR_EDATU->row['LOW'] = $tgl_fr;
                            $fce->LR_EDATU->row['HIGH'] = $tgl_to;
                            $fce->LR_EDATU->Append($fce->LR_EDATU->row);
                            
                            $fce->Call();
                            
                            // echo "<pre>";
                            // echo "bismilllah";
                            // var_dump($fce->RETURN_DATA->row);
                            //        var_dump($fce,$tgl_fr,$tgl_to,$xorg,sprintf("%010s", $no_so)); 
                            // echo "</pre>";
                    
                            if ($fce->GetStatus() == SAPRFC_OK) {
                                $fce->RETURN_DATA->Reset();
                                $s = 0;
                                while ($fce->RETURN_DATA->Next()) { 
                                    
                                    if($fce->RETURN_DATA->row["SO_REFF"]!=''){
                                        print_r($fce->RETURN_DATA->row["SO_REFF"]);
                                        
                                        $data[$s] = $fce->RETURN_DATA->row["SO_REFF"];
                                        $data_so2 = sprintf("%010s", $data[$s]);
                                        $data_so_ke2 = $data_so2;
    
                                    } else {
                                        $data[$s] = '';
                                    }
                                    $s++;
                                }
    
                            }
                            // echo "hasil sap sig";
                            // exit();
                            
                            $url = 'https://10.4.194.97/md/api_sbi/Update_SO_sbi.php';//$results['URL'];
                            $ndata = array(
                                "token" =>"9999999999",// $results['TOKEN'],
                                "systemid" => "QASSO",
                                "request_so" => 'reschedule',//atau reject
                                "alasan_reject" => '',//reject
                                "no_so" =>  $data_so_ke2,//3512124551,
                                "tanggal_request" =>$tanggalRequest, //20250428,
                                "tanggal_kirim" =>$tanggalKirim,//20250428,




                            );

                            $ch = curl_init();
                                    // Set cURL options
                            curl_setopt($ch, CURLOPT_URL, $url);
                            curl_setopt($ch, CURLOPT_POST, 1);
                            curl_setopt($ch, CURLOPT_POSTFIELDS, $ndata);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            // Disable SSL verification
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

                            $result = curl_exec($ch);
                            
                            if (curl_errno($ch)) {
                                // Handle cURL error
                                $result = "<br> cURL Error: " . curl_error($ch);
                            } else {
                                // No cURL error, process the result
                                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                                if ($httpCode == 200) {
                                    // Successful request, handle $result data
                                    // echo "<br> Successful result: " . $result;
                                } else {
                                    // Handle HTTP error
                                    $result = "HTTP Error: " . $httpCode;
                                }
                            }

                            $response = json_decode($result, true);
                        }


                        // echo 'ditutup kodenya';
                        // exit();
                    }

                    // Update Database OR_TRANS_DTL dan OR_TRANS_HDR
                    if($data['REQ_STATUS_SO'] == "null"){
                        $reqStatusSo = "";
                        $delivblock = "";
                    }else{
                        $reqStatusSo = $data['REQ_STATUS_SO'];
                        $delivblock = "Z1";
                    }

                    if($data['JENIS_KIRIM'] == "FRC" || $data['JENIS_KIRIM'] == "CIF")
                        $kolom_tgl_rdd = "TGL_LEADTIME";
                    else
                        $kolom_tgl_rdd = "TGL_KIRIM_APPROVE";

                    if($reqStatusSo != ''){
                        $update = "UPDATE OR_TRANS_DTL SET STATUS_LINE = 'REJECTED', $kolom_tgl_rdd = TO_DATE('{$data['FIRST_DATE']}', 'DD-MM-YY HH24:MI:SS'), LAST_UPDATE_DATE = TO_DATE('".date("d-m-Y H:i:s")."', 'DD-MM-YYYY HH24:MI:SS'), LAST_UPDATED_BY = '".$_SESSION['user_name']."' WHERE NO_SO = '{$data['NO_SO']}'";
                        $update2 = "UPDATE OR_TRANS_HDR SET STATUS = 'REJECTED', LAST_UPDATE_DATE = TO_DATE('".date("d-m-Y H:i:s")."', 'DD-MM-YYYY HH24:MI:SS'), LAST_UPDATED_BY = '".$_SESSION['user_name']."' WHERE NO_PP = {$data['NO_PP']}";
                        $query_update = oci_parse($conn, $update);
                        oci_execute($query_update);
                        $query_update = oci_parse($conn, $update2);
                        oci_execute($query_update);
                    } else
                        $update = "UPDATE OR_TRANS_DTL SET $kolom_tgl_rdd = TO_DATE('{$data['FIRST_DATE']}', 'DD-MM-YY HH24:MI:SS'), LAST_UPDATE_DATE = TO_DATE('".date("d-m-Y H:i:s")."', 'DD-MM-YYYY HH24:MI:SS'), LAST_UPDATED_BY = '".$_SESSION['user_name']."' WHERE NO_SO = '{$data['NO_SO']}'";
                        $query_update = oci_parse($conn, $update);
                        oci_execute($query_update);

                    // Update Database SUBM_SO_RESCHEDULE
                    $sql = "UPDATE SUBM_SO_RESCHEDULE SET STATUS = '4', APP2_DATE = SYSDATE, LAST_UPDATED_BY = '$user', LAST_UPDATED_DATE = SYSDATE  WHERE ID_SUB = '".$data['ID_SUB']."'";

                    $query_update = oci_parse($conn, $sql);
                    if(oci_execute($query_update)){
                        $keterangan = array('success' => 'Data has been approved successfully.');
                    }else{
                        $keterangan = array('errorMsg' => 'Failed to approve the data.');
                    }
                }

                // Mailer
                $dist = $data['INSERT_BY'];

                //get email distributor
                $getMailDist = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE NAMA = '{$dist}'";
                $cekDist = oci_parse($conn, $getMailDist);
                oci_execute($cekDist);
                $emailDistr = oci_fetch_array($cekDist);

                $dataLempar = array(
                    'email2' => $email2App[0],
                    'emaildist' =>  $emailDistr[0],
                    'soldto' => $data['ID_SOLD_TO'],
                    'shipto' => $data['ID_SHIPTO'],
                    'no_pp'=> $data['NO_PP'],//$shiptoname, 
                    'req_date'=> $data['TGL_SO_SUBMISSION'],

                    'req_by' => $_SESSION['user_name'],
                    'status' => $data['STATUS'],
                    'no_so' => $data['NO_SO'],
                    'submitter' => $data['SUBMITTER'],
                    'kd_prop' => $data['KD_PROP'],
                );

            }
        }else{
            $keterangan = array('errorMsg' => 'Failed to approve the data, because limits of submission');
        }

        // send mail 
        sendMail($dataLempar);

        echo json_encode($keterangan);
        exit;
    }

    if(isset($_GET['act']) && $_GET['act'] == 'reject'){
        // Ambil data dari permintaan JavaScript
        $datas = $_POST['data'];
        foreach($datas as $data){
            $user = $_SESSION['user_name'];
            // Update basis data
            $sql = "UPDATE SUBM_SO_RESCHEDULE SET STATUS = '3', REJECT_DATE = SYSDATE, REJECT_BY = '$user', LAST_UPDATED_BY = '$user', LAST_UPDATED_DATE = SYSDATE  WHERE ID_SUB = '".$data['ID_SUB']."'";

            $query_update = oci_parse($conn, $sql);
            if(oci_execute($query_update)){
                $keterangan = array('success' => 'Data has been rejected successfully.');
            }else{
                $keterangan = array('errorMsg' => 'Failed to reject the data.');
            }
        }

        echo json_encode($keterangan);
    }
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>List Approval SO</title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<style type="text/css">
      #outtable{
        padding:1px;
        border:1px solid #e3e3e3;
        width:600px;
        border-radius: 5px;
      }
 
      .short{
        width: 50px;
      }
 
      .normal{
        width: 150px;
      }
      .tabel_1{
        border-collapse: collapse;
        font-family: arial;
        color:#5E5B5C;
      }

      .btn {
        display: inline-block;
        border-radius: .3em;
        text-align: center;
        border: .1em solid;
        padding: .3em;
        background: white;
        margin-right: .2em;
        cursor: pointer;
    }

    .btn:not([disabled]) {
        color: white;
    }

    .yellow {
        border: .1em solid #edcf13;
        background: #ebd234;
    }

    .red {
        border: .1em solid #d50505;
        background: red;
    }

    .green {
        border: .1em solid #1cac04;
        background: green;
    }
 
      thead th{
        text-align: left;
        padding: 7px;
      }
 
      tbody td{
        border-top: 1px solid #e3e3e3;
        padding: 7px;
      }
    
    .loader-page{
        width:100%;
        height:100%;
        display:block;
        position: fixed;
        left: 0px;
        top: 0px;
        z-index: 1;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .loader {
        margin: 35vh 45vw;
        border: 16px solid #f3f3f3;
        border-radius: 50%;
        border-top: 16px solid rgb(198, 73, 52);
        /* border-bottom: 16px solid red; */
        width: 120px;
        height: 120px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
    }

    @-webkit-keyframes spin {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>
<div style="visibility:hidden;" class="loader-page">
  <div class="loader"></div>
</div>
<div align="center">   
    <table id="dg" title="<?=$titlepage;?>" class="easyui-datagrid" style="width:auto;height:350px">
        <thead>
            <tr>
                <th field="ck" checkbox="true"></th>
                <th field="NM_SHIPTO" width="150">Distributor</th>
                <th field="ID_SOLD_TO" width="150">Soldto</th>
                <th field="ID_SHIPTO" width="150">Shipto</th>     
                <th field="NO_SO" width="150">No SO</th>
                <th field="NO_PP" width="300">No PP</th>
                <th field="STATUS"  formatter="cek" width="200">Status</th>
                <th field="RDD_BEFORE" style="color: #63beff" width="200">Request Delivery Date Before</th>
                <th field="FIRST_DATE_BEFORE" style="color: #63beff" width="200">Schedule Delivery Date Before</th>
                <th field="RDD" style="color: #63beff" formatter="format_schedule_rdd" width="200" >Request Delivery Date</th>
                <th field="FIRST_DATE" style="color: #63beff" formatter="format_schedule_first_date" width="200" >Schedule Delivery Date</th>
                <th hidden field="REQ_STATUS_SO" style="color: #63beff" width="200">Request Status</th>
                <th hidden field="JENIS_KIRIM" style="color: #63beff" width="200">Jenis Kirim</th>
                <th field="INSERT_DATE" width="200">Created Date Submission</th>
                <th field="INSERT_BY" width="200">Created By</th>
                <th field="TYPE_APPROVAL"  formatter="type_app" width="200">Tipe Approval</th>
                
                <th hidden  field="APP1_ID" width="200">App1</th>
                <th hidden field="APP2_ID" width="200">App2</th>
                <!-- <th field="APPROVAL1" width="200">QTY SO</th>
                <th field="APPROVAL1" width="200">QTY SO terkirim</th>
                <th field="APPROVAL1" width="200">Sisa SO</th> -->
                <!-- <th field="APPROVE_AT" width="200">APPROVE/REJECT AT</th>
                <th field="APPROVE_BY" width="200">APPROVE/REJECT BY</th> -->
            </tr>
        </thead>
    </table>
    <div id="toolbar">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-ok" plain="true" onclick="approveAct()">Approve</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" plain="true" onclick="rejectAct()">Reject</a>

            <select id="filter_status" class="easyui-combobox" iconCls="icon-search" name="filter_status" style="width:150px;">
                    <option value="Waiting Approve">Waiting Approval 1</option>
                    <option value="Approved1">Waiting Approval 2</option>
                    <option value="Approved2">Approved</option>
                    <option value="Rejected">Rejected</option>
                    <option value="ALL">ALL</option>
            </select>

            <input id="start_date" class="easyui-datebox" label="Start Date" labelPosition="left" style="width:150px;">
            <input id="end_date" class="easyui-datebox" label="End Date" labelPosition="left" style="width:150px;">

            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="filterByDate()">Filter by Date</a>
            <a href="javascript:void(0)" id="btnExport" class="easyui-linkbutton" data-options="iconCls:'icon-arkdownload'" style="width:80px">Export</a>
    </div>

    <div id="-load" title="" class="easyui-dialog" closed="true" modal="true" closable="false" style="padding:10px;width:200px;height:auto;">
    <img src='icon/loading.gif' border='0'> Loading, please wait ... 
    </div>
    <script type="text/javascript">
    $(function(){
        $("#dg").datagrid({
                url:'list_approval_so.php?act=show',
                singleSelect:false,
                pagination:true, 
                pageList:[10,50,100,300,500,1000,5000,10000],
                pageSize:20,
                rownumbers:true,
                loadMsg:'Processing,please wait',
                height:'auto', 
                toolbar:'#toolbar'

                
        });
        $('#dg').datagrid('enableFilter');    
    });

        $('#filter_status').combobox({
            onChange: function(item) {
                var value = '';
                if (item == 'Waiting Approve') {
                    value = '1';
                }else if (item == 'Approved1') {
                    value = '2';
                }else if (item == 'Approved2') {
                    value = '4';
                } else if (item == 'Rejected') {
                    value = '3';
                }else{
                    value = '5';
                }
                
                aksiGet(value)

            }
        });

        function filterByDate() {
            var startDate = $('#start_date').datebox('getValue');
            var endDate = $('#end_date').datebox('getValue');

            $('#dg').datagrid({
                url:'list_approval_so.php?act=show&start_date='+startDate+'&end_date='+endDate
            }); 
        }

        function aksiGet(value) {
            $('#dg').datagrid({
                url:'list_approval_so.php?act=show&filter_status='+value
            });
            //  $('#dg').datagrid('enableFilter'); 
        }

        $("#btnExport").click(function() {        
            var myData = $('#dg').datagrid('getData');        
            var mapForm = document.createElement("form");
            mapForm.id = "formexport";
            mapForm.target = "dialogSave";
            mapForm.method = "POST";
            mapForm.action = "exportMapping.php?act=export";        
            $.each(myData.rows, function(k,v){
                $.each(v, function(k2, v2){
                    var hiddenField = document.createElement("input");              
                    hiddenField.type = "hidden";
                    hiddenField.name = "data[" + k + "][" + k2 + "]";
                    hiddenField.value = v2;
                    mapForm.appendChild(hiddenField);
                });
            });            
            document.body.appendChild(mapForm);
            mapForm.submit();
            document.body.removeChild(mapForm);
            
        });

    function cek(val,row){
        if(val=='1'){
            // return "<span style='color:yellow;'>Waiting Approve</span>";
            return `<span><button style="width:120px" class="btn yellow">Waiting Approval 1</button></span>`;
                                
        }else if (val=='2') {
            return `<span><button style="width:120px" class="btn yellow">Waiting Approval 2</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        }else if (val=='3') {
            return `<span><button style="width:120px" class="btn red">Rejected</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        }else if (val=='4') {
            return `<span><button style="width:120px" class="btn green">Approved</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        }else {
            // return "<span style='color:red;'>Rejected</span>";
            return `<span><button style="width:120px" class="btn red">Rejected</button></span>`;
        } 
            
    }

    function type_app(val,row){
        if(val=='REJECT'){
            // return "<span style='color:yellow;'>Waiting Approve</span>";
            return `<span><button style="width:120px" class="btn yellow">REJECT APPROVAL</button></span>`;
                                
        }else if (val=='REOPEN') {
            return `<span><button style="width:120px" class="btn yellow">REOPEN APPROVAL</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        }else if (val=='RESCHEDULE') {
            return `<span><button style="width:120px" class="btn yellow">RESCHEDULE</button></span>`;
            // return "<span style='color:green;'>Approved</span>";
        }else {
            // return "<span style='color:red;'>Rejected</span>";
            return `<span><button style="width:120px" class="btn red">Rejected</button></span>`;
        } 
    }

    function format_schedule_rdd(val, row) {
        if (row.TYPE_APPROVAL === 'RESCHEDULE') {
            return `<span>${row.RDD}</span>`;
        } else {
            return "<span>-</span>";
        }
    }

    function format_schedule_first_date(val, row) {
        if (row.TYPE_APPROVAL === 'RESCHEDULE') {
            return `<span>${row.FIRST_DATE}</span>`;
        } else {
            return "<span>-</span>";
        }
    }

    function rejectAct(){
        // var row = $('#dg').datagrid('getSelected');
        var row = $('#dg').datagrid('getSelections');
        if (row){
            $.messager.confirm('Confirm','are you sure to Reject this transaction?',function(r){
            if (r){
                $('#-load').dialog('open');
                $(".loader-page").css("visibility", "visible");
                $(".loader").css("visibility", "visible");
                $.post('list_approval_so.php?act=reject&',{data:row},function(result){
                if (result.success){
                    $('#-load').dialog('close');
                    $.messager.show({ // show error message
                    title: 'Success',
                    msg: result.success
                    });
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $.messager.show({ // show error message
                    title: 'Error',
                    msg: result.errorMsg
                    });
                }
                $(".loader").css("visibility", "hidden");
                $(".loader-page").css("visibility", "hidden");
                },'json');
            }
            });
        } else {
            $.messager.alert('Confirm','Select the data to be Reject !', 'info');
        }
    }

    function approveAct(){
        // var row = $('#dg').datagrid('getSelected');
        var row = $('#dg').datagrid('getSelections');
        // console.log(row);
        if (row){
            $.messager.confirm('Confirm','are you sure to approve this transaction?',function(r){
            if (r){
                $('#-load').dialog('open');
                $.post('list_approval_so.php?act=approve&',{data:row},function(result){
                if (result.success){
                    $('#-load').dialog('close');
                    $.messager.show({ // show error message
                    title: 'Success',
                    msg: result.success
                    });
                    $('#dg').datagrid('reload'); // reload the user data
                } else {
                    $('#-load').dialog('close');
                    $.messager.show({ // show error message
                    title: 'Error',
                    msg: result.errorMsg
                    });
                }
               
                },'json');
            }
            });
        } else {
            $.messager.alert('Confirm','Select the data to be Approve !', 'info');
        }
    }
</script>
<style type="text/css">
    #fm{
        margin:0;
        padding:10px;
    }
    .ftitle{
        font-size:14px;
        font-weight:bold;
        padding:5px 0;
        margin-bottom:10px;
        border-bottom:1px solid #ccc;
    }
    .fitem{
        margin-bottom:5px;
    }
    .fitem label{
        display:inline-block;
        width:120px;
    }
    .fitem input{
        width:160px;
    }
</style>
</div>
<? 
include ('../include/ekor.php'); 
?>
</body>
</html>
