<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':
    $token_in = trim($_POST['token']);
    $role = $fautoris->login($token_in);
    $jmlData = count($role['dataUserAuto']);

    $no_spj = isset($_POST['NO_SPJ']) ? $_POST['NO_SPJ'] : '';
    $org_filt = isset($_POST['ORG']) ? $_POST['ORG'] : '';
    $plant_filt = isset($_POST['PLANT']) ? $_POST['PLANT'] : '';
    $produk = isset($_POST['KODE_MATERIAL']) ? $_POST['KODE_MATERIAL'] : '';
    $toko = isset($_POST['KODE_SHIPTO']) ? $_POST['KODE_SHIPTO'] : '';
    $carcon = isset($_POST['CARCON_TYPE']) ? $_POST['CARCON_TYPE'] : '';
    $epod = isset($_POST['EPOD_TYPE']) ? $_POST['EPOD_TYPE'] : '';
    $inco = isset($_POST['INCOTERM']) ? $_POST['INCOTERM'] : '';
    $autosellin = isset($_POST['AUTO_SELL_IN']) ? $_POST['AUTO_SELL_IN'] : '';
    $tgl_spj_from = isset($_POST['TGL_FROM']) ? $_POST['TGL_FROM'] : '';
    $tgl_spj_to = isset($_POST['TGL_TO']) ? $_POST['TGL_TO'] : '';

    if ($role['status'] == true && $jmlData > 0) {

      $user_id = trim($role['dataUserAuto']['USER_ID']);
      
      $dirr = $_SERVER['PHP_SELF'];
      if (empty($token_in)) {
        $responseRequest = array("responseCode" => 400, "responseMessage" => "Parameter tidak lengkap", "data" => null);
        header('Content-Type: application/json');
        echo json_encode($responseRequest);
      } else {
          if (empty($tgl_spj_from) && empty($tgl_spj_to)) {
            $responseRequest = array("responseCode" => 400, "responseMessage" => "TGL_FROM dan TGL_TO harus di isi", "data" => null);
            header('Content-Type: application/json');
            echo json_encode($responseRequest);

            exit;
          }
          
          // if (empty($org_filt)) {
          //   $responseRequest = array("responseCode" => 400, "responseMessage" => "ORG harus diisi", "data" => null);
          //   header('Content-Type: application/json');
          //   echo json_encode($responseRequest);

          //   exit;
          // }

          $mp_coics=$fautoris->getComin($fautoris->koneksi(),$org_filt);
      
          if(count($mp_coics)>0){
              unset($inorg);$orgcounter=0;
              foreach ($mp_coics as $keyOrg => $valorgm){
                    $inorg .="'".$keyOrg."',";
                    $orgcounter++;
              }
              $inorg= rtrim($inorg, ',');        
          }else{
            $inorg= $org_filt;
          }

          $sql="
            WITH Latest_Log_Semen_Pod AS ( SELECT az.*, ROW_NUMBER ( ) OVER ( PARTITION BY az.NO_SPJ ORDER BY az.LAST_UPDATE_DATE DESC ) AS row_num FROM LOG_SEMEN_POD az ) SELECT
            a.*,
            b.NO_DOC_HDR AS NO_DOC_HDR1,
            b.tahun,
            x.tgl_terima,
            c.accounting_doc 
            FROM
              (
              SELECT
                ay.ID,
                ay.PLANT,
                ay.TIPE_DO,
                ay.NO_SHP_TRN,
                ay.ORG,
                ay.NO_POL,
                ay.VEHICLE_TYPE,
                ay.WARNA_PLAT,
                ay.SUPIR,
                ay.VENDOR,
                ay.NAMA_VENDOR,
                ay.KODE_PRODUK,
                ay.NAMA_PRODUK,
                ay.NO_ENTRY_SHEET,
                ay.SAL_OFFICE,
                ay.NAMA_SAL_OFF,
                ay.SATUAN_SHP,
                ay.QTY_SHP,
                ay.TARIF_COST,
                ay.SHP_COST,
                ay.NO_SO,
                ay.INCO,
                ay.SOLD_TO,
                ay.NAMA_SOLD_TO,
                ay.SHIP_TO,
                ay.NAMA_SHIP_TO,
                ay.ALAMAT_SHIP_TO,
                ay.SAL_DISTRIK,
                ay.NAMA_SAL_DIS,
                ay.KODE_KECAMATAN,
                ay.NAMA_KECAMATAN,
                ay.NO_INV_VENDOR,
                ay.NO_INV_SAP,
                ay.ACCOUNTING_DOC,
                TO_CHAR( ay.TANGGAL_KIRIM, 'DD-MM-YYYY' ) AS TANGGAL_KIRIMF,
                TO_CHAR( ay.TANGGAL_DATANG, 'DD-MM-YYYY' ) AS TANGGAL_DATANGF,
                TO_CHAR( ay.TANGGAL_BONGKAR, 'DD-MM-YYYY' ) AS TANGGAL_BONGKARF,
                TO_CHAR( ay.TANGGAL_INVOICE, 'DD-MM-YYYY' ) AS TANGGAL_INVOICEF,
                TO_CHAR( ay.LAST_UPDATE_DATE, 'DD-MM-YYYY' ) AS LAST_UPDATE_DATEF,
                TO_CHAR( ay.TGL_CLEARING, 'DD-MM-YYYY' ) AS TGL_CLEARINGF,
                ay.QTY_KTG_RUSAK,
                ay.QTY_SEMEN_RUSAK,
                ay.TOTAL_KTG_RUSAK,
                ay.TOTAL_KTG_REZAK,
                ay.TOTAL_SEMEN_RUSAK,
                ay.TOTAL_KLAIM_KTG,
                ay.TOTAL_KLAIM_SEMEN,
                ay.HARGA_TEBUS,
                ay.PDPKS,
                ay.TOTAL_KLAIM_ALL,
                ay.PENGELOLA,
                ay.NAMA_PENGELOLA,
                ay.NAMA_KAPAL,
                ay.STATUS,
                ay.STATUS2,
                ay.NO_PAJAK_EX,
                TO_CHAR( ay.TANGGAL_SIAP_TAGIH, 'DD-MM-YYYY' ) AS TANGGAL_SIAP_TAGIH,
                CONCAT( ay.SOLD_TO, TO_CHAR( ay.TGL_CLEARING, 'MMYYYY' ) ) AS NOMORFB,
                ay.NO_INVOICE,
                ay.FLAG_POD,
                ay.KETERANGAN_POD,
                ay.EVIDENCE_POD1,
                ay.EVIDENCE_POD2,
                ay.GEOFENCE_POD,
                az.NO_SPJ,
                az.TANGGAL_SPJ,
                az.ORG AS ORG_LOG,
                az.FLAG_POD AS LOG_FLAG_POD,
                az.KETERANGAN_POD AS LOG_KETERANGAN_POD,
                az.EVIDENCE_POD1 AS LOG_EVIDENCE_POD1,
                az.EVIDENCE_POD2 AS LOG_EVIDENCE_POD2,
                az.GEOFENCE_POD AS LOG_GEOFENCE_POD,
                CASE
                  WHEN ay.FLAG_POD = 'POD-FIOS' THEN 'CARCON'
                  ELSE 'NON CARCON'
                END AS CARCON,
                CASE
                  WHEN ay.FLAG_POD IS NOT NULL THEN 'E' || ay.FLAG_POD
                  ELSE 'NON EPOD'
                END AS EPOD,
                zls.STANDART_AREA AS LEAD_TIME,
                to_char(ay.TANGGAL_BONGKAR, 'HH24:MI') AS JAM_BONGKAR,
                CASE
                  WHEN ay.TANGGAL_BONGKAR IS NOT NULL THEN 'SELESAI BONGKAR'
                  ELSE 'BELUM BONGKAR'
                END AS AUTO_SELL_IN
              FROM
                EX_TRANS_HDR ay
                LEFT JOIN Latest_Log_Semen_Pod az ON ay.NO_SHP_TRN = az.NO_SPJ 
                AND az.row_num = 1 
                LEFT JOIN ZMD_LEADTIME_SO zls ON
                ay.PLANT = zls.PLANT
                AND ay.SAL_DISTRIK = zls.KOTA
                AND SUBSTR(ay.KODE_PRODUK, 0, 7) = zls.KD_MATERIAL
              WHERE
                ay.DELETE_MARK = '0'
                AND ay.TANGGAL_KIRIM BETWEEN TO_DATE('".$tgl_spj_from."', 'DD-MM-YYYY') AND TO_DATE('".$tgl_spj_to."', 'DD-MM-YYYY')
        ";
        
        $sql_orgin = "";

        if ($org_filt != "") {
            $sql .= " 
                AND ay.ORG IN ($inorg)
                ";

            $sql_orgin = "and ORG IN ($inorg)";
        }
        
        if ($plant_filt != "") {
            $sql .= " 
                AND ay.PLANT = '".$plant_filt."'
                ";
        }
        
        if ($produk != "") {
            $sql .= " 
                AND ay.KODE_PRODUK like '%".$produk."%'
                ";
        }
        
        if ($inco != "") {
            $sql .= " 
                AND ay.INCO like '%".$inco."%'
                ";
        }
        
        if ($toko != "") {
            $sql .= " 
                AND ay.SHIP_TO like '%".$toko."%'
                ";
        }
        
        if ($carcon != "") {
            if ($carcon == 'carcon') {
                $sql .= " 
                  AND ay.FLAG_POD = 'POD-FIOS' 
                    ";
            }elseif ($carcon == 'noncarcon') {
                $sql .= " 
                      AND ay.FLAG_POD != 'POD-FIOS' OR ay.FLAG_POD IS NULL 
                    ";
            }
        }
        
        if ($epod != "") {
            $sql .= " 
                AND ay.FLAG_POD like '%".$epod."%' 
                ";
        }
        
        if ($autosellin != "") {
            if ($autosellin == '1') {
                $sql .= " 
                    AND ay.TANGGAL_BONGKAR IS NOT NULL 
                    ";
            }elseif ($autosellin == '0') {
                $sql .= " 
                      AND ay.TANGGAL_BONGKAR IS NULL
                    ";
            }
        }
        
        
        if ($no_spj != "") {
            $sql .= " 
                AND ay.NO_SHP_TRN = '".$no_spj."'
                ";
        }
        
        $sql .=")
          a left join (
        select CONCAT(SOLD_TO, concat(BULAN, TAHUN)) as FB60_DOC_NO,TAHUN,NO_DOC_HDR
        from EX_FB60 where DELETE_MARK= '0' ".$sql_orgin."
        group by CONCAT(SOLD_TO, concat(BULAN, TAHUN)),TAHUN,NO_DOC_HDR
      
        ) b
        ON (b.FB60_DOC_NO = a.NOMORFB)";
        $sql .=" LEFT JOIN (SELECT * FROM EX_INVOICE WHERE DELETE_MARK = '0' ".$sql_orgin.") c ON (a.no_invoice = c.no_invoice)
            LEFT JOIN (SELECT * FROM KPI_TERIMA_INV_VENDOR WHERE no_invoice IS NOT NULL ".$sql_orgin." AND del = '0') x ON (a.no_invoice = x.no_invoice)
            ORDER BY a.VENDOR, a.NO_SHP_TRN ASC";
        // echo $sql;
        $query= oci_parse($fautoris->koneksi(), $sql);
        oci_execute($query);
        $dataAll = array();
        $datavoicesap = array();
        $data = array();
        $i = 0;
        while($row=oci_fetch_array($query)){
            $dataAll[$i] = $row;
            $datavoicesap[$row[ORG].$row[NO_DOC_HDR1].$row[TAHUN]]=$row[ORG]."|".$row[NO_DOC_HDR1]."|".$row[TAHUN];
            $i++;
        }
        
        $total=count($dataAll);
        if ($total > 0 && count($datavoicesap) > 0) {
          $sap = new SAPConnection();
          $sap->Connect("../include/sapclasses/logon_data.conf");
          
          if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
          }

          //Pemanggilan RFC Cari
          $fce = $sap->NewFunction ("Z_ZAPPSD_STATUS_INVOICE");
          if ($fce == false ) { $sap->PrintStatus(); exit; }

          foreach ($datavoicesap as $key => $value) {
              $aarayDocv=explode("|",$value);
              $ORGdocv=$aarayDocv[0];
              $NO_DOC_HDR1dov=$aarayDocv[1];
              $TAHUNdocv=$aarayDocv[2];
              if($NO_DOC_HDR1dov!='' && $ORGdocv!='' && $TAHUNdocv!=''){
                  $fce->I_INPUT->row["BUKRS"] = $ORGdocv;
                  $fce->I_INPUT->row["BELNR"] = $NO_DOC_HDR1dov;
                  $fce->I_INPUT->row["GJAHR"] = $TAHUNdocv;
                  $fce->I_INPUT->Append($fce->I_INPUT->row);
              }
          }
          $fce->Call();
          if ($fce->GetStatus() == SAPRFC_OK ) {		
              $fce->T_OUT->Reset();
              $s=0;
              unset($datadoc);
              while ( $fce->T_OUT->Next() ){ 
                      $datadoc[$fce->T_OUT->row['BUKRS'].$fce->T_OUT->row['BELNR'].$fce->T_OUT->row['GJAHR']] = $fce->T_OUT->row;      
                  $s++;
              }
          }else
              $fce->PrintStatus();
              
          $fce->Close();	
          $sap->Close();
        }

        foreach ($dataAll as $k => $v) {
            $data[$k]['PLANT'] = trim($v['PLANT']);
            $data[$k]['TIPE_SPJ'] = trim($v['TIPE_DO']);
            $data[$k]['NO_SPJ'] = trim($v['NO_SHP_TRN']);
            $data[$k]['TANGGAL_SPJ'] = trim($v['TANGGAL_SPJ']);
            $data[$k]['ORG'] = trim($v['ORG']);
            $data[$k]['NOPOL'] = trim($v['NO_POL']);
            $data[$k]['TIPE'] = trim($v['VEHICLE_TYPE']);
            $data[$k]['WARNA_PLAT'] = trim($v['WARNA_PLAT']);
            $data[$k]['DRIVER'] = trim($v['SUPIR']);
            $data[$k]['EXPEDITUR'] = trim($v['VENDOR']);
            $data[$k]['NAMA_EXPEDITUR'] = trim($v['NAMA_VENDOR']);
            $data[$k]['KODE_PRODUK'] = trim($v['KODE_PRODUK']);
            $data[$k]['NAMA_PRODUK'] = trim($v['NAMA_PRODUK']);
            $data[$k]['NO_ENTRY_SHEET'] = trim($v['NO_ENTRY_SHEET']);
            $data[$k]['PROVINSI'] = trim($v['SAL_OFFICE']);
            $data[$k]['NAMA_PROVINSI'] = trim($v['NAMA_SAL_OFF']);
            $data[$k]['SATUAN'] = trim($v['SATUAN_SHP']);
            $data[$k]['QTY'] = trim($v['QTY_SHP']);
            $data[$k]['TARIF'] = trim($v['TARIF_COST']);
            $data[$k]['TOTAL'] = trim($v['SHP_COST']);
            $data[$k]['NO_SO'] = trim($v['NO_SO']);
            $data[$k]['HARGA_TEBUS'] = trim($v['HARGA_TEBUS']);
            $data[$k]['SOLD_TO'] = trim($v['SOLD_TO']);
            $data[$k]['NAMA_SOLD_TO'] = trim($v['NAMA_SOLD_TO']);
            $data[$k]['INCOTERM'] = trim($v['INCO']);
            $data[$k]['SHIP_TO'] = trim($v['SHIP_TO']);
            $data[$k]['NAMA_SHIP_TO'] = trim($v['NAMA_SHIP_TO']);
            $data[$k]['ALAMAT_SHIP_TO'] = trim($v['ALAMAT_SHIP_TO']);
            $data[$k]['DISTRIK'] = trim($v['SAL_DISTRIK']);
            $data[$k]['NAMA_DISTRIK'] = trim($v['NAMA_SAL_DIS']);
            $data[$k]['KODE_KECAMATAN'] = trim($v['KODE_KECAMATAN']);
            $data[$k]['NAMA_KECAMATAN'] = trim($v['NAMA_KECAMATAN']);
            $data[$k]['REKAP_TAG'] = trim($v['NO_INV_VENDOR']);
            $data[$k]['NO_INVOICE'] = trim($v['NO_INVOICE']);
            $data[$k]['NO_PPL_1'] = trim($v['NO_INV_SAP']);
            $data[$k]['NO_PPL_2'] = trim($v['ACCOUNTING_DOC']);
            $data[$k]['TANGGAL_KIRIM'] = trim($v['TANGGAL_KIRIMF']);
            $data[$k]['TANGGAL_SIAP_TAGIH'] = trim($v['TANGGAL_SIAP_TAGIH']);
            $data[$k]['TANGGAL_DATANG'] = trim($v['TANGGAL_DATANGF']);
            $data[$k]['TANGGAL_BONGKAR'] = trim($v['TANGGAL_BONGKARF']);
            $data[$k]['JAM_BONGKAR'] = trim($v['JAM_BONGKAR']);
            $data[$k]['REKAP'] = trim($v['TANGGAL_INVOICEF']);
            $data[$k]['PPL'] = trim($v['LAST_UPDATE_DATEF']);
            $data[$k]['TANGGAL_CLREARING'] = trim($v['TGL_CLEARINGF']);
            $data[$k]['KLAIM_KANTONG'] = trim($v['QTY_KTG_RUSAK']);
            $data[$k]['KLAIM_SEMEN'] = trim($v['QTY_SEMEN_RUSAK']);
            $data[$k]['VAL_KANTONG'] = trim($v['TOTAL_KTG_RUSAK']);
            $data[$k]['VAL_RESAK'] = trim($v['TOTAL_KTG_REZAK']);
            $data[$k]['VAL_SEMEN'] = trim($v['TOTAL_SEMEN_RUSAK']);
            $data[$k]['TOTAL_KANTONG'] = trim($v['TOTAL_KLAIM_KTG']);
            $data[$k]['TOTAL_SEMEN'] = trim($v['TOTAL_KLAIM_SEMEN']);
            $data[$k]['HARGA_TEBUS'] = trim($v['HARGA_TEBUS']);
            $data[$k]['PDPPKS'] = trim($v['PDPKS']);
            $data[$k]['TOTAL_KLAIM'] = trim($v['TOTAL_KLAIM_ALL']);
            $data[$k]['VENDOR'] = trim($v['PENGELOLA']);
            $data[$k]['NAMA_VENDOR'] = trim($v['NAMA_PENGELOLA']);
            $data[$k]['NAMA_KAPAL'] = trim($v['NAMA_KAPAL']);
            $data[$k]['STATUS'] = trim($v['STATUS']);
            $data[$k]['STATUS2'] = trim($v['STATUS2']);
            $data[$k]['NO_PAJAK_EX'] = trim($v['NO_PAJAK_EX']);
            $data[$k]['TGL_TERIMA_DISTRANS'] = trim($v['TGL_TERIMA']);
            $data[$k]['TGL_TERIMA_VERIFIKASI'] = isset($datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_VER']) ? $datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_VER'] : "";
            $data[$k]['TGL_TERIMA_BENDAHARA'] = isset($datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_BEND']) ? $datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_BEND'] : "";
            $data[$k]['TGL_JATUH_TEMPO'] = isset($datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['ZFBDT']) ? $datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['ZFBDT'] : "";
            $data[$k]['POD'] = $dataAll[$k]['FLAG_POD']!='' ? $dataAll[$k]['FLAG_POD'] : $dataAll[$k]['LOG_FLAG_POD'];
            $data[$k]['KETERANGAN'] = $dataAll[$k]['FLAG_POD']!='' ? $dataAll[$k]['KETERANGAN_POD'] : $dataAll[$k]['LOG_KETERANGAN_POD'];

            $file_pod = "";
            $file_pod2 = "";
            if ($dataAll[$k]['FLAG_POD'] != '') {
              if ($dataAll[$k]['EVIDENCE_POD1'] != '') {
              $file_pod = $urlFile.$dataAll[$k]['EVIDENCE_POD1'];
              } 
            } else {
              if ($dataAll[$k]['LOG_EVIDENCE_POD1'] != '') {
                $file_pod = $urlFile.$dataAll[$k]['LOG_EVIDENCE_POD1'];
              }
            }
            
            if ($dataAll[$k]['FLAG_POD'] != '') {
              if ($dataAll[$k]['EVIDENCE_POD2'] != '') {
              $file_pod2 = $urlFile.$dataAll[$k]['EVIDENCE_POD2'];
              } 
            } else {
              if ($dataAll[$k]['LOG_EVIDENCE_POD2'] != '') {
                $file_pod2 = $urlFile.$dataAll[$k]['LOG_EVIDENCE_POD2'];
              }
            }
            
            $data[$k]['FILE_POD'] = $file_pod;
            $data[$k]['FILE_POD_2'] = $file_pod2;
            $data[$k]['GEOFENCE_POD'] = $dataAll[$k]['FLAG_POD']!='' ? $dataAll[$k]['GEOFENCE_POD'] : $dataAll[$k]['LOG_GEOFENCE_POD'];
            $data[$k]['STATUS_SYNC_POD'] = $dataAll[$k]['FLAG_POD']!='' ? "SUKSES" : "BELUM SINKRON";
            $data[$k]['CARCON_TYPE'] = $v['CARCON'];
            $data[$k]['EPOD_TYPE'] = $v['EPOD'];
            $data[$k]['LEAD_TIME'] = $v['INCO'] == 'FOT' ? ($v['LEAD_TIME'] + 1) : $v['LEAD_TIME'];
            $data[$k]['AUTO_SELL_IN'] = $v['AUTO_SELL_IN'];
        }

        $responseRequest = array(
          'responseCode' => 200,
          'responseMessage' => "Success ".count($data)." data found",
          'data' => $data
        );

        header('Content-Type: application/json');
        echo json_encode($responseRequest);


      }
    } else {
      $responseRequest = array(
        'responseCode' => 401,
        'responseMessage' => $role["keterangan"] ? $role["keterangan"] : "Data User Not Found",
        'data' => null
      );
      header('Content-Type: application/json');
      echo json_encode($responseRequest);
    }
    $byLog = 'getReleaseEpod';
    $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, $token_in);
    break;
}

class getReleaseEpod
{

  private $_basePath;
  private $_sapCon;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function getData($param)
  {
    $data = array();
    return $data;
  }
}
