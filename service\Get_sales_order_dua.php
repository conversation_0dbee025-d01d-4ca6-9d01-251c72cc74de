<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");
$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':
    $token_in = trim($_POST['token']);
    $role = $fautoris->login($token_in);
    $jmlData = count($role['dataUserAuto']);

    if($role['status']==true && $jmlData>0){

      $user_id = trim($role['dataUserAuto']['USER_ID']);
      $dirr = $_SERVER['PHP_SELF'];    
      $rolenn=$fautoris->keamananser($dirr,$user_id);//pembatasan akses ke service

      if ($rolenn==false) {
         $ret = array("status"=>false,"keterangan"=>"Tidak ada akses terhadap service ini");
         echo json_encode($ret);
      } else {
        $param['XVKORG'] = $_POST['XVKORG'];
        $param['XVBELN'] = $_POST['XVBELN'];
        $param['XBZIRK'] = $_POST['XBZIRK'];
        $param['XKUNNR'] = $_POST['XKUNNR'];
        $param['XFLAG'] = $_POST['XFLAG'];
        $param['XINCO1'] = $_POST['XINCO1'];
        $param['XBSTKD'] = $_POST['XBSTKD'];
        $param['XROUTE'] = $_POST['XROUTE'];
        $param['XMATNR'] = $_POST['XMATNR'];
        $param['XWERKS'] = $_POST['XWERKS'];
        $param['XAUGRU'] = $_POST['XAUGRU'];
        $param['XPOSNR'] = $_POST['XPOSNR'];
        $param['XPLTYP'] = $_POST['XPLTYP'];
        $param['XKUNNR2'] = $_POST['XKUNNR2'];
        $param['XAUART'] = $_POST['XAUART'];
        $param['X_SORT'] = $_POST['X_SORT'];
        $param['XVKBUR'] = $_POST['XVKBUR'];
        $param['X_PALLET'] = $_POST['X_PALLET'];
        $param['lr_augru'] = $_POST['lr_augru'];
        $param['edatu_from'] = $_POST['edatu_from'];
        $param['edatu_to'] = $_POST['edatu_to'];
        $param['audat_from'] = $_POST['audat_from'];
        $param['audat_to'] = $_POST['audat_to'];


        $get = new get_sales_order();
        $result = $get->get_data($param);
        if (empty($result)) {
          $responseRequest = array(
            'ResponseCode' => 404,
            'ResponseMessage' => 'No Data Found',
          );
          echo json_encode("No Data Found");
        } else {
          $responseRequest = array(
            'ResponseCode' => 200,
            'ResponseMessage' => json_encode($result),
          );
          echo json_encode($result);
        }
      }
    } else {
      echo json_encode($role);
    }
    // $byLog = 'get_sales_order';
    // $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, $token_in);
    break;
}

class get_sales_order
{

  private $_basePath;
  private $_sapCon;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function get_data($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      //$fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL"); v_lama
      $fce = $sap->NewFunction("Z_ZAPPSD_SO_ALL_V2");

      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc($fce, $param);
        return $data;
      }
    }
  }

  function rfc($fce, $param)
  {

    $fce->XVKORG = $param['XVKORG'];
    //$fce->XVBELN = $param['XVBELN'];
    //$fce->XBZIRK = $param['XBZIRK'];
    //$fce->XKUNNR = $param['XKUNNR'];
    //$fce->XFLAG = $param['XFLAG'];
    //$fce->XINCO1 = $param['XINCO1'];
    //$fce->XBSTKD = $param['XBSTKD'];
    //$fce->XROUTE = $param['XROUTE'];
    //$fce->XMATNR = $param['XMATNR'];
    //$fce->XWERKS = $param['XWERKS'];
    //$fce->XAUGRU = $param['XAUGRU'];
    $fce->XPOSNR = $param['XPOSNR'];
    //$fce->XPLTYP = $param['XPLTYP'];
    //$fce->XKUNNR2 = $param['XKUNNR2'];
    //$fce->XAUART = $param['XAUART'];
    //$fce->X_SORT = $param['X_SORT'];
    //$fce->XVKBUR = $param['XVKBUR'];
    //$fce->X_PALLET = $param['X_PALLET'];

    $fdate1 = str_replace("-", "", $param['edatu_from']);
    $tdate1 = str_replace("-", "", $param['edatu_to']);
    $fce->LR_EDATU->row['SIGN']='I';
    $fce->LR_EDATU->row['OPTION']='BT';
    $fce->LR_EDATU->row['LOW']= '02.04.2024';//$fdate1;
    $fce->LR_EDATU->row['HIGH']= '05.04.2024';//$tdate1;
    $fce->LR_EDATU->Append($fce->LR_EDATU->row);

    echo '<pre>';
    print_r($fce);
    echo '</pre>';

    exit();

    $fce->Call();
   // print_r($fce->RETURN['TYPE']);die();
    if($fce->RETURN['TYPE']=='E'){
      $this->_data[] = $fce->RETURN['MESSAGE'];
    }else{
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->RETURN_DATA->Reset();

      while ($fce->RETURN_DATA->Next()) {
        $this->_data[] = $fce->RETURN_DATA->row;
      }
    }
    }
    
    
    
    
    

    return $this->_data;
  }
}
