<?
ob_start();
//@16 Desember 2016
##################################################################################################
# For : Update Tanggal Clearing Otomatis ke database Oracle CSMS, dengan tanggal
# clearing tersebut digunakan sebagai dasar RUN PPL FB60. Sehingga data pasti sudah diverifikasi.
##################################################################################################
# php mailer #
//require_once('phpmailer.php');
//require_once('class.smtp.php');
ini_set("SMTP", "relay.sig.id"); //tambahan

include_once ('../include/or_fungsi.php');
include_once ('helper.php');
$fungsi     = new or_fungsi();
$ora_con    = $fungsi->or_koneksi();


# SAP Connection #
// require_once ("../include/sapclasses/sap.php");

$sap_con = New SAPConnection();
//$sap = $sap_con->getConnSAP();//prod
$sap = $sap_con->Connect("../include/sapclasses/logon_data.conf");

# SAP Connection #
//require_once("../../include/connect/SAPDataModule_Connection.php");
//$sap_con = New SAPDataModule_Connection();
//$sap = $sap_con->getConnSAP();//prod
//$sap = $sap_con->getConnSAP_Dev();//dev
#/SAP Connection #

#Koneksi Oracle###
//$ora_con = $sap_con->koneksiSD_Dev();//DEVSD
//$ora_con = $sap_con->koneksiSDOnline_Dev();//DEV
//$ora_con = $sap_con->koneksiSDOnline_Prod();//PROD koneksiSD_Dev //koneksiSDOnline_Prod
if(!$ora_con || !$sap_con) { echo "Koneksi oracle prod gagal"; exit;}

$conn=$ora_con; // ora_con

$globalSPJ = array();

$sql_cek_recalculate = "SELECT ROWNUM as r, EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1 FROM EX_TRANS_HDR
WHERE
DELETE_MARK = '0'  and INCO !='FOT' and STATUS2 = 'DRAFT' and ORG = '3000' AND KET_ERROR IS NULL
AND TIPE_TRANSAKSI = 'BAG'
AND TRUNC(TANGGAL_KIRIM) < TRUNC(SYSDATE) - 1 -- menyesuaikan dengan proses settlement yang delay sehari
ORDER BY VENDOR, SAL_DISTRIK,
NO_SHP_TRN ASC
";

//$sql_cek_recalculate =
//"
//SELECT * FROM EX_TRANS_HDR WHERE NO_SHP_TRN = '2000008647'"; //2000008649 2000008647  5000368249


$query_bn= @oci_parse($conn, $sql_cek_recalculate);
oci_execute($query_bn);

$jmlSend = 0;
// echo $sql_cek_recalculate;

/*rama*/
function deleteRecalculate($conn,$tablename,$field_id,$value_id)
	    {
		$queryDelete="DELETE FROM $tablename WHERE $field_id[0]='$value_id[0]'";
		for($j=1;$j< count($field_id);$j++)
		{
			$queryDelete.=' AND '."$field_id[$j]='$value_id[$j]'";
		}
}
/*rama*/
function insertRecalculate($conn,$field_names,$field_data,$tablename)
	    {

		$queryInsert = "INSERT INTO $tablename ($field_names[0]";
		for($k=1;$k< count($field_names);$k++)
		{
			$queryInsert.=', '."$field_names[$k]";
		}
		$queryInsert.=") VALUES ('$field_data[0]'";
		for($k=1;$k< count($field_data);$k++)
		{
			list($tang,$gal)=explode("_",$field_data[$k]);
			if($tang=="instgl")
				$queryInsert.=', '."TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";	
			else if($field_data[$k]=='SYSDATE')	
				$queryInsert.=', '."$field_data[$k]";
                        else if ($tang == "TO" && substr($gal, 0, 4) == "Date")
                        $queryInsert.=', ' . "$field_data[$k]";
			else	
			$queryInsert.=', '."'$field_data[$k]'";
		}
			$queryInsert.=')';
			
		$sqlqueryInsert= oci_parse($conn, $queryInsert);
		oci_execute($sqlqueryInsert);
	    }
		
/*rama*/
function updateRecalculate($conn,$field_names,$field_data,$tablename,$field_id,$value_id){
        unset($globalSPJ);
        $queryUpdate="UPDATE $tablename SET $field_names[0]='$field_data[0]'";
                        
        for($k=1;$k< count($field_names);$k++)
  		{
        list($tang,$gal)=explode("_",$field_data[$k]);
        if($tang=="updtgl")
        $queryUpdate.=', '."$field_names[$k]=TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
        else if($field_data[$k]=='SYSDATE')	
        $query.=', '."$field_names[$k]=$field_data[$k]";
        else	
        $queryUpdate.=', '."$field_names[$k]='$field_data[$k]'";
  		}
        $queryUpdate.=" WHERE $field_id[0]='$value_id[0]'";
        for($j=1;$j< count($field_id);$j++)
  		{
        $queryUpdate.=' AND '."$field_id[$j]='$value_id[$j]'";
        }
                        
        $sqlqueryUpdate= oci_parse($conn,$queryUpdate);
        oci_execute($sqlqueryUpdate);
        
        //echo '<pre>';
        //print_r($value_id[0]);
        //echo '||';
        //print_r($field_data[0]);
        //echo '</pre>';
        
        return $globalSPJ[$value_id[0]] = $field_data[0];
        //sendMail($value_id[0],$field_data[0]);
}

while($row=oci_fetch_array($query_bn)){

// if(substr($kode_produk, 0, 7) === '121-301'){
//     $prod_ket    = '[ Zak ]';
// }else if(substr($kode_produk, 0, 7) === '121-302'){
//     $prod_ket    = '[ Curah ]';
// }else if(substr($kode_produk, 0, 7) === '121-200'){
//     $prod_ket    = '[ Klinker ]';
// }else{
//     $prod_ket    = " ";
// }

$prod_ket    = " ";
$no_spj = $row['NO_SHP_TRN'];

if($no_spj != '' || $no_spj != null ){


$sql= "SELECT NO_SHP_TRN,SOLD_TO,SHIP_TO,QTY_SHP,PLANT,PLANT_RCV,ORG,NO_POL,WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_SHP_TRN = '$no_spj'";

$query= oci_parse($conn, $sql);
oci_execute($query);
//echo $sql;
while($row1=oci_fetch_array($query)){

        $no_shp_x=$row1['NO_SHP_TRN']; // ['NO_SHP_TRN']
		$qty_v=$row1['QTY_SHP'];
		$sold_to_v=$row1['SOLD_TO'];
		$plant_v=$row['PLANT']; //penambahan proses curah soldto 2304 & 7403
		if ($sold_to_v == '0000002403' or $sold_to_v == '0000007403')$hajar = "T"; //$sold_to_v == '0000002403' or $sold_to_v == '0000007403'
		else $hajar = "F";

		$sql_dtl= "SELECT * FROM EX_TRANS_DTL WHERE NO_SHP_TRN = '$no_shp_x' ";
		$query_dtl= oci_parse($conn, $sql_dtl);
		oci_execute($query_dtl);$row_dtl=oci_fetch_array($query_dtl);
		$no_so_v=$row_dtl['NO_SO'];
		$no_po_v=$row_dtl['NO_PO'];
		$posnr_v=$row_dtl['POSNR'];
		$kode_prod=$row_dtl['KODE_PRODUK'];
		$kode_prod_cek = substr($kode_prod, 0, -5);

		//echo $no_shp_x;
		
        ////////////ambil data SAP\\\\\\\\\\\\\\\\\\\\\\\
		
		$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_data.conf"); //  ../../sgg/include/sapclasses/logon_data.conf
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		$sap->PrintStatus();
		exit;
		}
		
         $fce = $sap->NewFunction ("Z_ZAPPSD_SEL_SHP_COST4_SP");//perubahan rfc
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			$fce->LR_EXTI1->row["SIGN"] = 'I';
			$fce->LR_EXTI1->row["OPTION"] = 'EQ';
			$fce->LR_EXTI1->row["LOW"] = $no_shp_x;
			$fce->LR_EXTI1->Append($fce->LR_EXTI1->row);

        $fce->LR_KUNNR->row["SIGN"] = 'I';
			$fce->LR_KUNNR->row["OPTION"] = 'EQ';
			$fce->LR_KUNNR->row["LOW"] = $row['SHIP_TO'];
			$fce->LR_KUNNR->Append($fce->LR_KUNNR->row);

        $fce->Call();
			if ($fce->GetStatus() == SAPRFC_OK ) {
				$fce->RETURN_DATA->Reset();
				$total_netwr =0;
				$cek_inke = 0;
                while ( $fce->RETURN_DATA->Next() ){
					$TKNUM[]=$tknum_in= $fce->RETURN_DATA->row["TKNUM"];
					$EXTI1[]=$exti1_in= $fce->RETURN_DATA->row["EXTI1"];
					$VBTYP[]=$vbtyp_in= $fce->RETURN_DATA->row["VBTYP"];
					$SHTYP[]=$shtyp_in= $fce->RETURN_DATA->row["SHTYP"];
					$FKNUM[]=$fknum_in= $fce->RETURN_DATA->row["FKNUM"];
					$FKPOS[]=$fkpos_in= $fce->RETURN_DATA->row["FKPOS"];
					$BUKRS[]=$bukrs_in= $fce->RETURN_DATA->row["BUKRS"];
					$NETWR[]=$netwr_in= round($fce->RETURN_DATA->row["NETWR"]*100,0);
					$total_netwr += $netwr_in;

					$MWSBP[]=$mwsbp_in= $fce->RETURN_DATA->row["MWSBP"];
					$FKPTY[]=$fkpty_in= $fce->RETURN_DATA->row["FKPTY"];
					$KALSM[]=$kalsm_in= $fce->RETURN_DATA->row["KALSM"];
					$VTEXT[]=$vtext_in= $fce->RETURN_DATA->row["VTEXT"];
					$WERKS[]=$werks_in= $fce->RETURN_DATA->row["WERKS"];
					$EKORG[]=$ekorg_in= $fce->RETURN_DATA->row["EKORG"];
					$EBELN[]=$ebeln_in= $fce->RETURN_DATA->row["EBELN"];

					$EBELP[]=$ebelp_in= $fce->RETURN_DATA->row["EBELP"];
					$LBLNI[]=$lblni_in= $fce->RETURN_DATA->row["LBLNI"];
					$TDLNR[]=$tdlnr_in= $fce->RETURN_DATA->row["TDLNR"];
					$STABR[]=$stabr_in= $fce->RETURN_DATA->row["STABR"];
					$KOSTL[]=$kostl_in= $fce->RETURN_DATA->row["KOSTL"];
					$PRCTR[]=$prctr_in= $fce->RETURN_DATA->row["PRCTR"];
					$BANKN[]=$bankn_in= $fce->RETURN_DATA->row["BANKN"];
					$BANKA[]=$banka_in= $fce->RETURN_DATA->row["BANKA"];
					$BRNCH[]=$brnch_in= $fce->RETURN_DATA->row["BRNCH"];
					$SAKTO[]=$sakto_in= $fce->RETURN_DATA->row["SAKTO"];
					$NETWR_DO[]=$netwr_do_in= round($fce->RETURN_DATA->row["NETWR_DO"],0);
					$NTGEW[]=$ntgew_in= (int)$fce->RETURN_DATA->row["NTGEW"];
					//$MWSBP_DO[]=$mwsbp_do_in= $fce->RETURN_DATA->row["MWSBP_DO"]*100;
					$LIFNR[]=$lifnr_in= $fce->RETURN_DATA->row["LIFNR"];
					$NAME1[]=$name1_in= $fce->RETURN_DATA->row["NAME1"];
					$BVTYP[]=$bvtyp_in= $fce->RETURN_DATA->row["BVTYP"];
					$BRAN1[]=$bran1_in= $fce->RETURN_DATA->row["BRAN1"];
					$VTEXTX[]=$vtextx_in= $fce->RETURN_DATA->row["VTEXTX"];
					$mwsbp_do_in= 0;// pajak di nolkan
					$harga_tebus = $netwr_do_in + $mwsbp_do_in;//ambil harga satuan
					if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $exti1_in == $no_shp_x and $stabr_in == "C" and $bvtyp_in != "" or $hajar == "T"){
						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200"  or $hajar == "T")){
							if ($cek_inke == 0){
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");
							$tablename="EX_TRANS_COST";
							deleteRecalculate($conn,$tablename,$field_id,$value_id); //sap_con
							
							echo "Delete PO <br>";
							}
							$cek_inke = 1;

							$field_names=array('NO_SHP_TRN','KODE_SHP_COST','SHP_COST','NO_ENTRY_SHEET','TYPE','FKPOS','DELETE_MARK','EBELN','EBELP');
							$field_data=array("$no_shp_x","$fknum_in","$netwr_in","$lblni_in","$fkpty_in","$fkpos_in","0","$ebeln_in","$ebelp_in");
							$tablename="EX_TRANS_COST";
							insertRecalculate($conn,$field_names,$field_data,$tablename);  //sap_con
							
							echo "Insert PO";
						}
					}
				}
				
				//echo "<pre>";
				//print_r($EBELN);
				//echo "<pre>";
				//die();

					$tipe = "";
					$msg = "";
					$tipe=$fce->RETURN["TYPE"];
					$msg=$fce->RETURN["MESSAGE"];
//echo "ebeln".$ebeln_in."netwr".$netwr_in."total net".$total_netwr."no shp x".$no_shp_x."extil1".$exti1_in."stabt in".$stabr_in."linfr".$lifnr_in."bvtyp".$bvtyp_in ."hajar".$hajar;
					if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $no_shp_x == $exti1_in and  $stabr_in == "C"
                    and $bvtyp_in != "" or $hajar == "T"){
//echo "harga tebus".$harga_tebus."kode ".$kode_prod_cek."Hajar".$hajar;
						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200" or $hajar == "T" )){

							//penambahan proses curah soldto 2304 & 7403
							if($hajar == "T"){
								if(!$werks_in){
								$werks_in = $plant_v;	
								}
							}

							// get warna plat
                            $warna_plat = $row1[WARNA_PLAT];
                            if(!$warna_plat){
                                $warna_plat = "";
                                $fce2 = $sap->NewFunction("Z_ZAPPSD_SELECT_TRUK");
                                $fce2->XPARAM["NOPOLISI"] = $row1[NO_POL];
                                $fce2->XDATA_APP["NMORG"] = $row1[ORG];
                                $fce2->XDATA_APP["NMPLAN"] = $row1[PLANT];

                                $fce2->Call();

                                if($fce2->GetStatus() == SAPRFC_OK){
                                    if($fce2->RETURN["TYPE"] == "S"){
										$fce2->RETURN_DATA->Reset();
                                        while ($fce2->RETURN_DATA->Next()) {
                                            $warna_plat = $fce2->RETURN_DATA->row["WARNA_PLAT"];
                                        }
                                    }
                                }
                            }

							$tarif_cost=round($netwr_in/$qty_v);
							$field_names=array('STATUS2','NO_SHP_SAP','SHP_COST','KODE_SHP_COST','NO_ENTRY_SHEET','TARIF_COST','EBELN','EBELP','KOSTL','PRCTR','NO_REK_DIS','NAMA_BANK_DIS','BANK_CABANG_DIS','HARGA_TEBUS','KET_ERROR','NO_GL_SHP','PLANT','UM_REZ','QTY_KTG_RUSAK','QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG','TOTAL_SEMEN_RUSAK','PDPKS','TOTAL_KLAIM_SEMEN','TOTAL_KLAIM_ALL','TANGGAL_DATANG','TANGGAL_BONGKAR','PENGELOLA','NAMA_PENGELOLA','BVTYP','KODE_KECAMATAN','NAMA_KECAMATAN','TANGGAL_SIAP_TAGIH','WARNA_PLAT');
							$field_data=array("OPEN","$tknum_in","$total_netwr","$fknum_in","$lblni_in","$tarif_cost","$ebeln_in","$ebelp_in","$kostl_in","$prctr_in","$bankn_in","$banka_in","$brnch_in","$harga_tebus","OK","$sakto_in","$werks_in","$ntgew_in","","","","","","","","","","","","$lifnr_in","$name1_in","$bvtyp_in","$bran1_in","$vtextx_in","SYSDATE","$warna_plat");
							$tablename="EX_TRANS_HDR";
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");

                            //karena sysdate ga bisa
                            $sql_up ="UPDATE EX_TRANS_HDR SET TANGGAL_SIAP_TAGIH = SYSDATE WHERE NO_SHP_TRN  = '$no_shp_x'";
                            $query_up= oci_parse($conn, $sql_up);
                            oci_execute($query_up);

							updateRecalculate($conn,$field_names,$field_data,$tablename,$field_id,$value_id);  //$sap_con

							$msg1=" OK SHP $no_shp_x ";
							$show_ket.='<br>';
							$show_ketmk=  $msg1;
							$globalSPJ[$no_shp_x]['KET']=$show_ketmk;
                            $globalSPJ[$no_shp_x]['MSG']='S';
						}else{
							$show_ket.="Gagal Update SHP $no_turunan, "; //$no_turunan
							if ($tipe == 'E')
							$show_ket.= $msg;
							else $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
							$show_ket.= '<br>';
							$msg .="Gagal Update SHP $no_turunan"; //$no_turunan
							$field_names=array('KET_ERROR');
							$field_data=array("$msg");
							$tablename="EX_TRANS_HDR";
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");

                            updateRecalculate($conn,$field_names,$field_data,$tablename,$field_id,$value_id); //$sap_con
							$show_ketmk=  $msg;
							$globalSPJ[$no_shp_x]['KET']=$show_ketmk;
                            $globalSPJ[$no_shp_x]['MSG']='E';

						}
					}else{
						$show_ket.="Gagal Update SHP $no_turunan, "; //$no_turunan
						if ($tipe == 'E')
						$show_ket.= $msg;
						else $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
						$show_ket.= '<br>';
						$msg .="Gagal Update SHP $no_turunan"; //$no_turunan
						$field_names=array('KET_ERROR');
						$field_data=array("$msg");
						$tablename="EX_TRANS_HDR";
						$field_id=array('NO_SHP_TRN');
						$value_id=array("$no_shp_x");

                        updateRecalculate($conn,$field_names,$field_data,$tablename,$field_id,$value_id); //$sap_con
						$show_ketmk=  $msg;
						$globalSPJ[$no_shp_x]['KET']=$show_ketmk;
                        $globalSPJ[$no_shp_x]['MSG']='E';
						
					}
					
			}
			
			$fce->Close();
  }

}
 $jmlSend++;
}

//}
//exit();
          //echo '<pre>';
          //print_r($prod_ket);
          //echo '</pre>';
		  //
		  //echo '<pre>';
          //print_r($globalSPJ);
          //echo '</pre>';
		  //echo $no_shp_x;
		 
		  if ($jmlSend > 0){
          $hasil = sendMail($globalSPJ,$prod_ket);
          }
          echo 'Recalculate Per Tanggal :'.date('d-m-Y');
          echo '</br>';


          echo 'Jumlah SPJ yang sukses :'.$hasil['jml_sukses'];
          echo '</br>';
          echo 'Jumlah SPJ yang error :'.$hasil['jml_fail'];
          echo '</br>';
          echo 'Total :'.count($globalSPJ);
		  
		 		
function sendMail($globalSPJ,$prod_ket){

$tgl       = date('d-m-Y');
//$message1	   = new PHPMailer();

//$to        = "<EMAIL>,<EMAIL>,<EMAIL>";
$user = new User_SP('3000');
$admin_trans = $user->get_admin_trans();
foreach($admin_trans as $at){
	if(!empty($at['ALAMAT_EMAIL'])){
		if(empty($to)){
			$to = $at['ALAMAT_EMAIL'];
		}else{
			$to .= ','.$at['ALAMAT_EMAIL'];
		}
	}
}

//$to        =  "<EMAIL>, <EMAIL>";//"<EMAIL>";
$subject   = "Report Recalculate Shipment Cost per Tanggal : $tgl '$prod_ket' ";

$message1 = "The mail message was sent with the following mail setting:\r\nSMTP = relay.sig.id\r\nsmtp_port = 25"; //tambahan
$message1  = "<html>";
$message1 .= "<h3>Berikut laporan hasil recalculate per tanggal  $tgl $prod_ket:</h3>";
$message1 .= "<center><h4>SUCCESSED RECALCULATE</h4></center>";
$message1 .= "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
$message1 .= "
             <tr style = 'background-color:#4CAF50;color:white;'>
             <th style='width:5%;'>No</th>
             <th style='width:25%;'>SPJ</th>
             <th style='width:70%;'>Keterangan</th>
             </tr>
             ";
$no= 0;
foreach($globalSPJ as $SPJ=>$val){
if($val['MSG']=='S'){
$no++;
$message1 .= "
             <tr>
             <td >$no</td>
             <td >$SPJ</td>
             <td >".$val['KET']."</td>
             </tr>
             ";

    }
}

if($no < 1){
$message1 .= "
              <tr style='background-color:red;'>
              <td colspan='3' align='center' style='color:white;'>
              No Data Success Recalculated !
              </td>
              </tr>
             ";
}else{

$message1 .= "
              <tr style='background-color:yellow;'>
              <td colspan='2'>
              Total Succes Recalculated
              </td>
              <td>
              <b>$no SPJ</b>
              </td>
              </tr>
             ";
}

$message1 .= "</table>";
$message1 .= "</br>";
$message1 .= "<center><h4>FAILED RECALCULATE</h4></center>";
$message1 .= "<table table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
$message1 .= "
             <tr style = 'background-color:#4CAF50;color:white;'>
             <th style='width:5%;'>No</th>
             <th style='width:25%;'>SPJ</th>
             <th style='width:70%;'>Keterangan</th>
             </tr>
             ";
$no1= 0;

//echo '<pre>';
//print_r($globalSPJ);
//echo '</pre>';

foreach($globalSPJ as $SPJ=>$val){
if($val['MSG']!='S'){
$no1++;
$message1 .= "
             <tr>
             <td>$no1</td>
             <td>$SPJ</td>
             <td>".$val['KET']."</td>
             </tr>
             ";

    }
}
if($no1 < 1){
$message1 .= "
              <tr style='background-color:red;'>
              <td colspan='3' align='center' style='color:white;'>
              No Data Fail Recalculated !
              </td>
              </tr>
             ";
}else{

$message1 .= "
              <tr style='background-color:yellow;'>
              <td colspan='2'>
              Total Fail Recalculated
              </td>
              <td >
              <b>$no1 SPJ</b>
              </td>
              </tr>
             ";
}
$message1 .= "</table>";
$message1 .= "</html>";

echo $message1;
//exit();

// Always set content-type when sending HTML email
$headers  = "MIME-Version: 1.0" . "\r\n";
$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";

// More headers
$headers .= 'From: <EMAIL>' . "\r\n"; // <EMAIL> 
//$headers .= 'Cc: <EMAIL>,<EMAIL>' . "\r\n";
// $headers .= 'Cc: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>' . "\r\n";
//$headers .= 'Cc: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>' . "\r\n";

//BCC
	//require_once('bcc_emailtekhnisi.php');
	//unset($toBCC);
	//foreach($arrayToBCC as $keybcc => $valBCC){
	//	$toBCC .=$valBCC.",";
	//}
	//$toBCC=rtrim($toBCC,",");
	//$headers .= "Bcc:".$toBCC."\r\n";

//echo $to;
//echo $subject;
//echo $message1;
//echo $headers; 
$send1=mail($to,$subject,$message1,$headers); //$send1

if (($send1 )){
		echo "Sending Mail Success";

	} else {
		echo "Sending Mail Failed";
	}

 $response['jml_sukses'] = $no;
 $response['jml_fail']   = $no1;
 return $response;

}
//}


?>

