<?php
// include('../../include/or_fungsi.php'); 

    class mappingPlantRencanaKirim
    {
		private $or_fungsi;
		private $conn;
		private $logfile;
		public $msg;
		public $dataFuncRFC;
		private $status;
        
        function __construct() {
			$this->status = 'SUKSES';
			$this->fungsi = new or_fungsi();
			$this->conn = $this->fungsi->or_koneksi();
			// $this->dummyData = []; // inisialisasi data dummy
			// $this->logfile = fopen(dirname(__FILE__).'/../log/'.get_class($this).'.log','a+');
		}

        function saveLog() {
			$this->msg = substr($this->msg, 0, 900);
			$sqllog = "INSERT INTO RFC_LOG VALUES ('MAPPING_PLANT_RENCANA_KIRIM_NEW',SYSDATE,'" . $this->msg . "')";
			$querylog = oci_parse($this->conn, $sqllog);
			if ($querylog) {
				$execlog = @oci_execute($querylog);
			}
			//set running down
			$sqlset_run = "UPDATE RFC_LIST_FUNCTION SET RFC_IS_RUNNING = 0,RFC_LOG = '" . $this->msg . "',RFC_STATUS = '" . $this->status . "' WHERE RFC_ID = '" . $this->dataFuncRFC['RFC_ID'] . "'";
			$query_run = oci_parse($this->conn, $sqlset_run);
			oci_execute($query_run);
			//end set
		}

        function setAutoRencanaKirimPO($ORG, $PLANT, $MATERIAL, $UNIT, $VALID_FROM, $VALID_TO, $NO_PO, $SISA_PO) {
			// Cek apakah data sudah ada
			$sqlCheck = "SELECT COUNT(*) AS JML FROM M_SETAUTO_RENCANA_KIRIM_PO 
						 WHERE ORG = :ORG AND PLANT = :PLANT AND MATERIAL = :MATERIAL AND NO_PO = :NO_PO";
		
			$stmtCheck = oci_parse($this->conn, $sqlCheck);
			oci_bind_by_name($stmtCheck, ':ORG', $ORG);
			oci_bind_by_name($stmtCheck, ':PLANT', $PLANT);
			oci_bind_by_name($stmtCheck, ':MATERIAL', $MATERIAL);
			oci_bind_by_name($stmtCheck, ':NO_PO', $NO_PO);
			oci_execute($stmtCheck);
		
			$row = oci_fetch_assoc($stmtCheck);
		
			if ($row['JML'] > 0) {
				// Data sudah ada, lakukan update
				$sqlUpdate = "UPDATE M_SETAUTO_RENCANA_KIRIM_PO SET 
								UNIT = :UNIT, 
								VALID_FROM = TO_DATE(:VALID_FROM, 'YYYY-MM-DD HH24:MI:SS'), 
								VALID_TO = TO_DATE(:VALID_TO, 'YYYY-MM-DD HH24:MI:SS'), 
								STATUS_PO = 0, 
								SISA_PO = :SISA_PO,
								DELETE_MARK = 0,
								UPDATED_AT = SYSDATE,
								UPDATED_BY = 'SCHEDULER'
							  WHERE ORG = :ORG AND PLANT = :PLANT AND MATERIAL = :MATERIAL AND NO_PO = :NO_PO";
		
				$stmtUpdate = oci_parse($this->conn, $sqlUpdate);
				oci_bind_by_name($stmtUpdate, ':UNIT', $UNIT);
				oci_bind_by_name($stmtUpdate, ':VALID_FROM', $VALID_FROM);
				oci_bind_by_name($stmtUpdate, ':VALID_TO', $VALID_TO);
				oci_bind_by_name($stmtUpdate, ':ORG', $ORG);
				oci_bind_by_name($stmtUpdate, ':PLANT', $PLANT);
				oci_bind_by_name($stmtUpdate, ':MATERIAL', $MATERIAL);
				oci_bind_by_name($stmtUpdate, ':SISA_PO', $SISA_PO);
				oci_bind_by_name($stmtUpdate, ':NO_PO', $NO_PO);
				oci_execute($stmtUpdate);
			} else {
				// Data belum ada, lakukan insert
				$sqlInsert = "INSERT INTO M_SETAUTO_RENCANA_KIRIM_PO 
							  (ORG, PLANT, MATERIAL, SATUAN, UNIT, VALID_FROM, VALID_TO, NO_PO, SISA_PO, STATUS_PO, DELETE_MARK, CREATED_AT, CREATED_BY) 
							  VALUES (
								:ORG, 
								:PLANT, 
								:MATERIAL, 
								'ZAK', 
								:UNIT, 
								TO_DATE(:VALID_FROM, 'YYYY-MM-DD HH24:MI:SS'), 
								TO_DATE(:VALID_TO, 'YYYY-MM-DD HH24:MI:SS'), 
								:NO_PO,  
								:SISA_PO,  
								0, 
								0,
								SYSDATE,
								'SCHEDULER')";
		
				$stmtInsert = oci_parse($this->conn, $sqlInsert);
				oci_bind_by_name($stmtInsert, ':ORG', $ORG);
				oci_bind_by_name($stmtInsert, ':PLANT', $PLANT);
				oci_bind_by_name($stmtInsert, ':MATERIAL', $MATERIAL);
				oci_bind_by_name($stmtInsert, ':UNIT', $UNIT);
				oci_bind_by_name($stmtInsert, ':VALID_FROM', $VALID_FROM);
				oci_bind_by_name($stmtInsert, ':VALID_TO', $VALID_TO);
				oci_bind_by_name($stmtInsert, ':NO_PO', $NO_PO);
				oci_bind_by_name($stmtInsert, ':SISA_PO', $SISA_PO);
				oci_execute($stmtInsert);
			}
		}

		function getKonversi($PLANT, $MATERIAL, $UNIT, $NO_PO){
			$sqlGetOrg = "SELECT ORG FROM OR_MAP_TOP_BRAND WHERE PLANT_ID = '$PLANT' AND ROWNUM <= 1 AND DELETE_MARK = 0";
            // echo "ini sqlGetOrg " . $sqlGetOrg;
			$stmtGetOrg = oci_parse($this->conn, $sqlGetOrg);

			oci_execute($stmtGetOrg);
        	$cekOrg = oci_fetch_assoc($stmtGetOrg);
        	$org = $cekOrg['ORG']; 

        	$strmat = "SELECT CAST(NTGEW AS float) as BERAT, A.* FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 A WHERE VKORG = '{$org}' AND MATNR = '$MATERIAL' AND WERKS = '$PLANT'";
        	// echo "<br>ini strmat " . $strmat;
			// echo $NO_PO;
			$stmtmat = oci_parse($this->conn, $strmat);
            oci_execute($stmtmat);
            $rowmat = oci_fetch_array($stmtmat, OCI_ASSOC);

            if ($rowmat['MEINS'] == "ZAK") {
				// echo "masuk sini 1";
                $qty =  $UNIT;
            } else {
				// echo "masuk sini 2";
                $qty = ( intval($UNIT) / $rowmat['BERAT'] ) * 1000;
            }
			// echo "<br>ini qty " . $qty;
            return $qty;
		}
		

		function run() {
			$this->msg .= "Start " . get_class($this) . "pada tanggal jam " . date('d-m-Y H:i:s') . ".<pre>";

            $datapp = array();

			unset($arraData);

			$sql_auto_delete = "UPDATE MAPPING_PLANT_RENCANA_KIRIM 
								SET DELETE_MARK = 1,
								UPDATED_AT = SYSDATE 
								WHERE
									DELETE_MARK = 0 
									AND TRUNC( CREATED_AT, 'MM' ) < TRUNC( SYSDATE, 'MM' )";
			$stmt_auto_delete = oci_parse($this->conn, $sql_auto_delete);
			oci_execute($stmt_auto_delete);

			// $createnow = date('2025-05-10');
			$createnow = date('Y-m-d');
			// echo $createnow;
            $sql = "SELECT * 
					FROM MAPPING_PLANT_RENCANA_KIRIM 
					WHERE DELETE_MARK = 0 
					ORDER BY GREATEST(CREATED_AT, UPDATED_AT) DESC";
			echo "ini = ".  $sql . "<br>";
			$stmt = oci_parse($this->conn, $sql);
			oci_execute($stmt);
			
			$result = array(); // gunakan sintaks array lama
			while ($row = oci_fetch_assoc($stmt)) {
				$results[] = $row; // Menyimpan NO_PO ke dalam array
			}

			if (empty($results)) {
				echo "Tidak ada data di pada tabel MAPPING_PLANT_RENCANA_KIRIM ";
				$this->msg .= "Tidak ada data di pada tabel MAPPING_PLANT_RENCANA_KIRIM<pre>";
				$this->saveLog();
			} else {
            foreach ($results as $row) {
                $org = $row['ORG'];
                $plant_asal = $row['PLANT_ASAL'];
                $plant_tujuan = $row['PLANT_TUJUAN'];
                $tgl_kirim = $row['TANGGAL_KIRIM'];
            }

            // OPENING Z_ZAPPSD_PO_OPEN
				$sap = new SAPConnection();
				$sap->Connect(dirname(__FILE__) . "/../../include/sapclasses/logon_data.conf");
				if ($sap->GetStatus() == SAPRFC_OK)
					$sap->Open();
					// echo $sap->PrintStatus();
				if ($sap->GetStatus() != SAPRFC_OK) {
					$this->msg .= $sap->PrintStatus();
					$this->status = 'GAGAL';
					$this->saveLog();
					return false;
				}

                $fce = $sap->NewFunction ("Z_ZAPPSD_PO_OPEN_NW");
				if ($fce == false ) { 
					$sap->PrintStatus(); 
					exit; 
				}

                //------------------- Set parameter export -------------------//
				$fce->I_BUKRS = $org;
				$fce->I_MODE = 'X';

                // $datenow = date('Ymd', strtotime('20250509'));
				$datenow = date('Ymd');
				// echo $datenow;
                $datelow=date('Ymd', strtotime($datenow. ' - 30 days'));
                $datehigh=date('Ymd', strtotime($datenow. ' + 30 days'));

				$fce->I_EINDT_FR = $datelow;
				$fce->I_EINDT_TO = $datehigh;
				
                $fce->I_WERKS = $plant_tujuan;
                $fce->I_RESWK = $plant_asal;

                //------------------- Panggil fungsi SAP -------------------//

				// echo "<pre>";
				// print_r($fce);
				// echo "</pre>";

				$fce->Call();

				if ($fce->GetStatus() == SAPRFC_OK ) {
					$fce->T_DATA->Reset();
					$s=0;
						
					while ( $fce->T_DATA->Next() ){ 
						$datadoc[$s]=$fce->T_DATA->row;
						$werks[$s]= $fce->T_DATA->row["WERKS"];
						$name1[$s] = $fce->T_DATA->row['NAME1'];
						$lgort[$s] = $fce->T_DATA->row['LGORT'];
						$lgobe[$s] = $fce->T_DATA->row['LGOBE'];
						$reswk[$s] = $fce->T_DATA->row['RESWK'];
						$name2[$s] = $fce->T_DATA->row['NAME2'];
						$ebeln[$s] = $fce->T_DATA->row['EBELN'];
						$ebelp[$s] = $fce->T_DATA->row['EBELP'];
						$matnr[$s] = $fce->T_DATA->row['MATNR'];
						$txz01[$s] = $fce->T_DATA->row['TXZ01'];
						$eindt[$s] = $fce->T_DATA->row['EINDT'];
						$bzirk[$s] = $fce->T_DATA->row['BZIRK'];
						$bztxt[$s] = $fce->T_DATA->row['BZTXT'];
						$menge[$s] = $fce->T_DATA->row['MENGE'];
						$meins[$s] = $fce->T_DATA->row['MEINS'];
						$menge_rel[$s] = $fce->T_DATA->row['MENGE_REL'];
						$ntgew[$s] = $fce->T_DATA->row['NTGEW'];
						$route[$s] = $fce->T_DATA->row['ROUTE'];
						$s++;
					}
						
					$total=count($datadoc);
                    // echo $total;
				} else {
					$fce->PrintStatus();
				}
				
				$fce->Close();	
				$sap->Close();
				//------------------- ENDING Z_ZAPPSD_PO_OPEN -------------------//
				
				for ($j = 0; $j < $total; $j++) {
					$ORG = $org;
					$PLANT = $werks[$j];
					$MATERIAL = $matnr[$j];
					$SATUAN = $meins[$j];
					$UNIT = $ntgew[$j];
					$UNIT = number_format($UNIT, 0, ',', '.');
					$VALID_FROM = date('Y-m-01 00:00:00');
					$VALID_TO = date('Y-m-t 00:00:00');
					$NO_PO = $ebeln[$j];
					$SISA_PO = $menge[$j] - $menge_rel[$j];

					if ($SATUAN == "ZAK") {
						// echo "UNIT" . $UNIT;
						$UNIT_KONVERSI =  $UNIT;
					} else {
						$UNIT_KONVERSI = ( intval($UNIT) / $rowmat['BERAT'] ) * 1000;
					}
					
					if ($ORG != '' && $PLANT != '' && $MATERIAL != '' && $UNIT_KONVERSI != '' && $NO_PO != '' && $VALID_FROM != '' && $VALID_TO != '') {
						$this->setAutoRencanaKirimPO($ORG, $PLANT, $MATERIAL, $UNIT_KONVERSI, $VALID_FROM, $VALID_TO, $NO_PO, $SISA_PO);
						$this->msg .= "Data PO : " . $NO_PO . "<br>";
						echo "SUKSES ";
						$this->status = 'SUKSES';
						$this->saveLog();
					} else {
						$this->msg .= "Data PO tidak lengkap.<br>";
						echo "GAGAL";
						$this->status = 'GAGAL';
						$this->saveLog();
					}
				}
			}
        }
    }

?>