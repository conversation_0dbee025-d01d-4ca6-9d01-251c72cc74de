<? 
ob_start();
session_start();
include ('../include/ex_fungsi.php');
require_once ('../pgr_sanitizer.php');
$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

//include ('../../../../prod/sd/sdonline/include/ex_fungsi.php');//prod
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
#netest

//$halaman_id=1633;//dev
//$halaman_id=3113;//prod
$dirr = $_SERVER['PHP_SELF'];    
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);
$user_id=$_SESSION['user_id'];
$user_org = $_SESSION['user_org'];
$vendor_id = $_SESSION['vendor_id'];
$vendor=$_SESSION['vendor_id'];
$nama_vendor=$_SESSION['nama_lengkap'];




/*$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca=$fungsi->ex_hanya_baca($vendor);
if (isset($_POST['vendor']))
$vendor = $_POST['vendor'];*/
//added by rotyyu
$vendor = $vendor_id;
//if($user_org=='2000' || $user_org=='7000'){
//    if($user_org=='2000'){
//        $user_orgcd='7000';
//    }else{
//        $user_orgcd='2000';
//    }
//    $orgIn = "'".$user_org."','".$user_orgcd."'";
//}else{
//    $orgIn = "'".$user_org."'";
//}
$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $inorg= rtrim($inorg, ',');        
}else{
   $inorg= $user_org;
}

$reportexel = $_POST['reportexel'];                
if($reportexel=='report'){
    
    //echo 'tekan data';
    //================================ eksport data ================================
      include('../Spreadsheet/Excel/Writer.php');
      $todate=date("Ymd"); 
      $xls =new Spreadsheet_Excel_Writer();
      $titt="LaporanRekapAll_".$todate.".xls";
      $xls->send($titt);
    //======================================================================================================
} 
include ('../include/validasi.php');

function tglIndo ($param){
    if($param!='' && $param!='00000000'){
        $tahun=substr($param, 0,4);
        $bulan=substr($param, 4,2);
        $tgl=substr($param, 6,2);
        $format =$tgl."-".$bulan."-".$tahun;
    }else{
        $format='--';
    }
    return $format;
}

$page="rekap_claim_all_1.php";
$no_shipment = trim($_POST['no_shipment']);
//$distributor = trim($_POST['distributor']);
$distributor = trim($_POST['sold_to']);
$no_pajak=trim($_POST['nopajak']);
$tipe_transaksi = trim($_POST['tipe_transaksi']);
$tanggal_mulai = trim($_POST['tanggal_mulai']);
$tanggal_selesai = trim($_POST['tanggal_selesai']);
if($tanggal_mulai=='') $tanggal_mulai = date("d-m-Y");
if($tanggal_selesai=='') $tanggal_selesai = date("d-m-Y"); 
$warna_plat = trim($_POST['warna_plat']);
$nopol = trim($_POST['nopol']);
$status = trim($_POST['status']);
$status2 = trim($_POST['status2']);
$kapal = trim($_POST['kapal']);
$plantcf = trim($_POST['plantcf']);
$ekspeditur=trim($_POST['vendor']); 
$namekspeditur=trim($_POST['nama_vendor']);

$currentPage="rekap_claim_all_1.php";
$komen="";
if(isset($_POST['cari'])){
if($tanggal_mulai!='' && $tanggal_selesai!=''){
		$pakeor=0;
		$sql= "
                        WITH Latest_Log_Semen_Pod AS ( SELECT az.*, ROW_NUMBER ( ) OVER ( PARTITION BY az.NO_SPJ ORDER BY az.LAST_UPDATE_DATE DESC ) AS row_num FROM LOG_SEMEN_POD az ) SELECT
a.*,
b.NO_DOC_HDR AS NO_DOC_HDR1,
b.tahun,
x.tgl_terima,
c.accounting_doc 
FROM
	(
	SELECT
		ay.ID,
		ay.PLANT,
		ay.TIPE_DO,
		ay.NO_SHP_TRN,
		ay.ORG,
		ay.NO_POL,
		ay.VEHICLE_TYPE,
		ay.WARNA_PLAT,
		ay.SUPIR,
		ay.VENDOR,
		ay.NAMA_VENDOR,
		ay.KODE_PRODUK,
		ay.NAMA_PRODUK,
		ay.NO_ENTRY_SHEET,
		ay.SAL_OFFICE,
		ay.NAMA_SAL_OFF,
		ay.SATUAN_SHP,
		ay.QTY_SHP,
		ay.TARIF_COST,
		ay.SHP_COST,
		ay.NO_SO,
		ay.INCO,
		ay.SOLD_TO,
		ay.NAMA_SOLD_TO,
		ay.SHIP_TO,
		ay.NAMA_SHIP_TO,
		ay.ALAMAT_SHIP_TO,
		ay.SAL_DISTRIK,
		ay.NAMA_SAL_DIS,
		ay.KODE_KECAMATAN,
		ay.NAMA_KECAMATAN,
		ay.NO_INV_VENDOR,
		ay.NO_INV_SAP,
		ay.ACCOUNTING_DOC,
		TO_CHAR( ay.TANGGAL_KIRIM, 'DD-MM-YYYY' ) AS TANGGAL_KIRIMF,
		TO_CHAR( ay.TANGGAL_DATANG, 'DD-MM-YYYY' ) AS TANGGAL_DATANGF,
		TO_CHAR( ay.TANGGAL_BONGKAR, 'DD-MM-YYYY' ) AS TANGGAL_BONGKARF,
		TO_CHAR( ay.TANGGAL_INVOICE, 'DD-MM-YYYY' ) AS TANGGAL_INVOICEF,
		TO_CHAR( ay.LAST_UPDATE_DATE, 'DD-MM-YYYY' ) AS LAST_UPDATE_DATEF,
		TO_CHAR( ay.TGL_CLEARING, 'DD-MM-YYYY' ) AS TGL_CLEARINGF,
		ay.QTY_KTG_RUSAK,
		ay.QTY_SEMEN_RUSAK,
		ay.TOTAL_KTG_RUSAK,
		ay.TOTAL_KTG_REZAK,
		ay.TOTAL_SEMEN_RUSAK,
		ay.TOTAL_KLAIM_KTG,
		ay.TOTAL_KLAIM_SEMEN,
		ay.HARGA_TEBUS,
		ay.PDPKS,
		ay.TOTAL_KLAIM_ALL,
		ay.PENGELOLA,
		ay.NAMA_PENGELOLA,
		ay.NAMA_KAPAL,
		ay.STATUS,
		ay.STATUS2,
		ay.NO_PAJAK_EX,
		TO_CHAR( ay.TANGGAL_SIAP_TAGIH, 'DD-MM-YYYY' ) AS TANGGAL_SIAP_TAGIH,
		CONCAT( ay.SOLD_TO, TO_CHAR( ay.TGL_CLEARING, 'MMYYYY' ) ) AS NOMORFB,
		ay.NO_INVOICE,
		ay.FLAG_POD,
		ay.KETERANGAN_POD,
		ay.EVIDENCE_POD1,
		ay.EVIDENCE_POD2,
		ay.GEOFENCE_POD,
		az.NO_SPJ,
		az.TANGGAL_SPJ,
		az.ORG AS ORG_LOG,
		az.FLAG_POD AS LOG_FLAG_POD,
		az.KETERANGAN_POD AS LOG_KETERANGAN_POD,
		az.EVIDENCE_POD1 AS LOG_EVIDENCE_POD1,
		az.EVIDENCE_POD2 AS LOG_EVIDENCE_POD2,
		az.GEOFENCE_POD AS LOG_GEOFENCE_POD 
	FROM
		EX_TRANS_HDR ay
		LEFT JOIN Latest_Log_Semen_Pod az ON ay.NO_SHP_TRN = az.NO_SPJ 
		AND az.row_num = 1 
	WHERE
		ay.DELETE_MARK = '0' 
                    ";
		if($no_shipment!=""){
                    $sql.=" AND ay.NO_SHP_TRN LIKE '$no_shipment' ";
		}
		if($distributor!=""){
			$sql.=" AND ( ay.SOLD_TO LIKE '$distributor' OR ay.NAMA_SOLD_TO LIKE '$distributor' ) ";			
		}
//		if($vendor!=""){
//			$sql.=" AND ( NAMA_VENDOR LIKE '$vendor' OR VENDOR LIKE '$vendor' ) ";			
//		}
                if($ekspeditur!=""){
			$sql.=" AND ( ay.NAMA_VENDOR LIKE '$ekspeditur' OR ay.VENDOR LIKE '$ekspeditur' ) ";			
		}
		if($tipe_transaksi!=""){
			$sql.=" AND ay.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";			
		}
                if($plantcf!=""){
			$sql.=" AND ay.PLANT LIKE '$plantcf' ";			
		}
                
		if($tanggal_mulai!="" or tanggal_selesai!=""){
			if ($tanggal_mulai=="")$tanggal_mulai_sql = "01-01-1990";
			else $tanggal_mulai_sql = $tanggal_mulai;
			if ($tanggal_selesai=="")$tanggal_selesai_sql = "12-12-9999";
			else $tanggal_selesai_sql = $tanggal_selesai;
			$sql.=" AND ay.TANGGAL_KIRIM BETWEEN TO_Date('$tanggal_mulai_sql', 'DD-MM-YYYY') AND TO_Date('$tanggal_selesai_sql', 'DD-MM-YYYY') ";			
		}
		if($warna_plat!=""){
			$sql.=" AND ay.WARNA_PLAT LIKE '$warna_plat' ";			
		}
		if($status!=""){
			$sql.=" AND ay.STATUS = '$status' ";			
		}
		if($status2!=""){
			$sql.=" AND ay.STATUS2 = '$status2' ";
		}
		if($nopol!=""){			
			$sql.=" AND ay.NO_POL LIKE '$nopol' ";			
		}
		if($kapal!=""){
			$sql.=" AND ay.NAMA_KAPAL LIKE '$kapal' ";
		}
                if($no_pajak!=""){
			$sql.=" AND ay.NO_PAJAK_EX LIKE '$no_pajak' ";
		}
                $sql.=" AND ay.ORG in ($inorg)";
                
                $sql .=")
                  a left join (
                select CONCAT(SOLD_TO, concat(BULAN, TAHUN)) as FB60_DOC_NO,TAHUN,NO_DOC_HDR
                from EX_FB60 where DELETE_MARK= '0' and ORG IN ($inorg)
                group by CONCAT(SOLD_TO, concat(BULAN, TAHUN)),TAHUN,NO_DOC_HDR
              
                ) b
                ON (b.FB60_DOC_NO = a.NOMORFB)";
                $sql .=" LEFT JOIN (SELECT * FROM EX_INVOICE WHERE DELETE_MARK = '0' AND ORG IN ($inorg)) c ON (a.no_invoice = c.no_invoice)
                    LEFT JOIN (SELECT * FROM KPI_TERIMA_INV_VENDOR WHERE no_invoice IS NOT NULL AND ORG IN ($inorg) AND del = '0') x ON (a.no_invoice = x.no_invoice)
                    ORDER BY a.VENDOR, a.NO_SHP_TRN ASC";
	
    //  echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);
  $total_v = 0;
  unset($dataAll);
	while($row=oci_fetch_array($query)){
		$dataAll[]=$row;
                if($row[NO_DOC_HDR1]!=''){
                    $datavoicesap[$row[ORG].$row[NO_DOC_HDR1].$row[TAHUN]]=$row[ORG]."|".$row[NO_DOC_HDR1]."|".$row[TAHUN];
                }
//                echo "<pre>";
 //               print_r($dataAll);
 //               echo "<pre>";
  }
  
	$total=count($dataAll);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";
        
        if($total>0 && count($datavoicesap)>0){
        
                //Koneksi SAP
                //$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210_clone_sd.php"; 
                $sap = new SAPConnection();
                $sap->Connect("../include/sapclasses/logon_data.conf");
                //$sap->Connect($link_koneksi_sap);
                if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
                         if ($sap->GetStatus() != SAPRFC_OK ) {
                         echo $sap->PrintStatus();
                         exit;
                 }

                //Pemanggilan RFC Cari
                $fce = $sap->NewFunction ("Z_ZAPPSD_STATUS_INVOICE");
                if ($fce == false ) { $sap->PrintStatus(); exit; }

                foreach ($datavoicesap as $key => $value) {
                    $aarayDocv=explode("|",$value);
                    $ORGdocv=$aarayDocv[0];
                    $NO_DOC_HDR1dov=$aarayDocv[1];
                    $TAHUNdocv=$aarayDocv[2];
                    if($NO_DOC_HDR1dov!='' && $ORGdocv!='' && $TAHUNdocv!=''){
                        $fce->I_INPUT->row["BUKRS"] = $ORGdocv;
                        $fce->I_INPUT->row["BELNR"] = $NO_DOC_HDR1dov;
                        $fce->I_INPUT->row["GJAHR"] = $TAHUNdocv;
                        $fce->I_INPUT->Append($fce->I_INPUT->row);
                    }
                }
                $fce->Call();
                if ($fce->GetStatus() == SAPRFC_OK ) {		
                    $fce->T_OUT->Reset();
                    $s=0;
                    unset($datadoc);
                    while ( $fce->T_OUT->Next() ){ 
                            $datadoc[$fce->T_OUT->row['BUKRS'].$fce->T_OUT->row['BELNR'].$fce->T_OUT->row['GJAHR']] = $fce->T_OUT->row;      
                        $s++;
                    }
                }else
                    $fce->PrintStatus();
                    $fce->Close();	
                    $sap->Close();
                    
                    
                 
        }
}else{
    $komen = "Periode Shipment harus diisi";
}    

}

//echo "<pre>";
//print_r($dataAll);
//echo "</pre>";


?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim All:)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<?
if($reportexel==''){
?>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<?}?>
</head>
<script> 
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9...!");
			 return false;
			 }
		  }
	 } 
   }

function IsNumeric2(obj,volIndex)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789.";
   var strChar;
   var strString = obj.value;
   var valVolIndex = volIndex;
   //alert ("dsda "+valVolIndex);
   if (strString.length == 0){
        alert("Harus Diisi Angka..!!!");
	 return false;
//	} else if (strString > valVolIndex){
//            alert("Adjusmant tidak boleh lebih dari Indexnya ..!!!");
//            return false;            
        }else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9...!");
			 return false;
			 }
		  }
	 } 
   }
function findvendor() {	
        var com = document.getElementById("org");
	var strURL="../or_laporan/cari_vendor.php?org="+com.value;
	popUp(strURL);
}
function ketik_vendor(obj) {
    var com = document.getElementById("org");
	var com_nama=document.getElementById('nama_vendor');						
	com_nama.value = "";
	var strURL="../or_laporan/ketik_vendor.php?org="+com.value+"&vendor="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('vendordiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function ketik_vendor2(obj) {
    var com = document.getElementById("org");
	var com_nama=document.getElementById('nama_vendor');						
	com_nama.value = "";
	var strURL="../or_laporan/ketik_vendor.php?org="+com.value+"&vendor="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('vendordiv').innerHTML=req.responseText;
                                        var com_vendor = document.getElementById("vendor");
                                       var com_btn_vendor = document.getElementById("btn_vendor");
                                       com_vendor.readOnly = true;                                      
                                       com_btn_vendor.style.display="none";
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function finddistr(org) {
		var com_org = document.getElementById('org');		
		var strURL="cari_distr.php?org="+com_org.value;
		popUp(strURL);
		} 
		  
function ketik_distr(obj) {
	var com_org = document.getElementById('org');		
	var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() { 
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {	
					document.getElementById("distrdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function cekTgl(){
    var a = document.getElementById("Tanggal Mulai").value;
    var b = document.getElementById("Tanggal Selesai").value;
    var explod = a.split('-');
    var explod2 = b.split('-');
    var tgl = new Date();
    var tgl_a = tgl.setFullYear(explod[2],explod[1],explod[0]);
    var tgl_b = tgl.setFullYear(explod2[2],explod2[1],explod2[0]);
    var milisecond = 60*60*24*1000;
    var c = ((tgl_b-tgl_a)/ milisecond);
        if(c>7){
            alert('Maaf, tanggal maksimal 7 hari :)');
            return false;
        }
}
</script> 
<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Report Cost Claim All  </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Cost Claim All </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td><input name="org" type="hidden" id="org" value="<?=$user_org?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No SPJ </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_shipment" name="no_shipment" value="<?=$no_shipment?>"/></td>
    </tr>
    <tr>
      <td width="13%"  class="puso">No Pajak Expeditur </td>
      <td width="1%"  class="puso">:</td>
      <td ><input type="text" id="nopajak" name="nopajak"  value="<?=$no_pajak?>" /></td>
    </tr>
      <td  class="puso">Plant</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="plantcf" name="plantcf"  value="<?=$plantcf?>" /></td>
    </tr>  
<!--    <tr>
      <td  class="puso">Distributor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="distributor" name="distributor"  value="<?=$distributor?>" /></td>
    </tr>-->
    <tr>
      <td  class="puso">Distributor </td>
      <td  class="puso">:</td>
      <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="distrdiv">
	  <input name="sold_to" id="sold_to" type="text" class="inputlabel" size="10" maxlength="10" value="" onChange="ketik_distr(this)"/>
	  <input name="nama_sold_to" id="nama_sold_to" class="inputlabel" type="text" size="30" value="" readonly="true"/>	    
	  <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/></div></td>
    </tr>
    <tr>
      <td  class="puso">Tipe Transaksi </td>
      <td  class="puso">:</td>
      <td ><select name="tipe_transaksi" id="tipe_transaksi">
        <option value="">---Pilih---</option>
        <? $fungsi->ex_tipe_trans($tipe_transaksi);?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Periode Shipment </td>
      <td  class="puso">:</td>
      <td ><input name="tanggal_mulai" type="text" id="Tanggal Mulai" <?=$hanyabaca?> required value="<?=$tanggal_mulai?>" />
        <input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Mulai');" value="..." /> &nbsp;&nbsp;&nbsp;
        s/d &nbsp;&nbsp;&nbsp;
        <input name="tanggal_selesai" type="text" id="Tanggal Selesai" <?=$hanyabaca?> required value="<?=$tanggal_selesai?>" />
        <input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Selesai');" value="..." />
		<span style="color:red;font-size: 15px;"> *</span>
		</td>
    </tr>
    <tr>
      <td  class="puso">Warna Plat </td>
      <td  class="puso">:</td>
      <td ><select name="warna_plat" id="warna plat">
          <option value="">---Pilih---</option>
          <? $fungsi->ex_warna_plat($warna_plat);?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">No Polisi </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="nopol" name="nopol"  value="<?=$nopol?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Nama Kapal </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="kapal" name="kapal"  value="<?=$kapal?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Ekspeditur </td>
      <td  class="puso">:</td>
      <td >
            <?if($vendor!=''){
            ?>    
             <div id="vendordiv">
                  <input name="vendor" type="text"  id="vendor" class="inputlabel" value="<?=$vendor;?>" maxlength="10" size="10" onChange="ketik_vendor2(this)" readonly="true"/>
                  <input name="nama_vendor" type="text"  id="nama_vendor" class="inputlabel" value="" readonly="true"  size="30"/>
              </div>    
            <?          
                echo '<script language="JavaScript" type="text/javascript">
                       var com_vendor = document.getElementById("vendor");
                       com_vendor.onchange();
                  </script>';
            }else{
            ?>
               <div id="vendordiv">
                  <input name="vendor" type="text"  id="vendor" class="inputlabel" value="<?=$ekspeditur;?>" onChange="ketik_vendor(this);" maxlength="10" size="10"/>
                  <input name="nama_vendor" type="text"  id="nama_vendor" class="inputlabel" value="<?=$namekspeditur;?>" readonly="true"  size="30"/>
                  <input name="btn_vendor" type="button" class="button" id="btn_vendor" value="..." onClick="findvendor()"/>
                </div>
          <?}?>
      </td>
    </tr>  
    <tr>
      <td  class="puso">Status</td>
      <td  class="puso">:</td>
      <td ><select name="status" id="status">
        <option value="">---Pilih---</option>
        <? $fungsi->ex_status($status);?>
      </select></td>
    </tr>
    <tr>
      <td  class="puso">Status2</td>
      <td  class="puso">:</td>
      <td ><select name="status2" id="status2">
        <option value="">---Pilih---</option>
        <? $fungsi->ex_status($status2);?>
      </select></td>
    </tr>
    <tr>
          <td  class="puso">&nbsp;</td>
          <td  class="puso">&nbsp;</td>
          <td>
              <input type="checkbox" name="reportexel" value="report">Report ke Excel 
          </td>
    </tr>  
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" onclick="return cekTgl();" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
if($total>0){
?>
        <?if($reportexel=='') { ?>  
	<div align="center">
	<table width="100%" align="center" class="adminlist">
	<tr>
	<th align="left" width="100%"><span class="style5">&nbsp;Rekap Data Cost Claim All </span></th>
	</tr>
	</table>
	</div> 
        <?}?>
	<div align="center" >
	<table width="100%" align="center" class="adminlist" id="myScrollTable">
	<thead>
	  <tr class="quote">
		 <td ><strong>&nbsp;&nbsp;No.</strong></td>
		 <td align="center"><strong>Plant</strong></td>
		 <td align="center"><strong >Tipe SPJ</strong></td>
		 <td align="center"><strong >SPJ</strong></td>
		 <td align="center"><strong>Org</strong></td>
		 <td align="center"><strong>No Polisi</strong></td>
		 <td align="center"><strong>Tipe</strong></td>
		 <td align="center"><strong>Plat</strong></td>
		 <td align="center"><strong>Sopir</strong></td>
		 <td align="center"><strong>Expeditur</strong></td>
		 <td align="center"><strong>Nama Expeditur</strong></td>
		 <td align="center"><strong>Tipe Semen</strong></td>
		 <td align="center"><strong>Nama Tipe Semen</strong></td>
		 <td align="center"><strong>Entri Sheet</strong></td>
		 <td align="center"><strong>Prop</strong></td>
		 <td align="center"><strong>Nama Prop</strong></td>
		 <td align="center"><strong>Satuan</strong></td>
                 <td align="center"><strong>Qty</strong></td>
                 <td align="center"><strong>Tarif</strong></td>
                 <td align="center"><strong>Total</strong></td>
                 <td align="center"><strong>SO</strong></td>
                 <td align="center"><strong>Incoterm</strong></td>
                 <td align="center"><strong>Dist</strong></td>
                 <td align="center"><strong>Nama Distributor</strong></td>
                 <td align="center"><strong>Ship To</strong></td>
                 <td align="center"><strong>Nama Ship To</strong></td>
                 <td align="center"><strong>Alamat</strong></td>
                 <td align="center"><strong>Distrik</strong></td>
                 <td align="center"><strong>Nama Distrik</strong></td>
                 <td align="center"><strong>Kecamatan</strong></td>
                 <td align="center"><strong>Nama Kecamatan</strong></td>
                 <td align="center"><strong>Rekap Tag</strong></td>
                 <td align="center"><strong>No Invoice</strong></td>
                 <td align="center"><strong>No PPL 1</strong></td>
                 <td align="center"><strong>No PPL 2</strong></td>
                 <td align="center"><strong>Tgl Kirim</strong></td>
                 <td align="center"><strong>Tgl Siap Tagih</strong></td>
                 <td align="center"><strong>Tgl Datang</strong></td>
                 <td align="center"><strong>Tgl Bongkar</strong></td>
                 <td align="center"><strong>Rekap</strong></td>
                 <td align="center"><strong>PPL</strong></td>
                 <td align="center"><strong>Tgl Clearing</strong></td>
                 <td align="center"><strong>Klaim Kantong</strong></td>
                 <td align="center"><strong>Klaim Semen</strong></td>
                 <td align="center"><strong>Rp. Kantong</strong></td>
                 <td align="center"><strong>Rp. Resak</strong></td>
                 <td align="center"><strong>Rp. Semen</strong></td>
                 <td align="center"><strong>Tot Kantong</strong></td>
                 <td align="center"><strong>Tot Semen</strong></td>
                 <td align="center"><strong>Harga Tebus</strong></td>
                 <td align="center"><strong>PDPPKS</strong></td>
                 <td align="center"><strong>Tot Klaim</strong></td>
                 <td align="center"><strong>Vendor</strong></td>
                 <td align="center"><strong>Nama Vendor</strong></td>
                 <td align="center"><strong>Nama Kapal</strong></td>
                 <td align="center"><strong>Status</strong></td>
                 <td align="center"><strong>Status2</strong></td>
                 <td align="center"><strong>No Pajak</strong></td>
                 <td align="center"><strong>Tgl Terima Distrans</strong></td>
                 <td align="center"><strong>Tgl Terima Verifikasi</strong></td>
                 <td align="center"><strong>Tgl Terima Bendahara</strong></td>
                 <td align="center"><strong>Tgl Jatuh Tempo</strong></td>
                 <td align="center"><strong>PoD</strong></td>
                 <td align="center"><strong>Keterangan</strong></td>
                 <td align="center"><strong>File PoD1</strong></td>
                 <td align="center"><strong>File PoD2</strong></td>
                 <td align="center"><strong>Geofence PoD</strong></td>
                 <td align="center"><strong>Status Sync PoD</strong></td>
      </tr >
	  </thead>
	  <tbody>
	  
  <?  //$tksmn = 0;
  	  //$tkktg = 0;
	  $tshp = 0;
	  $tclaim = 0;
	  $ttotal = 0;
	  $tqty = 0;
  		for($i=0; $i<$total;$i++) {
                    $datetime1 = date_format(date_create($datadoc[$com[$i].$NO_DOC_HDRv[$i].$TAHUNc[$i]]['TGL_BEND']),'Ymd');
                    $datetime2 = date_format(date_create(date("d-m-Y")),'Ymd');
                    $interval = floor(strtotime($datetime2) - strtotime($datetime1) ) / 86400 ;
                    if($interval>30 && $vendor==''){
                        $style = "background-color:red";
                    } else {
                        $style = "";
                    }

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0' id='$rowke'  style='$style' >";
			}
		else	{	
		echo "<tr class='row1'  id='$rowke'  style='$style' >";
      }

		?>     
		<td align="center"><?=$b; ?></td>
		<td align="center"><?=$dataAll[$i]['PLANT']; ?></td>
                <td align="center"><?=$dataAll[$i]['TIPE_DO']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_SHP_TRN']; ?></td>
                <td align="center"><?=$dataAll[$i]['ORG']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_POL']; ?></td>
                <td align="center"><?=$dataAll[$i]['VEHICLE_TYPE']; ?></td>
                <td align="center"><?=$dataAll[$i]['WARNA_PLAT']; ?></td>
                <td align="center"><?=$dataAll[$i]['SUPIR']; ?></td>
                <td align="center"><?=$dataAll[$i]['VENDOR']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_VENDOR']; ?></td>
                <td align="center"><?=$dataAll[$i]['KODE_PRODUK']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_PRODUK']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_ENTRY_SHEET']; ?></td>
                <td align="center"><?=$dataAll[$i]['SAL_OFFICE']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_SAL_OFF']; ?></td>
                <td align="center"><?=$dataAll[$i]['SATUAN_SHP']; ?></td>
                <td align="center"><?=$dataAll[$i]['QTY_SHP']; ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TARIF_COST'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['SHP_COST'],0,',','.'); ?></td>
                <td align="center"><?=$dataAll[$i]['NO_SO']; ?></td>
                <td align="center"><?=$dataAll[$i]['INCO']; ?></td>
                <td align="center"><?=$dataAll[$i]['SOLD_TO']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_SOLD_TO']; ?></td>
                <td align="center"><?=$dataAll[$i]['SHIP_TO']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_SHIP_TO']; ?></td>
                <td align="center"><?=$dataAll[$i]['ALAMAT_SHIP_TO']; ?></td>
                <td align="center"><?=$dataAll[$i]['SAL_DISTRIK']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_SAL_DIS']; ?></td>
                <td align="center"><?=$dataAll[$i]['KODE_KECAMATAN']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_KECAMATAN']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_INV_VENDOR']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_INVOICE']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_INV_SAP']; ?></td>
                <td align="center"><?=$dataAll[$i]['ACCOUNTING_DOC']; ?></td>
                <td align="center"><?=$dataAll[$i]['TANGGAL_KIRIMF']; ?></td>
                <td align="center"><?=$dataAll[$i]['TANGGAL_SIAP_TAGIH']; ?></td>
                <td align="center"><?=$dataAll[$i]['TANGGAL_DATANGF']; ?></td>
                <td align="center"><?=$dataAll[$i]['TANGGAL_BONGKARF']; ?></td>
                <td align="center"><?=$dataAll[$i]['TANGGAL_INVOICEF']; ?></td>
                <td align="center"><?=$dataAll[$i]['LAST_UPDATE_DATEF']; ?></td>
                <td align="center"><?=$dataAll[$i]['TGL_CLEARINGF']; ?></td>
                <td align="center"><?=number_format($dataAll[$i]['QTY_KTG_RUSAK'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['QTY_SEMEN_RUSAK'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TOTAL_KTG_RUSAK'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TOTAL_KTG_REZAK'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TOTAL_SEMEN_RUSAK'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TOTAL_KLAIM_KTG'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TOTAL_KLAIM_SEMEN'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['HARGA_TEBUS'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['PDPKS'],0,',','.'); ?></td>
                <td align="center"><?=number_format($dataAll[$i]['TOTAL_KLAIM_ALL'],0,',','.'); ?></td>
                <td align="center"><?=$dataAll[$i]['PENGELOLA']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_PENGELOLA']; ?></td>
                <td align="center"><?=$dataAll[$i]['NAMA_KAPAL']; ?></td>
                <td align="center"><?=$dataAll[$i]['STATUS']; ?></td>
                <td align="center"><?=$dataAll[$i]['STATUS2']; ?></td>
                <td align="center"><?=$dataAll[$i]['NO_PAJAK_EX']; ?></td>
                <td align="center">
                    <strong>
                        <?=$dataAll[$i]['TGL_TERIMA'];?>
                    </strong>
                </td>
                <td align="center">
                    <strong>
                        <?=tglIndo($datadoc[$com[$i].$NO_DOC_HDRv[$i].$TAHUNc[$i]]['TGL_VER']);?>
                    </strong>
                </td>
                <td align="center">
                    <strong>
                        <?=tglIndo($datadoc[$com[$i].$NO_DOC_HDRv[$i].$TAHUNc[$i]]['TGL_BEND']);?>
                    </strong>
                </td>
                <td align="center">
                    <strong>
                        <?=tglIndo($datadoc[$com[$i].$NO_DOC_HDRv[$i].$TAHUNc[$i]]['ZFBDT']);?>
                    </strong>
                </td>
                <td align="center">
                    <strong>
                        <? if($dataAll[$i]['FLAG_POD']!=''){echo $dataAll[$i]['FLAG_POD'];}else{echo $dataAll[$i]['LOG_FLAG_POD'];}?>
                    </strong>
                </td>
                <td align="center">
                    <strong>
                        <? if($dataAll[$i]['FLAG_POD']!=''){echo $dataAll[$i]['KETERANGAN_POD'];}else{echo $dataAll[$i]['LOG_KETERANGAN_POD'];}?>
                    </strong>
                </td>
                <td align="center">
                    <a href="<? if($dataAll[$i]['FLAG_POD']!=''){echo $dataAll[$i]['EVIDENCE_POD1'];}else{echo $dataAll[$i]['LOG_EVIDENCE_POD1'];}?> " target="_blank">
                        <? if( $dataAll[$i]['EVIDENCE_POD1'] != '' || $dataAll[$i]['LOG_EVIDENCE_POD1'] != '') echo 'Klik Disini'; ?>
                    </a>
                </td>
                <td align="center">
                    <a href="<? if($dataAll[$i]['FLAG_POD']!=''){echo $dataAll[$i]['EVIDENCE_POD2'];}else{echo $dataAll[$i]['LOG_EVIDENCE_POD2'];}?> " target="_blank">
                        <? if( $dataAll[$i]['EVIDENCE_POD2'] != ''|| $dataAll[$i]['LOG_EVIDENCE_POD2'] != '') echo 'Klik Disini'; ?>
                    </a>
                </td>
                <td align="center">
                    <strong>
                        <? if($dataAll[$i]['FLAG_POD']!=''){echo $dataAll[$i]['GEOFENCE_POD'];}else{echo $dataAll[$i]['LOG_GEOFENCE_POD'];} ?>
                    </strong>
                </td>
                <td align="left" <? if($dataAll[$i]['FLAG_POD']!=''){echo 'style="color: green;"';}else{ echo 'style="color: red;"';} ?>>
                    <strong >
                        <? if($dataAll[$i]['FLAG_POD']!=''){echo 'Sukses';}else{echo 'Belum Sinkron';} ?>
                    </strong>
                </td>
		</tr>
	  <? 
	  $tqty += $dataAll[$i]['QTY_SHP'];
	  $tshp += $dataAll[$i]['SHP_COST'];
	  $tclaim += $dataAll[$i]['TOTAL_KLAIM_ALL'];
	  } ?>
		</tbody>
          <?if($reportexel=='') { ?>  
	  <tr class="quote">
		<td colspan="17" align="center">
		TOTAL	 
                </td>
		<td align="center"><? echo number_format($tqty,0,",","."); ?></td>
                <td align="center"><? echo number_format($tshp,0,",","."); ?></td>
		<td align="center" colspan="31"></td>
		<td align="center"><? echo number_format($tclaim,0,",","."); ?></td>
		<td align="center" colspan="10"></td>
	    </tr>

	  <tr class="quote">
                <td colspan="61" align="center"><a href="<?=$currentPage;?>" target="isi" class="button">Back</a></td>
	  </tr>
          <?}?>  
	</table>
	</div>
<?}?>
<div align="center">
<?
echo $komen;
?>
</div>

<?if($reportexel=='') { ?>  
<p>&nbsp;</p>
<? if ($total> 40){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 900);
</script>
<?}?>

</p>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
	</script>
<?}?>
</body>
</html>
