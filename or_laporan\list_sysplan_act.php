<?php

session_start();
//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'itemid';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

//$ids = htmlspecialchars($_REQUEST['id']);
$VKORG = htmlspecialchars($_REQUEST['VKORG']);
$MATNR = htmlspecialchars($_REQUEST['MATNR']);
$WERKS = htmlspecialchars($_REQUEST['WERKS']);
$MAKTX = htmlspecialchars($_REQUEST['MAKTX']);
$MEINS= htmlspecialchars($_REQUEST['MEINS']);
$MSEHT = htmlspecialchars($_REQUEST['MSEHT']);
$VRKME = htmlspecialchars($_REQUEST['VRKME']);
$MSEHT2= htmlspecialchars($_REQUEST['MSEHT2']);
$NTGEW= htmlspecialchars($_REQUEST['NTGEW']);
$GEWEI= htmlspecialchars($_REQUEST['GEWEI']);
$MSEHT3= htmlspecialchars($_REQUEST['MSEHT3']);
$ERSDA= htmlspecialchars($_REQUEST['ERSDA']);
$LAEDA= htmlspecialchars($_REQUEST['LAEDA']);
/*echo "order : ".$order.'<br>';
echo "sort : ".$sort.'<br>';*/

if(isset ($aksi)){
//    if($aksi == 'show'){
//        displayData($conn,$sort,$order);
//        
//    }
    switch($aksi) { 
        case 'show' :
            {
            $sql = "
            SELECT
            *
            FROM
            RFC_Z_ZAPP_SELECT_SYSPLAN";
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            $result=array();
            $i=0;
            while($row=oci_fetch_array($query)){
                $result[$i]['XPARAM']   = $row['XPARAM'];
                $result[$i]['WERKS']    = $row['WERKS'];
                $result[$i]['NAME1']    = $row['NAME1'];
                $i++;
            }
                echo json_encode($result);
            }
        break;
        case 'add' :
            {
                $sql = "INSERT INTO RFC_Z_ZAPP_SELECT_SYSPLAN (XPARAM,WERKS,NAME1)
                        VALUES ('".$XPARAM."','".$WERKS."','".$NAME1."')";
                $query = oci_parse($conn, $sql);
                $result = oci_execute($query);
                            
                if ($result){
                    echo json_encode(array('success'=>true));
                } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
                }                
            }
        break;
        case 'delete' :
        {
           
               $sql = "DELETE FROM RFC_Z_ZAPP_SELECT_SYSPLAN where
                        WERKS   = '$WERKS'";

               $query = oci_parse($conn, $sql);
               $result = oci_execute($query);

               if ($result){
                    echo json_encode(array('success'=>true));
               } else {
                    echo json_encode(array('errorMsg'=>$sql));
               }
           
           
        }
        break;
        case 'update' :
        {
            $sql= "
            UPDATE RFC_Z_ZAPP_SELECT_SYSPLAN  set
            XPARAM   = '$XPARAM',
            WERKS   = '$WERKS',
            NAME1   = '$NAME1'
            
            where
            WERKS   = '$WERKS'";
            echo $sql;
            $query= oci_parse($conn, $sql);
            $result=oci_execute($query);
            
//            echo $sql;
            
            if ($result){
                    echo json_encode(array('success'=>true));
            } else {
                    echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
    }
    

    
    
}


?>
