<?php
include('../include/ex_fungsi.php');
require_once 'helper.php';

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$no_ba = $data->no_ba;
$org = $data->org;
$username_approver = $data->username_approver;
$id_approver = $id_approver;

$query_ba = "SELECT
EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL,
SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
FROM
EX_BA
JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
WHERE EX_BA.DELETE_MARK = '0' 
AND EX_BA.NO_BA = :no_ba
GROUP BY EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL
ORDER BY
EX_BA.ID DESC";
$sql_ba = oci_parse($conn, $query_ba);
oci_bind_by_name($sql_ba, ":no_ba", $no_ba);
oci_execute($sql_ba);

$data_ba = oci_fetch_array($sql_ba);
$status_ba = $data_ba['STATUS_BA'];
$no_ba = $data_ba['NO_BA'];
$user = new User_SP($org);

if($status_ba == 30){
    $value_spj = 'SETUJU';
    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
    $field_data = array("APPROVE KASIE", "SYSDATE", "$username_approver", "APPROVE KASIE");
    $tablename = "EX_TRANS_HDR";
    $field_id = array('NO_BA');
    $value_id = array("$no_ba");
    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
    $field_data = array("40", "SYSDATE", "$username_approver");
    $tablename = "EX_BA";
    $field_id = array('NO_BA');
    $value_id = array("$no_ba");
    $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

    // track
    $id_approver = '0' . $id_approver;
    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
    $field_data = array("$no_ba","40","Waiting Approval Pejabat Transportasi 2","$id_approver","SYSDATE");
    $tablename = "EX_BA_TRACK";
    $fungsi->insert($conn, $field_names, $field_data, $tablename);

    $org_v_ba = $data_ba['ORG'];
    $no_vendor_v_ba = $data_ba['NO_VENDOR'];
    $nama_vendor_v_ba = $data_ba['NAMA_VENDOR']; 
    $total_semen_v_ba = $data_ba['TOTAL_KLAIM_SEMEN'];
    $total_kantong_v_ba = $data_ba['TOTAL_KLAIM_KTG'];
    $total_ppdks_v_ba = $data_ba['PDPKS'];
    $total_inv_v_ba = $data_ba['SHP_COST'];

    $email_content_table = "
    <table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
    <div align=\"center\">
    <thead>
    <tr class=\"quote\">
    <td ><strong>&nbsp;&nbsp;No.</strong></td>
    <td align=\"center\"><strong>ORG</strong></td>
    <td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
    <td align=\"center\"><strong>EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
    <td align=\"center\"><strong>KLAIM KANTONG</strong></td>
    <td align=\"center\"><strong>PDPKS</strong></td>
    <td align=\"center\"><strong>TOTAL OA</strong></td>
    <td align=\"center\"><strong>STATUS</strong></td>
    </tr>
    </thead>
    <tbody>";

    $email_content_table .= " 
    <td align=\"center\">1</td>
    <td align=\"center\">".$org_v_ba."</td>       
    <td align=\"center\">".$no_ba."</td>
    <td align=\"center\">".$no_vendor_v_ba."</td>
    <td align=\"center\">".$nama_vendor_v_ba."</td>
    <td align=\"center\">".number_format($total_semen_v_ba,0,",",".")."</td>
    <td align=\"center\">".number_format($total_kantong_v_ba,0,",",".")."</td>
    <td align=\"center\">".number_format($total_ppdks_v_ba,0,",",".")."</td>
    <td align=\"center\">".number_format($total_inv_v_ba,2,",",".")."</td>
    <td align=\"center\">Waiting Approval Pejabat Transportasi 2</td>
    </tr>";

    //sendEmail
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];
    $mailCc = '';
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Notifikasi Approve Berita Acara Rekapitulasi', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table);
    }

    // sendmail ke KABIRO
    // dev
    // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Approval BA Rekapitulasi Kabiro' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
    // prod
    // $mailTo = "";
    // $data = array(
    //     "no_ba" => $no_ba,
    //     "org" => $org
    // );
    // $data = json_encode($data);
    // $approve_param = "approve_ba_kabiro||$data";
    // $approve_param_encode = base64_encode($approve_param);
    // $approve_link = get_base_url() . "ex_ba_sp/via_email.php?kode=$approve_param_encode";

    $kabiro = $user->get_kabiro();
    foreach($kabiro as $k){
        if(!empty($k['ALAMAT_EMAIL'])){
            $data = array(
                "no_ba" => $no_ba,
                "username_approver" => $k['NAMA'],
                "id_approver" => $k['ID'],
                "org" => $org
            );
            $data = json_encode($data);
            $approve_param = "approve_ba_kabiro||$data";
            $approve_param_encode = base64_encode($approve_param);
            $approve_link = get_base_url() . "ex_ba_sp/via_email.php?kode=$approve_param_encode";

            $mailTo = $k['ALAMAT_EMAIL'];
            sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve Berita Acara Rekapitulasi', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table, $approve_link);
        }
    }
    $show_ket = 'Dokumen BASTP berhasil disetujui';
}else if($status_ba >= 40){
    $show_ket = 'Dokumen BASTP sudah disetujui';
}else{
    $show_ket = 'Dokumen BASTP sedang tidak dalam status perlu persetujuan kasie';
}
?>