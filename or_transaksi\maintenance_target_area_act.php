<?php

session_start();

include ('../include/or_fungsi.php');
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

require_once('../MainPHPExcel/MainPHPExcel.php');
require_once '../ex_report/excel_reader2.php';

$result = array();
$user_id=$_SESSION['user_id'];

$aksi = htmlspecialchars($_REQUEST['act']);
$sort = isset($_POST['sort']) ? strval($_POST['sort']) : 'kode_region';
$order = isset($_POST['order']) ? strval($_POST['order']) : 'asc';

$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$region = htmlspecialchars($_REQUEST['region']);
$provinsi = htmlspecialchars($_REQUEST['provinsi']);
$area = htmlspecialchars($_REQUEST['area']);
$distrik = htmlspecialchars($_REQUEST['distrik']);
$distrik_ret = htmlspecialchars($_REQUEST['distrik_ret']);

$delete = htmlspecialchars($_REQUEST['delete']);
$id = htmlspecialchars($_REQUEST['id']);
$created_by = htmlspecialchars($user_name);
$UPDATE_BY = ($user_name) ? htmlspecialchars($user_name) : 'menu';


if(isset($aksi)){
    switch($aksi) {
        case 'upload_file' :
        {
            if($_FILES['file_upload']){
                $excelFile  = $_FILES['file_upload'];
                $uploadDir = 'template_xls/';
                $fileExtension = pathinfo($excelFile['name'], PATHINFO_EXTENSION);
                $allowedExtensions = array('xls', 'xlsx');

                if (!file_exists($excelFile['tmp_name']) || !is_dir($uploadDir)) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Please upload file"
                    ));
                    exit();
                }

                if (!is_readable($excelFile['tmp_name'])) {
                    header('Content-Type: application/json');
                    echo json_encode(array(
                        "status" => 500,
                        "message" => "Uploaded file is not readable."
                    ));
                    exit();
                }
                if (!in_array($fileExtension, $allowedExtensions)) {
                    header('Content-Type: application/json');
                    echo json_encode(
                        array(
                            "status" => 400,
                            "message" => "Invalid file type. Please upload an Excel file, not an $fileExtension file.",
                            "fileExtension" => $fileExtension
                        )
                    );
                    return;
                }
                // $data = readExcel($excelFile['tmp_name']);
                $data = array();
                try {
                    $cell = new Spreadsheet_Excel_Reader($excelFile['tmp_name']);
                    $totalRow = $cell->rowcount($sheet_index=0);
                    $lengthPopulatedColumn = 5;
                    for ($row = 3; $row <= $totalRow; $row++) {
                        for ($column = 1; $column <= $lengthPopulatedColumn; $column++) {                 
                            $data[$row][$column] = $cell->val($row, $column);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                // Variabel untuk menyimpan pesan hasil
                $messageRows = array(
                    'success' => array(),
                    'database' => array(),  // Untuk menyimpan baris duplikat di database
                    'incomplete' => array()  // Untuk menyimpan baris dengan data tidak lengkap
                );

                // Array untuk mendeteksi duplikasi di dalam file Excel
                $excelDuplicateCheck = array();

                foreach ($data as $rowNumber => $row) {
                    // Skip baris yang kosong
                    if (empty($row[1]) && empty($row[2]) && empty($row[3]) && empty($row[4])) {
                        continue;
                    }

                    // Periksa data kosong
                    if (empty($row[1]) || empty($row[2]) || empty($row[3]) || empty($row[4])) {
                        $messageRows['incomplete'][] = $rowNumber;
                        continue;
                    }

                    // Cek duplikasi di database
                    if (checkDuplicateData($conn, $row[1], $row[2], $row[3], $row[4])) {
                        if (update($conn, $row[1], $row[2], $row[3], $row[4], $row[5], $created_by)) {
                            $messageRows['successUpdate'][] = $rowNumber;
                        } else {
                            $messageRows['failedUpdate'][] = $rowNumber;
                        }
                        // $messageRows['database'][] = $rowNumber;
                        // continue;
                    }else {
                        // Jika tidak ada masalah, lakukan upload
                        if (insert($conn, $row[1], $row[2], $row[3], $row[4], $row[5], $created_by)) {
                            $messageRows['success'][] = $rowNumber;
                        } else {
                            $messageRows['system'][] = $rowNumber;
                        }
                    }

                }

                // Gabungkan hasil
                $resultMessage = "";

                // Notifikasi untuk baris yang sukses
                if (!empty($messageRows['success'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['success']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diinputkan. ";
                }
                
                // Notifikasi untuk baris yang sukses update
                if (!empty($messageRows['successUpdate'])) {
                    $adjustedSuccess = array_map('adjustRowNumber', $messageRows['successUpdate']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSuccess) . " berhasil diupdate. ";
                }

                // Notifikasi untuk baris duplikat di database
                if (!empty($messageRows['database'])) {
                    $adjustedDatabase = array_map('adjustRowNumber', $messageRows['database']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedDatabase) . " sudah ada di database. ";
                }

                // Notifikasi untuk baris dengan data tidak lengkap
                if (!empty($messageRows['incomplete'])) {
                    $adjustedIncomplete = array_map('adjustRowNumber', $messageRows['incomplete']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedIncomplete) . " memiliki data yang tidak lengkap. ";
                }

                // Notifikasi untuk baris yang gagal karena kesalahan sistem
                if (!empty($messageRows['system'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['system']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diinputkan karena kesalahan sistem. ";
                }
                
                // Notifikasi untuk baris yang gagal update karena kesalahan sistem
                if (!empty($messageRows['failedUpdate'])) {
                    $adjustedSystem = array_map('adjustRowNumber', $messageRows['failedUpdate']);
                    $resultMessage .= "Baris ke-" . implode(',', $adjustedSystem) . " gagal diupdate karena kesalahan sistem. ";
                }

                // Kirim hasil sebagai JSON
                echo json_encode(array('success' => true, 'data' => $resultMessage));
            }
        }
        break;        
        case 'show' :
        {
            displayData($conn,$sort,$order);
        }
        break;
        case 'add':
        {
            if (checkDuplicateData($conn,$region,$provinsi,$area,$distrik)) {
                echo json_encode(array('errorMsg' => 'Data sebelumnya sudah ada, silahkan cek kembali!'));
            } else {
                if (insert($conn,$region,$provinsi,$area,$distrik, $distrik_ret, $created_by)) {
                    echo json_encode(array('success'=>true,'info'=>"Data berhasil ditambahkan!"));
                } else {
                    echo json_encode(array('errorMsg' => 'Gagal menambahkan data. Silakan coba lagi!'));
                }
            }
        }
        break;
        case 'edit' :
        {
            $sqlcek= "UPDATE MAPPING_MASTER_WILAYAH set KODE_REGION = '$region', KODE_PROVINSI = '$provinsi', KODE_DISTRIK = '$distrik', DISTRIK_RET = '$distrik_ret', AREA = '$area', UPDATED_AT = SYSDATE, UPDATED_BY = '$user_name' where ID = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Edit data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
        case 'delete' :
        {
            $sqlcek= "DELETE FROM MAPPING_MASTER_WILAYAH WHERE id = $id";
            $querycek= oci_parse($conn, $sqlcek);
            $return=oci_execute($querycek);
            if ($return){
                echo json_encode(array('success'=>true,'info'=>"Delete data success"));
            } else {
                echo json_encode(array('errorMsg'=>'Some errors occured.'));
            }
        }
        break;
    }
}

function displayData($conn,$sort,$order){
    $org = $_SESSION['user_org'];
    if($conn){
        $sql1 = "SELECT
        mmw.*
        ,kota.NM_KOTA AS NAMA_DISTRIK
        ,prov.NM_PROV AS NAMA_PROVINSI
        FROM
            MAPPING_MASTER_WILAYAH mmw
            LEFT JOIN ZREPORT_M_KOTA kota ON mmw.KODE_DISTRIK = kota.KD_KOTA 
            LEFT JOIN ZREPORT_M_PROVINSI prov ON mmw.KODE_PROVINSI = prov.KD_PROV 
        ORDER BY
            mmw.$sort $order";
        $query= oci_parse($conn, $sql1);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]['ID'] = $row['ID'];
            $result[$i]['REGION'] = $row['KODE_REGION'];
            $result[$i]['PROVINSI'] = $row['KODE_PROVINSI'];
            $result[$i]['NAMA_PROVINSI'] = $row['NAMA_PROVINSI'];
            $result[$i]['AREA'] = $row['AREA'];
            $result[$i]['DISTRIK'] = $row['KODE_DISTRIK'];
            $result[$i]['NAMA_DISTRIK'] = $row['NAMA_DISTRIK'];
            $result[$i]['DISTRIK_RET'] = $row['DISTRIK_RET'];
            $result[$i]['created_at'] = $row['created_at'] == null ? 'tidak ada keterangan' : $row['created_at'];
            $result[$i]['created_by'] = $row['created_by'] == null ? 'tidak ada keterangan' : $row['created_by'];
            $result[$i]['UPDATED_AT'] = $row['UPDATED_AT'] == null ? 'tidak ada keterangan' : $row['UPDATED_AT'];
            $result[$i]['UPDATED_BY'] = $row['UPDATED_BY'] == null ? 'tidak ada keterangan' : $row['UPDATED_BY'];
            $i++;
        }
        echo json_encode($result);  
    }
}

function insert($conn,$region,$provinsi,$area,$distrik, $distrik_ret, $created_by){
    $sqlcek= "INSERT INTO MAPPING_MASTER_WILAYAH (KODE_REGION, KODE_PROVINSI, KODE_DISTRIK, AREA, DISTRIK_RET, created_at, created_by) values ('".$region."','".$provinsi."','".$distrik."','".$area."', '".$distrik_ret."',  SYSDATE, '".$created_by."')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        $e = oci_error($query);
        error_log("SQL Error: " . $e['message']);
        return false;
    }
}

function update($conn,$region,$provinsi,$area,$distrik, $distrik_ret, $created_by){
    $sqlcek= "UPDATE MAPPING_MASTER_WILAYAH set DISTRIK_RET = '$distrik_ret', UPDATED_AT = SYSDATE, UPDATED_BY = '$created_by' ,KODE_REGION = '$region'  where  KODE_PROVINSI = '$provinsi' AND KODE_DISTRIK = '$distrik' ";
    // $sqlcek= "INSERT INTO MAPPING_MASTER_WILAYAH (KODE_REGION, KODE_PROVINSI, KODE_DISTRIK, AREA, DISTRIK_RET, created_at, created_by) values ('".$region."','".$provinsi."','".$distrik."','".$area."', '".$distrik_ret."',  SYSDATE, '".$created_by."')";
    $query = oci_parse($conn, $sqlcek);
    $result = oci_execute($query);
    
    if ($result){
        return true;
    } else {
        return false;
    }
}


// Tambahkan function untuk mengecek duplikasi data
function checkDuplicateData($conn,$region,$provinsi,$area,$distrik) {
    $sql_count = "SELECT COUNT(*) AS TOTAL 
                FROM MAPPING_MASTER_WILAYAH
                WHERE 
                    KODE_DISTRIK = :DISTRIK
                    -- AND KODE_REGION = :AREA
                ";

    $query_count = oci_parse($conn, $sql_count);
    oci_bind_by_name($query_count, ':DISTRIK', $distrik);
    // oci_bind_by_name($query_count, ':AREA', $area);

    oci_execute($query_count);
    $row_count = oci_fetch_array($query_count, OCI_ASSOC);
    $result = $row_count['TOTAL'] > 0;

    // Mengembalikan true jika ada duplikasi data
    return $result;
}

function adjustRowNumber($num) {
    return $num - 2;
}



?>
