<?
############ SAP Connection  ###################################################
//$link_koneksi_sap = "/opt/lampp/htdocs/sgg/include/connect/sap_sd_210.php"; // PROD    ../include/sapclasses/logon_data.conf";
$link_koneksi_sap = "../include/sapclasses/logon_data.conf";
require_once 'helper.php';
require_once 'api/smbr.php';
################################################################################

require_once ('../security_helper.php');
sanitize_global_input();

switch ($action) {
//==============================================================================================================
case "recalculate_shp_smbr":
	$user_id=$_SESSION['user_id'];
	$user_name=$_SESSION['user_name'];
	$ke_cek = 0;
	$total_cek = $_POST['total'];

	for($i=0;$i<$total_cek;$i++){
		$idke="id_app".$i;
	
		if(isset($_POST[$idke])){
			$ke_cek ++;
			$id_data=$_POST[$idke];
			if ($ke_cek == 1){
			$sql_in .= " AND NO_SHP_TRN IN (";
			$sql_in .= "'$id_data'";
			}else
			$sql_in .= " , '$id_data'";
		
		}
	}
		if ($ke_cek >= 1)
		$sql_in .= " )";
		else
		$sql_in = " AND NO_SHP_TRN = '1'";

if ($ke_cek >= 1){


$sql_del ="UPDATE EX_TRANS_HDR SET SOLD_TO = SHIP_TO, NAMA_SOLD_TO = NAMA_SHIP_TO WHERE SOLD_TO IS NULL AND DELETE_MARK = '0'";
$query_del= oci_parse($conn, $sql_del);
oci_execute($query_del);

	$sql= "SELECT NO_SHP_TRN,SOLD_TO,SHIP_TO,QTY_SHP,PLANT,PLANT_RCV,ORG,NO_POL,WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' $sql_in  ";
	// echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_shp_x=$row[NO_SHP_TRN]; 
		$qty_v=$row[QTY_SHP];
		$sold_to_v=$row[SOLD_TO];
		$plant_v=$row[PLANT]; //penambahan proses curah soldto 2304 & 7403
		if ($sold_to_v == '0000002403' or $sold_to_v == '0000007403')$hajar = "T";
		else $hajar = "F";
		
		$sql_dtl= "SELECT * FROM EX_TRANS_DTL WHERE NO_SHP_TRN = '$no_shp_x' ";
		$query_dtl= oci_parse($conn, $sql_dtl);
		oci_execute($query_dtl);
	
		$row_dtl=oci_fetch_array($query_dtl);
		$no_so_v=$row_dtl[NO_SO];  
		$no_po_v=$row_dtl[NO_PO];  
		$posnr_v=$row_dtl[POSNR];  
		$kode_prod=$row_dtl[KODE_PRODUK];
		$kode_prod_cek = substr($kode_prod, 0, -5);

			$exti1 = $no_shp_x;
			if ($row[SOLD_TO] != ""){
				$kunnr = $row[SOLD_TO];
			}else{
				$kunnr = $row[SHIP_TO];
			}

            // $param = array(
            //     "lr_exti1" => array(
            //         "sign"   => "I",
            //         "option" => "EQ",
            //         "low"    => $exti1,
            //         "high"   => ""
            //     ),
            //     "lr_kunnr" => array(
            //         "sign"   => "I",
            //         "option" => "EQ",
            //         "low"    => $kunnr,
            //         "high"   => ""
            //     )
            // );

            $param = array(
                "no_shipment" => $exti1
            );
		
            $api_smbr = new ApiSmbr();
            $response = $api_smbr->get_shipment_cost($param);
            if($response['success']){
                $datas = $response['data'];

                foreach($datas as $data){
                    $total_netwr =0;
				    $cek_inke = 0;
                    $TKNUM[]     = $tknum_in     = $data['tknum'];
                    $VBTYP[]     = $vbtyp_in     = $data['vbtyp'];
                    $SHTYP[]     = $shtyp_in     = $data['shtyp'];
                    $FKNUM[]     = $fknum_in     = $data['fknum'];
                    $FKPOS[]     = $fkpos_in     = $data['fkpos'];
                    $BUKRS[]     = $bukrs_in     = $data['bukrs'];
                    $NETWR[]     = $netwr_in     = round($data['netwr'] * 100, 0);
                    $total_netwr += $netwr_in;

                    $MWSBP[]     = $mwsbp_in     = $data['mwsbp'];
                    $FKPTY[]     = $fkpty_in     = $data['fkpty'];
                    $KALSM[]     = $kalsm_in     = $data['kalsm'];
                    $VTEXT[]     = $vtext_in     = $data['vtext'];
                    $WERKS[]     = $werks_in     = $data['werks'];
                    $EKORG[]     = $ekorg_in     = $data['ekorg'];
                    $EBELN[]     = $ebeln_in     = $data['ebeln'];
                    $EBELP[]     = $ebelp_in     = $data['ebelp'];
                    $LBLNI[]     = $lblni_in     = $data['lblni'];
                    $TDLNR[]     = $tdlnr_in     = $data['tdlnr'];
                    $STABR[]     = $stabr_in     = $data['stabr'];
                    $KOSTL[]     = $kostl_in     = $data['kostl'];
                    $PRCTR[]     = $prctr_in     = $data['prctr'];
                    $BANKN[]     = $bankn_in     = $data['bankn'];
                    $BANKA[]     = $banka_in     = $data['banka'];
                    $BRNCH[]     = $brnch_in     = $data['brnch'];
                    $SAKTO[]     = $sakto_in     = $data['sakto'];
                    $NETWR_DO[]  = $netwr_do_in  = round($data['netwr_do'], 0);
                    $NTGEW[]     = $ntgew_in     = (int)$data['ntgew'];
                    $LIFNR[]     = $lifnr_in     = $data['lifnr'];
                    $NAME1[]     = $name1_in     = $data['name1'];
                    $BVTYP[]     = $bvtyp_in     = $data['bvtyp'];
                    $BRAN1[]     = $bran1_in     = $data['bran1'];
                    $VTEXTX[]    = $vtextx_in    = $data['vtextx'];
					$mwsbp_do_in= 0;// pajak di nolkan
					$harga_tebus = $netwr_do_in + $mwsbp_do_in;//ambil harga satuan

                    if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $stabr_in == "C" and $lifnr_in != "" and $bvtyp_in != "" or $hajar == "T"){
						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200"  or $hajar == "T")){
							if ($cek_inke == 0){
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x"); 
							$tablename="EX_TRANS_COST";
							$fungsi->delete($conn,$tablename,$field_id,$value_id);
							}
						$cek_inke = 1;
	
						$field_names=array('NO_SHP_TRN','KODE_SHP_COST','SHP_COST','NO_ENTRY_SHEET','TYPE','FKPOS','DELETE_MARK','EBELN','EBELP');
						$field_data=array("$no_shp_x","$fknum_in","$netwr_in","$lblni_in","$fkpty_in","$fkpos_in","0","$ebeln_in","$ebelp_in"); 
						$tablename="EX_TRANS_COST";
						$fungsi->insert_safe($conn,$field_names,$field_data,$tablename);  
						}
					}
                }	
                
                if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and  $stabr_in == "C" and $lifnr_in != "" or $hajar == "T"){

                    if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200" or $hajar == "T" )){
                        //penambahan proses curah soldto 2304 & 7403
                        if($hajar == "T"){
                            if(!$werks_in){
                            $werks_in = $plant_v;	
                            }
                        }

                        $tarif_cost=round($netwr_in/$qty_v);
                        $field_names=array('STATUS2','TANGGAL_SIAP_TAGIH','NO_SHP_SAP','SHP_COST','KODE_SHP_COST','NO_ENTRY_SHEET','TARIF_COST','EBELN','EBELP','KOSTL','PRCTR','NO_REK_DIS','NAMA_BANK_DIS','BANK_CABANG_DIS','HARGA_TEBUS','KET_ERROR','NO_GL_SHP','PLANT','UM_REZ','QTY_KTG_RUSAK','QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG','TOTAL_SEMEN_RUSAK','PDPKS','TOTAL_KLAIM_SEMEN','TOTAL_KLAIM_ALL','TANGGAL_DATANG','TANGGAL_BONGKAR','PENGELOLA','NAMA_PENGELOLA','BVTYP','KODE_KECAMATAN','NAMA_KECAMATAN');
                        $field_data=array("OPEN","SYSDATE","$tknum_in","$total_netwr","$fknum_in","$lblni_in","$tarif_cost","$ebeln_in","$ebelp_in","$kostl_in","$prctr_in","$bankn_in","$banka_in","$brnch_in","$harga_tebus","OK","$sakto_in","$werks_in","$ntgew_in","","","","","","","","","","","","$lifnr_in","$name1_in","$bvtyp_in","$bran1_in","$vtextx_in"); 		
                        $tablename="EX_TRANS_HDR";
                        $field_id=array('NO_SHP_TRN');
                        $value_id=array("$no_shp_x");
                        $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
                        $show_ket.=" OK SHP $no_shp_x, ";
                        $show_ket.= '<br>';
                    }else{
                        $show_ket.="Gagal Update SHP $no_turunan, ";
                        $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
                        $show_ket.= '<br>';
                        $msg .="Gagal Update SHP $no_turunan";
                        $field_names=array('KET_ERROR');
                        $field_data=array("$msg"); 		
                        $tablename="EX_TRANS_HDR";
                        $field_id=array('NO_SHP_TRN');
                        $value_id=array("$no_shp_x");
                        $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
                    
                    }
                }else{
                    $show_ket.="Gagal Update SHP $no_turunan, ";
                    $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
                    $show_ket.= '<br>';
                    $msg .="Gagal Update SHP $no_turunan";
                    $field_names=array('KET_ERROR');
                    $field_data=array("$msg"); 		
                    $tablename="EX_TRANS_HDR";
                    $field_id=array('NO_SHP_TRN');
                    $value_id=array("$no_shp_x");
                    $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
                }
            }else{
                
                $show_ket.="Gagal Update SHP $no_turunan, ";
                $show_ket.= $response['msg'];
                $show_ket.= '<br>';
                $msg .="Gagal Update SHP $no_turunan, " .$response['msg'];
                $field_names=array('KET_ERROR');
                $field_data=array("$msg"); 		
                $tablename="EX_TRANS_HDR";
                $field_id=array('NO_SHP_TRN');
                $value_id=array("$no_shp_x");
                $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
            }
	}
}

	$habis = "recalculate_shp.php";
	break;
//==============================================================================================================
//==============================================================================================================
case "recalculate_shp_sp":
	$user_id=$_SESSION['user_id'];
	$user_name=$_SESSION['user_name'];
	$ke_cek = 0;
	$total_cek = $_POST['total'];

	for($i=0;$i<$total_cek;$i++){
		$idke="id_app".$i;
	
		if(isset($_POST[$idke])){
			$ke_cek ++;
			$id_data=$_POST[$idke];
			if ($ke_cek == 1){
			$sql_in .= " AND NO_SHP_TRN IN (";
			$sql_in .= "'$id_data'";
			}else
			$sql_in .= " , '$id_data'";
		
		}
	}
		if ($ke_cek >= 1)
		$sql_in .= " )";
		else
		$sql_in = " AND NO_SHP_TRN = '1'";
	

	$sap = new SAPConnection();
	$sap->Connect($link_koneksi_sap);
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}

if ($ke_cek >= 1){


$sql_del ="UPDATE EX_TRANS_HDR SET SOLD_TO = SHIP_TO, NAMA_SOLD_TO = NAMA_SHIP_TO WHERE SOLD_TO IS NULL AND DELETE_MARK = '0'";
$query_del= oci_parse($conn, $sql_del);
oci_execute($query_del);

	$sql= "SELECT NO_SHP_TRN,SOLD_TO,SHIP_TO,QTY_SHP,PLANT,PLANT_RCV,ORG,NO_POL,WARNA_PLAT FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' $sql_in  ";
	// echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_shp_x=$row[NO_SHP_TRN]; 
		$qty_v=$row[QTY_SHP];
		$sold_to_v=$row[SOLD_TO];
		$plant_v=$row[PLANT]; //penambahan proses curah soldto 2304 & 7403
		if ($sold_to_v == '0000002403' or $sold_to_v == '0000007403')$hajar = "T";
		else $hajar = "F";
		
		$sql_dtl= "SELECT * FROM EX_TRANS_DTL WHERE NO_SHP_TRN = '$no_shp_x' ";
		$query_dtl= oci_parse($conn, $sql_dtl);
		oci_execute($query_dtl);
	
		$row_dtl=oci_fetch_array($query_dtl);
		$no_so_v=$row_dtl[NO_SO];  
		$no_po_v=$row_dtl[NO_PO];  
		$posnr_v=$row_dtl[POSNR];  
		$kode_prod=$row_dtl[KODE_PRODUK];
		$kode_prod_cek = substr($kode_prod, 0, -5);

                        //$fce = $sap->NewFunction ("Z_ZAPPSD_SEL_SHP_COST2");
                        $fce = $sap->NewFunction ("Z_ZAPPSD_SEL_SHP_COST4_SP");//perubahan rfc
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			$fce->LR_EXTI1->row["SIGN"] = 'I';
			$fce->LR_EXTI1->row["OPTION"] = 'EQ';
			$fce->LR_EXTI1->row["LOW"] = $no_shp_x;
			$fce->LR_EXTI1->Append($fce->LR_EXTI1->row);

			if ($row[SOLD_TO] != ""){
				$fce->LR_KUNNR->row["SIGN"] = 'I';
				$fce->LR_KUNNR->row["OPTION"] = 'EQ';
				$fce->LR_KUNNR->row["LOW"] = $row[SOLD_TO];
				$fce->LR_KUNNR->Append($fce->LR_KUNNR->row);
			}else{
				$fce->LR_KUNNR->row["SIGN"] = 'I';
				$fce->LR_KUNNR->row["OPTION"] = 'EQ';
				$fce->LR_KUNNR->row["LOW"] = $row[SHIP_TO];
				$fce->LR_KUNNR->Append($fce->LR_KUNNR->row);
			}
			$fce->Call();

			if ($fce->GetStatus() == SAPRFC_OK ) {	
				$fce->RETURN_DATA->Reset();
				$total_netwr =0;
				$cek_inke = 0;
				while ( $fce->RETURN_DATA->Next() ){
					$TKNUM[]=$tknum_in= $fce->RETURN_DATA->row["TKNUM"];
					$EXTI1[]=$exti1_in= $fce->RETURN_DATA->row["EXTI1"];
					$VBTYP[]=$vbtyp_in= $fce->RETURN_DATA->row["VBTYP"];
					$SHTYP[]=$shtyp_in= $fce->RETURN_DATA->row["SHTYP"];
					$FKNUM[]=$fknum_in= $fce->RETURN_DATA->row["FKNUM"];
					$FKPOS[]=$fkpos_in= $fce->RETURN_DATA->row["FKPOS"];
					$BUKRS[]=$bukrs_in= $fce->RETURN_DATA->row["BUKRS"];
					$NETWR[]=$netwr_in= round($fce->RETURN_DATA->row["NETWR"]*100,0);
					$total_netwr += $netwr_in;
	
					$MWSBP[]=$mwsbp_in= $fce->RETURN_DATA->row["MWSBP"];
					$FKPTY[]=$fkpty_in= $fce->RETURN_DATA->row["FKPTY"];
					$KALSM[]=$kalsm_in= $fce->RETURN_DATA->row["KALSM"];
					$VTEXT[]=$vtext_in= $fce->RETURN_DATA->row["VTEXT"];
					$WERKS[]=$werks_in= $fce->RETURN_DATA->row["WERKS"];
					$EKORG[]=$ekorg_in= $fce->RETURN_DATA->row["EKORG"];
					$EBELN[]=$ebeln_in= $fce->RETURN_DATA->row["EBELN"];
	
					$EBELP[]=$ebelp_in= $fce->RETURN_DATA->row["EBELP"];
					$LBLNI[]=$lblni_in= $fce->RETURN_DATA->row["LBLNI"];
					$TDLNR[]=$tdlnr_in= $fce->RETURN_DATA->row["TDLNR"];
					$STABR[]=$stabr_in= $fce->RETURN_DATA->row["STABR"];
					$KOSTL[]=$kostl_in= $fce->RETURN_DATA->row["KOSTL"];
					$PRCTR[]=$prctr_in= $fce->RETURN_DATA->row["PRCTR"];
					$BANKN[]=$bankn_in= $fce->RETURN_DATA->row["BANKN"];
					$BANKA[]=$banka_in= $fce->RETURN_DATA->row["BANKA"];
					$BRNCH[]=$brnch_in= $fce->RETURN_DATA->row["BRNCH"];
					$SAKTO[]=$sakto_in= $fce->RETURN_DATA->row["SAKTO"];
					$NETWR_DO[]=$netwr_do_in= round($fce->RETURN_DATA->row["NETWR_DO"],0);
					$NTGEW[]=$ntgew_in= (int)$fce->RETURN_DATA->row["NTGEW"];
					//$MWSBP_DO[]=$mwsbp_do_in= $fce->RETURN_DATA->row["MWSBP_DO"]*100;
					$LIFNR[]=$lifnr_in= $fce->RETURN_DATA->row["LIFNR"];
					$NAME1[]=$name1_in= $fce->RETURN_DATA->row["NAME1"];
					$BVTYP[]=$bvtyp_in= $fce->RETURN_DATA->row["BVTYP"];
					$BRAN1[]=$bran1_in= $fce->RETURN_DATA->row["BRAN1"];
					$VTEXTX[]=$vtextx_in= $fce->RETURN_DATA->row["VTEXTX"];
					$mwsbp_do_in= 0;// pajak di nolkan
					$harga_tebus = $netwr_do_in + $mwsbp_do_in;//ambil harga satuan
					//if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $exti1_in == $no_shp_x and $stabr_in == "C" and $lifnr_in != "" and $bvtyp_in != "" or $hajar == "T"){
					if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $exti1_in == $no_shp_x and $stabr_in == "C"  and $bvtyp_in != "" or $hajar == "T"){
						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200"  or $hajar == "T")){
							if ($cek_inke == 0){
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x"); 
							$tablename="EX_TRANS_COST";
							$fungsi->delete($conn,$tablename,$field_id,$value_id);
							}
						$cek_inke = 1;
	
						$field_names=array('NO_SHP_TRN','KODE_SHP_COST','SHP_COST','NO_ENTRY_SHEET','TYPE','FKPOS','DELETE_MARK','EBELN','EBELP');
						$field_data=array("$no_shp_x","$fknum_in","$netwr_in","$lblni_in","$fkpty_in","$fkpos_in","0","$ebeln_in","$ebelp_in"); 
						$tablename="EX_TRANS_COST";
						$fungsi->insert_safe($conn,$field_names,$field_data,$tablename);  
						}
					}				
				
				}
				
					$tipe = "";
					$msg = "";
					$tipe=$fce->RETURN["TYPE"];			
					$msg=$fce->RETURN["MESSAGE"];	
				
					if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $no_shp_x == $exti1_in and  $stabr_in == "C"  or $hajar == "T"){
				//	if ($ebeln_in!="" and $netwr_in!="" and $total_netwr > 0 and $no_shp_x == $exti1_in and  $stabr_in == "C" and $lifnr_in != "" or $hajar == "T"){

						if ($harga_tebus!="" and $harga_tebus > 0 or ( $kode_prod_cek == "121-200" or $hajar == "T" )){
							//penambahan proses curah soldto 2304 & 7403
							if($hajar == "T"){
								if(!$werks_in){
								$werks_in = $plant_v;	
								}
							}

                            // get warna plat
                            $warna_plat = $row[WARNA_PLAT];
                            if(!$warna_plat){
                                $warna_plat = "";
                                $fce2 = $sap->NewFunction("Z_ZAPPSD_SELECT_TRUK");
                                $fce2->XPARAM["NOPOLISI"] = $row[NO_POL];
                                $fce2->XDATA_APP["NMORG"] = $row[ORG];
                                $fce2->XDATA_APP["NMPLAN"] = $row[PLANT];

                                $fce2->Call();

                                if($fce2->GetStatus() == SAPRFC_OK){
                                    $fce2->RETURN_DATA->Reset();
                                    if($fce2->RETURN["TYPE"] == "S"){
                                        while ($fce2->RETURN_DATA->Next()) {
                                            $warna_plat = $fce2->RETURN_DATA->row["WARNA_PLAT"];
                                        }
                                    }
                                }
                            }

							$tarif_cost=round($netwr_in/$qty_v);
							$field_names=array('STATUS2','TANGGAL_SIAP_TAGIH','NO_SHP_SAP','SHP_COST','KODE_SHP_COST','NO_ENTRY_SHEET','TARIF_COST','EBELN','EBELP','KOSTL','PRCTR','NO_REK_DIS','NAMA_BANK_DIS','BANK_CABANG_DIS','HARGA_TEBUS','KET_ERROR','NO_GL_SHP','PLANT','UM_REZ','QTY_KTG_RUSAK','QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG','TOTAL_SEMEN_RUSAK','PDPKS','TOTAL_KLAIM_SEMEN','TOTAL_KLAIM_ALL','TANGGAL_DATANG','TANGGAL_BONGKAR','PENGELOLA','NAMA_PENGELOLA','BVTYP','KODE_KECAMATAN','NAMA_KECAMATAN','WARNA_PLAT');
							$field_data=array("OPEN","SYSDATE","$tknum_in","$total_netwr","$fknum_in","$lblni_in","$tarif_cost","$ebeln_in","$ebelp_in","$kostl_in","$prctr_in","$bankn_in","$banka_in","$brnch_in","$harga_tebus","OK","$sakto_in","$werks_in","$ntgew_in","","","","","","","","","","","","$lifnr_in","$name1_in","$bvtyp_in","$bran1_in","$vtextx_in","$warna_plat"); 		
							$tablename="EX_TRANS_HDR";
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");
							$fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
							$show_ket.=" OK SHP $no_shp_x, ";
							$show_ket.= '<br>';
						}else{
							$show_ket.="Gagal Update SHP $no_turunan, ";
							if ($tipe == 'E')
							$show_ket.= $msg;
							else $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
							$show_ket.= '<br>';
							$msg .="Gagal Update SHP $no_turunan";
							$field_names=array('KET_ERROR');
							$field_data=array("$msg"); 		
							$tablename="EX_TRANS_HDR";
							$field_id=array('NO_SHP_TRN');
							$value_id=array("$no_shp_x");
							$fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);
						
						}
					}else{
						$show_ket.="Gagal Update SHP $no_turunan, ";
						if ($tipe == 'E')
						$show_ket.= $msg;
						else $show_ket.="Cek Harga Shipment, PO atau shipment cost yang terbentuk..  ";
						$show_ket.= '<br>';
						$msg .="Gagal Update SHP $no_turunan";
						$field_names=array('KET_ERROR');
						$field_data=array("$msg"); 		
						$tablename="EX_TRANS_HDR";
						$field_id=array('NO_SHP_TRN');
						$value_id=array("$no_shp_x");
						$fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);

					}
				
			}
			$fce->Close();	

	}
}

		$sap->Close();	

	$habis = "recalculate_shp.php";
	break;
//============================================================================================================================
//============================================================================================================================
case "create_ba":
    $user_id = $_SESSION['user_id'];
    $user_name = $_SESSION['user_name'];
    $id_user_approval = $_POST['id_user_approval'];
    $sampai = $_POST['total'];
    $totChecked = $_POST['totalCheked'];
    $NO_BA_vendor_in = trim(strtoupper($_POST['NO_BA_vendor']));
    $no_pajak_vendor_in = $_POST['pjk1'] . "." . $_POST['pjk2'] . "-" . $_POST['pjk3'] . "." . $_POST['pjk4'];
    $pajak_bin = $no_pajak_vendor_in; //$_POST['pjk1'].".".$_POST['pjk2']."-".$_POST['pjk3'].".".substr($_POST['pjk4'],0,7)."*";
    $no_vendor_in = $_POST['no_vendor'];
    // echo $no_vendor_in;exit;
    $nama_vendor_in = $_POST['nama_vendor'];
    $warna_plat_in = $_POST['warna_plat'];
    $bulan = $_POST['bulan'];
    $tahun = $_POST['tahun'];
    $orgSpj = $_POST['orgSPJ'];
    $spt_cek = $_POST['spt_cek'];
    $tanggal_pjk = $_POST['tanggal_pjk'];
    $no_rek = $_REQUEST['no_rek'];
    $nama_bank = trim($_REQUEST['nama_bank']);
    $cabang_bank = trim($_REQUEST['cabang_bank']);
    $no_kwitansi = $_REQUEST['no_kwitansi_vendor'];
    $bvtyp = $_REQUEST['bvtyp'];      
    $noBaF = $_POST['orgSPJ'].'.'.$_POST['termin'].'.'.(int)$no_vendor_in.'.'.time();
    if ($warna_plat_in == "KUNING" || $warna_plat_in == "HITAM")
        $no_pajak_vendor_in = "";
   //Limit number spj per invoice
   if($totChecked < 501){
    unset($orgchose);
    for ($kn = 0; $kn < $sampai; $kn++) {
        $idken = "idke" . $kn;
        $urutken = "urutke" . $kn;
        $orgkej = "orgke" . $kn;

        if (isset($_POST[$idken])) {
            $id_upn = $_POST[$idken];
            $orgkej = $_POST[$orgkej];
            $orgchose[$orgkej] = $orgkej;
        }
    }
   
    if (count($orgchose) == 1) {

        // if ($spt_cek == "GABUNGAN") {
        //     $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND to_char(tanggal_kirim,'MM-YYYY')='$bulan-$tahun' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX <> '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
        //     $query_spt = oci_parse($conn, $sql_spt);
        //     oci_execute($query_spt);
        //     $row_spt = oci_fetch_assoc($query_spt);
        //     $data_spt = $row_spt[HIT];
        //     if ($row_spt[HIT] > 0) {
        //         $cek_data = false;
        //         $show_ket .= " Pajak Anda Gabungan, harus sama untuk satu periode pajak.. <br>";
        //     }else
        //         $cek_data = true;

        //     if ($cek_data) {
        //         $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND to_char(tanggal_kirim,'MM-YYYY') <> '$bulan-$tahun' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
        //         $query_spt = oci_parse($conn, $sql_spt);
        //         oci_execute($query_spt);
        //         $row_spt = oci_fetch_assoc($query_spt);
        //         $data_spt = $row_spt[HIT];

        //         if ($row_spt[HIT] > 0) {
        //             $cek_data = false;
        //             $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
        //         }else
        //             $cek_data = true;
        //     }
        // }else {
        //     // cek unutk transaksi
        //     // tidak ada boleh no pajak yang sama
        //     $sql_spt = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE VENDOR='$no_vendor_in' AND NO_PAJAK_EX IS NOT NULL AND NO_PAJAK_EX = '$no_pajak_vendor_in' AND DELETE_MARK = '0' ";
        //     $query_spt = oci_parse($conn, $sql_spt);
        //     oci_execute($query_spt);
        //     $row_spt = oci_fetch_assoc($query_spt);
        //     $data_spt = $row_spt[HIT];

        //     if ($row_spt[HIT] > 0) {
        //         $cek_data = false;
        //         $show_ket .= " No Pajak Anda Pernah Sama Untuk Periode Pajak Lainnya.. <br>";
        //     }else
        //         $cek_data = true;
        // }

        if ($warna_plat_in == "KUNING" || $warna_plat_in == "HITAM") {
            $cek_data = true;
            $show_ket = "";
        }

        if ($cek_data) {
            //Cek is already cretae BA
            //$sqlCntInv = "SELECT count(*) AS JMLHINV from EX_BA where DELETE_MARK ='0' AND ORG ='$orgSpj' AND NO_VENDOR = '$no_vendor_in' AND TGL_BA BETWEEN trunc(sysdate) AND trunc(sysdate)+1  ORDER BY TGL_BA DESC";
            // $sqlCntInv ="
            //     SELECT
            //             COUNT(TBL1.NO_INVOICE) AS JMLHINV
            //     FROM
            //             (
            //                     SELECT
            //                             EXI.NO_INVOICE
            //                     FROM
            //                             EX_INVOICE EXI
            //                     JOIN EX_TRANS_HDR ETH ON ETH.NO_INVOICE = EXI.NO_INVOICE
            //                     WHERE
            //                             EXI.DELETE_MARK = '0'
            //                     AND EXI.ORG = '$orgSpj'
            //                     AND EXI.NO_VENDOR = '$no_vendor_in'
            //                     AND ETH.TANGGAL_INVOICE BETWEEN TRUNC (SYSDATE)
            //                     AND TRUNC (SYSDATE) + 1
            //                     AND ETH.WARNA_PLAT ='$warna_plat_in'
            //                     GROUP BY
            //                             EXI.NO_INVOICE
            //             ) TBL1
            // ";
            // // echo $sqlCntInv;
            // $queryCntInv = oci_parse($conn, $sqlCntInv);
            // // print_r($queryCntInv);exit;
            // oci_execute($queryCntInv);
            // $row_cnt = oci_fetch_assoc($queryCntInv);
            // $jmlhInv = $row_cnt[JMLHINV];
            // $t=date('d-m-Y');
            // $cektglnow = (int)date("d",strtotime($t));
            // if(($jmlhInv > 100)){
            //     $show_ket = "Unable create BA (Reach Limit Day)";                
            // }else{
                $data_900 = 0;
                $NO_BA_in = '';
            for ($k = 0; $k < $sampai; $k++) {
                $idke = "idke" . $k;
                $urutke = "urutke" . $k;

                if (isset($_POST[$idke])) {
                    $id_up = $_POST[$idke];
                    // echo $id_up;
                    if ($NO_BA_in == "") {
                        $NO_BA_in = $fungsi->new_invoice_ba($conn);
                        $inv_old = $NO_BA_in;
                        $no_inv_data_900[] = $NO_BA_in;
                    }
                    // if ($data_900 == 850) {
                    //     $NO_BA_in = $fungsi->new_invoice_ba($conn);
                    //     //$no_pajak_vendor_in=$$no_pajak_vendor_in;
                    //     $no_inv_data_900[] = $NO_BA_in;
                    //     $data_900 = 0;
                    // }
                    if ($NO_BA_in != $inv_old)
                        $no_pajak_vendor_in_sql = $pajak_bin;
                    else
                        $no_pajak_vendor_in_sql = $no_pajak_vendor_in;

                    if (!$NO_BA_vendor_in) {
                        $NO_BA_vendor_in = $NO_BA_in;
                    }
                    $field_names = array('NO_BA', 'STATUS', 'TANGGAL_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2', 'NO_TAGIHAN');
                    $field_data = array("$NO_BA_in", "OPEN BA", "SYSDATE", "SYSDATE", "$user_name", "OPEN BA", "$inv_old");
                    $tablename = "EX_TRANS_HDR";
                    // $field_id = array('ID', 'STATUS', 'STATUS2');
                    // $value_id = array("$id_up", "OPEN BA", "OPEN BA");
                    $field_id = array('ID');
                    $value_id = array("$id_up");
                    // print_r($value_id);
                    // print_r($field_names)'<br>';
                    // print_r($field_data)'<br>';
                    // print_r($tablename)'<br>';
                    // print_r($field_id)'<br>';
                    // print_r($value_id)'<br>';exit;
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    
                    $data_900+=1;
                }
            }

            // for ($in = 0; $in < count($no_inv_data_900); $in++) {
                // $NO_BA_in = $no_inv_data_900[$in];
                $sqlcek = "SELECT ORG, QTY_KTG_RUSAK, QTY_SEMEN_RUSAK, SUM(TOTAL_KLAIM_ALL) AS TOTAL_KLAIM, SUM(TOTAL_KTG_REZAK) AS TOTAL_KTG, SUM(TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN, SUM(PDPKS) AS TOTAL_PDPKS, SUM(TOTAL_KTG_RUSAK) AS TOTAL_PDPKK, SUM(SHP_COST) AS SHP_COST FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND NO_BA = '$NO_BA_in' GROUP BY ORG,QTY_KTG_RUSAK, QTY_SEMEN_RUSAK ";
                $querycek = oci_parse($conn, $sqlcek);
                oci_execute($querycek);
                $row_data = oci_fetch_assoc($querycek);
                $qty_semen_rusak = $row_data[QTY_SEMEN_RUSAK];
                $qty_ktg_rusak = $row_data[QTY_KTG_RUSAK];
                $total_klaim_in = $row_data[TOTAL_KLAIM];
                $total_shp_in = $row_data[SHP_COST];
                $total_ktg_in = $row_data[TOTAL_KTG];
                $total_semen_in = $row_data[TOTAL_SEMEN];
                $total_pdpks_in = $row_data[TOTAL_PDPKS];
                $total_pdpkk_in = $row_data[TOTAL_PDPKK];
                $org_in = $row_data[ORG];
                //tambahan pengujian pajak 11 persen 01-04-2022
                if ($warna_plat_in == "HITAM")
                    if (date("Ymd") >= 20220401){
                        $pajak_in = round($total_shp_in * 0.11, 0);
                    }else{
                        $pajak_in = round($total_shp_in * 0.1, 0);   
                    }
                //----------------------------------
                else
                    $pajak_in = 0;
                $total_tagihan = ($total_shp_in + $pajak_in) - $total_klaim_in;
                $field_names = array('NO_BA_EX', 'NO_PAJAK_EX', 'TOTAL_INV', 'PAJAK_INV', 'NO_VENDOR', 'NAMA_VENDOR', 'KLAIM_KTG', 'KLAIM_SEMEN', 'PDPKS', 'NO_BA', 'TGL_BA', 'PDPKK', 'DELETE_MARK', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'TGL_PAJAK_EX', 'TOTAL_INVOICE', 'ORG', 'TGL_TERMIN', 'TERMIN', 'NO_REKENING', 'BANK', 'BVTYP', 'BANK_CABANG', 'NO_KWITANSI','NO_BAF','COUNT_SPJ','STATUS_BA', 'ID_USER_APPROVAL');
                $field_data = array("$NO_BA_vendor_in", "$no_pajak_vendor_in", "$total_shp_in", "$pajak_in", "$no_vendor_in", "$nama_vendor_in", "$qty_ktg_rusak", "$qty_semen_rusak", "$total_pdpks_in", "$NO_BA_in", "SYSDATE", "$total_pdpkk_in", "0", "SYSDATE", "$user_name", "instgl_$tanggal_pjk", "$total_tagihan", "$org_in", "$tgl_tremin", "$termin", "$no_rek", "$nama_bank", "$bvtyp", "$cabang_bank", "$no_kwitansi","$noBaF","$sampai","10", $id_user_approval);
                $tablename = "EX_BA";
                // print_r($field_names)."<br>";
                // print_r($field_data);
                // print_r($fungsi->insert($conn, $field_names, $field_data, $tablename))
                $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

                //INSERT LOG HISTORY BA
                $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
                $field_data = array("$NO_BA_in","10","OPEN","$user_id","SYSDATE");
                $tablename = "EX_BA_TRACK";
                $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
                // print_r($field_names, $field_data, $tablename);
                $show_ket .= "BASTP Sukses Di Buat Dengan No $NO_BA_in <br>";
            // }

            // $dendak3->pemotongan_dendak3($conn, $NO_BA_in, $no_vendor_in, $total_shp_in, $user_name);
            $total_tagihan50 = floatval($total_shp_in) * 0.5;
            //SQL GET DENDA YG SUDAH TERPOTONG
            // if($orgSpj == '7000'){
            //     $sql = "SELECT
            //     TABLEA.ID_VENDOR,
            //     TABLEA.NO_DOKUMEN,
            //     TABLEB.TERPOTONG,
            //     TABLEA.JUMLAH,
            //     (
            //         TABLEA.JUMLAH - TABLEB.TERPOTONG
            //     ) AS SISA
            // FROM
            //     (
            //         SELECT
            //             ID_VENDOR,
            //             NO_DOKUMEN,
            //             TOTAL_DENDA AS JUMLAH
            //         FROM
            //             EX_DENDAK3_DOC
            //         WHERE
            //             ID_VENDOR = '$no_vendor_in'
            //     ) TABLEA
            // LEFT JOIN (
            //     SELECT
            //         NO_DOC_DENDA AS NO_DOKUMEN,
            //         NVL (SUM(KREDIT), 0) AS TERPOTONG
            //     FROM
            //         EX_DENDAK3_SALDO
            //     GROUP BY
            //         NO_DOC_DENDA
            // ) TABLEB ON TABLEA.NO_DOKUMEN = TABLEB.NO_DOKUMEN";

            // $queryk3 = oci_parse($conn, $sql);
            // $raw = oci_execute($queryk3);

            // $affected_doc = array();
            // $nilai_denda = 0; //nilai denda yang akan dipotongkan
            // $nilai_saldo = 0;

            // while ($row = oci_fetch_array($queryk3)) {
            //     $tmp_denda = 0;
            //     //jika ada sisa dari tansaksi sbelumnya akan mengambil kolom sisa
            //     if ($row[SISA] == null) {
            //         $tmp_denda = floatval($row[JUMLAH]);
            //     } else {
            //         $tmp_denda = floatval($row[SISA]);
            //     }
            //     // print_r($row);
            //     $jumlah_denda = floatval($row[JUMLAH]);
            //     $no_doc = $row[NO_DOKUMEN];
            //     //jika dibawah 50% oa maka akan langsung di potongkan
            //     // $show_ket .= "$total_tagihan50 .<<< $no_doc    $tmp_denda  -> ";

            //     if ($total_tagihan50 > $tmp_denda) { //jika nilai tagihan invoice > dari denda k3
            //         $total_tagihan50 -= $tmp_denda;
            //         $nilai_denda +=$tmp_denda;
            //         // $this->insert($conn, $username, $tmp_denda, $no_doc, $NO_BA, $jumlah_denda);
            //         if ($tmp_denda > 0) {
            //             $sqlk31 = "
            //         INSERT INTO EX_DENDAK3_SALDO 
            //         (KREDIT, SALDO, NO_DOC_DENDA, NO_INVOICE, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
            //         VALUES 
            //         ($tmp_denda, $jumlah_denda, '$no_doc', '$NO_BA_in', SYSDATE, SYSDATE, '$user_name')
            //     ";

            //             $queryk31 = oci_parse($conn, $sqlk31);
            //             $res = oci_execute($queryk31);
            //         }
            //     } else {
            //         //jika melebihi 5-% OA maka akan otomatis mengambil nilai dari 50% OA tersebut
            //         //Jika nilai denda melebihi 50% nilai invoice yang ditagihkan maka nilai denda sebesar 50% nilai invoice

            //         $denda50 = $total_tagihan50;
            //         // $sisa_denda = $tmp_denda - $denda50;
            //         // $this->insert($conn, $username, $denda50, $no_doc, $NO_BA, $jumlah_denda);
            //         if ($denda50 > 0) {
            //             $sqlk32 = "
            //         INSERT INTO EX_DENDAK3_SALDO 
            //         (KREDIT, SALDO, NO_DOC_DENDA, NO_BA, DATE_INVOICE, DATE_UPDATE, UPDATE_BY)
            //         VALUES 
            //         ($denda50, $jumlah_denda, '$no_doc', '$NO_BA_in', SYSDATE,SYSDATE, '$user_name')
            //     ";

            //             $queryk32 = oci_parse($conn, $sqlk32);
            //             $res = oci_execute($queryk32);
            //         }


            //         break;
            //     }
            //  }
            // }
            //start  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1
            //Bundle POEX
            $dateNow = date('Y-m');
            $datepastMonth = date('Y-m', strtotime("-1 month"));
            $ketPOX = $totTransPOEX = $ketNiliPotongan = $saldoPOEX = '';
            // $sqlCPOEX = "
            //         SELECT
            //         *
            // FROM
            //         (
            //                 SELECT
            //                         TABLEA. ID,
            //                         TABLEA. ORG,
            //                         TABLEA. NAME,
            //                         TABLEA.EKSPEDITUR,
            //                         TABLEA.NUM,
            //                         TABLEA.PERSEN,
            //                         TABLEA.BA_NUMBER,
            //                         TABLEA.JUMLAH,
            //                         TABLEA.ACTIVE_DATE,
            //                         TABLEB.TERPOTONG,
            //                         TABLEA.CREATE_DATE,
            //                         TABLEA.PRIORTS,
            //                         (
            //                                 TABLEA.JUMLAH - TABLEB.TERPOTONG
            //                         ) AS SISA
            //                 FROM
            //                         (
            //                                 SELECT
            //                                         ID,
            //                                         ORG,
            //                                         NAME,
            //                                         NUM,
            //                                         EKSPEDITUR,
            //                                         BA_NUMBER,
            //                                         PERSEN,
            //                                         JUMLAH AS JUMLAH,
            //                                         PRIORTS,
            //                                         TO_CHAR (START_DATE, 'MM-YYYY') AS ACTIVE_DATE,
            //                                         CREATE_DATE
            //                                 FROM
            //                                         M_POTONGAN_OA
            //                                 WHERE
            //                                         EKSPEDITUR = '$no_vendor_in'
            //                                 AND ORG = '$orgSpj'
            //                                 AND IS_DELETE = '0' --AND TO_CHAR (START_DATE, 'YYYY-MM') LIKE '2018-05'
            //                                 AND TO_CHAR (START_DATE, 'YYYY-MM') BETWEEN '$datepastMonth'
            //                                 AND '$dateNow'
            //                                 ORDER BY
            //                                         PRIORTS ASC
            //                         ) TABLEA
            //                 LEFT JOIN (
            //                         SELECT
            //                                 NUM,
            //                                 NVL (SUM(NILAI_TRANSAKSI), 0) AS TERPOTONG
            //                         FROM
            //                                 M_POTONGAN_OA_TRANS
            //                         GROUP BY
            //                                 NUM
            //                 ) TABLEB ON TABLEA.NUM = TABLEB.NUM
            //                 WHERE
            //                         (
            //                                 TABLEA.JUMLAH - TABLEB.TERPOTONG
            //                         ) > 0
            //                 OR (
            //                         TABLEA.JUMLAH - TABLEB.TERPOTONG
            //                 ) IS NULL
            //                 ORDER BY
            //                         TABLEA.PRIORTS
            //         )
            // WHERE
            //         SISA > 0
            // OR SISA IS NULL
            // AND ROWNUM = 1
            // ";
            // $queryCPOEX = oci_parse($conn, $sqlCPOEX);
            // oci_execute($queryCPOEX);
            // while ($rowkCPOEX = oci_fetch_array($queryCPOEX)) {
            //     $persen = $rowkCPOEX['PERSEN'] / 100;
            //     $totEksCost50 = $persen * $total_shp_in;
            //     $numBA = $rowkCPOEX['BA_NUMBER'];
            //     $nuM = $rowkCPOEX['NUM'];
            //     $id = $rowkCPOEX['ID'];
            //     if (is_null($rowkCPOEX['SISA']) && is_null($rowkCPOEX['TERPOTONG'])) {
            //         $nilaiPOEX = $rowkCPOEX['JUMLAH'];
            //     } else if ($rowkCPOEX['SISA'] > 0) {
            //         $nilaiPOEX = $rowkCPOEX['SISA'];
            //     } else {
            //         $nilaiPOEX = '0';
            //     }
            //     if ($nilaiPOEX > '0' && $nilaiPOEX < $totEksCost50) {
            //         $sqlIPOEX = "
            //                 INSERT INTO M_POTONGAN_OA_TRANS
            //                 VALUES
            //                         (
            //                                 sequence_um_master.nextval,
            //                                 '$numBA',
            //                                 '$NO_BA_in',
            //                                 'create_ba',
            //                                 SYSDATE,
            //                                 '0',
            //                                 '$nilaiPOEX',
            //                                 '0',
            //                                 SYSDATE,
            //                                 '$user_name',
            //                                 '',
            //                                 '',
            //                                 '',
            //                                 '$nuM'
            //                         )";
            //         $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
            //         $insertPOEXTrans = oci_execute($querryIPOEXT);
            //         if (!$insertPOEXTrans) {
            //             $error = oci_error($querryIPOEXT);
            //             echo "Insert MPOEXT failed-1";
            //             exit();
            //         }
            //     } else if ($nilaiPOEX > '0') {
            //         $saldoPOEX = $nilaiPOEX - $totEksCost50;
            //         $sqlIPOEX = "
            //                 INSERT INTO M_POTONGAN_OA_TRANS
            //                 VALUES
            //                         (
            //                                 sequence_um_master.nextval,
            //                                 '$numBA',
            //                                 '$NO_BA_in',
            //                                 'create_ba',
            //                                 SYSDATE,
            //                                 '$saldoPOEX',
            //                                 '$totEksCost50',
            //                                 '0',
            //                                 SYSDATE,
            //                                 '$user_name',
            //                                 '',
            //                                 '',
            //                                 '',
            //                                 '$nuM'                       
            //                         )";
            //         $querryIPOEXT = oci_parse($conn, $sqlIPOEX);
            //         $insertPOEXTrans = oci_execute($querryIPOEXT);
            //         if (!$insertPOEXTrans) {
            //             $error = oci_error($querryIPOEXT);
            //             echo "Insert MPOEXT failed-2";
            //             exit();
            //         }
            //         break;
            //     }
            //     $totEksCost50 = $totEksCost50 - $nilaiPOEX;
            // }
            //end  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1

            // $sql_9 = "SELECT COUNT(*) AS HIT FROM EX_TRANS_HDR WHERE NO_TAGIHAN = '$inv_old' AND DELETE_MARK = '0' ";
            // $query_9 = oci_parse($conn, $sql_9);
            // oci_execute($query_9);
            // $row_9 = oci_fetch_assoc($query_9);
            // $data_9 = $row_9[HIT];

            // if ($data_9 > 850) {

            //     $field_names = array('PAJAK_N');
            //     $field_data = array("N");
            //     $tablename = "EX_TRANS_HDR";
            //     $field_id = array('NO_TAGIHAN');
            //     $value_id = array("$inv_old");
            //     $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            //     $field_names = array('PAJAK_N');
            //     $field_data = array("Y");
            //     $tablename = "EX_TRANS_HDR";
            //     $field_id = array('NO_BA');
            //     $value_id = array("$inv_old");
            //     $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            // } else {
            //     $field_names = array('NO_TAGIHAN');
            //     $field_data = array("");
            //     $tablename = "EX_TRANS_HDR";
            //     $field_id = array('NO_BA');
            //     $value_id = array("$inv_old");
            //     $fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
            // }
        //   } 
        } else {
            $show_ket .= "Data No Pajak Tidak Valid cek no pajak anda pada transaksi sebelumnya..";
        }
    } else {
        $show_ket .= "Org berbeda tidak bisa disatukan dalam satu invoice.!!!";
    }
   }else{
       $show_ket .= "Mohon maaf,jumlah spj maksimal per invoice harus 500";
   }


   //send email to kepala ditran
    $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = $id_user_approval";
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $data_email = $row[ALAMAT_EMAIL];
    // $emailTO = '<EMAIL>';
    // $emailTOCC = '<EMAIL>';
    $emailTO = $data_email;
    $emailTOCC = '';
    sendMailApprove($NO_BA_in,$org_in,$no_vendor_in,$nama_vendor_in,$total_semen_in,$total_pdpks_in,$total_shp_in,$emailTO,$emailTOCC);

    $habis = "create_ba.php";
    break;
//============================================================================================================================
//============================================================================================================================
        case "verif_ba":
            $user = new User_SP();
            $total = $_POST['total'];
            // print_r($total);exit;
            for ($k = 0; $k < $total; $k++) {
                $no_spj_v = "no_spj" . $k;
                $status_spj_v = "status_spj" . $k;
            $user_id = $_SESSION['user_id'];
            $user_name = $_SESSION['user_name'];
            $id_ba = $_POST['id_ba'];
            $no_ba = $_POST['no_ba'];
            $no_spj = $_POST[$no_spj_v];
            // print_r($no_spj);exit;
            $status_spj = $_POST[$status_spj_v];
            // print_r($status_spj);exit;
            // if($status_spj == '1'){
            //     $value_spj = 'OPEN';
            // }elseif($status_spj == '2'){
            //     $value_spj = 'REVERSE';
            // }else{
                $value_spj = 'SETUJU';
            // }
                        $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                        $field_data = array("VERIF ADMIN TRANSPORT", "SYSDATE", "$user_name", "$value_spj");
                        $tablename = "EX_TRANS_HDR";
                        $field_id = array('NO_BA', 'NO_SHP_TRN');
                        $value_id = array("$no_ba", "$no_spj");
                        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
    
                        $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                        $field_data = array("30", "SYSDATE", "$user_name");
                        $tablename = "EX_BA";
                        $field_id = array('NO_BA');
                        $value_id = array("$no_ba");
                        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
    
                        $show_ket .= "Verifikasi SPJ $no_spj by $user_name Sukses Di Tambahkan dengan rincian $value_spj <br>";
        }
    
                        $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
                        $field_data = array("$no_ba","30","WAITING APPROVAL PEJABAT TRANSPORTASI 1","$user_id","SYSDATE");
                        $tablename = "EX_BA_TRACK";
                        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
            $habis = "daftar_ba_trans.php";

            $query_ba = "SELECT
                            EX_BA.ID,
                            EX_BA.NO_BA,
                            EX_BA.NO_VENDOR,
                            EX_BA.TOTAL_INV,
                            EX_BA.PAJAK_INV,
                            EX_BA.NAMA_VENDOR,
                            EX_BA.KLAIM_KTG,
                            EX_BA.KLAIM_SEMEN,
                            EX_BA.PDPKS,
                            EX_BA.PDPKK,
                            EX_BA.DELETE_MARK,
                            EX_BA.ORG,
                            EX_BA.TOTAL_INVOICE,
                            EX_BA.TGL_BA,
                            EX_BA.STATUS_BA,
                            EX_BA.FILENAME,
                            EX_BA.ALASAN_REJECT,
                            EX_BA.ID_USER_APPROVAL,
                            SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
                            SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
                            SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
                            SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
                            SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
                            SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
                          SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
                          SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
                          SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
                          SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
                            to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
                        FROM
                            EX_BA
                            JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
                        WHERE EX_BA.DELETE_MARK = '0' 
                            AND EX_BA.NO_BA = '$no_ba'
                        GROUP BY EX_BA.ID,
                            EX_BA.NO_BA,
                            EX_BA.NO_VENDOR,
                            EX_BA.TOTAL_INV,
                            EX_BA.PAJAK_INV,
                            EX_BA.NAMA_VENDOR,
                            EX_BA.KLAIM_KTG,
                            EX_BA.KLAIM_SEMEN,
                            EX_BA.PDPKS,
                            EX_BA.PDPKK,
                            EX_BA.DELETE_MARK,
                            EX_BA.ORG,
                            EX_BA.TOTAL_INVOICE,
                            EX_BA.TGL_BA,
                            EX_BA.STATUS_BA,
                            EX_BA.FILENAME,
                            EX_BA.ALASAN_REJECT,
                            EX_BA.ID_USER_APPROVAL
                        ORDER BY
                            EX_BA.ID DESC";
            $sql_ba = oci_parse($conn, $query_ba);
            oci_execute($sql_ba);
    
            $data_ba = oci_fetch_array($sql_ba);
            // print_r($data);exit;
            $no_ba_v_ba = $data_ba['NO_BA'];
            $org_v_ba = $data_ba['ORG'];
            $no_vendor_v_ba = $data_ba['NO_VENDOR'];
            $nama_vendor_v_ba = $data_ba['NAMA_VENDOR'];
            $total_semen_v_ba = $data_ba['TOTAL_KLAIM_SEMEN'];
            $total_kantong_v_ba = $data_ba['TOTAL_KLAIM_KTG'];
            $total_ppdks_v_ba = $data_ba['PDPKS'];
            $total_inv_v_ba = $data_ba['SHP_COST'];
            // $last_updated_by_v_ba = $data_ba['LAST_UPDATED_BY'];
            //INSERT LOG HISTORY BA
            $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
            <div align=\"center\">
            <thead>
            <tr class=\"quote\">
            <td ><strong>&nbsp;&nbsp;No.</strong></td>
            <td align=\"center\"><strong>ORG</strong></td>
            <td align=\"center\"><strong>BA REKAPITULASI</strong></td>
            <td align=\"center\"><strong>EKSPEDITUR</strong></td>
            <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
            <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
            <td align=\"center\"><strong>KLAIM KANTONG</strong></td>
            <td align=\"center\"><strong>PDPKS</strong></td>
            <td align=\"center\"><strong>TOTAL OA</strong></td>
            <td align=\"center\"><strong>STATUS</strong></td>
            </tr>
            </thead>
            <tbody>";
    
            $email_content_table .= " 
            <td align=\"center\">1</td>
            <td align=\"center\">".$org_v_ba."</td>       
            <td align=\"center\">".$no_ba_v_ba."</td>
            <td align=\"center\">".$no_vendor_v_ba."</td>
            <td align=\"center\">".$nama_vendor_v_ba."</td>
            <td align=\"center\">".number_format($total_semen_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_kantong_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_ppdks_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_inv_v_ba,2,",",".")."</td>
            <td align=\"center\">Waiting Approval Pejabat Transportasi 1</td>
            </tr>";
            
            //sendEmail
            $mailCc = "";
            $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
            $query = oci_parse($conn, $sql);
            oci_execute($query);
            $row = oci_fetch_assoc($query);
            $mailTo = $row[ALAMAT_EMAIL];

            // sendmail ke KASIE
            // dev
            // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Approval BA Rekapitulasi Kasie' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
            // prod
            $kasie = $user->get_kasie();
            foreach($kasie as $k){
                if(!empty($k['ALAMAT_EMAIL'])){
                    if(empty($mailTo)){
                        $mailTo = $k['ALAMAT_EMAIL'];
                    }else{
                        $mailTo .= ','.$k['ALAMAT_EMAIL'];
                    }
                }
            }

            if(!empty($mailTo)){
                sendMail($mailTo, $mailCc, 'Notifikasi Approve BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
            }
            //end sendEmail
            break;
//============================================================================================================================
//============================================================================================================================

case "approve_kasie":
    $user = new User_SP();
    $total = $_POST['total'];
    // print_r($total);exit;
    for ($k = 0; $k < $total; $k++) {
        $no_spj_v = "no_spj" . $k;
        $status_spj_v = "status_spj" . $k;
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $id_ba = $_POST['id_ba'];
        // echo $id_ba;exit;
        $no_ba = $_POST['no_ba'];
        $no_spj = $_POST[$no_spj_v];
        $value_spj = 'SETUJU';
                $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                $field_data = array("APPROVE KASIE", "SYSDATE", "$user_name", "APPROVE KASIE");
                $tablename = "EX_TRANS_HDR";
                $field_id = array('NO_BA', 'NO_SHP_TRN');
                $value_id = array("$no_ba", "$no_spj");
                $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                $field_data = array("40", "SYSDATE", "$user_name");
                $tablename = "EX_BA";
                $field_id = array('NO_BA');
                $value_id = array("$no_ba");
                $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                // $show_ket .= "NO BA $no_ba Sukses Di Approve oleh $user_name <br>";
                $show_ket .= "Verifikasi SPJ $no_spj by $user_name Sukses Di Tambahkan dengan rincian $value_spj <br>";
}
    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
                    $field_data = array("$no_ba","40","Waiting Approval Pejabat Transportasi 2","$user_id","SYSDATE");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
    $habis = "kasie_ba_trans.php";

    $query_ba = "SELECT
                    EX_BA.ID,
                    EX_BA.NO_BA,
                    EX_BA.NO_VENDOR,
                    EX_BA.TOTAL_INV,
                    EX_BA.PAJAK_INV,
                    EX_BA.NAMA_VENDOR,
                    EX_BA.KLAIM_KTG,
                    EX_BA.KLAIM_SEMEN,
                    EX_BA.PDPKS,
                    EX_BA.PDPKK,
                    EX_BA.DELETE_MARK,
                    EX_BA.ORG,
                    EX_BA.TOTAL_INVOICE,
                    EX_BA.TGL_BA,
                    EX_BA.STATUS_BA,
                    EX_BA.FILENAME,
                    EX_BA.ALASAN_REJECT,
                    EX_BA.ID_USER_APPROVAL,
                    SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
                    SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
                    SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
                    SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
                    SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
                    SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
                  SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
                  SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
                  SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
                  SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
                    to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
                FROM
                    EX_BA
                    JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
                WHERE EX_BA.DELETE_MARK = '0' 
                    AND EX_BA.NO_BA = '$no_ba'
                GROUP BY EX_BA.ID,
                    EX_BA.NO_BA,
                    EX_BA.NO_VENDOR,
                    EX_BA.TOTAL_INV,
                    EX_BA.PAJAK_INV,
                    EX_BA.NAMA_VENDOR,
                    EX_BA.KLAIM_KTG,
                    EX_BA.KLAIM_SEMEN,
                    EX_BA.PDPKS,
                    EX_BA.PDPKK,
                    EX_BA.DELETE_MARK,
                    EX_BA.ORG,
                    EX_BA.TOTAL_INVOICE,
                    EX_BA.TGL_BA,
                    EX_BA.STATUS_BA,
                    EX_BA.FILENAME,
                    EX_BA.ALASAN_REJECT,
                    EX_BA.ID_USER_APPROVAL
                ORDER BY
                    EX_BA.ID DESC";
    $sql_ba = oci_parse($conn, $query_ba);
    oci_execute($sql_ba);

    $data_ba = oci_fetch_array($sql_ba);
    // print_r($data);exit;
    $no_ba_v_ba = $data_ba['NO_BA'];
    $org_v_ba = $data_ba['ORG'];
    $no_vendor_v_ba = $data_ba['NO_VENDOR'];
    $nama_vendor_v_ba = $data_ba['NAMA_VENDOR']; 
    $total_semen_v_ba = $data_ba['TOTAL_KLAIM_SEMEN'];
    $total_kantong_v_ba = $data_ba['TOTAL_KLAIM_KTG'];
    $total_ppdks_v_ba = $data_ba['PDPKS'];
    $total_inv_v_ba = $data_ba['SHP_COST'];
    // $last_updated_by_v_ba = $data_ba['LAST_UPDATED_BY'];
    //INSERT LOG HISTORY BA
    $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
    <div align=\"center\">
    <thead>
    <tr class=\"quote\">
    <td ><strong>&nbsp;&nbsp;No.</strong></td>
    <td align=\"center\"><strong>ORG</strong></td>
    <td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
    <td align=\"center\"><strong>EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
    <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
    <td align=\"center\"><strong>KLAIM KANTONG</strong></td>
    <td align=\"center\"><strong>PDPKS</strong></td>
    <td align=\"center\"><strong>TOTAL OA</strong></td>
    <td align=\"center\"><strong>STATUS</strong></td>
    </tr>
    </thead>
    <tbody>";

    $email_content_table .= " 
    <td align=\"center\">1</td>
    <td align=\"center\">".$org_v_ba."</td>       
    <td align=\"center\">".$no_ba_v_ba."</td>
    <td align=\"center\">".$no_vendor_v_ba."</td>
    <td align=\"center\">".$nama_vendor_v_ba."</td>
    <td align=\"center\">".number_format($total_semen_v_ba,0,",",".")."</td>
    <td align=\"center\">".number_format($total_kantong_v_ba,0,",",".")."</td>
    <td align=\"center\">".number_format($total_ppdks_v_ba,0,",",".")."</td>
    <td align=\"center\">".number_format($total_inv_v_ba,2,",",".")."</td>
    <td align=\"center\">Waiting Approval Pejabat Transportasi 2</td>
    </tr>";

    //sendEmail
    $mailCc = "";
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];

    // sendmail ke KABIRO
    // dev
    // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Approval BA Rekapitulasi Kabiro' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
    // prod
    $kabiro = $user->get_kabiro();
    foreach($kabiro as $k){
        if(!empty($k['ALAMAT_EMAIL'])){
            if(empty($mailTo)){
                $mailTo = $k['ALAMAT_EMAIL'];
            }else{
                $mailTo .= ','.$k['ALAMAT_EMAIL'];
            }
        }
    }

    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Notifikasi Approve BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
    }

    break;
//============================================================================================================================
//============================================================================================================================
case "approve_kabiro":
    $user = new User_SP();
    $total = $_POST['total'];
    // print_r($total);exit;
    for ($k = 0; $k < $total; $k++) {
        $no_spj_v = "no_spj" . $k;
        $status_spj_v = "status_spj" . $k;
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $id_ba = $_POST['id_ba'];
        
        $no_ba = $_POST['no_ba'];
        $no_spj = $_POST[$no_spj_v];
        $value_spj = 'SETUJU';
        if (isset($_FILES['upload_sign_kabiro']['name']))
                {
                $file_name = $_FILES['upload_sign_kabiro']['name'];
                // echo $file_name;exit;
                $file_tmp = $_FILES['upload_sign_kabiro']['tmp_name'];
         
                move_uploaded_file($file_tmp,"./upload/".$file_name);
                $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                $field_data = array("APPROVE KABIRO", "SYSDATE", "$user_name", "APPROVE KABIRO");
                $tablename = "EX_TRANS_HDR";
                $field_id = array('NO_BA', 'NO_SHP_TRN');
                $value_id = array("$no_ba", "$no_spj");
                $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                if($file_name != ''){
                    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY','FILENAME');
                    $field_data = array("50", "SYSDATE", "$user_name","$file_name");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }else{
                    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                    $field_data = array("50", "SYSDATE", "$user_name");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                }
                

                // $show_ket .= "NO BA $no_ba Sukses Di Approve oleh $user_name <br>";
                $show_ket .= "Verifikasi SPJ $no_spj by $user_name Sukses Di Tambahkan dengan rincian $value_spj <br>";
            }else{
                $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                $field_data = array("APPROVE KABIRO", "SYSDATE", "$user_name", "APPROVE KABIRO");
                $tablename = "EX_TRANS_HDR";
                $field_id = array('NO_BA', 'NO_SHP_TRN');
                $value_id = array("$no_ba", "$no_spj");
                $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
                $field_data = array("50", "SYSDATE", "$user_name");
                $tablename = "EX_BA";
                $field_id = array('NO_BA');
                $value_id = array("$no_ba");
                $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                // $show_ket .= "NO BA $no_ba Sukses Di Approve oleh $user_name <br>";
                $show_ket .= "Verifikasi SPJ $no_spj by $user_name Sukses Di Tambahkan dengan rincian $value_spj <br>";
            }
}
    // sendMail($kode_produk2,$nama_produk2,$kode_kantong2,$nama_kantong2,$distributor2,$nama_distributor2,$kode_provinsi2,$provinsi2,$volume_rilis2,$volume_rilis_so2,$total_sparebag2,$kelompok_transaksi2,$bulan2,$tahun2,$email,$emailCC);
    // $email = '<EMAIL>';
    // $emailCC = '<EMAIL>';
    // sendMail($no_ba,$emailCC);
    // exit;
        $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
                    $field_data = array("$no_ba","50","Completed","$user_id","SYSDATE");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
    $habis = "kabiro_ba_trans.php";

    //sendEmail
    $email_content_table = "";
    $mailCc = "";
    $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
    $query = oci_parse($conn, $sql);
    oci_execute($query);
    $row = oci_fetch_assoc($query);
    $mailTo = $row[ALAMAT_EMAIL];
    if(!empty($mailTo)){
        sendMail($mailTo, $mailCc, 'Notifikasi Approve BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
    }
    break;
    //============================================================================================================================
    //============================================================================================================================
    case "reject_ba":
        $total = $_POST['total'];
        $ket_cancel = $_POST['ket_cancel'];
        $spj_reject = "";
        for ($k = 0; $k < $total; $k++) {
        $no_spj_v = "no_spj" . $k;
        // $status_spj_v = "status_spj" . $k;
        $idke = "idke" . $k;
        $urutke = "urutke" . $k;
       
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $id_ba = $_POST['id_ba'];
        $no_ba = $_POST['no_ba'];
        $no_spj = $_POST[$no_spj_v];
        $cek = $_POST[$idke];
        // print_r($cek);exit;
        
        if($cek == ""){
            $spj_reject .= $no_spj.' REJECT. ';
            $value_spj = '<strong style="color:red">REJECT</strong>';
        }else{
            $value_spj = '<strong style="color:green">SETUJU</strong>';
        }
            
        
                    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                    $field_data = array("REJECT ADMIN TRANSPORT", "SYSDATE", "$user_name", "REJECT ADMIN TRANSPORT");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_BA', 'NO_SHP_TRN');
                    $value_id = array("$no_ba", "$no_spj");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY','TIPE_ALASAN','ALASAN_REJECT');
                    $field_data = array("11", "SYSDATE", "$user_name",'1',"$ket_cancel");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Verifikasi SPJ $no_spj by $user_name Di Tambahkan dengan rincian $value_spj <br>";
                    
        
    }
                    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT','KOMENTAR_REJECT');
                    $field_data = array("$no_ba","11","REJECTED","$user_id","SYSDATE","$ket_cancel");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
        $show_ket .= "<br><strong style='color:red'>NO BASTP $no_ba telah di reject karena ada SPJ dengan status REJECT dengan Keterangan $ket_cancel</strong>";
        $habis = "daftar_ba_trans.php";
        
        //sendEmail
        $mailCc = "";
        $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $row = oci_fetch_assoc($query);
        $mailTo = $row[ALAMAT_EMAIL];
        $email_content_table = '';
        if(!empty($mailTo)){
            sendMail($mailTo, $mailCc, 'Notifikasi Reject BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
        }
        //end sendEmail

        break;
    //============================================================================================================================
    case "reject_kasie":
        $total = $_POST['total'];
        // print_r($total);exit;
        $ket_cancel = $_POST['ket_cancel'];
        $spj_reject = "";
        for ($k = 0; $k < $total; $k++) {
        $no_spj_v = "no_spj" . $k;
        // $status_spj_v = "status_spj" . $k;
        $idke = "idke" . $k;
        $urutke = "urutke" . $k;
       
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $id_ba = $_POST['id_ba'];
        $no_ba = $_POST['no_ba'];
        $no_spj = $_POST[$no_spj_v];
        $cek = $_POST[$idke];
        // print_r($cek);exit;
        if($cek == ""){
            $spj_reject .= $no_spj.' REJECT. ';
            $value_spj = '<strong style="color:red">REJECT</strong>';
        }else{
            $value_spj = '<strong style="color:green">SETUJU</strong>';
        }
            
        
                    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                    $field_data = array("REJECT KASIE", "SYSDATE", "$user_name", "REJECT KASIE");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_BA', 'NO_SHP_TRN');
                    $value_id = array("$no_ba", "$no_spj");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY','TIPE_ALASAN','ALASAN_REJECT');
                    $field_data = array("11", "SYSDATE", "$user_name",'1',"$ket_cancel");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Verifikasi SPJ $no_spj by $user_name Di Tambahkan dengan rincian $value_spj <br>";
                    
        
    }
                    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT','KOMENTAR_REJECT');
                    $field_data = array("$no_ba","11","REJECTED","$user_id","SYSDATE","$ket_cancel");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
        $show_ket .= "<br><strong style='color:red'>NO BASTP $no_ba telah di reject karena ada SPJ dengan status REJECT dengan Keterangan $ket_cancel</strong>";
        $habis = "kasie_ba_trans.php";

        //sendEmail
        $mailCc = "";
        $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $row = oci_fetch_assoc($query);
        $mailTo = $row[ALAMAT_EMAIL];
        if(!empty($mailTo)){
            sendMail($mailTo, $mailCc, 'Notifikasi Reject BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
        }
        //end sendEmail

        break;
     //============================================================================================================================
     case "reject_kabiro":
        $total = $_POST['total'];
        $ket_cancel = $_POST['ket_cancel'];
        // print_r($total);exit;
        $spj_reject = "";
        for ($k = 0; $k < $total; $k++) {
        $no_spj_v = "no_spj" . $k;
        // $status_spj_v = "status_spj" . $k;
        $idke = "idke" . $k;
        $urutke = "urutke" . $k;
       
        $user_id = $_SESSION['user_id'];
        $user_name = $_SESSION['user_name'];
        $id_ba = $_POST['id_ba'];
        $no_ba = $_POST['no_ba'];
        $no_spj = $_POST[$no_spj_v];
        $cek = $_POST[$idke];
        // print_r($cek);exit;
        if($cek == ""){
            $spj_reject .= $no_spj.' REJECT. ';
            $value_spj = '<strong style="color:red">REJECT</strong>';
        }else{
            $value_spj = '<strong style="color:green">SETUJU</strong>';
        }
            
                    if (isset($_FILES['upload_sign_kabiro']['name']))
                    {
                    $file_name = $_FILES['upload_sign_kabiro']['name'];
                    // echo $file_name;exit;
                    $file_tmp = $_FILES['upload_sign_kabiro']['tmp_name'];
                    move_uploaded_file($file_tmp,"./upload/".$file_name);
                    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                    $field_data = array("REJECT KABIRO", "SYSDATE", "$user_name", "REJECT KABIRO");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_BA', 'NO_SHP_TRN');
                    $value_id = array("$no_ba", "$no_spj");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    if($file_name != ''){
                        $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY','FILENAME','TIPE_ALASAN','ALASAN_REJECT');
                        $field_data = array("11", "SYSDATE", "$user_name","$file_name",'1',"$ket_cancel");
                        $tablename = "EX_BA";
                        $field_id = array('NO_BA');
                        $value_id = array("$no_ba");
                        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }else{
                        $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY','TIPE_ALASAN','ALASAN_REJECT');
                        $field_data = array("11", "SYSDATE", "$user_name",'1',"$ket_cancel");
                        $tablename = "EX_BA";
                        $field_id = array('NO_BA');
                        $value_id = array("$no_ba");
                        $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
                    }
                    

                    $show_ket .= "Verifikasi SPJ $no_spj by $user_name Di Tambahkan dengan rincian $value_spj <br>";
                    }else{
                    $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
                    $field_data = array("REJECT KABIRO", "SYSDATE", "$user_name", "REJECT KABIRO");
                    $tablename = "EX_TRANS_HDR";
                    $field_id = array('NO_BA', 'NO_SHP_TRN');
                    $value_id = array("$no_ba", "$no_spj");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY','TIPE_ALASAN','ALASAN_REJECT');
                    $field_data = array("11", "SYSDATE", "$user_name",'1',"$ket_cancel");
                    $tablename = "EX_BA";
                    $field_id = array('NO_BA');
                    $value_id = array("$no_ba");
                    $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

                    $show_ket .= "Verifikasi SPJ $no_spj by $user_name Di Tambahkan dengan rincian $value_spj <br>";
                    }
                    
        
    }
                    $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT','KOMENTAR_REJECT');
                    $field_data = array("$no_ba","11","REJECTED","$user_id","SYSDATE","$ket_cancel");
                    $tablename = "EX_BA_TRACK";
                    $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
        $show_ket .= "<br><strong style='color:red'>NO BASTP $no_ba telah di reject karena ada SPJ dengan status REJECT dengan Keterangan $ket_cancel</strong>";
        $habis = "kabiro_ba_trans.php";

        //sendEmail
        $mailCc = "";
        $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba."' and STATUS_BA = 10";
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $row = oci_fetch_assoc($query);
        $mailTo = $row[ALAMAT_EMAIL];
        if(!empty($mailTo)){
            sendMail($mailTo, $mailCc, 'Notifikasi Reject BASTP', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan BASTP tsb.', $email_content_table);
        }
        //end sendEmail
        break;
//============================================================================================================================
}
?>
