<?php
session_start();
include('../include/my_fungsi.php');
include('../include/validasi.php');
require_once('../security_helper.php');
sanitize_global_input();

$fungsi = new my_fungsi();
$conn = $fungsi->koneksi();

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->get_halam_id($dirr);

$csrf_token = generate_csrf_token();

log_security_event('PAGE_ACCESS', 'edit_easyui.php accessed');

if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
  log_security_event('UNAUTHORIZED_ACCESS', 'User not authenticated');
  echo "Harap Login terlebih dahulu";
  exit();
}

?>

<script language="javascript">
  var message = "You dont have permission to right click";

  function clickIE() {
    if (document.all) {
      (message);
      return false;
    }
  }

  function clickNS(e) {
    if (document.layers || (document.getElementById && !document.all)) {
      if (e.which == 2 || e.which == 3) {
        (message);
        return false;
      }
    }
  }

  if (document.layers) {
    document.captureEvents(Event.MOUSEDOWN);
    document.onmousedown = clickNS;
  } else {
    document.onmouseup = clickNS;
    document.oncontextmenu = clickIE;
  }

  document.oncontextmenu = new Function("return false");

  function getXMLHTTP() {
    var xmlhttp = false;
    try {
      xmlhttp = new XMLHttpRequest();
    } catch (e) {
      try {
        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
      } catch (e) {
        try {
          xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e1) {
          xmlhttp = false;
        }
      }
    }
    return xmlhttp;
  }

  function validateInput(input) {
    return input.replace(/[<>\"'&]/g, '');
  }

  function sanitizeOutput(text) {
    return text.replace(/[<>\"'&]/g, function(match) {
      const escapeMap = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;',
        '&': '&amp;'
      };
      return escapeMap[match];
    });
  }
</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  <title>Aplikasi SGG Online : Setting Master Dokumen Invoice</title>
  <script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
  <script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
  <script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
  <script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
  <link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
  <link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
  <link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
  <link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
  <link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
  <link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
  <link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
  <link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
  <link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
  <script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
  <script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
  <script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
  <script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
  <script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>

<body>
  <div align="center">
    <table id="dg" title="Tabel Master Dokumen E-invoice" class="easyui-datagrid" style="width:1130px;height:350px">
      <thead>
        <tr>
          <th field="ID" data-options="hidden:true" width="20">ID</th>
          <th field="NAMA_DOKUMEN" width="30">Nama Dokumen</th>
        </tr>
      </thead>
    </table>
    <div id="toolbar">
      <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">New</a>
      <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="editAct()">Edit</a>
      <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" onclick="destroyAct()">Remove</a>
    </div>
  </div>

  <div id="dlg" class="easyui-dialog" style="width:516px;height:250px;padding:10px 20px" closed="true" resizable="true" buttons="#dlg-buttons">
    <!-- <div class="ftitle">Matrik TOP</div> -->
    <form id="fm" method="post" novalidate>
      <!-- CSRF Protection -->
      <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>" />
      <input type="hidden" name="ID" id="idmat" value="" />
      <div class="fitem">
        <label>Nama Dokumen:</label>
        <input id="NAMA_DOKUMEN" name="NAMA_DOKUMEN" class="easyui-textbox" style="width:200px;" 
               onblur="this.value=validateInput(this.value)" 
               onkeyup="this.value=validateInput(this.value)">
      </div>
    </form>
    <div id="dlg-buttons">
      <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px">Save</a>
      <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
    </div>
  </div>

  <script type="text/javascript">

    $('#dg').datagrid({
      // view: detailview,
      url: 'proses_easyui.php?action=listeasyui&csrf_token=<?php echo $csrf_token; ?>',
      toolbar: '#toolbar',
      pagination: true,
      idField: 'ID',
      rownumbers: true,
      fitColumns: true,
      singleSelect: true,
      onLoadSuccess: function(data) {
        console.log('Data loaded successfully');
      },
      onLoadError: function(xhr, status, error) {
        console.error('Data load error:', error);
        log_security_event('DATA_LOAD_ERROR', 'Error loading data: ' + error);
      }
    });

    $('#dg').datagrid('enableFilter');

    function newAct() {
      
      $('#dlg').dialog('open').dialog('setTitle', 'Tambah Master Dokumen');
      $('#fm').form('clear');
      
      log_security_event('NEW_ACTION', 'User initiated new document creation');
    }

    function editAct() {

      var row = $('#dg').datagrid('getSelected');
      if (row) {
        var sanitizedRow = {};
        for (var key in row) {
          if (row.hasOwnProperty(key)) {
            sanitizedRow[key] = sanitizeOutput(row[key]);
          }
        }
        
        $('#dlg').dialog('open').dialog('setTitle', 'Edit Master Dokumen');
        $('#fm').form('load', sanitizedRow);
        
        log_security_event('EDIT_ACTION', 'User editing document ID: ' + row.ID);
      } else {
        $.messager.show({
          title: 'Warning',
          msg: 'Anda belum mimilih data!!'
        });
      }
    }

    function saveAct() {

      var formData = $('#fm').serializeArray();
      var isValid = true;
      
      formData.forEach(function(item) {
        if (item.name === 'NAMA_DOKUMEN' && (item.value.length < 1 || item.value.length > 255)) {
          isValid = false;
          $.messager.show({
            title: 'Validation Error',
            msg: 'Nama Dokumen must be between 1 and 255 characters'
          });
        }
      });

      if (!isValid) return;

      $('#fm').form('submit', {
        url: 'proses_easyui.php?action=savedata',
        onSubmit: function() {
          return $(this).form('validate');
        },
        success: function(result) {
          try {
            var result = eval('(' + result + ')');
            console.log(result);
            if (result.errorMsg) {
              $.messager.show({
                title: 'Error',
                msg: sanitizeOutput(result.errorMsg)
              });
              log_security_event('SAVE_ERROR', 'Error saving data: ' + result.errorMsg);
            } else {
              $('#dlg').dialog('close');
              $('#dg').datagrid('reload');
              $('#fm').form('clear');
              log_security_event('SAVE_SUCCESS', 'Data saved successfully');
            }
          } catch (e) {
            console.error('JSON parse error:', e);
            log_security_event('JSON_PARSE_ERROR', 'Invalid JSON response: ' + result);
            $.messager.show({
              title: 'Error',
              msg: 'Invalid server response'
            });
          }
        },
        error: function(xhr, status, error) {
          log_security_event('AJAX_ERROR', 'AJAX error: ' + error);
          $.messager.show({
            title: 'Error',
            msg: 'Server communication error'
          });
        }
      });
    }

    function destroyAct() {

      var row = $('#dg').datagrid('getSelected');
      if (row) {
        $.messager.confirm('Confirm', 'Anda Yakin akan menghapus data Ini?', function(r) {
          if (r) {
            $.post('proses_easyui.php?action=deletedata&', {
              id: row.ID,
              csrf_token: '<?php echo $csrf_token; ?>'
            }, function(result) {
              if (result.success) {
                $('#dg').datagrid('reload');
                log_security_event('DELETE_SUCCESS', 'Document deleted: ' + row.ID);
              } else {
                $.messager.show({
                  title: 'Error',
                  msg: sanitizeOutput(result.errorMsg)
                });
                log_security_event('DELETE_ERROR', 'Error deleting document: ' + result.errorMsg);
              }
            }, 'json').fail(function(xhr, status, error) {
              log_security_event('DELETE_AJAX_ERROR', 'AJAX error during delete: ' + error);
              $.messager.show({
                title: 'Error',
                msg: 'Server communication error'
              });
            });
          }
        });
      } else {
        $.messager.show({
          title: 'Warning',
          msg: 'Anda belum mimilih data!!'
        });
      }
    }

    $(document).ready(function() {
      $('body').on('DOMNodeInserted', function(e) {
        if (e.target.nodeType === 3) { // Text node
          e.target.textContent = sanitizeOutput(e.target.textContent);
        }
      });
    });
  </script>

  <style type="text/css">
    #fm {
      margin: 0;
      padding: 10px;
    }

    .ftitle {
      font-size: 14px;
      font-weight: bold;
      padding: 0;
      margin-bottom: 7px;
      border-bottom: 1px solid #ccc;
    }

    .fitem {
      margin-bottom: 3px;
    }

    .fitem label {
      display: inline-block;
      width: 120px;
    }

    .fitem input {
      width: 160px;
    }

    @media print {
      input[name="csrf_token"] {
        display: none !important;
      }
    }
  </style>

  <br><br>

  <div class="nonPrint">
    <?php 
    log_security_event('PAGE_COMPLETE', 'edit_easyui.php loaded successfully');
    include('../include/ekor.php'); 
    ?>
  </div>
</body>
</html>
