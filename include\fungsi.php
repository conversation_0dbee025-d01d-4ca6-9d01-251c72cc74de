<?
include_once('sapclasses/sap.php');




class fungsi
{


	var $username_conn = "dev";
	var $password_conn = "semeru2";
//	var $db = '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
	var $db = '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = DEVSGG)(SERVER = DEDICATED)))';

	//     var $username_conn = "APPSGG";
	//     var $password_conn = "sgmerdeka99";
	// var $db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = cmsdb.sggrp.com )(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';

	public function __construct()
	{
		@header('Content-Type: text/html; charset=utf-8');
	}

	public function koneksi()
	{
		$conn = oci_connect($this->username_conn, $this->password_conn, $this->db, 'AL32UTF8');
		if (!$conn)
			return false;
		else
			return $conn;
	}


	/*	public function koneksi_tmp()
	{
		$conn_tmp = oci_connect($this->username_conn_tmp, $this->password_conn_tmp, $this->db_tmp );
		if (!$conn_tmp)
			return false;
		else
		 return $conn_tmp;
	}
*/
	function app_bulan($ting)
	{
		$k = array('januari', 'februari', 'maret', 'april', 'mei', 'juni', 'juli', 'agustus', 'september', 'oktober', 'november', 'desember');
		$l = array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12');
		for ($x = 0; $x < count($k); $x++) {
			echo ("<option value='$l[$x]' title='$k[$x]' ");
			if ($l[$x] == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}

	function login($conn, $nama, $pass, $capt, $code)
	{
		$awo = "SELECT * FROM TB_USER_BOOKING WHERE NAMA='$nama' and PASSWORD1='$pass' and DELETE_MARK <> '1' ";
		$query = @oci_parse($conn, $awo);
		@oci_execute($query);
		$dataawo = @oci_fetch_assoc($query);
		$user_name = $dataawo['NAMA'];
		$passnya = $dataawo['PASSWORD1'];

		if ($nama == $user_name && $pass == $passnya && $capt == $code) {
			$_SESSION['user_name'] = $dataawo['NAMA'];
			$_SESSION['nama_lengkap'] = $dataawo['NAMA_LENGKAP'];
			$_SESSION['user_tipe'] = $dataawo['USER_TYPE'];
			$_SESSION['user_id'] = $dataawo['ID'];
			$_SESSION['distr_id'] = $dataawo['DISTRIBUTOR_ID'];
			$_SESSION['distr_nm'] = $dataawo['NAMA_DISTRIBUTOR'];
			$_SESSION['vendor_id'] = $dataawo['VENDOR'];
			$_SESSION['user_org'] = $dataawo['ORG'];
			$_SESSION['channel'] = $dataawo['CHANNEL'];
			if ($dataawo['ORG'] == '2000') {
				$_SESSION['user_org'] = 7000;
			}
			return true;
		}
		return false;
	}
	function del_session_user()
	{
		unset($_SESSION['user_name']);
		unset($_SESSION['nama_lengkap']);
		unset($_SESSION['user_tipe']);
		unset($_SESSION['user_id']);
		unset($_SESSION['user_org']);
		unset($_SESSION['distr_id']);
		unset($_SESSION['user_setbhs']);
		session_destroy();
	}

	function formatDateIndonesia($tanggal)
	{
		list($tgl, $bln, $thn) = split("-", $tanggal);
		if ($bln == "1") $bln = "Januari";
		else if ($bln == "2") $bln = "Februari";
		else if ($bln == "3") $bln = "Maret";
		else if ($bln == "4") $bln = "April";
		else if ($bln == "5") $bln = "Mei";
		else if ($bln == "6") $bln = "Juni";
		else if ($bln == "7") $bln = "Juli";
		else if ($bln == "8") $bln = "Agustus";
		else if ($bln == "9") $bln = "September";
		else if ($bln == "10") $bln = "Oktober";
		else if ($bln == "11") $bln = "November";
		else if ($bln == "12") $bln = "Desember";
		$newdate = $tgl . " " . $bln . " " . $thn;
		return $newdate;
	}
	// function hitungton($text, $qty)
	// {
	// 	$conn = $this->Koneksi();
	// 		$sql = "SELECT NTGEW FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 WHERE MAKTX = `$text` AND ROWNUM = 1";
	// 	$result = oci_parse($conn, $sql);
	// 	oci_execute($result);
	// 	$data = oci_fetch_assoc($result);
	// 	$tipe=$data['NTGEW'];
	// 	// $tipe = ereg_replace("[^0-9]", "", $text);
	// 	$ada = strlen($tipe);
	// 	if ($ada > 0) {
	// 		//$hitungton = ($result * $qty) / 1000;
	// 		$hitungton = ($tipe * $qty) / 1000;
	// 	} else {
	// 		$hitungton = $qty;
	// 	}
	// 	//return $data['NTGEW'];
	// 	return $hitungton;
	// }
	function hitungton($text, $qty)
{
    $conn = $this->Koneksi();
    $kalimat = preg_replace('/^\s+|\s+$/u', '', $text);
   $sql = "SELECT NTGEW FROM RFC_Z_ZCSD_LIST_MAT_SALES_2 WHERE MAKTX='$kalimat' AND ROWNUM = 1";
		$query = @oci_parse($conn, $sql);
		@oci_execute($query);
		$data = @oci_fetch_assoc($query);
		
    
    $tipe = $data['NTGEW'];
    
    $ada = strlen($tipe);
    
    if ($ada > 0) {
        // Menghitung tonase
        $hitungton = ($tipe * $qty) / 1000;
    } else {
        $hitungton = $qty;
    }
    
    return $hitungton;
   
}

	function kpl_new_antri($conn)
	{
		$sql = "SELECT KPL_TRANS_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result = oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc($result);
		$new_number = $data['NEXTVAL'];
		/*
		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='000000000'.$new_number;
		if($panjang==2)$new_number_ok='00000000'.$new_number;
		if($panjang==3)$new_number_ok='0000000'.$new_number;
		if($panjang==4)$new_number_ok='000000'.$new_number;
		if($panjang==5)$new_number_ok='00000'.$new_number;
		if($panjang==6)$new_number_ok='0000'.$new_number;
		if($panjang==7)$new_number_ok='000'.$new_number;
		if($panjang==8)$new_number_ok='00'.$new_number;
		if($panjang==9)$new_number_ok='0'.$new_number;
		*/
		return $new_number;
	}

	function qtyso($qty)
	{
		$panjang = strlen(strval($qty));
		if ($panjang == 1) $qtyso = '000000000000' . $qty;
		if ($panjang == 2) $qtyso = '00000000000' . $qty;
		if ($panjang == 3) $qtyso = '0000000000' . $qty;
		if ($panjang == 4) $qtyso = '000000000' . $qty;
		if ($panjang == 5) $qtyso = '00000000' . $qty;
		if ($panjang == 6) $qtyso = '0000000' . $qty;
		if ($panjang == 7) $qtyso = '000000' . $qty;
		if ($panjang == 8) $qtyso = '00000' . $qty;
		if ($panjang == 9) $qtyso = '0000' . $qty;
		if ($panjang == 10) $qtyso = '000' . $qty;
		if ($panjang == 11) $qtyso = '00' . $qty;
		if ($panjang == 12) $qtyso = '0' . $qty;
		return $qtyso;
	}
	function nospm($kode)
	{
		$panjang = strlen(strval($kode));
		if ($panjang == 1) $nospm = '00000' . $kode;
		if ($panjang == 2) $nospm = '0000' . $kode;
		if ($panjang == 3) $nospm = '000' . $kode;
		if ($panjang == 4) $nospm = '00' . $kode;
		if ($panjang == 5) $nospm = '0' . $kode;
		if ($panjang == 6) $nospm = $kode;
		return $nospm;
	}


	function sapcode($kode)
	{
		$panjang = strlen(strval($kode));
		if ($panjang == 1) $sapcode = '000000000' . $kode;
		if ($panjang == 2) $sapcode = '00000000' . $kode;
		if ($panjang == 3) $sapcode = '0000000' . $kode;
		if ($panjang == 4) $sapcode = '000000' . $kode;
		if ($panjang == 5) $sapcode = '00000' . $kode;
		if ($panjang == 6) $sapcode = '0000' . $kode;
		if ($panjang == 7) $sapcode = '000' . $kode;
		if ($panjang == 8) $sapcode = '00' . $kode;
		if ($panjang == 9) $sapcode = '0' . $kode;
		if ($panjang == 10) $sapcode = $kode;
		return $sapcode;
	}

	//nilai maximum dari sebuah field
	//$conn = koneksi database, 
	//tbl= table, 
	//$result = nama field yang akan dicek (satu field)
	public function maximum($conn, $tbl, $result)
	{
		$sql = "SELECT COUNT($result) AS $result FROM $tbl WHERE DELETE_MARK <> '1' ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	public function maxByOne($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT MAX($result) AS $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby' ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	//fungsi untuk mencek apakah ada isi data pada data base
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan ( array), 
	//$findby= isi field sebagai parameter pengecekan ( array),
	//$result = nama field yang akan dicek (satu field)
	public function isThere($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT COUNT($result) AS $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field[0]='$findby[0]'";
		for ($k = 1; $k < count($findby); $k++) {
			$sql .= 'AND ' . "$field[$k]='$findby[$k]'";
		}
		//echo $sql;
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}

	//fungsi untuk hitung jumlah  data base
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan ( array), 
	//$findby= isi field sebagai parameter pengecekan ( array),
	//$result = nama field yang akan dicek (satu field)
	public function findSum($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT SUM($result) AS $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby'";
		//echo $sql;
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
public function findSumByMany1($conn,$tbl,$field,$findby,$result){
		$sql="SELECT DISTINCT SUM($result) AS $result FROM $tbl WHERE DELETE_MARK=0 AND $field[0]='$findby[0]'";
		for($k=1;$k< count($findby);$k++)
		{
			$sql.=' AND '."$field[$k]='$findby[$k]'";
		}
		
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$row=oci_fetch_assoc($query);
		echo $sql;
		return $row[$result];
	}

	//fungsi untuk hitung jumlah data database dg banyak parameter
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan (array), 
	//$findby= isi field sebagai parameter pengecekan (array),
	//$result = nama field yang dicari (satu field)
	public function findSumByMany($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT SUM($result) AS $result FROM $tbl WHERE DELETE_MARK=0 AND $field[0]='$findby[0]'";
		for ($k = 1; $k < count($findby); $k++) {
			$sql .= ' AND ' . "$field[$k]='$findby[$k]'";
		}

		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		//echo $sql;
		return $row[$result];
	}

	//fungsi untuk mencari satu field data dari banyak field dimana isi field adalah unik.
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan (array), 
	//$findby= isi field sebagai parameter pengecekan (array),
	//$result = nama field yang dicari (satu field)
	public function findOneByMany($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field[0]='$findby[0]'";
		for ($k = 1; $k < count($findby); $k++) {
			$sql .= ' AND ' . "$field[$k]='$findby[$k]'";
		}

		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		//echo $sql;
		return $row[$result];
	}

	//fungsi untuk mencari satu field data dari satu field dimana isi field adalah unik.
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan , 
	//$findby= isi field sebagai parameter pengecekan ,
	//$result = nama field yang dicari (satu field)
	public function findOneByOneTrust($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT $result FROM $tbl WHERE $field = '$findby'  ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	public function findOneByOne($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby'  ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	public function findOneByOneTgl($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT to_char($result,'DD-MM-YYYY') $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby'  ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	public function findOneByOneTgl2($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT to_char($result,'DD-MM-YYYY HH24:MI:SS') $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby'  ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	//fungsi untuk mencari satu field data dari satu atau beberapa field dimana isi field yang di tangkap adalah nilai terakhir.
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan (array), 
	//$findby= isi field sebagai parameter pengecekan (array),
	//$result = nama field yang dicari (satu field)
	//$orderby = nama field untuk sortir
	public function findManyByOne($conn, $tbl, $field, $findby, $result, $orderby)
	{
		$sql = "SELECT $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby' ORDER BY $orderby ASC  ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		while ($row = oci_fetch_array($query)) {
			$kode = $row[$result];
		}
		return $kode;
	}

	//fungsi untuk mencari data berdasarkan like.
	//$conn = koneksi database, 
	//tbl= table, 
	//$field = nama field sebagai parameter pengecekan (array), 
	//$findby= isi like sebagai parameter pengecekan,
	//$result = nama field yang dicari (satu field)
	public function findManyByLike($conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT COUNT($result) AS $result FROM $tbl WHERE DELETE_MARK <> '1' AND ( $field[0] like '$findby'";
		for ($k = 1; $k < count($field); $k++) {
			$sql .= 'OR ' . "$field[$k] like '$findby'";
		}
		$sql .= " ) ";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	//untuk insert ke database
	//$conn= koneksi ke database
	//$field_names= nama-nama field database (array)
	//$field_data= isi dari field database yang akan diisi (array)
	//$tablename= nama table database

	public function insert($conn,$field_names,$field_data,$tablename)
	{

		$query = "INSERT INTO $tablename ($field_names[0]";
		for($k=1;$k< count($field_names);$k++)
		{
			$query.=', '."$field_names[$k]";
		}
		$query.=") VALUES ('$field_data[0]'";
		for($k=1;$k< count($field_data);$k++)
		{
			list($tang,$gal)=split("_",$field_data[$k]);
			if($tang=="instgl")
				$query.=', '."TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";	
			else if($field_data[$k]=='SYSDATE')	
				$query.=', '."$field_data[$k]";
                        else if ($tang == "TO" && substr($gal, 0, 4) == "Date")
                        $query.=', ' . "$field_data[$k]";
			else	
			$query.=', '."'$field_data[$k]'";
		}
			$query.=')';
			// echo"<br>". $query;
			
		$sqlquery= oci_parse($conn, $query);
		$result = oci_execute($sqlquery);
		return $result;
	}

	// public function insert($conn, $field_names, $field_data, $tablename)
	// {

	// 	$query = "INSERT INTO $tablename ($field_names[0]";
	// 	for ($k = 1; $k < count($field_names); $k++) {
	// 		$query .= ', ' . "$field_names[$k]";
	// 	}
	// 	$query .= ") VALUES ('$field_data[0]'";
	// 	for ($k = 1; $k < count($field_data); $k++) {
	// 		list($tang, $gal) = split("_", $field_data[$k]);
	// 		if ($tang == "instgl")
	// 			$query .= ', ' . "TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
	// 		else if ($field_data[$k] == 'SYSDATE')
	// 			$query .= ', ' . "$field_data[$k]";
	// 		else if ($tang == "TO" && substr($gal, 0, 4) == "Date")
	// 			$query .= ', ' . "$field_data[$k]";
	// 		else
	// 			$query .= ', ' . "'$field_data[$k]'";
	// 	}
	// 	$query .= ')';
	// 	// echo"<br>". $query;

	// 	$sqlquery = oci_parse($conn, $query);
	// 	$result = oci_execute($sqlquery);
	// 	return $result;
	// }

	//untuk update ke database
	//$conn = koneksi ke database
	//$field_names = nama-nama field database (array)
	//$field_data = isi dari field database yang akan diisi (array)
	//$tablename = nama table database
	//$field_id = nama field database yang dijadikan filter (array)
	//$value_id= nilai/data dari field database yang dijadikan filter (array)
	public function update($conn, $field_names, $field_data, $tablename, $field_id, $value_id)
	{
		$query = "UPDATE $tablename SET $field_names[0]='$field_data[0]'";
		for ($k = 1; $k < count($field_names); $k++) {
			list($tang, $gal) = split("_", $field_data[$k]);
			if ($tang == "updtgl")
				$query .= ', ' . "$field_names[$k]=TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
			else if ($field_data[$k] == 'SYSDATE')
				$query .= ', ' . "$field_names[$k]=$field_data[$k]";
			else
				$query .= ', ' . "$field_names[$k]='$field_data[$k]'";
		}
		$query .= " WHERE $field_id[0]='$value_id[0]'";
		for ($j = 1; $j < count($field_id); $j++) {
			$query .= ' AND ' . "$field_id[$j]='$value_id[$j]'";
		}
		// echo "<br><br>".$query;
		$sqlquery = oci_parse($conn, $query);
		$result = oci_execute($sqlquery);
		return $result;
	}
	
	function validate_identifier($name) {
		return preg_match('/^[A-Z_][A-Z0-9_]*$/i', $name);
	}


	public function insert_safe($conn, $field_names, $field_data, $tablename) {
		// Validasi nama tabel
		if (!$this->validate_identifier($tablename)) {
			throw new Exception("Invalid table name: ". $tablename);
		}

		// Validasi kolom
		foreach ($field_names as $col) {
			if (!$this->validate_identifier($col)) {
				throw new Exception("Invalid column name: " . $col);
			}
		}

		$query = "INSERT INTO " . $tablename . " (" . implode(', ', $field_names) . ") VALUES (";

		$bind_values = array();

		for ($i = 0; $i < count($field_data); $i++) {
			$val = $field_data[$i];
			$col = $field_names[$i];
			$ph  = ":val" . $i;

			// Tangani instgl_... sebagai TO_DATE
			if (strpos($val, 'instgl_') === 0) {
				$tanggal = substr($val, 7);
				$query .= "TO_DATE($ph, 'DD-MM-YYYY HH24:MI:SS')";
				$bind_values[$ph] = $tanggal;
			}
			// Tangani TO_Date(...) langsung dari input (jika sangat dibutuhkan — hati-hati!)
			elseif (strpos($val, 'TO_Date') === 0) {
				$query .= $val;
			}
			// Tangani SYSDATE tanpa bind
			elseif (strtoupper($val) === 'SYSDATE') {
				$query .= "SYSDATE";
			}
			// Nilai biasa, bind normal
			else {
				$query .= $ph;
				$bind_values[$ph] = $val;
			}

			if ($i < count($field_data) - 1) {
				$query .= ", ";
			}
		}

		$query .= ")";
		
		// Eksekusi query
		$stmt = oci_parse($conn, $query);
		if (!$stmt) {
			throw new Exception("Gagal parse SQL.");
		}

		// Bind semua nilai
		foreach ($bind_values as $ph => $val) {
			oci_bind_by_name($stmt, $ph, $bind_values[$ph]);
		}

		$result = oci_execute($stmt);
		if (!$result) {
			$e = oci_error($stmt);
			throw new Exception("OCI Error: " . $e['message']);
		}

		return true;
	}

	public function update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id) {
		// Validasi nama tabel
		if (!$this->validate_identifier($tablename)) {
			trigger_error('Invalid table name: ' . $tablename, E_USER_ERROR);
			return false;
		}

		// Validasi semua kolom
		$all_columns = array_merge($field_names, $field_id);
		for ($i = 0; $i < count($all_columns); $i++) {
			if (!$this->validate_identifier($all_columns[$i])) {
				trigger_error('Invalid column name: ' . $all_columns[$i], E_USER_ERROR);
				return false;
			}
		}

		$query = "UPDATE " . $tablename . " SET ";
		$set_clauses = array();
		$bind_values = array();

		for ($i = 0; $i < count($field_names); $i++) {
			$col = $field_names[$i];
			$val = $field_data[$i];

			if (strtoupper($val) === 'SYSDATE') {
				$set_clauses[] = $col . " = SYSDATE";
			} elseif (strpos($val, 'updtgl_') === 0) {
				$tanggal = substr($val, 7);
				$ph = ":tgl_" . $i;
				$set_clauses[] = $col . " = TO_DATE(" . $ph . ", 'DD-MM-YYYY HH24:MI:SS')";
				$bind_values[$ph] = $tanggal;
			} else {
				$ph = ":val_" . $i;
				$set_clauses[] = $col . " = " . $ph;
				$bind_values[$ph] = $val;
			}
		}

		$query .= implode(', ', $set_clauses);

		// WHERE clause
		$where_clauses = array();
		for ($j = 0; $j < count($field_id); $j++) {
			$col = $field_id[$j];
			$ph = ":id_" . $j;
			$where_clauses[] = $col . " = " . $ph;
			$bind_values[$ph] = $value_id[$j];
		}

		$query .= ' WHERE ' . implode(' AND ', $where_clauses);

		// Parse dan bind
		$stmt = oci_parse($conn, $query);
		if (!$stmt) {
			trigger_error("OCI Parse Error", E_USER_ERROR);
			return false;
		}

		foreach ($bind_values as $ph => $val) {
			oci_bind_by_name($stmt, $ph, $bind_values[$ph]);
		}

		$result = oci_execute($stmt);
		if (!$result) {
			$e = oci_error($stmt);
			trigger_error("OCI Execute Error: " . $e['message'], E_USER_ERROR);
			return false;
		}

		return true;
	}


	public function update_inv($conn, $field_names, $field_data, $tablename, $field_id, $value_id)
	{
		$query = "UPDATE $tablename SET $field_names[0]='$field_data[0]'";
		for ($k = 1; $k < count($field_names); $k++) {
			list($tang, $gal) = split("_", $field_data[$k]);
			if ($tang == "updtgl")
				$query .= ', ' . "$field_names[$k]=TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
			else if ($field_data[$k] == 'SYSDATE')
				$query .= ', ' . "$field_names[$k]=$field_data[$k]";
			else
				$query .= ', ' . "$field_names[$k]='$field_data[$k]'";
		}
		$query .= " WHERE $field_id[0]='$value_id[0]'";
		for ($j = 1; $j < count($field_id); $j++) {
			$query .= ' AND ' . "$field_id[$j] IN ('$value_id[$j]')";
		}
		// echo "<br><br>".$query;
		$sqlquery = oci_parse($conn, $query);
		$result = oci_execute($sqlquery);
		return $result;
	}

	//untuk insert ke database from database
	//$conn= koneksi ke database
	//$field_names= nama-nama field database (array)
	//$field_data= isi dari field database yang akan diisi (array)
	//$tablename= nama table database
	public function insertinto($conn, $field_names, $field_data, $tablenameto, $tablenamefrom, $field_id, $value_id)
	{
		$query = "INSERT INTO $tablenameto ($field_names[0]";
		for ($k = 1; $k < count($field_names); $k++) {
			$query .= ', ' . "$field_names[$k]";
		}
		$query .= ") (SELECT $field_data[0]";
		for ($k = 1; $k < count($field_data); $k++) {
			list($tang, $gal) = split("_", $field_data[$k]);
			if ($tang == "instgl")
				$query .= ', ' . "TO_Date( '$gal', 'DD-MM-YYYY HH24:MI:SS ')";
			else if ($field_data[$k] == 'SYSDATE')
				$query .= ', ' . "$field_data[$k]";
			else
				$query .= ', ' . "$field_data[$k]";
		}
		$query .= " FROM $tablenamefrom ";
		$query .= " WHERE $field_id[0]='$value_id[0]')";

		//echo $query;

		$sqlquery = oci_parse($conn, $query);
		@oci_execute($sqlquery);
	}


	//untuk delete ke database
	//$conn = koneksi ke database
	//$tablename = nama table database
	//$field_id = nama field database yang dijadikan filter (array)
	//$value_id= nilai/data dari field database yang dijadikan filter (array)
	public function delete($conn, $tablename, $field_id, $value_id)
	{
		$query = "DELETE FROM $tablename WHERE $field_id[0]='$value_id[0]'";
		for ($j = 1; $j < count($field_id); $j++) {
			$query .= ' AND ' . "$field_id[$j]='$value_id[$j]'";
		}

		$sqlquery = oci_parse($conn, $query);
		oci_execute($sqlquery);
	}
	public function close($conn)
	{
		oci_close($conn);
	}

	function acak()
	{
		srand((float) microtime() * 10000000);
		$angka = array("0", "1", "2", "3", "4", "5", "6", "7", "8", "9");
		$rand_angka = array_rand($angka, 2);
		$huruf = array("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");
		$rand_huruf = array_rand($huruf, 3);


		//$random=date('s') . date('i') . $huruf[$rand_huruf[0]] . $huruf[$rand_huruf[1]] . date('H') . date('d') .date('m') . date('y') ;
		//$random=date('s') . date('i') . $huruf[$rand_huruf[0]] . $huruf[$rand_huruf[1]] . date('y') . date('d') .date('m') ;
		$random = date('s') . date('i') . $huruf[$rand_huruf[0]] . $huruf[$rand_huruf[1]] . $huruf[$rand_huruf[2]] . date('y') . date('d') . date('m');
		return $random;
	}
	function acaklogin()
	{
		srand((float) microtime() * 10000000);
		$angka = array("0", "1", "2", "3", "4", "5", "6", "7", "8", "9");
		$rand_angka = array_rand($angka, 2);
		$huruf = array("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");
		$rand_huruf = array_rand($huruf, 2);
		$random = $angka[$rand_angka[0]] . $huruf[$rand_huruf[0]] . $huruf[$rand_huruf[1]] . $angka[$rand_angka[1]];
		return $random;
	}

	function aplikasi($conn, $fak)
	{
		$sql = "SELECT * FROM TB_APLIKASI WHERE DELETE_MARK <> '1' ORDER BY APLIKASI ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['APLIKASI'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "--Select Application--";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]' title='$daftar[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function distributor($conn, $fak)
	{
		$sql = "SELECT * FROM TB_DISTRIBUTOR WHERE DELETE_MARK <> '1' ORDER BY NAMA_LENGKAP ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ORACLE_DISTRIBUTOR_ID'];
			$nama[$a] = $row['NAMA_LENGKAP'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Distributor";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function distributoruser($conn, $user)
	{
		$sql = "SELECT DISTRIBUTOR_ID,(SELECT NAMA_LENGKAP FROM TB_DISTRIBUTOR WHERE ID=TB_DISTR_VS_PLANT.DISTRIBUTOR_ID) AS DISTRIBUTOR FROM TB_DISTR_VS_PLANT WHERE PLANT_CODE IN (SELECT PLANT FROM TB_USER_VS_PLANT WHERE USER_ID='$user')";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['DISTRIBUTOR_ID'];
			$nama[$a] = $row['DISTRIBUTOR'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Distributor";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $distr) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function arrayorg()
	{
		$arrayvalorg = array(
			'2000' => '2000 Semen Indonesia',
			'3000' => 'Semen Padang',
			'4000' => 'Semen Tonasa',
			'5000' => 'Semen Gresik',
			'6000' => 'TLCC',
			'7000' => 'Semen Indonesia',
			'7900' => 'MD Semen Indonesia',
			'1000' =>'Semen Baturaja'
		);
		return $arrayvalorg;
	}

	function tanggalSvOrder($tanggal) {
		$dateParts = explode(' ', $tanggal);
		$dateOnly = $dateParts[0];
		$formattedDate = str_replace('-', '', $dateOnly);
		return $formattedDate;
	}
	
	function org($ting='mana ku tahu, ya aku nggak tahu')
	{
		$kd = array(1 => '2000', '3000', '4000', '5000', '6000', '7000', '7900');
		$nm = array(1 => 'Semen Indonesia', 'Semen Padang', 'Semen Tonasa', 'Semen Gresik', 'TLCC', 'Semen Indonesia', 'MD Semen Indonesia');
		for ($x = 0; $x <= count($kd); $x++) {
			$kd_value = isset($kd[$x]) ? $kd[$x] : '';
			$nm_value = isset($nm[$x]) ? $nm[$x] : '';

			echo ("<option value='$kd_value'");
			if ($kd_value == $ting) {
				echo (" selected");
			}
			echo (">$nm_value</option>");
		}
	}

	function plant($conn, $fak)
	{
		$sql = "SELECT * FROM TB_PLANT WHERE DELETE_MARK <> '1' ORDER BY DESC_PLANT ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['KODE_PLANT'];
			$nama[$a] = $row['DESC_PLANT'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Plant";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function plantdl($conn, $fak)
	{
		$sql = "SELECT * FROM TB_PLANT WHERE KODE_PLANT LIKE '34%' AND DELETE_MARK <> '1' ORDER BY DESC_PLANT ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['KODE_PLANT'];
			$nama[$a] = $row['DESC_PLANT'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Plant";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function plantorder($conn, $distr)
	{
		$sql = "SELECT * FROM TB_DISTR_VS_PLANT WHERE DELETE_MARK <> '1' AND DISTRIBUTOR_ID='$distr' ORDER BY PLANT_NAME ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['PLANT_CODE'];
			$nama[$a] = $row['PLANT_NAME'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Plant";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $distr) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function incoterm($conn, $distr)
	{
		$sql = "SELECT * FROM TB_DISTR_VS_INCOTERM WHERE DELETE_MARK <> '1' AND DISTRIBUTOR_ID='$distr' ORDER BY INCOTERM_DESC ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['INCOTERM_CODE'];
			$nama[$a] = $row['INCOTERM_DESC'];
			$plant[$a] = $row['PLANT'];
			$daftar[$a] = $kode[$a] . ' - ' . $nama[$a];
		}
		$daftar[0] = "Pilih Incoterm";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$plant[$x]' value='$kode[$x]'");
			if ($kode[$x] == $distr) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function pembayaran($conn, $bayar)
	{
		$sql = "SELECT * FROM TB_PEMBAYARAN WHERE DELETE_MARK <> '1' ORDER BY PAYMENT_CODE ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['PAYMENT_CODE'];
			$nama[$a] = $row['PAYMENT_DESC'];
			$daftar[$a] = $kode[$a] . ' - ' . $nama[$a];
		}
		$daftar[0] = "Pilih Pembayaran";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function shipto($conn, $distr, $tujuan)
	{
		$sql = "SELECT * FROM TB_DISTR_VS_TUJUAN WHERE DISTRIBUTOR_ID='$distr' AND DELETE_MARK <> '1'";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['SAP_CODE'];
			$nama[$a] = $row['DISTRIBUTOR'];
			$kota[$a] = $row['TUJUAN'];
			$daftar[$a] = $kode[$a] . ' - ' . $kota[$a];
		}
		$daftar[0] = "Pilih Ship To";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$kota[$x]' value='$kode[$x]'");
			if ($kode[$x] == $tujuan) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function bank($conn, $bank)
	{
		$sql = "SELECT * FROM MASTER_BANK";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['INISIAL_BANK'];
			$nama[$a] = $row['NAMA_BANK'];
			$daftar[$a] = $kode[$a] . ' - ' . $nama[$a];
		}
		$daftar[0] = "Pilih Nama Bank";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $bank) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function provinsi($conn, $fak)
	{
		$sql = "SELECT NAMA_PROVINSI FROM TB_PROVINSI WHERE DELETE_MARK <> '1' ORDER BY NAMA_PROVINSI ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['NAMA_PROVINSI'];
			$nama[$a] = $row['NAMA_PROVINSI'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Provinsi";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function provinsi_1($conn, $fak)
	{
		$sql = "SELECT PROVINSI, KODE_PROVINSI FROM PT_MASTER_PROVINSI ORDER BY KODE_PROVINSI, PROVINSI ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['NAMA_PROVINSI'];
			$nama[$a] = $row['NAMA_PROVINSI'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Provinsi";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function syaratmuat($conn, $fak)
	{
		$sql = "SELECT * FROM SYARATPEMUATAN WHERE DELETE_MARK <> '1' ORDER BY PEMUATAN ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['PEMUATAN'];
			$nama[$a] = $row['DESCRIPTION'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Pemuatan";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function tipe_semen($conn, $fak)
	{
		$sql = "SELECT * FROM TIPE_SEMEN WHERE DELETE_MARK <> '1' ORDER BY TIPE_SEMEN ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['KODE_SEMEN'];
			$nama[$a] = $row['TIPE_SEMEN'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Tipe Semen";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function kemasan($conn, $fak)
	{
		$sql = "SELECT * FROM TIPE_KEMASAN WHERE DELETE_MARK <> '1' ORDER BY TIPE_KEMASAN ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['TIPE_KEMASAN'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Kemasan";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}

	function distributor_sas($conn, $fak)
	{
		$sql = "SELECT * FROM TB_DISTRIBUTOR_SAS WHERE DELETE_MARK <> '1' ORDER BY NAMA_LENGKAP ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['NAMA_LENGKAP'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Distributor";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function agen($conn, $fak)
	{
		$sql = "SELECT * FROM TB_AGEN WHERE DELETE_MARK <> '1' ORDER BY NAMA_LENGKAP ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['NAMA_LENGKAP'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Agen";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function agen2($conn, $fak, $fak2)
	{
		$sql = "SELECT * FROM TB_AGEN WHERE DISTRIBUTOR_ID = '$fak2' AND DELETE_MARK <> '1' ORDER BY NAMA_LENGKAP ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['NAMA_LENGKAP'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Agen";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function agen2sas($conn, $fak, $fak2)
	{
		$sql = "SELECT * FROM TB_AGEN_SAS WHERE DISTRIBUTOR_ID = '$fak2' AND DELETE_MARK <> '1' ORDER BY ID ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['NAMA_LENGKAP'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Agen";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function daftar($conn, $fak)
	{
		$sql = "SELECT * FROM TB_DAFTAR WHERE DELETE_MARK <> '1' ORDER BY ID ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['NO_BOOKING'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Nomor Booking";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function jenisantrian($conn, $ting)
	{
		$sql = "SELECT * FROM TB_JENIS_ANTRIAN ORDER BY ID ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['JENIS_ANTRIAN'];
			$daftar[$a] = $nama[$a];
		}
		$daftar[0] = "Pilih Jenis Antrian";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option title='$nama[$x]' value='$kode[$x]'");
			if ($kode[$x] == $ting) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function truk($conn, $fak)
	{
		$sql = "SELECT * FROM TB_TRUK WHERE DELETE_MARK <> '1' ORDER BY ID ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $row['NO_POLISI'];
			$nama2[$a] = $row['NO_STNK'];
			$daftar[$a] = $nama[$a] . "	>>	" . $nama2[$a];
		}
		$daftar[0] = "Pilih Truk";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function truk2($conn, $fak, $dis)
	{
		$sql = "SELECT * FROM TB_TRUK WHERE DISTRIBUTOR_ID = '$dis' AND DELETE_MARK <> '1' ORDER BY NO_POLISI ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$nama[$a] = $datauni['NO_POLISI'];
			$nama2[$a] = $datauni['STNK'];
			$daftar[$a] = $nama[$a] . "	>>	" . $nama2[$a];
		}
		$daftar[0] = "Pilih Truk";
		$kode[0] = "";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $fak) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function NoOrderAgen($conn, $ting, $distributor_id, $nomor_do)
	{
		if ($nomor_do == "") $nomor_do = '%';
		$sql = "SELECT * FROM TB_DO_DATA WHERE DISTRIBUTOR_='$distributor_id' and (STATUS ASSINGN IS NULL or STATUS_ASSIGN = 0) and ORDER_NO LIKE '$nomorD$nomor_do' ORDER BY ORDER_NO ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ID'];
			$daftar[$a] = $row['ORDER_NO'] . "     >>>      " . $row['TIPE_SEMEN'] . "      >>>      " . $row['QTY1'] . "   ZAK    ";
		}
		for ($x = 1; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $ting) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function NoOrderBooking($conn, $ting, $agen_id, $nomor_do)
	{
		if ($nomor_do == "") $nomor_do = '%';
		$uni = "SELECT * FROM DAFTARORDER_V WHERE AGEN_ID='$agen_id' and (STATUS_ORDER IS NULL OR STATUS_ORDER = 0) and ORDER_NO LIKE '$nomor_do' ORDER BY ORDER_NO ASC";
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$kode[$a] = $row['ORDER_ID'];
			$daftar[$a] = $row['ORDER_NO'] . "     >>>      " . $row['TIPE_SEMEN'] . "      >>>      " . $row['QTY1'] . "   ZAK    ";
		}
		for ($x = 1; $x <= $a; $x++) {
			echo ("<option value='$kode[$x]'");
			if ($kode[$x] == $ting) {
				echo ("selected");
			}
			echo (">$daftar[$x]</option>");
		}
	}
	function tipesemen($ting)
	{
		$k = array(1 => 'PPC', 'SMC', 'DO dalam Sumbar');
		for ($x = 0; $x <= count($k); $x++) {
			if ($x == 0) $x = "";
			echo ("<option value='$x'");
			if ($x == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}
	function tipebesar($ting)
	{
		$k = array('ALL', 'DO', 'SAS');
		for ($x = 0; $x < count($k); $x++) {
			echo ("<option value='$x'");
			if ($x == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}
	function usertipe($ting)
	{
		$k = array(1 => 'admin', 'agen', 'distributor', 'adminpemasaran', 'pemasaran', 'ppi', 'sas', 'agen_sas', 'akuntansi');
		for ($x = 0; $x <= count($k); $x++) {
			if ($x == 0) $x = "";
			$val = $k[$x];
			echo ("<option value='$val'");
			if ($val == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}
	function keamanan($halaman_id, $user_id)
	{
		$action_page = 0;
		if ($user_id != "" and $user_id != NULL) {
			$sql_action = "SELECT COUNT(*) AS ACTION FROM USER_HALAMAN_V WHERE DELETE_MARK <> '1' AND USER_ID = '$user_id' AND HALAMAN_ID = '$halaman_id' ORDER BY ACTION ASC";
			$query_action = oci_parse($this->koneksi(), $sql_action);
			oci_execute($query_action);
			$row_action = oci_fetch_assoc($query_action);
			$action_page = $row_action[ACTION];
		}
		return $action_page;
	}
	function keamanan0($user_id)
	{
		$action_page = 0;
		if ($user_id != "" and $user_id != NULL) {
			$action_page = 1;
		}
		return $action_page;
	}

	function clearsessi_kapasitas()
	{
		unset($_SESSION['jenis_antriannya']);
		unset($_SESSION['tgl_hariannya']);
		unset($_SESSION['kapasitasnya']);
	}
	function clearsessi_assign()
	{
		unset($_SESSION['order_no_assign']);
		unset($_SESSION['order_id_assign']);
		unset($_SESSION['item_no_assign']);
		unset($_SESSION['tipe_semen_assign']);
		unset($_SESSION['qty1_assign']);
	}
	function clearsessi_daftar()
	{
		unset($_SESSION['order_id_daftar']);
		unset($_SESSION['order_no_daftar']);
		unset($_SESSION['item_no_daftar']);
		unset($_SESSION['tipe_semen_daftar']);
		unset($_SESSION['qty1_daftar']);
	}
	function clearsessi_assign_sas()
	{
		unset($_SESSION['sas_no_assign']);
		unset($_SESSION['sas_id_assign']);
		unset($_SESSION['item_no_assign_sas']);
		unset($_SESSION['tipe_semen_assign_sas']);
		unset($_SESSION['qty1_assign_sas']);
	}
	function clearsessi_daftar_sas()
	{
		unset($_SESSION['sas_id_daftar']);
		unset($_SESSION['sas_no_daftar']);
		unset($_SESSION['item_no_daftar_sas']);
		unset($_SESSION['tipe_semen_daftar_sas']);
		unset($_SESSION['qty1_daftar_sas']);
	}
	function status_order($status)
	{
		if ($status == 0) $hasil = "Open";
		else if ($status == 1) $hasil = "Booking";
		else if ($status == 2) $hasil = "Check In";
		else if ($status == 3) $hasil = "Shipment";
		else if ($status == -1) $hasil = "Void";
		return $hasil;
	}
	function status_po($status)
	{
		if ($status == 0) $hasil = "Open";
		else if ($status == 1) $hasil = "SO Blok";
		else if ($status == 2) $hasil = "SO Approve";
		else if ($status == 3) $hasil = "Split DO";
		else if ($status == 4) $hasil = "Void";
		else if ($status == 5) $hasil = "Telah Dibayar";
		else if ($status == 6) $hasil = "Assign";
		return $hasil;
	}

	function new_daftar_number($conn)
	{
		$sql = "SELECT TB_DAFTAR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result = oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc($result);
		$BOL = $data['NEXTVAL'];
		return $BOL;
	}
	function Aksi($ting)
	{
		$k = array(1 => 'View', 'Add', 'Edit');
		for ($x = 0; $x <= count($k); $x++) {
			echo ("<option value='$x'");
			if ($x == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}
	function crm_new_so_number($conn)
	{
		$sql = "SELECT CRM_SO_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result = oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc($result);
		$new_number = $data['NEXTVAL'];

		$panjang = strlen(strval($new_number));
		if ($panjang == 1) $new_number_ok = '000000000' . $new_number;
		if ($panjang == 2) $new_number_ok = '00000000' . $new_number;
		if ($panjang == 3) $new_number_ok = '0000000' . $new_number;
		if ($panjang == 4) $new_number_ok = '000000' . $new_number;
		if ($panjang == 5) $new_number_ok = '00000' . $new_number;
		if ($panjang == 6) $new_number_ok = '0000' . $new_number;
		if ($panjang == 7) $new_number_ok = '000' . $new_number;
		if ($panjang == 8) $new_number_ok = '00' . $new_number;
		if ($panjang == 9) $new_number_ok = '0' . $new_number;
		return $new_number_ok;
	}
	function crm_new_spj($conn)
	{
		$sql = "SELECT CRM_SPJ_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result = oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc($result);
		$new_number = $data['NEXTVAL'];

		$panjang = strlen(strval($new_number));
		if ($panjang == 1) $new_number_ok = '000000000' . $new_number;
		if ($panjang == 2) $new_number_ok = '00000000' . $new_number;
		if ($panjang == 3) $new_number_ok = '0000000' . $new_number;
		if ($panjang == 4) $new_number_ok = '000000' . $new_number;
		if ($panjang == 5) $new_number_ok = '00000' . $new_number;
		if ($panjang == 6) $new_number_ok = '0000' . $new_number;
		if ($panjang == 7) $new_number_ok = '000' . $new_number;
		if ($panjang == 8) $new_number_ok = '00' . $new_number;
		if ($panjang == 9) $new_number_ok = '0' . $new_number;
		if ($panjang == 10) $new_number_ok = $new_number;
		return $new_number_ok;
	}
	function crm_new_spj_dtl($conn)
	{
		$sql = "SELECT CRM_SPJ_DTL_SEQ.NEXTVAL FROM SYS.DUAL";
		$result = oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc($result);
		$new_number = $data['NEXTVAL'];

		$panjang = strlen(strval($new_number));
		if ($panjang == 1) $new_number_ok = '000000000' . $new_number;
		if ($panjang == 2) $new_number_ok = '00000000' . $new_number;
		if ($panjang == 3) $new_number_ok = '0000000' . $new_number;
		if ($panjang == 4) $new_number_ok = '000000' . $new_number;
		if ($panjang == 5) $new_number_ok = '00000' . $new_number;
		if ($panjang == 6) $new_number_ok = '0000' . $new_number;
		if ($panjang == 7) $new_number_ok = '000' . $new_number;
		if ($panjang == 8) $new_number_ok = '00' . $new_number;
		if ($panjang == 9) $new_number_ok = '0' . $new_number;
		if ($panjang == 10) $new_number_ok = $new_number;
		return $new_number_ok;
	}
	function crm_new_spj_new($conn, $field)
	{
		$kd_dist = substr($field, 7, 10);

		$sql = "SELECT CRM_SPJ_DTL_SEQ.NEXTVAL FROM SYS.DUAL";
		$result = oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc($result);
		$new_number = $data['NEXTVAL'];

		$panjang = strlen(strval($new_number));
		if ($panjang == 1) $new_number_ok = "$kd_dist" . '000000' . $new_number;
		if ($panjang == 2) $new_number_ok = "$kd_dist" . '00000' . $new_number;
		if ($panjang == 3) $new_number_ok = "$kd_dist" . '0000' . $new_number;
		if ($panjang == 4) $new_number_ok = "$kd_dist" . '000' . $new_number;
		if ($panjang == 5) $new_number_ok = "$kd_dist" . '00' . $new_number;
		if ($panjang == 6) $new_number_ok = "$kd_dist" . '0' . $new_number;
		if ($panjang == 7) $new_number_ok = "$kd_dist" . $new_number;
		//                if($panjang==8)$new_number_ok='00'.$new_number;
		//                if($panjang==9)$new_number_ok='0'.$new_number;
		//		if($panjang==10)$new_number_ok=$new_number;
		return $new_number_ok;
	}
	function linenum($kode)
	{
		$panjang = strlen(strval($kode));
		if ($panjang == 1) $linenum = '00000' . $kode;
		if ($panjang == 2) $linenum = '0000' . $kode;
		if ($panjang == 3) $linenum = '000' . $kode;
		if ($panjang == 4) $linenum = '00' . $kode;
		if ($panjang == 5) $linenum = '0' . $kode;
		if ($panjang == 6) $linenum = $kode;
		return $linenum;
	}
	function gudang($kode)
	{
		$panjang = strlen(strval($kode));
		if ($panjang == 1) $gudang = '00' . $kode;
		if ($panjang == 2) $gudang = '0' . $kode;
		if ($panjang == 3) $gudang = $kode;
		return $gudang;
	}

	function Parent($ting)
	{
		$k = array('-', '1', '2', '3', '4');
		for ($x = 0; $x < count($k); $x++) {
			echo ("<option value='$x'");
			if ($k[$x] == $ting) {
				echo ("selected");
			}
			echo (">$k[$x]</option>");
		}
	}
	function Icon($ting)
	{
		$k = array(1 => 'folder', 'text');
		for ($x = 0; $x < count($k); $x++) {
			$y = $x + 1;
			echo ("<option value='$y'");
			if ($k[$y] == $ting) {
				echo ("selected");
			}
			echo (">$k[$y]</option>");
		}
	}
	function tipeuser($ting)
	{
		$k = array(1 => 'admin', 'pemasaran', 'distribusi', 'distributor', 'vendor', 'areamanager', 'toko');
		for ($x = 0; $x < count($k); $x++) {
			$y = $x + 1;
			echo ("<option value='$k[$y]'");
			if ($k[$y] == $ting) {
				echo ("selected");
			}
			echo (">$k[$y]</option>");
		}
	}
	function jabatan()
	{
		$jabatan = array(
			'2000' => '2000 Semen Indonesia',
			'SPC' => '30',
			'OI' => '31',
			'MANAGER OI' => '40',
			'IPP' => '50',
			'PETUGAS GUDANG' => '20',

		);
		return $jabatan;
	}

	//mapping com sales
	function getComin($conn, $orginj)
	{
		$arraData = array();
		$sql = "SELECT ORGIN 
				FROM OR_MAP_COM 
				WHERE orgm = :orginj AND delete_mark = 0 
				GROUP BY ORGIN";
		$query2 = oci_parse($conn, $sql);
		oci_bind_by_name($query2, ":orginj", $orginj);
		oci_execute($query2);

		while ($dataRec1 = oci_fetch_array($query2)) {
			$arraData[$dataRec1['ORGIN']] = $dataRec1['ORGIN'];
		}

		return $arraData;
	}

	function or_com($conn, $orgin)
	{
		$arraData = array();
		$sql = "select ORGIN from OR_MAP_COM where orgm='$orgin' and delete_mark=0
                    group by ORGIN";
		$query2 = oci_parse($conn, $sql);
		oci_execute($query2);
		$k = array();
		$k1 = array();
		$nama = $this->arrayorg();
		$i = 1;

		while ($dataRec1 = oci_fetch_array($query2)) {
			if ($dataRec1['ORGIN'] == '2000') {
				continue;
			} else {
				$k[$i] = $dataRec1['ORGIN'];
				$k1[$i] = $nama[$dataRec1['ORGIN']];
				$i++;
			}
		}

		for ($x = 1; $x <= count($k); $x++) {
			echo ("<option value='$k[$x]'");
			if ($k[$x] == $orgin) {
				echo ("selected");
			}
			echo (">$k[$x] $k1[$x]</option>");
		}
	}

	
	function or_pricelist3($ting){
        $orgkey=trim($_SESSION['user_org']);
        $sql_price= "select KEY from ZREPORT_M_PRICE 
                where DELETE_MARK=0 and ORG='$orgkey' and TIPE=2
                group by KEY
                ";		

        $conn = $this->koneksi();
        $query_price=oci_parse($conn,$sql_price);
        oci_execute($query_price);unset($dataprice);
        while ($price=oci_fetch_assoc($query_price)){
            $dataprice[$price['KEY']] = $price['KEY'];
        }
		$sql = "SELECT * FROM RFC_Z_ZAPPSD_MST_PRICE";
                               
                $query= oci_parse($conn, $sql);
                oci_execute($query);
                while($datafunc=oci_fetch_assoc($query)){
                    if(count($dataprice)>0){
                        if($dataprice[$datafunc["PLTYP"]]!=''){    
                            $price[]= $datafunc["PLTYP"];
                            $pricedesc[]= $datafunc["PTEXT"];
                        }
                    }else{
                        $price[]= $datafunc["PLTYP"];
                        $pricedesc[]= $datafunc["PTEXT"];
                    }  
                }
			
		for($x=0;$x<count($price);$x++)
		{
		echo("
		<option value='$price[$x]' title='$pricedesc[$x]' ");
		if($price[$x] == $ting){echo("selected");}
		echo(">$price[$x]"." - "."$pricedesc[$x]</option>");
		}
	
	}
	

	function or_pricelist2($ting, $tung){
        $orgkey=trim($_SESSION['user_org']);
        $sql_price= "select KEY from ZREPORT_M_PRICE 
                where DELETE_MARK=0 and ORG='$orgkey' and TIPE=2
                group by KEY
                ";		

        $conn = $this->koneksi();
        $query_price=oci_parse($conn,$sql_price);
        oci_execute($query_price);unset($dataprice);
        while ($price=oci_fetch_assoc($query_price)){
            $dataprice[$price['KEY']] = $price['KEY'];
        }
		$sql = "SELECT * FROM RFC_Z_ZAPPSD_MST_PRICE";
                               
                $query= oci_parse($conn, $sql);
                oci_execute($query);
                while($datafunc=oci_fetch_assoc($query)){
                    if(count($dataprice)>0){
                        if($dataprice[$datafunc["PLTYP"]]!=''){    
                            $price[]= $datafunc["PLTYP"];
                            $pricedesc[]= $datafunc["PTEXT"];
                        }
                    }else{
                        $price[]= $datafunc["PLTYP"];
                        $pricedesc[]= $datafunc["PTEXT"];
                    }  
                }
			
		for($x=0;$x<count($price);$x++)
		{
		echo("
		<option value='$price[$x]' title='$pricedesc[$x]' ");
		if($price[$x] == $ting){echo("selected");}
		if($price[$x] == $tung){echo("selected");}
		echo(">$price[$x]"." - "."$pricedesc[$x]</option>");
		}
	
	}

	public function getmainhalam_id($conn, $halaman)
	{
		$posisi = strpos($halaman, "sdonline/");
		$newdir = substr($halaman, $posisi);
		$lokasi = str_replace("sdonline/", "", $newdir);
		$lokasi = str_replace("sdonline/", "", $newdir);
		$array  = array();
		$query  = "SELECT ID FROM TB_MASTER_HALAMAN WHERE LOKASI_FILE='$lokasi' ";
		$query2 = oci_parse($conn, $query);
		oci_execute($query2);
		$result = oci_fetch_array($query2);
		return $result['ID'];
	}

	function nama_kapal($conn, $vendor)
	{
		$sql = "SELECT NAMA_KAPAL FROM EX_TRANS_HDR TH WHERE VENDOR = '$vendor' AND DELETE_MARK = 0 AND STATUS IN ('DRAFT', 'OPEN')
                AND ORG IN (SELECT ORGIN FROM OR_MAP_COM WHERE ORGIN = TH.ORG) AND NAMA_KAPAL IS NOT NULL GROUP BY NAMA_KAPAL";
				echo $sql;
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$a = 0;
		while ($row = oci_fetch_array($query)) {
			$a += 1;
			$namakapal[$a] = $row['NAMA_KAPAL'];
		}
		$namakapal[0] = "Pilih Nama Kapal";
		for ($x = 0; $x <= $a; $x++) {
			echo ("<option value='$namakapal[$x]'");
			echo (">$namakapal[$x]</option>");
		}
	}

	/* Find Fungsi Koneksi ke database Bo_conn */
	public function findOneByOne_Boconn($bo_conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field = '$findby'  ";
		$query = oci_parse($bo_conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
	}
	public function findOneByMany_Boconn($bo_conn, $tbl, $field, $findby, $result)
	{
		$sql = "SELECT $result FROM $tbl WHERE DELETE_MARK <> '1' AND $field[0]='$findby[0]'";
		for ($k = 1; $k < count($findby); $k++) {
			$sql .= ' AND ' . "$field[$k]='$findby[$k]'";
		}

		$query = oci_parse($bo_conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		return $row[$result];
		//echo $sql;
	}
}
