<?php
require_once '../../include/ex_fungsi.php';
require_once '../library/phpqrcode/qrlib.php';
require_once '../library/tcpdf/tcpdf.php';
require_once '../library/fpdi/fpdi.php';
require_once '../helper.php';
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$main_org = '1000';
$limit_approval_hour = get_batas_waktu_approval($conn, $main_org);
$level1 = $limit_approval_hour['LEVEL1'];
$level2 = $limit_approval_hour['LEVEL2'];
$level3 = $limit_approval_hour['LEVEL3'];
$level4 = $limit_approval_hour['LEVEL4'];

$query_ba = "SELECT
EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.ID_USER_APPROVAL,
EX_BA.SIGN_ORDER_ID_1,
EX_BA.SIGN_STATUS_1,
EX_BA.SIGN_TOKEN_1,
SUM(EX_TRANS_HDR.SHP_COST) AS SHP_COST,
SUM(EX_TRANS_HDR.PDPKS) AS PDPKS,
SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
FROM
EX_BA
JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
JOIN EX_BA_TRACK ON (EX_BA.NO_BA = EX_BA_TRACK.NO_BA AND EX_BA.STATUS_BA = EX_BA_TRACK.STATUS_BA)
WHERE EX_BA.DELETE_MARK = '0' 
AND (
(EX_BA.STATUS_BA = '10' AND EX_BA_TRACK.CREATED_AT >= (SYSDATE - NUMTODSINTERVAL($level1, 'HOUR')))
OR
(EX_BA.STATUS_BA = '20' AND EX_BA_TRACK.CREATED_AT >= (SYSDATE - NUMTODSINTERVAL($level2, 'HOUR')))
OR
(EX_BA.STATUS_BA = '30' AND EX_BA_TRACK.CREATED_AT >= (SYSDATE - NUMTODSINTERVAL($level3, 'HOUR')))
OR
(EX_BA.STATUS_BA = '40' AND EX_BA_TRACK.CREATED_AT >= (SYSDATE - NUMTODSINTERVAL($level4, 'HOUR')))
)
AND EX_BA_TRACK.CREATED_BY LIKE '0%'
AND EX_BA.ORG IN ('3000','1000')
GROUP BY EX_BA.ID,
EX_BA.NO_BA,
EX_BA.NO_VENDOR,
EX_BA.TOTAL_INV,
EX_BA.PAJAK_INV,
EX_BA.NAMA_VENDOR,
EX_BA.KLAIM_KTG,
EX_BA.KLAIM_SEMEN,
EX_BA.PDPKS,
EX_BA.PDPKK,
EX_BA.DELETE_MARK,
EX_BA.ORG,
EX_BA.TOTAL_INVOICE,
EX_BA.TGL_BA,
EX_BA.STATUS_BA,
EX_BA.FILENAME,
EX_BA.ALASAN_REJECT,
EX_BA.SIGN_ORDER_ID_1,
EX_BA.SIGN_STATUS_1,
EX_BA.SIGN_TOKEN_1,
EX_BA.ID_USER_APPROVAL
ORDER BY
EX_BA.ID DESC";
$sql_ba = oci_parse($conn, $query_ba);
oci_execute($sql_ba);

$show_ket = "";
$user = new User_SP($main_org);
while ($row = oci_fetch_assoc($sql_ba)) {
    $data = $row;
    $status = $data ['STATUS_BA'];
    // Auto Sign Pejabat Expeditur =============================================================================
    if($status == '10'){
        $no_ba = $data['ID'];
        $no_ba_v = $data['NO_BA'];
        $org_v = $data['ORG'];
        $no_vendor_v = $data['NO_VENDOR'];
        $nama_vendor_v = $data['NAMA_VENDOR'];
        $total_semen_v = $data['TOTAL_KLAIM_SEMEN'];
        $total_kantong_v = $data['TOTAL_KLAIM_KTG'];
        $total_ppdks_v = $data['PDPKS'];
        $total_inv_v = $data['SHP_COST'];
        $status_ba = $data['STATUS_BA'];
        $id_user_approval = $data ['ID_USER_APPROVAL'];

        $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
        <div align=\"center\">
        <thead>
        <tr class=\"quote\">
        <td ><strong>&nbsp;&nbsp;No.</strong></td>
        <td align=\"center\"><strong>ORG</strong></td>
        <td align=\"center\"><strong>BA REKAPITULASI</strong></td>
        <td align=\"center\"><strong>EKSPEDITUR</strong></td>
        <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
        <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
        <td align=\"center\"><strong>PDPKS</strong></td>
        <td align=\"center\"><strong>TOTAL</strong></td>
        <td align=\"center\"><strong>STATUS</strong></td>
        </tr>
        </thead>
        <tbody>";
        
        $email_content_table .= " 
        <td align=\"center\">1</td>
        <td align=\"center\">".$org_v."</td>       
        <td align=\"center\">".$no_ba_v."</td>
        <td align=\"center\">".$no_vendor_v."</td>
        <td align=\"center\">".$nama_vendor_v."</td>
        <td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
        <td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
        <td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
        <td align=\"center\">Open</td>
        </tr>";
        
        // Update filename
        $tableName = 'EX_BA';
        $field_id = array('ID');
        $value_id = array("$no_ba");
        $filename = "DokumenBA-$no_ba_v.pdf";
        $fieldNames = array('FILENAME');
        $fieldData = array($filename);

        $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);

        // URL to be encoded in QR Code
        $param = array(
            "no_ba" => $no_ba_v,
            "level" => "pejabat_eks"
        );
        $param = base64_encode(json_encode($param));
        $signUrl = get_base_url() . "ex_ba_sp/api/verify_sign_ba.php?kode=" . $param;
        $qrFile = dirname(__FILE__) . "/upload/qr_code.png";
        // Generate QR Code
        QRcode::png($signUrl, $qrFile, QR_ECLEVEL_L, 5);

        $pdfExporter = new PdfExporter();
        $response = $pdfExporter->beritaAcara($no_ba_v);

        // Menyimpan pdf ke dalam file di CSMS
        $pdf = fopen(dirname(__FILE__) . '/upload/' . $filename, 'w');
        fwrite($pdf, $response);
        fclose($pdf);

        // Open existing pdf
        $pdf = new FPDI();
        $pdf->AddPage();
        $pdf->setSourceFile( dirname(__FILE__) . '/upload/' . $filename); // Load existing PDF
        $tplIdx = $pdf->importPage(1);
        $pdf->useTemplate($tplIdx, 0, 0, 210);

        // Embed QR Code
        $pdf->Image('upload/qr_code.png', 25, 95, 20, 20, 'PNG');
        $pdf->Output(dirname(__FILE__) . '/upload/' . $filename, 'F');
        
        $tableName = 'EX_BA';
        $field_id = array('ID');
        $value_id = array("$no_ba");
        
        // update status
        $fieldNames = array('STATUS_BA');
        $fieldData = array('20');
        $fungsi->update_safe($conn, $fieldNames, $fieldData, $tableName, $field_id, $value_id);
        
        // track status
        $id_user_approval = '00' . $id_user_approval;
        $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
        $field_data = array("$no_ba_v","30","WAITING APPROVAL TRANSPORT","$id_user_approval","SYSDATE");
        $tablename = "EX_BA_TRACK";
        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
        
        //sendEmail
        $mailCc = "";
        $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba_v."' and STATUS_BA = 10";
        $query = oci_parse($conn, $sql);
        oci_execute($query);
        $row = oci_fetch_assoc($query);
        $mailTo = $row[ALAMAT_EMAIL];
        if(!empty($mailTo)){
            sendMail($mailTo, $mailCc, 'Notifikasi Approve Berita Acara Rekapitulasi', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table);
        }
        
        // dev
        // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Verif BA Rekapitulasi' and B.ORG = '".$org_v."' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
        // prod
        $mailTo = "";
        $data = array(
            "no_ba" => $no_ba_v,
            "org" => $main_org
        );
        $data = json_encode($data);
        $approve_param = "approve_ba_trans||$data";
        $approve_param_encode = base64_encode($approve_param);
        $approve_link = get_base_url() . "ex_ba_sp/via_email.php?kode=$approve_param_encode";

        $admin_trans = $user->get_admin_trans();
        foreach($admin_trans as $at){
        if(!empty($at['ALAMAT_EMAIL'])){
                $mailTo = $at['ALAMAT_EMAIL'];
            }
        }
        $mailTo .= ', <EMAIL>';
        sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve Berita Acara Rekapitulasi', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table, $approve_link);

        $show_ket .= 'Dokumen BASTP ( ' . $no_ba_v . ' ) berhasil ditandatangani oleh pejabat expeditur <br>';
        }
        // Auto Approve Admin Transport =============================================================================
        else if($status == '20'){
            $value_spj = 'SETUJU';
            $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
            $field_data = array("VERIF ADMIN TRANSPORT", "SYSDATE", "SYSTEM", "$value_spj");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('NO_BA');
            $value_id = array("$no_ba_v");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
            $field_data = array("30", "SYSDATE", "SYSTEM");
            $tablename = "EX_BA";
            $field_id = array('NO_BA');
            $value_id = array("$no_ba_v");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            // track status
            $admin_trans = $user->get_admin_trans();
            $id_admin_trans = '00' . $admin_trans[0]['ID'];
            $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
            $field_data = array("$no_ba_v","30","WAITING APPROVAL PEJABAT TRANSPORTASI 1","0","SYSDATE");
            $tablename = "EX_BA_TRACK";
            $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

            $no_ba_v_ba = $data['NO_BA'];
            $org_v_ba = $data['ORG'];
            $no_vendor_v_ba = $data['NO_VENDOR'];
            $nama_vendor_v_ba = $data['NAMA_VENDOR'];
            $total_semen_v_ba = $data['TOTAL_KLAIM_SEMEN'];
            $total_kantong_v_ba = $data['TOTAL_KLAIM_KTG'];
            $total_ppdks_v_ba = $data['PDPKS'];
            $total_inv_v_ba = $data['SHP_COST'];

            $email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
            <div align=\"center\">
            <thead>
            <tr class=\"quote\">
            <td ><strong>&nbsp;&nbsp;No.</strong></td>
            <td align=\"center\"><strong>ORG</strong></td>
            <td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
            <td align=\"center\"><strong>EKSPEDITUR</strong></td>
            <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
            <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
            <td align=\"center\"><strong>KLAIM KANTONG</strong></td>
            <td align=\"center\"><strong>PDPKS</strong></td>
            <td align=\"center\"><strong>TOTAL OA</strong></td>
            <td align=\"center\"><strong>STATUS</strong></td>
            </tr>
            </thead>
            <tbody>";

            $email_content_table .= " 
            <td align=\"center\">1</td>
            <td align=\"center\">".$org_v_ba."</td>       
            <td align=\"center\">".$no_ba_v_ba."</td>
            <td align=\"center\">".$no_vendor_v_ba."</td>
            <td align=\"center\">".$nama_vendor_v_ba."</td>
            <td align=\"center\">".number_format($total_semen_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_kantong_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_ppdks_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_inv_v_ba,2,",",".")."</td>
            <td align=\"center\">Waiting Approval Pejabat Transportasi 1</td>
            </tr>";

            //sendEmail
            $mailCc = "";
            $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba_v."' and STATUS_BA = 10";
            $query = oci_parse($conn, $sql);
            oci_execute($query);
            $row = oci_fetch_assoc($query);
            $mailTo = $row[ALAMAT_EMAIL];
            if(!empty($mailTo)){
                sendMail($mailTo, $mailCc, 'Notifikasi Approve Berita Acara Rekapitulasi', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table);
            }

            // sendmail ke KASIE
            // dev
            // $sql = "SELECT B.ALAMAT_EMAIL FROM TB_USER_RESPONSIBILITY A JOIN TB_USER_BOOKING B ON A.USER_ID = B.ID JOIN TB_MASTER_RESPONSIBILITY C ON A.RESPONSIBILITY_ID = C.ID where C.NAMA_RESPONSIBILITY='Approval BA Rekapitulasi Kasie' AND B.DELETE_MARK = 0 AND B.ALAMAT_EMAIL IS NOT NULL";
            // prod
            $mailTo = "";
            $kasie = $user->get_kasie();
            foreach($kasie as $k){
                if(!empty($k['ALAMAT_EMAIL'])){
                    $data = array(
                        "no_ba" => $no_ba_v_ba,
                        "username_approver" => $k['NAMA'],
                        "id_approver" => $k['ID'],
                        "org" => $main_org
                    );
                    $data = json_encode($data);
                    $approve_param = "approve_ba_kasie||$data";
                    $approve_param_encode = base64_encode($approve_param);
                    $approve_link = get_base_url() . "ex_ba_sp/via_email.php?kode=$approve_param_encode";

                    $mailTo = $k['ALAMAT_EMAIL'];
                    sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve Berita Acara Rekapitulasi', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table, $approve_link);
                }
            }

            $show_ket .= 'Dokumen BASTP ( ' . $no_ba_v . ' ) berhasil disetujui oleh admin transport <br>';
        }
        // Auto Approve Kasie =============================================================================
        else if($status == "30"){
            $value_spj = 'SETUJU';
            $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
            $field_data = array("APPROVE KASIE", "SYSDATE", "SYSTEM", "APPROVE KASIE");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('NO_BA');
            $value_id = array("$no_ba");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
            $field_data = array("40", "SYSDATE", "SYSTEM");
            $tablename = "EX_BA";
            $field_id = array('NO_BA');
            $value_id = array("$no_ba");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            // track
            $kasie = $user->get_kasie();
            $id_kasie = '00' . $kasie[0]['ID'];
            $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
            $field_data = array("$no_ba","40","Waiting Approval Pejabat Transportasi 2","0","SYSDATE");
            $tablename = "EX_BA_TRACK";
            $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

            $data_ba = oci_fetch_array($sql_ba);
            $no_ba_v_ba = $data_ba['NO_BA'];
            $org_v_ba = $data_ba['ORG'];
            $no_vendor_v_ba = $data_ba['NO_VENDOR'];
            $nama_vendor_v_ba = $data_ba['NAMA_VENDOR']; 
            $total_semen_v_ba = $data_ba['TOTAL_KLAIM_SEMEN'];
            $total_kantong_v_ba = $data_ba['TOTAL_KLAIM_KTG'];
            $total_ppdks_v_ba = $data_ba['PDPKS'];
            $total_inv_v_ba = $data_ba['SHP_COST'];

            $email_content_table = "
            <table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
            <div align=\"center\">
            <thead>
            <tr class=\"quote\">
            <td ><strong>&nbsp;&nbsp;No.</strong></td>
            <td align=\"center\"><strong>ORG</strong></td>
            <td align=\"center\"><strong>BASTP REKAPITULASI</strong></td>
            <td align=\"center\"><strong>EKSPEDITUR</strong></td>
            <td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
            <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
            <td align=\"center\"><strong>KLAIM KANTONG</strong></td>
            <td align=\"center\"><strong>PDPKS</strong></td>
            <td align=\"center\"><strong>TOTAL OA</strong></td>
            <td align=\"center\"><strong>STATUS</strong></td>
            </tr>
            </thead>
            <tbody>";

            $email_content_table .= " 
            <td align=\"center\">1</td>
            <td align=\"center\">".$org_v_ba."</td>       
            <td align=\"center\">".$no_ba_v_ba."</td>
            <td align=\"center\">".$no_vendor_v_ba."</td>
            <td align=\"center\">".$nama_vendor_v_ba."</td>
            <td align=\"center\">".number_format($total_semen_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_kantong_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_ppdks_v_ba,0,",",".")."</td>
            <td align=\"center\">".number_format($total_inv_v_ba,2,",",".")."</td>
            <td align=\"center\">Waiting Approval Pejabat Transportasi 2</td>
            </tr>";

            //sendEmail
            $mailCc = "";
            $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba_v_ba."' and STATUS_BA = 10";
            $query = oci_parse($conn, $sql);
            oci_execute($query);
            $row = oci_fetch_assoc($query);
            $mailTo = $row[ALAMAT_EMAIL];
            if(!empty($mailTo)){
                sendMail($mailTo, $mailCc, 'Notifikasi Approve Berita Acara Rekapitulasi', $no_ba_v_ba, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table);
            }

            $mailTo = "";
            $kabiro = $user->get_kabiro();
            foreach($kabiro as $k){
                if(!empty($k['ALAMAT_EMAIL'])){
                    $data = array(
                        "no_ba" => $no_ba_v_ba,
                        "username_approver" => $k['NAMA'],
                        "id_approver" => $k['ID'],
                        "org" => $main_org
                    );
                    $data = json_encode($data);
                    $approve_param = "approve_ba_kabiro||$data";
                    $approve_param_encode = base64_encode($approve_param);
                    $approve_link = get_base_url() . "ex_ba_sp/via_email.php?kode=$approve_param_encode";

                    $mailTo = $k['ALAMAT_EMAIL'];
                    sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve Berita Acara Rekapitulasi', $no_ba, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table, $approve_link);
                }
            }

            $show_ket .= 'Dokumen BASTP ( ' . $no_ba_v . ' ) berhasil disetujui oleh kasie <br>';
        }
        // Auto Approve Kabiro =============================================================================
        else if($status == "40"){
            $no_ba_v = $data['NO_BA'];
            $filename = $data['FILENAME'];

            // URL to be encoded in QR Code
            $param = array(
                "no_ba" => $no_ba_v,
                "level" => "kabiro"
            );
            $param = base64_encode(json_encode($param));
            $signUrl = get_base_url() . "ex_ba_sp/api/verify_sign_ba.php?kode=" . $param;
            $qrFile = dirname(__FILE__) . "/upload/qr_code.png";
            // Generate QR Code
            QRcode::png($signUrl, $qrFile, QR_ECLEVEL_L, 5);

            // Open existing pdf
            $pdf = new FPDI();
            $pdf->AddPage();
            $pdf->setSourceFile( dirname(__FILE__) . '/upload/' . $filename); // Load existing PDF
            $tplIdx = $pdf->importPage(1);
            $pdf->useTemplate($tplIdx, 0, 0, 210);

            // Embed QR Code
            $pdf->Image('upload/qr_code.png', 135, 95, 20, 20, 'PNG');
            $pdf->Output(dirname(__FILE__) . '/upload/' . $filename, 'F');

            $field_names = array('STATUS', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY', 'STATUS2');
            $field_data = array("APPROVE KABIRO", "SYSDATE", "SYSTEM", "APPROVE KABIRO");
            $tablename = "EX_TRANS_HDR";
            $field_id = array('NO_BA');
            $value_id = array("$no_ba");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            $field_names = array('STATUS_BA', 'LAST_UPDATE_DATE', 'LAST_UPDATED_BY');
            $field_data = array("50", "SYSDATE", "SYSTEM");
            $tablename = "EX_BA";
            $field_id = array('NO_BA');
            $value_id = array("$no_ba");
            $fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

            // track
            $kabiro = $user->get_kabiro();
            $id_kabiro = '00' . $kabiro[0]['ID'];
            $field_names = array('NO_BA','STATUS_BA','VALUE_BA','CREATED_BY','CREATED_AT');
            $field_data = array("$no_ba","50","Completed","0","SYSDATE");
            $tablename = "EX_BA_TRACK";
            $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);

            //sendEMail
            $email_content_table = "";
            $mailCc = "";
            $sql = "SELECT B.ALAMAT_EMAIL FROM EX_BA_TRACK A JOIN TB_USER_BOOKING B ON A.CREATED_BY = B.ID where A.NO_BA ='".$no_ba_v."' and STATUS_BA = 10";
            $query = oci_parse($conn, $sql);
            oci_execute($query);
            $row = oci_fetch_assoc($query);
            $mailTo = $row[ALAMAT_EMAIL];
            if(!empty($mailTo)){
                sendMail($mailTo, $mailCc, 'Otomatis - Notifikasi Approve Berita Acara Rekapitulasi', $no_ba_v, 'Mohon untuk ditindaklanjuti pengajuan Berita Acara tsb.', $email_content_table);
            }

            $show_ket .= 'Dokumen BASTP ( ' . $no_ba_v . ' ) berhasil disetujui oleh kabiro <br>';
        }
    }


if(!$show_ket){
    $show_ket = 'Tidak ada Dokumen BASTP yang dapat diproses';
}
?>

<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Pesan!</strong>
            <br>
            <br>
            <div class="alert alert-warning" role="alert"><?= $show_ket ?></div>
        </div>
    </div>