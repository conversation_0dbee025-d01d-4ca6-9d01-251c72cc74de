<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':
    $token_in = trim($_POST['token']);
    $role = $fautoris->login($token_in);
    $jmlData = count($role['dataUserAuto']);
    $urlFile = "https://dev-app.sig.id/dev/sd/sdonline/ex_report/"; // DEV
    // $urlFile = "https://csms.sig.id/sdonline/ex_report/"; // PROD

    $no_shipment = isset($_POST['NO_SPJ']) ? $_POST['NO_SPJ'] : '';
    $no_pajak = isset($_POST['NO_PAJAK']) ? $_POST['NO_PAJAK'] : '';
    $plantcf = isset($_POST['PLANT']) ? $_POST['PLANT'] : '';
    $distributor = isset($_POST['KODE_SOLDTO']) ? $_POST['KODE_SOLDTO'] : '';
    $tipe_transaksi = isset($_POST['TIPE_TRANSAKSI']) ? $_POST['TIPE_TRANSAKSI'] : '';
    $tgl_spj_from = isset($_POST['TGL_FROM']) ? $_POST['TGL_FROM'] : '';
    $tgl_spj_to = isset($_POST['TGL_TO']) ? $_POST['TGL_TO'] : '';
    $warna_plat = isset($_POST['WARNA_PLAT']) ? $_POST['WARNA_PLAT'] : '';
    $nopol = isset($_POST['NOPOL']) ? $_POST['NOPOL'] : '';
    $kapal = isset($_POST['NAMA_KAPAL']) ? $_POST['NAMA_KAPAL'] : '';
    $ekspeditur = isset($_POST['EKSPEDITUR']) ? $_POST['EKSPEDITUR'] : '';
    $status = isset($_POST['STATUS']) ? $_POST['STATUS'] : '';
    $status2 = isset($_POST['STATUS2']) ? $_POST['STATUS2'] : '';

    if ($role['status'] == true && $jmlData > 0) {

      $user_id = trim($role['dataUserAuto']['USER_ID']);
      $user_org = trim($role['dataUserAuto']['ORG']);
      // echo $user_org;exit;
      $mp_coics=$fautoris->getComin($fautoris->koneksi(),$user_org);
      // echo "<pre>";
      // print_r($mp_coics);
      // echo "</pre>";exit;
      if(count($mp_coics)>0){
          unset($inorg);$orgcounter=0;
          foreach ($mp_coics as $keyOrg => $valorgm){
                $inorg .="'".$keyOrg."',";
                $orgcounter++;
          }
          $inorg= rtrim($inorg, ',');        
      }else{
        $inorg= $user_org;
      }

      $dirr = $_SERVER['PHP_SELF'];
      if (empty($token_in)) {
        $responseRequest = array("responseCode" => 400, "responseMessage" => "Parameter tidak lengkap", "data" => null);
        header('Content-Type: application/json');
        echo json_encode($responseRequest);
      } else {
          if (empty($tgl_spj_from) && empty($tgl_spj_to)) {
            $responseRequest = array("responseCode" => 400, "responseMessage" => "Parameter tidak lengkap", "data" => null);
            header('Content-Type: application/json');
            echo json_encode($responseRequest);

            exit;
          }
          $sql="
            WITH Latest_Log_Semen_Pod AS ( SELECT az.*, ROW_NUMBER ( ) OVER ( PARTITION BY az.NO_SPJ ORDER BY az.LAST_UPDATE_DATE DESC ) AS row_num FROM LOG_SEMEN_POD az ) SELECT
            a.*,
            b.NO_DOC_HDR AS NO_DOC_HDR1,
            b.tahun,
            x.tgl_terima,
            c.accounting_doc 
            FROM
              (
              SELECT
                ay.ID,
                ay.PLANT,
                ay.TIPE_DO,
                ay.NO_SHP_TRN,
                ay.ORG,
                ay.NO_POL,
                ay.VEHICLE_TYPE,
                ay.WARNA_PLAT,
                ay.SUPIR,
                ay.VENDOR,
                ay.NAMA_VENDOR,
                ay.KODE_PRODUK,
                ay.NAMA_PRODUK,
                ay.NO_ENTRY_SHEET,
                ay.SAL_OFFICE,
                ay.NAMA_SAL_OFF,
                ay.SATUAN_SHP,
                ay.QTY_SHP,
                ay.TARIF_COST,
                ay.SHP_COST,
                ay.NO_SO,
                ay.INCO,
                ay.SOLD_TO,
                ay.NAMA_SOLD_TO,
                ay.SHIP_TO,
                ay.NAMA_SHIP_TO,
                ay.ALAMAT_SHIP_TO,
                ay.SAL_DISTRIK,
                ay.NAMA_SAL_DIS,
                ay.KODE_KECAMATAN,
                ay.NAMA_KECAMATAN,
                ay.NO_INV_VENDOR,
                ay.NO_INV_SAP,
                ay.ACCOUNTING_DOC,
                TO_CHAR( ay.TANGGAL_KIRIM, 'DD-MM-YYYY' ) AS TANGGAL_KIRIMF,
                TO_CHAR( ay.TANGGAL_DATANG, 'DD-MM-YYYY' ) AS TANGGAL_DATANGF,
                TO_CHAR( ay.TANGGAL_BONGKAR, 'DD-MM-YYYY' ) AS TANGGAL_BONGKARF,
                TO_CHAR( ay.TANGGAL_INVOICE, 'DD-MM-YYYY' ) AS TANGGAL_INVOICEF,
                TO_CHAR( ay.LAST_UPDATE_DATE, 'DD-MM-YYYY' ) AS LAST_UPDATE_DATEF,
                TO_CHAR( ay.TGL_CLEARING, 'DD-MM-YYYY' ) AS TGL_CLEARINGF,
                ay.QTY_KTG_RUSAK,
                ay.QTY_SEMEN_RUSAK,
                ay.TOTAL_KTG_RUSAK,
                ay.TOTAL_KTG_REZAK,
                ay.TOTAL_SEMEN_RUSAK,
                ay.TOTAL_KLAIM_KTG,
                ay.TOTAL_KLAIM_SEMEN,
                ay.HARGA_TEBUS,
                ay.PDPKS,
                ay.TOTAL_KLAIM_ALL,
                ay.PENGELOLA,
                ay.NAMA_PENGELOLA,
                ay.NAMA_KAPAL,
                ay.STATUS,
                ay.STATUS2,
                ay.NO_PAJAK_EX,
                TO_CHAR( ay.TANGGAL_SIAP_TAGIH, 'DD-MM-YYYY' ) AS TANGGAL_SIAP_TAGIH,
                CONCAT( ay.SOLD_TO, TO_CHAR( ay.TGL_CLEARING, 'MMYYYY' ) ) AS NOMORFB,
                ay.NO_INVOICE,
                ay.FLAG_POD,
                ay.KETERANGAN_POD,
                ay.EVIDENCE_POD1,
                ay.EVIDENCE_POD2,
                ay.GEOFENCE_POD,
                az.NO_SPJ,
                az.TANGGAL_SPJ,
                az.ORG AS ORG_LOG,
                az.FLAG_POD AS LOG_FLAG_POD,
                az.KETERANGAN_POD AS LOG_KETERANGAN_POD,
                az.EVIDENCE_POD1 AS LOG_EVIDENCE_POD1,
                az.EVIDENCE_POD2 AS LOG_EVIDENCE_POD2,
                az.GEOFENCE_POD AS LOG_GEOFENCE_POD,
                CASE
                  WHEN ay.FLAG_POD = 'POD-FIOS' THEN 'CARCON'
                  ELSE 'NON CARCON'
                END AS CARCON,
                CASE
                  WHEN ay.FLAG_POD IS NOT NULL THEN 'E' || ay.FLAG_POD
                  ELSE 'NON EPOD'
                END AS EPOD,
                zls.STANDART_AREA AS LEAD_TIME,
                to_char(ay.TANGGAL_BONGKAR, 'HH24:MI') AS JAM_BONGKAR,
                CASE
                  WHEN ay.TANGGAL_BONGKAR IS NOT NULL THEN 'SELESAI BONGKAR'
                  ELSE 'BELUM BONGKAR'
                END AS AUTO_SELL_IN
              FROM
                EX_TRANS_HDR ay
                LEFT JOIN Latest_Log_Semen_Pod az ON ay.NO_SHP_TRN = az.NO_SPJ 
                AND az.row_num = 1 
                LEFT JOIN ZMD_LEADTIME_SO zls ON
                ay.PLANT = zls.PLANT
                AND ay.SAL_DISTRIK = zls.KOTA
                AND SUBSTR(ay.KODE_PRODUK, 0, 7) = zls.KD_MATERIAL
              WHERE
                ay.DELETE_MARK = '0'
                AND ay.TANGGAL_KIRIM BETWEEN TO_DATE('".$tgl_spj_from."', 'DD-MM-YYYY') AND TO_DATE('".$tgl_spj_to."', 'DD-MM-YYYY')
        ";

        if($no_shipment!=""){
          $sql.=" AND ay.NO_SHP_TRN LIKE '$no_shipment' ";
        }
        
        if($distributor!=""){
          $sql.=" AND ( ay.SOLD_TO LIKE '$distributor' OR ay.NAMA_SOLD_TO LIKE '$distributor' ) ";			
        }
        
        if($ekspeditur!=""){
          $sql.=" AND ( ay.NAMA_VENDOR LIKE '$ekspeditur' OR ay.VENDOR LIKE '$ekspeditur' ) ";			
        }
        
        if($tipe_transaksi!=""){
          $sql.=" AND ay.TIPE_TRANSAKSI LIKE '$tipe_transaksi' ";			
        }
        
        if($plantcf!=""){
          $sql.=" AND ay.PLANT LIKE '$plantcf' ";			
        }

        if($warna_plat!=""){
          $sql.=" AND ay.WARNA_PLAT LIKE '$warna_plat' ";			
        }

        if($status!=""){
          $sql.=" AND ay.STATUS = '$status' ";			
        }

        if($status2!=""){
          $sql.=" AND ay.STATUS2 = '$status2' ";
        }

        if($nopol!=""){			
          $sql.=" AND ay.NO_POL LIKE '$nopol' ";			
        }

        if($kapal!=""){
          $sql.=" AND ay.NAMA_KAPAL LIKE '$kapal' ";
        }
                    if($no_pajak!=""){
          $sql.=" AND ay.NO_PAJAK_EX LIKE '$no_pajak' ";
        }
                    
        if($org!=""){
          $sql.=" AND ay.ORG in = '$org' ";
        }

        $sql .=")
          a left join (
        select CONCAT(SOLD_TO, concat(BULAN, TAHUN)) as FB60_DOC_NO,TAHUN,NO_DOC_HDR
        from EX_FB60 where DELETE_MARK= '0' and ORG IN ($inorg)
        group by CONCAT(SOLD_TO, concat(BULAN, TAHUN)),TAHUN,NO_DOC_HDR
      
        ) b
        ON (b.FB60_DOC_NO = a.NOMORFB)";
        $sql .=" LEFT JOIN (SELECT * FROM EX_INVOICE WHERE DELETE_MARK = '0' AND ORG IN ($inorg)) c ON (a.no_invoice = c.no_invoice)
            LEFT JOIN (SELECT * FROM KPI_TERIMA_INV_VENDOR WHERE no_invoice IS NOT NULL AND ORG IN ($inorg) AND del = '0') x ON (a.no_invoice = x.no_invoice)
            ORDER BY a.VENDOR, a.NO_SHP_TRN ASC";

        $query= oci_parse($fautoris->koneksi(), $sql);
        oci_execute($query);
        $dataAll = array();
        $datavoicesap = array();
        $data = array();
        $i = 0;
        while($row=oci_fetch_array($query)){
            $dataAll[$i] = $row;
            $datavoicesap[$row[ORG].$row[NO_DOC_HDR1].$row[TAHUN]]=$row[ORG]."|".$row[NO_DOC_HDR1]."|".$row[TAHUN];
            $i++;
        }

        $total=count($dataAll);
        if ($total > 0 && count($datavoicesap) > 0) {
          $sap = new SAPConnection();
          $sap->Connect("../include/sapclasses/logon_data.conf");
          
          if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
            if ($sap->GetStatus() != SAPRFC_OK ) {
            echo $sap->PrintStatus();
            exit;
          }

          //Pemanggilan RFC Cari
          $fce = $sap->NewFunction ("Z_ZAPPSD_STATUS_INVOICE");
          if ($fce == false ) { $sap->PrintStatus(); exit; }

          foreach ($datavoicesap as $key => $value) {
              $aarayDocv=explode("|",$value);
              $ORGdocv=$aarayDocv[0];
              $NO_DOC_HDR1dov=$aarayDocv[1];
              $TAHUNdocv=$aarayDocv[2];
              if($NO_DOC_HDR1dov!='' && $ORGdocv!='' && $TAHUNdocv!=''){
                  $fce->I_INPUT->row["BUKRS"] = $ORGdocv;
                  $fce->I_INPUT->row["BELNR"] = $NO_DOC_HDR1dov;
                  $fce->I_INPUT->row["GJAHR"] = $TAHUNdocv;
                  $fce->I_INPUT->Append($fce->I_INPUT->row);
              }
          }
          $fce->Call();
          if ($fce->GetStatus() == SAPRFC_OK ) {		
              $fce->T_OUT->Reset();
              $s=0;
              unset($datadoc);
              while ( $fce->T_OUT->Next() ){ 
                      $datadoc[$fce->T_OUT->row['BUKRS'].$fce->T_OUT->row['BELNR'].$fce->T_OUT->row['GJAHR']] = $fce->T_OUT->row;      
                  $s++;
              }
          }else
              $fce->PrintStatus();
              
          $fce->Close();	
          $sap->Close();
        }

        foreach ($dataAll as $k => $v) {
          $data[$k]['PLANT'] = $v['PLANT'];
          $data[$k]['TIPE_SPJ'] = $v['TIPE_DO'];
          $data[$k]['NO_SPJ'] = $v['NO_SHP_TRN'];
          $data[$k]['ORG'] = $v['ORG'];
          $data[$k]['NOPOL'] = $v['NO_POL'];
          $data[$k]['TIPE'] = $v['VEHICLE_TYPE'];
          $data[$k]['WARNA_PLAT'] = $v['WARNA_PLAT'];
          $data[$k]['DRIVER'] = $v['SUPIR'];
          $data[$k]['EXPEDITUR'] = $v['VENDOR'];
          $data[$k]['NAMA_EXPEDITUR'] = $v['NAMA_VENDOR'];
          $data[$k]['KODE_PRODUK'] = $v['KODE_PRODUK'];
          $data[$k]['NAMA_PRODUK'] = $v['NAMA_PRODUK'];
          $data[$k]['NO_ENTRY_SHEET'] = $v['NO_ENTRY_SHEET'];
          $data[$k]['PROVINSI'] = $v['SAL_OFFICE'];
          $data[$k]['NAMA_PROVINSI'] = $v['NAMA_SAL_OFF'];
          $data[$k]['SATUAN'] = $v['SATUAN_SHP'];
          $data[$k]['QTY'] = $v['QTY_SHP'];
          $data[$k]['TARIF'] = $v['TARIF_COST'];
          $data[$k]['TOTAL'] = $v['SHP_COST'];
          $data[$k]['NO_SO'] = $v['NO_SO'];
          $data[$k]['INCOTERM'] = $v['INCO'];
          $data[$k]['SOLD_TO'] = $v['SOLD_TO'];
          $data[$k]['NAMA_SOLD_TO'] = $v['NAMA_SOLD_TO'];
          $data[$k]['SHIP_TO'] = $v['SHIP_TO'];
          $data[$k]['NAMA_SHIP_TO'] = $v['NAMA_SHIP_TO'];
          $data[$k]['ALAMAT_SHIP_TO'] = $v['ALAMAT_SHIP_TO'];
          $data[$k]['DISTRIK'] = $v['SAL_DISTRIK'];
          $data[$k]['NAMA_DISTRIK'] = $v['NAMA_SAL_DIS'];
          $data[$k]['KODE_KECAMATAN'] = $v['KODE_KECAMATAN'];
          $data[$k]['NAMA_KECAMATAN'] = $v['NAMA_KECAMATAN'];
          $data[$k]['REKAP_TAG'] = $v['NO_INV_VENDOR'];
          $data[$k]['NO_INVOICE'] = $v['NO_INVOICE'];
          $data[$k]['NO_PPL_1'] = $v['NO_INV_SAP'];
          $data[$k]['NO_PPL_2'] = $v['ACCOUNTING_DOC'];
          $data[$k]['TANGGAL_KIRIM'] = $v['TANGGAL_KIRIMF'];
          $data[$k]['TANGGAL_SIAP_TAGIH'] = $v['TANGGAL_SIAP_TAGIH'];
          $data[$k]['TANGGAL_DATANG'] = $v['TANGGAL_DATANGF'];
          $data[$k]['TANGGAL_BONGKAR'] = $v['TANGGAL_BONGKARF'];
          $data[$k]['REKAP'] = $v['TANGGAL_INVOICEF'];
          $data[$k]['PPL'] = $v['LAST_UPDATE_DATEF'];
          $data[$k]['TANGGAL_CLREARING'] = $v['TGL_CLEARINGF'];
          $data[$k]['KLAIM_KANTONG'] = $v['QTY_KTG_RUSAK'];
          $data[$k]['KLAIM_SEMEN'] = $v['QTY_SEMEN_RUSAK'];
          $data[$k]['VAL_KANTONG'] = $v['TOTAL_KTG_RUSAK'];
          $data[$k]['VAL_RESAK'] = $v['TOTAL_KTG_REZAK'];
          $data[$k]['VAL_SEMEN'] = $v['TOTAL_SEMEN_RUSAK'];
          $data[$k]['TOTAL_KANTONG'] = $v['TOTAL_KLAIM_KTG'];
          $data[$k]['TOTAL_SEMEN'] = $v['TOTAL_KLAIM_SEMEN'];
          $data[$k]['HARGA_TEBUS'] = $v['HARGA_TEBUS'];
          $data[$k]['PDPPKS'] = $v['PDPKS'];
          $data[$k]['TOTAL_KLAIM'] = $v['TOTAL_KLAIM_ALL'];
          $data[$k]['VENDOR'] = $v['PENGELOLA'];
          $data[$k]['NAMA_VENDOR'] = $v['NAMA_PENGELOLA'];
          $data[$k]['NAMA_KAPAL'] = $v['NAMA_KAPAL'];
          $data[$k]['STATUS'] = $v['STATUS'];
          $data[$k]['STATUS2'] = $v['STATUS2'];
          $data[$k]['NO_PAJAK_EX'] = $v['NO_PAJAK_EX'];
          $data[$k]['TGL_TERIMA_DISTRANS'] = $v['TGL_TERIMA'];
          $data[$k]['TGL_TERIMA_VERIFIKASI'] = isset($datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_VER']) ? $datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_VER'] : "";
          $data[$k]['TGL_TERIMA_BENDAHARA'] = isset($datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_BEND']) ? $datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['TGL_BEND'] : "";
          $data[$k]['TGL_JATUH_TEMPO'] = isset($datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['ZFBDT']) ? $datadoc[$com[$k].$NO_DOC_HDRv[$k].$TAHUNc[$k]]['ZFBDT'] : "";
          $data[$k]['POD'] = $dataAll[$k]['FLAG_POD']!='' ? $dataAll[$k]['FLAG_POD'] : $dataAll[$k]['LOG_FLAG_POD'];
          $data[$k]['KETERANGAN'] = $dataAll[$k]['FLAG_POD']!='' ? $dataAll[$k]['KETERANGAN_POD'] : $dataAll[$k]['LOG_KETERANGAN_POD'];

          $file_pod = "";
          $file_pod2 = "";
          if ($dataAll[$k]['FLAG_POD'] != '') {
            if ($dataAll[$k]['EVIDENCE_POD1'] != '') {
             $file_pod = $urlFile.$dataAll[$k]['EVIDENCE_POD1'];
            } 
          } else {
            if ($dataAll[$k]['LOG_EVIDENCE_POD1'] != '') {
              $file_pod = $urlFile.$dataAll[$k]['LOG_EVIDENCE_POD1'];
            }
          }
          
          if ($dataAll[$k]['FLAG_POD'] != '') {
            if ($dataAll[$k]['EVIDENCE_POD2'] != '') {
             $file_pod2 = $urlFile.$dataAll[$k]['EVIDENCE_POD2'];
            } 
          } else {
            if ($dataAll[$k]['LOG_EVIDENCE_POD2'] != '') {
              $file_pod2 = $urlFile.$dataAll[$k]['LOG_EVIDENCE_POD2'];
            }
          }
          
          $data[$k]['FILE_POD'] = $file_pod;
          $data[$k]['FILE_POD_2'] = $file_pod2;
          $data[$k]['GEOFENCE_POD'] = $dataAll[$k]['FLAG_POD']!='' ? $dataAll[$k]['GEOFENCE_POD'] : $dataAll[$k]['LOG_GEOFENCE_POD'];
          $data[$k]['STATUS_SYNC_POD'] = $dataAll[$k]['FLAG_POD']!='' ? "SUKSES" : "BELUM SINKRON";
        }

        $responseRequest = array(
          'responseCode' => 200,
          'responseMessage' => "Success ".count($data)." data found",
          'data' => $data
        );

        header('Content-Type: application/json');
        echo json_encode($responseRequest);


      }
    } else {
      $responseRequest = array(
        'responseCode' => 401,
        'responseMessage' => $role["keterangan"] ? $role["keterangan"] : "Data User Not Found",
        'data' => null
      );
      header('Content-Type: application/json');
      echo json_encode($responseRequest);
    }
    $byLog = 'getReleaseEpod';
    $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, $token_in);
    break;
}

class getReleaseEpod
{

  private $_basePath;
  private $_sapCon;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function getData($param)
  {
    $data = array();
    return $data;
  }
}
